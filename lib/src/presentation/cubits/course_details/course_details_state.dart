part of 'course_details_cubit.dart';

abstract class CourseDetailsState extends Equatable {
  final CourseDetailsDashboardObj? courseDetailsDashboardData;
  const CourseDetailsState({this.courseDetailsDashboardData});

  @override
  List<Object?> get props => [courseDetailsDashboardData];
}

class CourseDetailsDashboardInfoInitial extends CourseDetailsState {
  CourseDetailsDashboardInfoInitial();
}

class CourseDetailsDashboardInfoLoading extends CourseDetailsState {
  final bool isDataLoading;
  const CourseDetailsDashboardInfoLoading({required this.isDataLoading});

  @override
  List<Object> get props => [isDataLoading];
}

class CourseDetailsDashboardInfoFetched extends CourseDetailsState {
  const CourseDetailsDashboardInfoFetched({super.courseDetailsDashboardData});

  @override
  List<Object?> get props => [courseDetailsDashboardData];
}

class CourseDetailsDashboardInfoError extends CourseDetailsState {
  final String error;
  const CourseDetailsDashboardInfoError({required this.error});

  @override
  List<Object> get props => [error];
}

class CourseAssignmentsFetched extends CourseDetailsState {
  final List<CourseAssignments> courseAssignments;
  const CourseAssignmentsFetched({required this.courseAssignments});

  @override
  List<Object?> get props => [courseAssignments];
}

class CourseDetailsDashboardProgressFetched extends CourseDetailsState {
  final UserCourseProgress userCourseProgress;
  final List<CourseAssignments> courseAssignments;
  const CourseDetailsDashboardProgressFetched(
      {required this.userCourseProgress, required this.courseAssignments});

  @override
  List<Object?> get props => [userCourseProgress, courseAssignments];
}

class CourseDetailsVideoResFetched extends CourseDetailsState {
  final List<CourseVideoResource> courseVideoResource;
  const CourseDetailsVideoResFetched(this.courseVideoResource,
      {required super.courseDetailsDashboardData});

  @override
  List<Object> get props => [courseVideoResource];
}

class CourseDetailsFileResFetched extends CourseDetailsState {
  final List<CourseFileResource> courseFileResource;
  CheckPointData checkPointData;
  CourseDetailsFileResFetched(this.courseFileResource, this.checkPointData,
      {required super.courseDetailsDashboardData});

  @override
  List<Object> get props => [courseFileResource];
}

class CourseResInfoFetchError extends CourseDetailsState {
  final String error;
  const CourseResInfoFetchError(this.error,
      {required super.courseDetailsDashboardData});

  @override
  List<Object> get props => [error];
}

// get course progress
class GetCourseResProgressState extends CourseDetailsState {
  final ResourceProgress progress;
  const GetCourseResProgressState({required this.progress});

  @override
  List<Object> get props => [progress];
}

class GetCourseResProgressFailureState extends CourseDetailsState {
  final String error;
  const GetCourseResProgressFailureState({required this.error});

  @override
  List<Object> get props => [error];
}

class SetCourseResProgressState extends CourseDetailsState {
  const SetCourseResProgressState();

  @override
  List<Object> get props => [];
}

class SetCourseResProgressFailureState extends CourseDetailsState {
  final String error;
  const SetCourseResProgressFailureState({required this.error});

  @override
  List<Object> get props => [error];
}

class PageResourceFetched extends CourseDetailsState {
  final CoursePageResource pageContentData;

  const PageResourceFetched({required this.pageContentData});

  @override
  List<Object> get props => [pageContentData];
}

// Study Material View
// Fetch Page Content

class PageResourceError extends CourseDetailsState {
  final String error;
  const PageResourceError({required this.error});

  @override
  List<Object> get props => [error];
}
