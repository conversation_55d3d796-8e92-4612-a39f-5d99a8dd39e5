import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'login_state.dart';

// class LoginCubit extends Cubit<LoginState> {
//   LoginCubit() : super(const LoginInitial());

//   void updateColor(String text) {
//     if (text.isNotEmpty) {
//       emit(const LoginInitial(buttonColor: true));
//     } else {
//       emit(const LoginInitial(buttonColor: false));
//     }
  
//   }
// }
