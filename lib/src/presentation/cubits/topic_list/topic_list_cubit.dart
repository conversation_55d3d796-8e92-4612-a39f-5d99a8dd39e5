import 'dart:io';

import '/src/domain/models/category/user_category.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../config/app_config/api_constants.dart';
import '../../../config/app_config/preference_config.dart';
import '../../../domain/repositories/topic_repository.dart';
import '../../../domain/repositories/user_repository.dart';
import '../../../utils/constants/helper.dart';

part 'topic_list_state.dart';

class TopicListCubit extends Cubit<TopicListState> {
  final TopicRepository _topicRepository;
  final UserRepository _userRepository;

  TopicListCubit(this._topicRepository, this._userRepository)
      : super(const TopicListInitial());

  String orgId = '';
  bool loading = false;

  Future<void> updateItemColor(String text) async {
    if (text.isNotEmpty) {
      emit(const TopicListItemColour(color: true));
    } else {
      emit(const TopicListItemColour(color: false));
    }
  }

  setLoading(bool loadStatus) {
    loading = loadStatus;
    emit(TopicListLoading(isDataLoading: loadStatus));
  }

  Future<void> getUserTopic() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      orgId = _prefs.getString(PREF_KEY_ORG_ID) ?? '';
      if (orgId.isNotEmpty) {
        emit(await _getUserCategory(orgId));
      }
    } on PostgrestException catch (e) {
      if (e.code == TOKEN_EXPIRED_STATUS_CODE) {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          await getUserTopic();
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      } else {
        debugPrint(
            "[PostgrestException][TopicListCubit][getUserTopic] :${e.details}");
        emit(TopicListError(error: e.details));
      }
    } on AuthException catch (e) {
      debugPrint('[TopicListCubit][getUserTopic][AuthException]: $e');
    } on SocketException catch (e) {
      debugPrint('[TopicListCubit][getUserTopic][SocketException]: $e');
    } on Exception catch (e) {
      debugPrint('[TopicListCubit][getUserTopic][Exception]: $e');
    }
  }

  _getUserCategory(String orgId) async {
    final categoryList = await _topicRepository.getCategoryByHierarchy(orgId);
    return TopicFetchSuccess(categoryList: categoryList);
  }

  Future<void> expanding(bool value, int index) async {
    if (value == true) {
      emit(TopicListExapanding(index: index, expand: true));
    } else {
      emit(TopicListExapanding(index: index, expand: false));
    }
  }
}
