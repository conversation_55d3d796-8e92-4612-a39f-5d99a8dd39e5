// ignore_for_file: unnecessary_null_comparison

import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:rxdart/rxdart.dart';

import '../../../domain/services/localizations/sl_strings.dart';
import '../../../domain/services/localizations/string_keys.dart';
import '../../../utils/constants/strings.dart';
import '../../../utils/helper/regex_strings.dart';
part 'validation_state.dart';

class EmailValidationError {
  static String validate(String value) {
    if (value == null || value.isEmpty) {
      return SLStrings.getTranslatedString(KEY_EMAIL_REQUIRED);
    }

    String pattern = emailValidationRegex;
    RegExp regExp = new RegExp(pattern);
    if (!regExp.hasMatch(value)) {
      return SLStrings.getTranslatedString(KEY_INVALID_EMAIL_ADDRESS);
    }

    return '';
  }
}

class PhoneNumberValidator {
  static String validate(String phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return SLStrings.getTranslatedString(KEY_PHONE_NUM_REQUIRED);
    }

    if (phoneNumber.length != 10) {
      return SLStrings.getTranslatedString(KEY_PHONE_NUM_LENGTH_ERROR);
    }

    // Regular expression for phone numbers
    final RegExp regex = RegExp(phoneNumValidationRegex);
    if (!regex.hasMatch(phoneNumber)) {
      return SLStrings.getTranslatedString(KEY_INVALID_PHONE_NUM);
    }

    return '';
  }
}

class ValidationCubit extends Cubit<ValidationState> with Validation {
  ValidationCubit() : super(validationInitial());

  bool isLoading = false;
  String? error;

  ///
  /// Stream validation for textfields
  ///

  /// Validate name using streams when focus loses
  final _firstNameController = BehaviorSubject<String>();
  Stream<String> get firstName =>
      _firstNameController.stream.transform(validateFirstNameInput);

  Function(String) get updateFirstName => _firstNameController.sink.add;
  void clearFirstNameError() => _firstNameController.sink.addError('');

  /// Validate last name using streams when focus loses
  final _lastNameController = BehaviorSubject<String>();
  Stream<String> get lastName =>
      _lastNameController.stream.transform(validateLastNameInput);

  Function(String) get updateLastName => _lastNameController.sink.add;
  void clearLastNameError() => _lastNameController.sink.addError('');

  /// Validate email using streams when focus loses
  final _emailController = BehaviorSubject<String>();
  Stream<String> get email =>
      _emailController.stream.transform(validateEmailInput);

  Function(String) get changeEmail => _emailController.sink.add;
  void clearEmailError() => _emailController.sink.addError('');

  validateEmail(String email) {
    String status = EmailValidationError.validate(email);
    if (status != '') emit(EmailValidationFailure(emailError: status));
  }

  /// Validate password using streams when focus loses
  final _passwordController = BehaviorSubject<String>();
  Stream<String> get password =>
      _passwordController.stream.transform(validatePasswordInput);

  Function(String) get changePassword => _passwordController.sink.add;
  void clearPasswordError() => _passwordController.sink.addError('');

  /// Validate confirm password using streams when focus loses
  final _confirmPasswordController = BehaviorSubject<String>();
  Stream<String> get confirmPassword => _confirmPasswordController.stream
      .transform(validatePasswords(_passwordController.stream));
  // _confirmPasswordController.stream.transform(validatePasswords(_passwordController.stream));

  // _confirmPasswordController.stream.transform(validateConfirmPasswordInput);
  Function(String) get changeConfirmPassword =>
      _confirmPasswordController.sink.add;
  void clearConfirmPasswordError() =>
      _confirmPasswordController.sink.addError('');

  validatePassword(String password, String confirmPassword) {
    String? status;
    if (password.trim() != confirmPassword.trim()) {
      status = SLStrings.getTranslatedString(KEY_PASSWORDS_MISMATCH);
    }
    emit(PasswordValidationFailure(passwordError: status));
  }

  /// Validate phone number using streams when focus loses
  final _phoneNumController = BehaviorSubject<String>();
  Stream<String> get phoneNum =>
      _phoneNumController.stream.transform(validatePhoneInput);

  Function(String) get changeNumber => _phoneNumController.sink.add;
  void clearPhoneError() => _phoneNumController.sink.addError('');

  validatePhoneNumber(String phNum) {
    String status = PhoneNumberValidator.validate(phNum.trim());
    emit(PhoneNumberValidationFailure(phoneNumError: status));
  }

  void checkSubmitButtonStatus(
      bool allFieldsOccupied,
      String text,
      String firstName,
      String lastName,
      String? emailError,
      String? passwordError,
      String? confirmPasswordError,
      String? phNoError) {
    if (text.isNotEmpty && allFieldsOccupied) {
      // bool isValidEmail = validateEmail();

      if ((emailError == null || emailError.isEmpty) &&
          (passwordError == null || passwordError.isEmpty) &&
          (confirmPasswordError == null || confirmPasswordError.isEmpty) &&
          (phNoError == null || phNoError.isEmpty) &&
          firstName.trim().isNotEmpty &&
          lastName.trim().isNotEmpty) {
        emit(const SubmitButtonStatus(enableButton: true));
      } else {
        emit(const SubmitButtonStatus(enableButton: false));
      }
    } else {
      emit(const SubmitButtonStatus(enableButton: false));
    }
  }

  validateAllFields() {
    Future.delayed((const Duration(milliseconds: 10)), () {
      emit(const ValidatedAllFields());
    });
  }
}

mixin Validation {
  final validateFirstNameInput = StreamTransformer<String, String>.fromHandlers(
      handleData: (_firstName, sink) {
    var error = _firstName.trim().isNotEmpty ? null : FIELD_REQUIRED;

    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(_firstName);
    }
  });

  final validateLastNameInput = StreamTransformer<String, String>.fromHandlers(
      handleData: (_lastName, sink) {
    var error = _lastName.trim().isNotEmpty ? null : FIELD_REQUIRED;

    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(_lastName);
    }
  });

  final validateEmailInput =
      StreamTransformer<String, String>.fromHandlers(handleData: (email, sink) {
    var error = EmailValidationError.validate(email.trim());
    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(email);
    }
  });

  final validatePasswordInput = StreamTransformer<String, String>.fromHandlers(
      handleData: (password, sink) {
    var error;
    if (password == null || password.trim() == '') {
      error = SLStrings.getTranslatedString(KEY_PASSWORD_REQUIRED);
    } else if (password.trim().length < 6) {
      error = SLStrings.getTranslatedString(KEY_PASSWORD_LENGTH_MISMATCH);
    }
    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(password);
    }
  });

  final validateConfirmPasswordInput =
      StreamTransformer<String, String>.fromHandlers(
          handleData: (confirmPassword, sink) {
    var error;

    if (confirmPassword == null || confirmPassword.trim() == '') {
      error = SLStrings.getTranslatedString(KEY_PASSWORD_REQUIRED);
    } else if (confirmPassword.trim().length < 6) {
      error = SLStrings.getTranslatedString(KEY_PASSWORD_LENGTH_MISMATCH);
    }
    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(confirmPassword);
    }
  });

  final validatePhoneInput = StreamTransformer<String, String>.fromHandlers(
      handleData: (phoneNum, sink) {
    var error = PhoneNumberValidator.validate(phoneNum.trim());

    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(phoneNum);
    }
  });

  StreamTransformer<String, String> validatePasswords(Stream<String> password) {
    return StreamTransformer<String, String>.fromHandlers(
      handleData: (confirmPassword, sink) {
        var error;

        password.first.then((passwordValue) {
          if (confirmPassword == passwordValue) {
            error = null;
          } else if (confirmPassword == null || confirmPassword.trim() == '') {
            error = SLStrings.getTranslatedString(KEY_PASSWORD_REQUIRED);
          } else if (confirmPassword.trim().length < 6) {
            error = SLStrings.getTranslatedString(KEY_PASSWORD_LENGTH_MISMATCH);
          } else {
            error = SLStrings.getTranslatedString(KEY_PASSWORDS_MISMATCH);
          }

          if (error != null) {
            sink.addError(error);
          } else {
            sink.add(confirmPassword);
          }
        });
      },
    );
  }
}
