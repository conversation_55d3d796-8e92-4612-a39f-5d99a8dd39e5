import 'dart:ui';
// ignore_for_file: must_be_immutable

import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/app_config/preference_config.dart';
import '../../config/enums/alert_types.dart';
import '../../domain/models/course.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '../cubits/service_cubit/service_cubit.dart';
import '../cubits/splash_screen/splash_screen_cubit.dart';
import '../widgets/exit_on_double_tap.dart';
import '/src/utils/constants/nums.dart';

import '/src/config/themes/app_theme.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/presentation/widgets/empty_screen_view.dart';

import '../widgets/app_bar.dart';
import '../widgets/loading_indicator.dart';
import '/src/presentation/cubits/course_list/course_list_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/router/app_router.dart';
import '../../config/themes/theme_colors.dart';
import '../../utils/constants/strings.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../cubits/topic_list/topic_list_cubit.dart';
import '../widgets/button_widget.dart';

@RoutePage()
class TopicListViewScreen extends HookWidget {
  final bool isFromLogin;
  TopicListViewScreen({Key? key, this.isFromLogin = false}) : super(key: key);

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  String topicValue = "";
  String topicId = "";

  List<Map<String, dynamic>> topicList = [];

  final ValueNotifier<bool> _isDataLoading = ValueNotifier(true);

  final TextEditingController _searchBarController = TextEditingController();
  final FocusNode _searchBarFocusNode = FocusNode();

  final ValueNotifier<List<Map<String, dynamic>>> _searchResult =
      ValueNotifier<List<Map<String, dynamic>>>([]);

  _resetTopicSelection() async {
    TOPIC_NAME = '';

    SharedPreferences _prefs = await SharedPreferences.getInstance();

    _prefs.remove(PREF_KEY_TOPIC_NAME);
  }

  _resetCourseSelection() async {
    COURSE_ID = '';
    COURSE_NAME = '';
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    _prefs.remove(PREF_KEY_COURSE_ID);
    _prefs.remove(PREF_KEY_COURSE_NAME);
  }

  @override
  Widget build(BuildContext context) {
    ValueNotifier<String?> selectedTopic = useState<String?>(null);

    final topicCubit = BlocProvider.of<TopicListCubit>(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final courseListCubit = BlocProvider.of<CourseListCubit>(context);
    final _serviceCubit = BlocProvider.of<ServiceCubit>(context);

    useEffect(() {
      Future<void>.microtask(() async {
        topicCubit.setLoading(true);
        _isDataLoading.value = true;
        await topicCubit.getUserTopic();
        topicList = topicCubit.state.topicList;
        if (TOPIC_NAME.isEmpty) {
          // ensure fcm token uplaod is occurred every time user changes topic
          // upload only when user login for 1st time
          if (!kIsWeb) {
            await _serviceCubit.uploadFCMToken();
            await _serviceCubit.uploadDeviceInfo();
          }
        }
        _searchResult.value = List.from(topicList);

        topicCubit.setLoading(false);
        _isDataLoading.value = false;
      });
      return null;
    }, const []);

    return ExitOnDoubleTap(
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Scaffold(
          key: _scaffoldKey,
          resizeToAvoidBottomInset: false,
          backgroundColor: AppTheme.bgColor,
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(70),
              child: AppBarWidget(
                isFromLogin: true, //!isFromLogin,
                title: TOPIC_LIST_APP_TITLE,
                leadingIconName: BACK_ARROW,
                trailingWidget: Container(),
                leadingBtnAction: () async {
                  SharedPreferences _prefs =
                      await SharedPreferences.getInstance();
                  _prefs.remove(PREF_KEY_TOPIC_ID);
                  _prefs.remove(PREF_KEY_TOPIC_NAME);
                  TOPIC_NAME = '';
                  appRouter.pop();
                },
              )),
          body: BlocBuilder<TopicListCubit, TopicListState>(
              builder: (context, state) {
            if (state is TopicListSuccess) {
              topicList = state.topicList;
            }
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              child: ValueListenableBuilder(
                  valueListenable: _searchResult,
                  builder: (context, searchData, __) {
                    return LayoutBuilder(builder: (context, constraints) {
                      return ValueListenableBuilder(
                          valueListenable: _isDataLoading,
                          builder: (context, __, _) {
                            return Stack(
                              children: [
                                _searchBarWidget(topicCubit, constraints),
                                (_searchResult.value.isNotEmpty)
                                    ? Align(
                                        child: SizedBox(
                                          width: constraints.maxWidth < 720
                                              ? constraints.maxWidth
                                              : 800,
                                          child: Column(
                                            children: [
                                              const SizedBox(height: 80),
                                              Flexible(
                                                child: ScrollConfiguration(
                                                  behavior:
                                                      ScrollConfiguration.of(
                                                              context)
                                                          .copyWith(
                                                    scrollbars: false,
                                                    dragDevices: {
                                                      PointerDeviceKind.touch,
                                                      PointerDeviceKind.mouse,
                                                    },
                                                  ),
                                                  child: ListView.builder(
                                                    itemCount:
                                                        searchData.length,
                                                    itemBuilder:
                                                        (BuildContext context,
                                                            int index) {
                                                      List<Map<String, dynamic>>
                                                          childrenList =
                                                          searchData[index]
                                                              ['Children'];
                                                      return Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: <Widget>[
                                                          Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .only(
                                                                    left: 16,
                                                                    right: 16,
                                                                    top: 10),
                                                            child: Card(
                                                              color: childrenList
                                                                      .isNotEmpty
                                                                  ? AppTheme
                                                                      .ternaryBlue
                                                                  : LmsColors
                                                                      .white,
                                                              margin:
                                                                  const EdgeInsets
                                                                      .only(
                                                                      top: 10,
                                                                      bottom:
                                                                          10),
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12.0),
                                                              ),
                                                              elevation: 2.0,
                                                              child: Theme(
                                                                data: Theme.of(
                                                                        context)
                                                                    .copyWith(
                                                                        dividerColor:
                                                                            LmsColors.transparent),
                                                                child:
                                                                    ExpansionTile(
                                                                  onExpansionChanged: (expansionStatus) async => await _saveInfo(
                                                                      _searchResult
                                                                              .value[index]
                                                                          [
                                                                          'Parent'],
                                                                      selectedTopic,
                                                                      context),
                                                                  leading: Image.asset(
                                                                      'assets/images/topic.png',
                                                                      height:
                                                                          25,
                                                                      width: 25,
                                                                      color: selectedTopic.value !=
                                                                              topicList[index]['Parent'][
                                                                                  "name"]
                                                                          ? AppTheme
                                                                              .iconColor
                                                                          : AppTheme
                                                                              .primaryBlue),
                                                                  trailing: childrenList
                                                                          .isNotEmpty
                                                                      ? const Icon(
                                                                          Icons
                                                                              .arrow_drop_down_outlined,
                                                                          size:
                                                                              30,
                                                                          color: AppTheme
                                                                              .iconColor)
                                                                      : selectedTopic.value ==
                                                                              searchData[index]['Parent']["name"]
                                                                          ? const Icon(
                                                                              size: 30,
                                                                              Icons.check,
                                                                              color: AppTheme.primaryBlue,
                                                                            )
                                                                          : const SizedBox(),
                                                                  collapsedBackgroundColor:
                                                                      LmsColors
                                                                          .white,
                                                                  title: Text(
                                                                      searchData[index]['Parent']
                                                                              [
                                                                              'name'] ??
                                                                          "",
                                                                      style: selectedTopic.value !=
                                                                              topicList[index]['Parent'][
                                                                                  "name"]
                                                                          ? LMSFonts.mediumFont(
                                                                              20.0,
                                                                              AppTheme
                                                                                  .primaryTextColorBlack,
                                                                              1)
                                                                          : const TextStyle(
                                                                              fontSize: 20,
                                                                              color: AppTheme.primaryBlue)),
                                                                  children: [
                                                                    subTopic(
                                                                        index,
                                                                        context,
                                                                        selectedTopic)
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ),
                                              _buttonClick(
                                                  screenWidth,
                                                  topicCubit.state,
                                                  topicCubit,
                                                  topicValue,
                                                  courseListCubit,
                                                  context)
                                            ],
                                          ),
                                        ),
                                      )
                                    : _isDataLoading.value
                                        ? Container()
                                        : const EmptyScreenView(
                                            title: TOPICS_EMPTY),
                                _isDataLoading.value
                                    ? Positioned.fill(
                                        child: LoadingIndicatorClass())
                                    : Container(),
                              ],
                            );
                          });
                    });
                  }),
            );
          }),
        ),
      ),
    );
  }

  Widget subTopic(
      int index, BuildContext context, ValueNotifier<String?> selectedTopic) {
    return Column(
      children: _searchResult.value[index]['Children'].map<Widget>((topic) {
        return Padding(
          padding: const EdgeInsets.only(
            top: 8.0,
            bottom: 8,
            left: 20,
          ),
          child: ListTile(
              dense: true,
              trailing: selectedTopic.value == topic["name"]
                  ? const Icon(
                      size: 30,
                      Icons.check,
                      color: AppTheme.primaryBlue,
                    )
                  : null,
              title: Row(
                children: [
                  const SizedBox(
                    width: 40,
                  ),
                  Expanded(
                    child: Text(topic["name"],
                        style: selectedTopic.value == topic["name"]
                            ? LMSFonts.mediumFont(
                                14.0, AppTheme.primaryAppColor, 0)
                            : LMSFonts.regularFont(
                                14.0,
                                AppTheme.primaryTextColorBlack,
                              )),
                  ),
                ],
              ),
              onTap: () async =>
                  await _saveInfo(topic, selectedTopic, context)),
        );
      }).toList(),
    );
  }

  Widget _divider() {
    return const Divider(
      color: AppTheme.primaryBlue,
      thickness: 1,
      indent: 17,
      endIndent: 17,
    );
  }

  Widget _buttonClick(
      double screenWidth,
      TopicListState state,
      TopicListCubit topicListCubit,
      String topicValue,
      CourseListCubit courseCubit,
      BuildContext context) {
    return Align(
        alignment: Alignment.bottomCenter,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: ButtonWidget(
            child: SizedBox(
              width: double.infinity,
              child: Text(
                COMMON_BUTTON_TEXT,
                style: LMSFonts.buttonStyle(16.0),
                textAlign: TextAlign.center,
              ),
            ),
            // textColor: LmsColors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(buttonRadius),
            ),

            onPressed: topicValue != ""
                ? () async {
                    try {
                      topicListCubit.setLoading(true);
                      _isDataLoading.value = true;

                      final _splashScreenCubit =
                          BlocProvider.of<SplashScreenCubit>(context);
                      await _splashScreenCubit
                          .getSubscriptionPlansforOrg(context);
                      List<Course> courseList =
                          await _fetchCourseForTopic(courseCubit);

                      _resetCourseSelection();
                      topicListCubit.setLoading(false);
                      _isDataLoading.value = false;

                      if (courseList.isEmpty) {
                        await showDialog(
                          context: context,
                          builder: (context) {
                            return MultiActionDialogue(
                                alertType: AlertType.warning,
                                title: WARNING,
                                content: EMPTY_COURSE,
                                cancelBtnText: CLOSE,
                                confirmBtnText: '',
                                onCancelTap: () {
                                  kIsWeb
                                      ? Navigator.of(context).pop()
                                      : appRouter.pop();
                                },
                                onContinueTap: () {});
                          },
                        );
                      } else {
                        kIsWeb
                            ? Beamer.of(context)
                                .beamToNamed('/course-list', data: {
                                "topic": topicValue,
                                "isFromLogin": true,
                              })
                            : await appRouter.push(CourseListViewRoute(
                                topic: topicValue, isFromLogin: true));
                      }
                    } on Exception catch (e) {
                      // TODO
                    }
                  }
                : null,
          ),
        ));
  }

  _searchBarWidget(TopicListCubit topicListCubit, BoxConstraints constraints) {
    return Positioned(
      top: 20,
      left: 0,
      right: 0,
      child: Align(
        child: Container(
          width: constraints.maxWidth < 720 ? constraints.maxWidth : 770,
          decoration: BoxDecoration(
            color: AppTheme.searchBarBg,
            borderRadius: BorderRadius.circular(44.0),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: TextField(
            controller: _searchBarController,
            focusNode: _searchBarFocusNode,
            textInputAction: TextInputAction.search,
            decoration: InputDecoration(
              hintText: 'Search',
              hintStyle:
                  LMSFonts.regularFont(14, AppTheme.searchBarPlaceholderText),
              border: InputBorder.none,
              prefixIcon: Image.asset(
                '$ASSETS_PATH/search_icon.png',
                height: 24,
                width: 24,
              ),
              suffixIcon: GestureDetector(
                onTap: () {
                  _searchBarController.clear();
                  _searchResult.value = topicList;

                  _searchBarFocusNode.unfocus();
                },
                child: const Icon(
                  Icons.close,
                  size: 24,
                  color: AppTheme.primaryAppColor,
                ),
              ),
            ),
            onChanged: (val) => onQueryChanged(topicListCubit, val),
          ),
        ),
      ),
    );
  }

  ///handle plan search
  void onQueryChanged(TopicListCubit topicListCubit, String newQuery) {
    _searchResult.value = [];

    for (var topic in topicList) {
      if ((topic['Parent']["name"] ?? '')
          .toLowerCase()
          .contains(newQuery.trim().toLowerCase())) {
        // if category exists
        // result found
        _searchResult.value.add(topic);
      } else {
        //no result
      }
    }
  }

  Future<List<Course>> _fetchCourseForTopic(CourseListCubit courseCubit) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.setString(PREF_KEY_TOPIC_ID, topicId);
    _prefs.setString(PREF_KEY_TOPIC_NAME, topicValue);
    TOPIC_NAME = topicValue;

    bool _hasAccessToFetchCourse = await _getAccessInfo();

    if (_hasAccessToFetchCourse) {
      await courseCubit.getUserCourse();
      final courseList = courseCubit.state.course;

      if (courseList.isEmpty) {
        _prefs.setString(PREF_KEY_TOPIC_ID, "");
        _prefs.setString(PREF_KEY_TOPIC_NAME, "");
        TOPIC_NAME = "";
      }

      return courseList;
    } else {
      appRouter.push(const NoAccessViewRoute());
      return [];
    }
  }

  Future<bool> _getAccessInfo() async {
    // decides whether to show course selection screen
    String screen = PrivilegeAccessConsts.SCREEN_COURSE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_COURSE_LIST;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _saveInfo(Map<String, dynamic> topic, ValueNotifier<String?> selectedTopic,
      BuildContext context) async {
    _resetTopicSelection();
    topicValue = topic["name"];
    selectedTopic.value = topicValue;
    topicId = topic["id"];
    context.read<TopicListCubit>().updateItemColor(topicValue);
  }
}
