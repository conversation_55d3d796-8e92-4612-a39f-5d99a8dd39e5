import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';

import '../../../config/app_config/preference_config.dart';
import '../../../domain/services/localizations/sl_strings.dart';
import '../../../domain/services/localizations/string_keys.dart';
import '../app_config/app_config_cubit.dart';
import '../validation/validation_cubit.dart';
import '/src/config/app_config/sl_config.dart';
import 'package:supabase/supabase.dart';

import '../../../domain/models/sl_user.dart';

import '/src/utils/constants/strings.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:rxdart/rxdart.dart';

import '../../widgets/alert_popup/permission_popup.dart';
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:image_cropper/image_cropper.dart';
import '../../../domain/models/profile.dart';
import '../../../domain/repositories/user_repository.dart';
import '../../widgets/alert_popup/filetype_warning_widget.dart';
import 'profile_state.dart';
import 'package:package_info_plus/package_info_plus.dart';

class UserCubit extends Cubit<UserState> {
  final UserRepository _userRepository;
  final ImagePicker _imagePicker = ImagePicker();
  File? imageChosen;

  String? userFirstName;
  String? userLastName;
  String? userPhoneNum;
  String? userProfilePic;

  UserCubit(this._userRepository) : super(UserInitial());

  Future getUserInfo(String userId) async {
    emit(await _loadUser());
  }

  void setLoading(bool isLoading, Profile? profile, bool resetData) {
    emit(UserLoadingState(
      isLoading: isLoading,
      profile: state.profile,
      imageChosen: resetData
          ? File(NULL_PATH)
          : state.imageChosen?.path != NULL_PATH
              ? imageChosen
              : File(NULL_PATH),
      orgList: state.orgList,
    ));
  }

  Future<UserState> _loadUser() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    String userId = _prefs.getString(PREF_KEY_USER_ID) ?? '';

    try {
      final userProfileInfo = await _userRepository.getUserInfo(userId);
      final organizations = await _userRepository.getUserOrganizations(userId);
      userFirstName = userProfileInfo.firstName;
      userLastName = userProfileInfo.lastName;
      userPhoneNum = userProfileInfo.phoneNum;
      userProfilePic = userProfileInfo.avatarUrl;
      SLUser.shared.first_name = userProfileInfo.firstName ?? '';
      SLUser.shared.last_name = userProfileInfo.lastName ?? '';
      SLUser.shared.avatar_url = userProfileInfo.avatarUrl ?? '';

      return (UserLoadedState(
        profile: userProfileInfo,
        orgList: organizations,
      ));
    } on Exception catch (error) {
      return (UserErrorState(error: error.toString()));
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint("[Exception][UserCubit][_loadUser] :${e.toString()}");
      return (UserErrorState(error: e.toString()));
    }
  }

  setProfileUpdationStatus(bool didChangeProfile) {
    emit(UserProfileChange(
        didChangeProfile: didChangeProfile,
        profile: state.profile,
        orgList: state.orgList));
  }

  Future updateUserInfo(String userId, String firstName, String lastName,
      String phoneNum, String avatarUrl, File? file) async {
    try {
      setLoading(true, state.profile, false);
      await _userRepository.updateUserInfo(
          userId, firstName, lastName, phoneNum, avatarUrl, file);
      state.profile?.firstName = firstName;
      state.profile?.lastName = lastName;
      state.profile?.phoneNum = phoneNum;
      state.profile?.avatarUrl = avatarUrl;

      emit(UserUpdatedState(profile: state.profile, orgList: state.orgList));
    } on PostgrestException catch (error) {
      debugPrint(
          '[PostgrestException][UserCubit][updateUserInfo] => ${error.details.toString()}');
      emit(UserErrorState(
        error: error.details.toString(),
        profile: state.profile,
        orgList: state.orgList,
      ));
    } on Exception catch (error) {
      debugPrint("[Exception][UserCubit][updateUserInfo] :${error.toString()}");
      emit(UserErrorState(
        error: error.toString() + 'org: $ORG_ID, user:$USER_ID',
        profile: state.profile,
        orgList: state.orgList,
      ));
    }
  }

  Future<String?> pickProfilePicFromGallery(BuildContext context) async {
    try {
      setLoading(true, state.profile, false);
      if (Platform.isAndroid) {
        await retrieveLostData();
      }
      imageChosen = File(NULL_PATH);

      final PermissionStatus status;

      // Only check for storage < Android 13
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

        ///
        /// If your app targets Android 13 or higher
        /// and needs to access image files,
        /// you must request 'READ_MEDIA_IMAGES' permission
        /// instead of the READ_EXTERNAL_STORAGE permission:
        ///
        if (androidInfo.version.sdkInt >= 33) {
          status = await Permission.photos.request();
        } else {
          status = await Permission.storage.request();
        }
      } else {
        // iOS
        status = await Permission.photos.request();
      }

      if (status == PermissionStatus.granted ||
          status == PermissionStatus.limited) {
        final pickedFile = await _imagePicker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 50,
          // maxWidth: 80,
          // maxHeight: 80,
        );

        if (pickedFile != null) {
          if (pickedFile.path.endsWith('.jpeg') ||
              pickedFile.path.endsWith('.jpg') ||
              pickedFile.path.endsWith('.png') ||
              pickedFile.path.endsWith('.HEIC')) {
            final File file = File(pickedFile.path);
            File? croppedFile = await cropImage(file) ?? File(NULL_PATH);
            final targetPath = await getApplicationCacheDirectory();
            final compressedFile = await compressAndGetFile(
                croppedFile,
                targetPath.path +
                    "${DateTime.now().millisecondsSinceEpoch}.jpeg");
            int fileSize = compressedFile.lengthSync();
            if (fileSize <= 1024 * 1024) {
              imageChosen = compressedFile;
              emit(ProfilePicUpdatedFromGallery(
                imageFile: croppedFile,
                profile: state.profile,
                orgList: state.orgList,
                imageChosen: imageChosen,
              ));
            } else {
              return showFileWarning(context,
                  SLStrings.getTranslatedString(KEY_FILE_SIZE_WARNING));
            }
          } else {
            return showFileWarning(
                context, SLStrings.getTranslatedString(KEY_FILE_TYPE_WARNING));
          }
          return pickedFile.path;
        }
      } else {
        if (status == PermissionStatus.denied) {
          emit(
              UserLoadingState(profile: state.profile, orgList: state.orgList));
          return null;
        } else if (status == PermissionStatus.permanentlyDenied) {
          await showWarning(context, GALLERY, '');
          emit(
              UserLoadingState(profile: state.profile, orgList: state.orgList));
          return null;
        } else {
          await showWarning(context, GALLERY,
              SLStrings.getTranslatedString(KEY_FAILED_TO_OPEN_GALLERY));
        }
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint(
          "[Exception][UserCubit][pickProfilePicFromGallery] :${e.toString()}");
      emit(UserLoadingState(profile: state.profile, orgList: state.orgList));
    }
    return null;
  }

  Future<String?> captureProfilePicFromCamera(BuildContext context) async {
    try {
      if (Platform.isAndroid) {
        await retrieveLostData();
      }
      imageChosen = File(NULL_PATH);

      PermissionStatus status = await Permission.camera.request();

      if (status == PermissionStatus.granted) {
        // final organizations =
        //     await _userRepository.getUserOrganizations(userId);
        final pickedFile = await _imagePicker.pickImage(
            source: ImageSource.camera, imageQuality: 50);

        if (pickedFile != null) {
          final File file = File(pickedFile.path);
          File? croppedFile = await cropImage(file) ?? File(NULL_PATH);
          final targetPath = await getApplicationCacheDirectory();
          final compressedFile = await compressAndGetFile(
              croppedFile,
              targetPath.path +
                  "${DateTime.now().millisecondsSinceEpoch}.jpeg");
          int fileSize = compressedFile.lengthSync();
          if (fileSize <= 1024 * 1024) {
            imageChosen = compressedFile;
            emit(ProfilePicUpdatedFromCamera(
              imageFile: croppedFile,
              profile: state.profile,
              orgList: state.orgList,
              imageChosen: imageChosen,
            ));
          } else {
            return showFileWarning(
                context, SLStrings.getTranslatedString(KEY_FILE_SIZE_WARNING));
          }
        }
        return pickedFile?.path;
      } else {
        if (status == PermissionStatus.denied) {
          emit(
              UserLoadingState(profile: state.profile, orgList: state.orgList));
          return null;
        } else if (status == PermissionStatus.permanentlyDenied) {
          showWarning(context, CAMERA, '');
          emit(
              UserLoadingState(profile: state.profile, orgList: state.orgList));
          return null;
        } else {
          showWarning(context, CAMERA,
              SLStrings.getTranslatedString(KEY_FAILED_TO_OPEN_CAMERA));
        }
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint(
          "[Exception][UserCubit][pickProfilePicFromCamera] :${e.toString()}");
      emit(UserLoadingState(profile: state.profile, orgList: state.orgList));
    }
    return null;
  }

  // 2. compress file and get file.
  Future<File> compressAndGetFile(File file, String targetPath) async {
    var result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: 85,
    );
    return File(result!.path);
  }

  Future<File?> cropImage(File file) async {
    try {
      // Image cropping functionality removed for Android SDK 35 compatibility
      // Return the original file without cropping
      return file;
    } on Exception catch (e) {
      debugPrint("[Exception][UserCubit][cropImage] :${e.toString()}");
      return null;
    }
  }

  Future<void> getLostData() async {
    try {
      final LostDataResponse response = await _imagePicker.retrieveLostData();
      if (response.isEmpty) {
        return;
      }
      final List<XFile>? files = response.files;
    } on Exception catch (e) {
      debugPrint("[Exception][UserCubit][getLostData] :${e.toString()}");
    }
  }

  Future<void> retrieveLostData() async {
    final LostDataResponse response = await _imagePicker.retrieveLostData();
    String? _retrieveDataError;
    try {
      if (response.isEmpty) {
        return;
      }
      if (response.file != null) {
        if (response.type == RetrieveType.video) {
          return;
        } else {
          if (response.files == null) {
            // _setImageFileListFromFile(response.file);
          } else {
            // _mediaFileList = response.files;
          }
        }
      } else {
        _retrieveDataError = response.exception?.code;
        debugPrint(_retrieveDataError);
      }
    } on Exception catch (e) {
      debugPrint("[Exception][UserCubit][retrieveLostData] :${e.toString()}");
    }
  }

  Future resetProfileInfo(UserState state, UserCubit userCubit) async {
    try {
      userCubit.setLoading(true, state.profile, true);
      state.profile?.firstName = userCubit.userFirstName;
      state.profile?.lastName = userCubit.userLastName;
      state.profile?.phoneNum = userCubit.userPhoneNum ?? '';
      imageChosen = File(NULL_PATH);
      userCubit.setLoading(false, state.profile, true);
      return Future(() {});
    } on Exception catch (e) {
      debugPrint("[Exception][UserCubit][resetProfileInfo] :${e.toString()}");
    }
  }

  ///
  /// app version
  ///
  getVersionNumber() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String appVersion = packageInfo.version;
    SLConfig.APP_VERSION = appVersion.toString();
  }

  // user signout
  Future<dynamic> userSignout() async {
    try {
      final response = await _userRepository.userSignout();
      if (response == true) {
        emit(UserSignoutSuccess());
      } else {
        emit(UserSignoutError(error: "Failed to logout.Please try again"));
      }
    } on AuthException catch (e) {
      emit(UserSignoutError(error: e.message));
    } on SocketException {
      emit(UserSignoutError(error: 'Network Disconnected'));
    } on Exception catch (e) {
      emit(UserSignoutError(error: e.toString()));
    }
  }

  ///
  /// Stream validation for textfields
  ///

  /// Validate name using streams when focus loses
  final _firstNameController = BehaviorSubject<String>();
  Stream<String> get firstName =>
      _firstNameController.stream.transform(validateFirstNameInput);

  Function(String) get updateFirstName => _firstNameController.sink.add;

  final validateFirstNameInput = StreamTransformer<String, String>.fromHandlers(
      handleData: (_firstName, sink) {
    var error = _firstName.trim().isNotEmpty
        ? null
        : SLStrings.getTranslatedString(KEY_FIELD_REQUIRED);

    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(_firstName);
    }
  });

  /// Validate last name using streams when focus loses
  final _lastNameController = BehaviorSubject<String>();
  Stream<String> get lastName =>
      _lastNameController.stream.transform(validateLastNameInput);

  Function(String) get updateLastName => _lastNameController.sink.add;

  final validateLastNameInput = StreamTransformer<String, String>.fromHandlers(
      handleData: (_lastName, sink) {
    var error = _lastName.trim().isNotEmpty
        ? null
        : SLStrings.getTranslatedString(KEY_FIELD_REQUIRED);

    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(_lastName);
    }
  });

  /// Validate phone number using streams when focus loses
  final _phoneController = BehaviorSubject<String>();
  Stream<String> get phoneNum =>
      _phoneController.stream.transform(validatePhoneNumInput);

  Function(String) get updatephoneNum => _phoneController.sink.add;

  final validatePhoneNumInput = StreamTransformer<String, String>.fromHandlers(
      handleData: (_phoneNum, sink) {
    String? error = PhoneNumberValidator.validate(_phoneNum.trim());
    error = error.trim().isEmpty ? null : error;

    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(_phoneNum);
    }
  });
}
