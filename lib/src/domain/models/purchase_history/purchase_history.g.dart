// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase_history.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PurchaseHistory _$PurchaseHistoryFromJson(Map<String, dynamic> json) =>
    PurchaseHistory(
      result: json['result'] == null
          ? null
          : PurchaseHistoryResult.fromJson(
              json['result'] as Map<String, dynamic>),
      status: json['status'] as String?,
    );

Map<String, dynamic> _$PurchaseHistoryToJson(PurchaseHistory instance) =>
    <String, dynamic>{
      'result': instance.result,
      'status': instance.status,
    };

PurchaseHistoryResult _$PurchaseHistoryResultFromJson(
        Map<String, dynamic> json) =>
    PurchaseHistoryResult(
      pendingPlanListsByUser: (json['pending_plan_lists_by_user']
              as List<dynamic>?)
          ?.map(
              (e) => PendingPlanListsByUser.fromJson(e as Map<String, dynamic>))
          .toList(),
      purchasedPlanListsByUser:
          (json['purchased_plan_lists_by_user'] as List<dynamic>?)
              ?.map((e) =>
                  PurchasedPlanListsByUser.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$PurchaseHistoryResultToJson(
        PurchaseHistoryResult instance) =>
    <String, dynamic>{
      'pending_plan_lists_by_user': instance.pendingPlanListsByUser,
      'purchased_plan_lists_by_user': instance.purchasedPlanListsByUser,
    };

PendingPlanListsByUser _$PendingPlanListsByUserFromJson(
        Map<String, dynamic> json) =>
    PendingPlanListsByUser(
      status: json['status'] as String?,
      planName: PendingPlanListsByUser._uppercaseFirstName(
          json['plan_name'] as String?),
      price: (json['price'] as num?)?.toInt(),
      currency: $enumDecodeNullable(_$CurrencyEnumMap, json['currency']),
      subscriptionValidTo: json['subscription_valid_to'] == null
          ? null
          : DateTime.parse(json['subscription_valid_to'] as String),
      subscriptionValidFrom: json['subscription_valid_from'] == null
          ? null
          : DateTime.parse(json['subscription_valid_from'] as String),
    );

Map<String, dynamic> _$PendingPlanListsByUserToJson(
        PendingPlanListsByUser instance) =>
    <String, dynamic>{
      'price': instance.price,
      'currency': _$CurrencyEnumMap[instance.currency],
      'status': instance.status,
      'plan_name': instance.planName,
      'subscription_valid_to': instance.subscriptionValidTo?.toIso8601String(),
      'subscription_valid_from':
          instance.subscriptionValidFrom?.toIso8601String(),
    };

const _$CurrencyEnumMap = {
  Currency.CURRENCY_INR: 'inr',
  Currency.INR: 'INR',
};

PurchasedPlanListsByUser _$PurchasedPlanListsByUserFromJson(
        Map<String, dynamic> json) =>
    PurchasedPlanListsByUser(
      price: (json['price'] as num?)?.toInt(),
      currency: $enumDecodeNullable(_$CurrencyEnumMap, json['currency']),
      planName: json['plan_name'] as String?,
      isExpired: json['is_expired'] as bool? ?? false,
      purchaseDate: json['purchase_date'] == null
          ? null
          : DateTime.parse(json['purchase_date'] as String),
      subscriptionStatus: json['subscription_status'] as String?,
      subscriptionValidTo: json['subscription_valid_to'] == null
          ? null
          : DateTime.parse(json['subscription_valid_to'] as String),
      subscriptionValidFrom: json['subscription_valid_from'] == null
          ? null
          : DateTime.parse(json['subscription_valid_from'] as String),
      userSubscriptionEndDate: json['user_subscription_end_date'] == null
          ? null
          : DateTime.parse(json['user_subscription_end_date'] as String),
      userSubscriptionStartDate: json['user_subscription_start_date'] == null
          ? null
          : DateTime.parse(json['user_subscription_start_date'] as String),
    );

Map<String, dynamic> _$PurchasedPlanListsByUserToJson(
        PurchasedPlanListsByUser instance) =>
    <String, dynamic>{
      'price': instance.price,
      'currency': _$CurrencyEnumMap[instance.currency],
      'plan_name': instance.planName,
      'is_expired': instance.isExpired,
      'purchase_date': instance.purchaseDate?.toIso8601String(),
      'subscription_status': instance.subscriptionStatus,
      'subscription_valid_to': instance.subscriptionValidTo?.toIso8601String(),
      'subscription_valid_from':
          instance.subscriptionValidFrom?.toIso8601String(),
      'user_subscription_end_date':
          instance.userSubscriptionEndDate?.toIso8601String(),
      'user_subscription_start_date':
          instance.userSubscriptionStartDate?.toIso8601String(),
    };
