import 'dart:convert';

import 'package:SmartLearn/src/utils/constants/strings.dart';
import 'package:flutter/material.dart';

import '../../config/app_config/api_config.dart';
import '../../domain/models/check_point_quiz.dart';
import '/src/domain/models/exam_summary.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/models/exam.dart';
import '../../domain/models/exam_review.dart';
import '../../domain/models/question_data.dart';
import '/src/domain/repositories/exam_repository.dart';

class ExamRepositoryImpl extends ExamRepository {
  ExamRepositoryImpl();

  // fetch exam list for the course
  @override
  Future<List<Exam>> fetchExamList(
      String orgId, String courseId, String userId) async {
    final response =
        await Supabase.instance.client.rpc(APIConfig.GET_QUIZ_LIST_FN, params: {
      "course_id": courseId,
      "org_id": orgId,
      "user_id": userId,
      "quizes_of_course_data": {
        "quiz_type": ["Main", "Practice"]
      }
    });
    List<dynamic> responseData = response ?? [];

    final exams = examFromJson(jsonEncode(responseData));
    return exams;
  }

// fetch attended exam list for the course
  @override
  Future<List<Exam>> fetchAttemptedExamList(
      String orgId, String courseId, String userId) async {
    final response = await Supabase.instance.client.rpc(
        APIConfig.GET_ATTEMPTED_QUIZ_LIST_FN,
        params: {"course_id": courseId, "org_id": orgId, "user_id": userId});

    List<dynamic> responseData = response ?? [];
    final exams = examFromJson(jsonEncode(responseData));
    return exams;
  }

// fetch questions for an exam
  @override
  Future<List<QuestionData>> fetchQuestionsOfQuiz(String examId) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_QUESTIONS_OF_QUIZ_FN, params: {
      'quiz_id': examId,
    });

    List<dynamic> responseData = response ?? [];

    List<QuestionData> questionFromJson = List<QuestionData>.from(
        responseData.map((x) => QuestionData.fromJson(x, examId)));

    return questionFromJson;
  }

  // fetch checkpoint quiz data
  @override
  Future<CheckPointQuiz?> fetchCheckpointQuiz(
      Map<String, dynamic> jsonBody) async {
    CheckPointQuiz? quizData;

    final response = await Supabase.instance.client
        .rpc(APIConfig.FETCH_CHECKPOINT_QUIZ, params: jsonBody);

    if (response != null) {
      Map<String, dynamic> responseData = response;
      quizData = CheckPointQuiz.fromJson(responseData);
    }

    return quizData;
  }

  /// Called when student starts exam
  @override
  Future<dynamic> startExam(Map<String, dynamic> jsonBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.START_QUIZ_FN, params: jsonBody);
    if (response != null) {
      Map<String, dynamic> responseData = response;
      return responseData;
    } else {
      return false;
    }
  }

  @override
  Future<dynamic> submitExam(Map<String, dynamic> jsonBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.SUBMIT_QUIZ_FN, params: jsonBody);

    if (response != null) {
      Map<String, dynamic> responseData = response;

      return responseData['status'];
    } else {
      return false;
    }
  }

// Fetch Exam review list
  @override
  Future<List<dynamic>> fetchExamReviewList(
      String examId, String quizAttemptId) async {
    final response = await Supabase.instance.client
        .from(APIConfig.V_FETCH_REVIEW_LIST)
        .select()
        .eq("quiz_id", examId)
        .eq('quiz_attempt_id', quizAttemptId);

    List<dynamic> responseData = response;
    // Map<String, dynamic> responseDecoded =
    //     responseData.isNotEmpty ? jsonDecode(responseData.first) : {};
    // List<ExamReview> questionFromJson =
    //     List<ExamReview>.from(responseData.map((x) => ExamReview.fromJson(x)));

    return responseData;
  }

// Calculate Exam Result
  @override
  Future<dynamic> calculateExamResult(quizAttemptId) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.CALCULATE_RESULT_FN, params: {
      'quiz_attempt_id': quizAttemptId,
    });

    if (response != null) {
      return response;
    }
  }

  // Fetch Exam Summary
  @override
  Future<List<ExamSummary>> fetchExamResultStatisticalData(
      quizAttemptId, quizId) async {
    debugPrint("USER_ID:- $USER_ID");
    debugPrint("quizAttemptId:- $quizAttemptId");
    debugPrint("quizId:- $quizId");
    final response = await Supabase.instance.client.rpc(
        APIConfig.FETCH_RESULTS_FN,
        params: {'quiz_attempt_id': quizAttemptId, "quiz_id": quizId});
    List<dynamic> responseData = response ?? [];

    List<ExamSummary> questionFromJson = List<ExamSummary>.from(
        responseData.map((x) => ExamSummary.fromJson(x)));

    return questionFromJson;
  }

  @override
  Future<dynamic> submitCheckPointExam(Map<String, dynamic> jsonBody) async {
    try {
      final response = await Supabase.instance.client
          .rpc(APIConfig.END_CHECKPOINT_QUIZ, params: jsonBody);
      Map<String, dynamic> responseData = response;
      return responseData;
    } on Exception catch (e) {
      debugPrint('[Exception][submitCheckPointExam] => ${e.toString()}');
    }
  }
}
