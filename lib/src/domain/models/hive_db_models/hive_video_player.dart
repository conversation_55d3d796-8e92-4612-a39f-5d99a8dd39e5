import 'package:hive/hive.dart';

part 'hive_video_player.g.dart';

@HiveType(typeId: 1)
class HiveVideoPlayerData {
  @HiveField(0)
  final String videoId;
  
  @HiveField(1)
  final String title;
  
  @HiveField(2)
  final String thumbnailUrl;
  
  @HiveField(3)
  final int positionInSeconds;
  
  @HiveField(4)
  final bool isCompleted;

  HiveVideoPlayerData({
    required this.videoId,
    required this.title,
    required this.thumbnailUrl,
    required this.positionInSeconds,
    required this.isCompleted,
  });
}
