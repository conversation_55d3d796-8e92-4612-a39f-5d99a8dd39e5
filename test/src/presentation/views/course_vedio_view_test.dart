import 'package:SmartLearn/src/presentation/views/course_details_view.dart';
import 'package:SmartLearn/src/utils/constants/api_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  Widget createWidgetUnderTest() {
    return MaterialApp(
      title: 'Flutter Tests',
      home: CourseDetailsView(
        courseId: MOCK_COURSE_ID,
        courseName: MOCK_COURSE_ID,
        isFromLogin: false,
      ),
    );
  }

  testWidgets('course video view ...', (tester) async {
    (widgetTester) async {
      await widgetTester.pumpWidget(createWidgetUnderTest());
      expect(find.text('Flutter tests'), findsOneWidget);
    };
  });
}
