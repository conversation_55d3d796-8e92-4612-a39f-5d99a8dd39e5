import 'package:equatable/equatable.dart';
// ignore: unused_import
import 'package:floor/floor.dart';

import '../../utils/constants/strings.dart';
import 'source.dart';

class Organization extends Equatable {
  final String? userId;
  final String? orgId;
  final String? orgName;
  
  const Organization({
    this.userId,
    this.orgId,
    this.orgName,
  });

  factory Organization.fromJson(Map<String, dynamic> json) {
    return Organization(
        userId: json['user_id'],
        orgId: json['org_id'],
        orgName: json['org_name']);
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [userId, orgId, orgName];
  }
}
