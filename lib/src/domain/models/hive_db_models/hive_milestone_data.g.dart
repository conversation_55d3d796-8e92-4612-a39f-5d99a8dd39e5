// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_milestone_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveMileStoneDataAdapter extends TypeAdapter<HiveMileStoneData> {
  @override
  final int typeId = 2;

  @override
  HiveMileStoneData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveMileStoneData(
      id: fields[0] as int?,
      videoId: fields[1] as String,
      milestoneId: fields[2] as String,
      sessionStart: fields[3] as int,
      sessionEnd: fields[4] as int,
      sessionResponse: fields[5] as String,
      sessionStatus: fields[6] as int,
    );
  }

  @override
  void write(BinaryWriter writer, HiveMileStoneData obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.videoId)
      ..writeByte(2)
      ..write(obj.milestoneId)
      ..writeByte(3)
      ..write(obj.sessionStart)
      ..writeByte(4)
      ..write(obj.sessionEnd)
      ..writeByte(5)
      ..write(obj.sessionResponse)
      ..writeByte(6)
      ..write(obj.sessionStatus);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveMileStoneDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
