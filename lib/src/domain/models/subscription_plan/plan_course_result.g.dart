// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_course_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlanCourseResult _$PlanCourseResultFromJson(Map<String, dynamic> json) =>
    PlanCourseResult(
      result: (json['result'] as List<dynamic>?)
          ?.map((e) => PlanCourse.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] as String?,
    );

Map<String, dynamic> _$PlanCourseResultToJson(PlanCourseResult instance) =>
    <String, dynamic>{
      'result': instance.result,
      'status': instance.status,
    };

PlanCourse _$PlanCourseFromJson(Map<String, dynamic> json) => PlanCourse(
      courseId: json['course_id'] as String?,
      resources: (json['resources'] as List<dynamic>?)
          ?.map((e) => Resource.fromJson(e as Map<String, dynamic>))
          .toList(),
      courseName: json['course_name'] as String?,
      courseDesc: json['summary'] as String?,
      mappingType: json['mapping_type'] as String?,
      intro: (json['resource_intro_data'] as List<dynamic>?)
          ?.map((e) => ResourcesIntro.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PlanCourseToJson(PlanCourse instance) =>
    <String, dynamic>{
      'course_id': instance.courseId,
      'resources': instance.resources,
      'course_name': instance.courseName,
      'summary': instance.courseDesc,
      'mapping_type': instance.mappingType,
      'resource_intro_data': instance.intro,
    };

Resource _$ResourceFromJson(Map<String, dynamic> json) => Resource(
      name: json['name'] as String?,
      type: json['type'] as String?,
      description: json['description'] as String?,
      resourceId: json['resource_id'] as String?,
    );

Map<String, dynamic> _$ResourceToJson(Resource instance) => <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'description': instance.description,
      'resource_id': instance.resourceId,
    };

ResourcesIntro _$ResourcesIntroFromJson(Map<String, dynamic> json) =>
    ResourcesIntro(
      id: json['id'] as String?,
      intro_name: json['name'] as String?,
    );

Map<String, dynamic> _$ResourcesIntroToJson(ResourcesIntro instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.intro_name,
    };
