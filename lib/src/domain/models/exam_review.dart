import '/src/config/enums/question_answer_type.dart';

import 'question_data.dart';

class ExamReview {
  String? quizId;
  String? questionWithOptions;
  String? responseSummary;
  String? orgId;
  String? courseId;
  String? courseName;
  String? quizName;
  String? startTime;
  String? endTime;
  String? duration;

  String? questionType;
  String? answerType;

  ExamReview(
      {this.quizId,
      this.questionWithOptions,
      this.responseSummary,
      this.orgId,
      this.courseId,
      this.courseName,
      this.quizName,
      this.endTime,
      this.startTime,
      this.duration,
      this.questionType,
      this.answerType});

  ExamReview.fromJson(Map<String, dynamic> json) {
    quizId = json['quiz_id'] ?? '';
    questionWithOptions = json['question_with_options'];
    responseSummary = json['response_summary'] != null &&
            json['response_summary'].toString().trim() != ''
        ? json['response_summary']
        : 'N/A';
    orgId = json['org_id'] ?? '';
    courseId = json['course_id'] ?? '';
    courseName = json['course_name'] ?? '';
    quizName = json['quiz_name'] ?? '';
    startTime = json['quiz_start_time'] ?? '';
    endTime = json['quiz_end_time'] ?? '';
    duration = json['duration'] ?? '';
  }

  /// for performance improvement
  /// fetching review data  from db
  /// from the exam info
  ///
  factory ExamReview.fromDBJson(
      Map<String, dynamic> dbJson, Map<String, dynamic> responseJson) {
    String answerType = QuestAnswerType.plainText.value;

    List<dynamic> jsonList = dbJson['answers'] ?? [];
    jsonList
        .removeWhere((element) => element == null || element["answer"] == '');
    List<Answer> answers = jsonList.isNotEmpty
        ? jsonList.map((item) => Answer.fromJson(item)).toList()
        : [];

    jsonList = dbJson['selected_answer_ids'] ?? [];
    List<String> selectedIds = jsonList.isNotEmpty
        ? jsonList.map((item) => item.toString()).toList()
        : [];
    // List<Map<String, dynamic>> answersChosen = [];
    List<String> answersChosen = [];

    for (var answer in answers) {
      if (answer.answerType == QuestAnswerType.html.value) {
        answerType = answer.answerType;
      }
      for (var selecetdAnsId in selectedIds) {
        if (answer.answerId == selecetdAnsId) {
          // answersChosen.add(
          //     {'answer': answer.answerVal, 'answer_type': answer.answer_type});
          answersChosen.add(answer.answerVal);
        }
      }
    }

    String answerText =
        answersChosen.toSet().toList().join(', ').toString().trim();

    // quizId : responseJson['quiz_id'] ?? '';
    // questionWithOptions : dbJson['question_text'];
    // responseSummary : answerText.isNotEmpty ? answerText : 'N/A';
    // orgId : responseJson['org_id'] ?? '';
    // courseId : responseJson['course_id'] ?? '';
    // courseName : responseJson['course_name'] ?? '';
    // quizName : responseJson['quiz_name'] ?? '';
    // startTime : responseJson['quiz_start_time'] ?? '';
    // endTime : responseJson['quiz_end_time'] ?? '';
    // duration : responseJson['duration'] ?? '';
    // questionType : dbJson['question_type'] ?? QuestAnswerType.plainText.value;
    // answerType : QuestAnswerType.plainText.value;

    return ExamReview(
      quizId: responseJson['quiz_id'] ?? '',
      questionWithOptions: dbJson['question_text'],
      responseSummary: answerText.isNotEmpty ? answerText : 'N/A',
      orgId: responseJson['org_id'] ?? '',
      courseId: responseJson['course_id'] ?? '',
      courseName: responseJson['course_name'] ?? '',
      quizName: responseJson['quiz_name'] ?? '',
      startTime: responseJson['quiz_start_time'] ?? '',
      endTime: responseJson['quiz_end_time'] ?? '',
      duration: responseJson['duration'] ?? '',
      questionType: dbJson['question_type'] ?? QuestAnswerType.plainText.value,
      answerType: answerType,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['quiz_id'] = quizId;
    data['question_with_options'] = questionWithOptions;
    data['response_summary'] = responseSummary;
    data['org_id'] = orgId;
    data['course_id'] = courseId;
    data['course_name'] = courseName;
    data['quiz_name'] = quizName;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['duration'] = duration;

    return data;
  }
}
