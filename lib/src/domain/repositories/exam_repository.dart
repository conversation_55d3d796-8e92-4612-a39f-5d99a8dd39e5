import '../models/check_point_quiz.dart';
import '/src/domain/models/exam.dart';
import '../models/exam_summary.dart';
import '../models/question_data.dart';

abstract class ExamRepository {
  Future<List<Exam>> fetchExamList(
      String orgId, String courseId, String userId);
  Future<List<Exam>> fetchAttemptedExamList(
      String orgId, String courseId, String userId);

  Future<List<QuestionData>> fetchQuestionsOfQuiz(String examId);
  Future<CheckPointQuiz?> fetchCheckpointQuiz(Map<String, dynamic> jsonBody);

  Future<dynamic> submitExam(Map<String, dynamic> jsonBody);
  Future<dynamic> submitCheckPointExam(Map<String, dynamic> jsonBody);
  Future<dynamic> startExam(Map<String, dynamic> jsonBody);

  Future<List<dynamic>> fetchExamReviewList(
      String examId, String quizAttemptId);
  Future<dynamic> calculateExamResult(String quizAttemptId);
  Future<List<ExamSummary>> fetchExamResultStatisticalData(
      quizAttemptId, quizId);
}
