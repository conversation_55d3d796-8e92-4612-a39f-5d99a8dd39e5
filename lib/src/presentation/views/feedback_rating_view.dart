import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/utils/constants/strings.dart';
import 'package:flutter/material.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../config/themes/theme_colors.dart';
import '../widgets/button_widget.dart';

typedef IntCallback = Function(int val);

class FeedbackDialouge extends StatelessWidget {
  final TextEditingController feedbackController;
  final int selectedUptoIndex;
  final IntCallback updateStars;
  final VoidCallback onSubmit;

  const FeedbackDialouge({
    super.key,
    required this.feedbackController,
    required this.selectedUptoIndex,
    required this.updateStars,
    required this.onSubmit,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      },
      child: Center(
        child: Material(
          color: Colors.transparent,
          child: Container(
            margin: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
            decoration: BoxDecoration(
                color: AppTheme.secondaryBlue,
                borderRadius: BorderRadius.circular(8.0)),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    FEEDBACK_TITLE,
                    textAlign: TextAlign.center,
                    style:
                        LMSFonts.boldFont(20, AppTheme.primaryTextColorBlack),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    FEEDBACK_CONTENT,
                    textAlign: TextAlign.center,
                    style: LMSFonts.semiBoldFont(
                        16, AppTheme.primaryTextColorBlack.withOpacity(0.5)),
                  ),
                  const SizedBox(height: 20),
                  _feedbackStars(context),
                  const SizedBox(height: 20),
                  _feedbackTextField(),
                  const SizedBox(height: 25),
                  _buttonWidget(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _feedbackStars(BuildContext context) {
    return Wrap(children: [
      _starImage(context, false, 0),
      _starImage(context, false, 1),
      _starImage(context, false, 2),
      _starImage(context, false, 3),
      _starImage(context, false, 4),
    ]);
  }

  Widget _starImage(BuildContext context, bool isSelected, int index) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: GestureDetector(
        onTap: () {
          /// TO DO: Change color upto selection
          updateStars(index);
        },
        child: Container(
          color: Colors.transparent,
          child: Image.asset(
            selectedUptoIndex >= index
                ? 'assets/images/star_filled.png'
                : 'assets/images/star.png',
            height: 40,
            width: 40,
            color: selectedUptoIndex >= index
                ? Colors.yellow
                : AppTheme.pitchBlack,
          ),
        ),
      ),
    );
  }

  Widget _feedbackTextField() {
    return TextField(
      controller: feedbackController,
      keyboardType: TextInputType.multiline,
      maxLines: 7,
      style: LMSFonts.regularFontWithHeight(
          16, AppTheme.primaryTextColorBlack, 1.0),
      decoration: InputDecoration(
          hintText: FEEDBACK_PLACEHOLDER,
          focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppTheme.borderColor)),
          enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppTheme.borderColor))),
    );
  }

  Widget _buttonWidget(BuildContext context) {
    Size popupSize = MediaQuery.of(context).size;
    return Align(
      alignment: Alignment.bottomCenter,
      child: ButtonWidget(
          child: SizedBox(
            width: double.infinity,
            child: Text(
              FEEDBACK_BTN,
              style: LMSFonts.buttonStyle(18),
              textAlign: TextAlign.center,
            ),
          ),
          textColor: LmsColors.white,
          minimumSize: Size(popupSize.width, 50),
          color: AppTheme.primaryBlue,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          onPressed: () async {
            /// TO DO: Submit data
            Navigator.pop(context);
            showMyDialogue(context);
          }),
    );
  }

  // Function to show the dialogue
  void showMyDialogue(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: FEEDBACK_SUBMITTED,
          iconWidget: const Icon(Icons.check_circle, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }
}
