import 'package:equatable/equatable.dart';
// ignore: unused_import
import 'package:floor/floor.dart';

class Course extends Equatable {
  final String? course_id;
  final String? category_id;
  final String? short_name;
  final String? full_name;
  const Course(
      {this.course_id, this.category_id, this.short_name, this.full_name});

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      course_id: json['course_id'],
      category_id: json['category_id'],
      short_name: json['short_name'].toString().trim(),
      full_name: json['full_name'].toString().trim(),
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [course_id, category_id, short_name, full_name];
  }
}
