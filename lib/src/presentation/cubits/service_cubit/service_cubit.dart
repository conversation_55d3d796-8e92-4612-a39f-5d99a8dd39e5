import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../domain/services/notification/notification_handler.dart';
import '../../../utils/helper/privilege_access_mapper.dart';
import '/src/config/app_config/preference_config.dart';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../utils/constants/helper.dart';
import '/src/domain/models/push_notification.dart';
import '/src/domain/repositories/service_repository.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../utils/constants/strings.dart';

part 'service_state.dart';

class ServiceCubit extends Cubit<ServiceState> {
  ServiceCubit(this._serviceRepository) : super(ServiceInitial());

  final ServiceRepository _serviceRepository;

  uploadFCMToken() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    String fcmToken = _prefs.getString(PREF_KEY_FCM_TOKEN) ?? '';

    final reqBody = {'device_token': fcmToken, 'status': 'active'};
    try {
      if (USER_ID.isNotEmpty && ORG_ID.isNotEmpty && fcmToken.isNotEmpty) {
        final response =
            await _serviceRepository.uploadFCMToken(reqBody, USER_ID, ORG_ID);

        if (response != null && response['status'] == 'success') {
          Map<String, dynamic> json = response;
          json['fcm_token'] = fcmToken;
          PushNotificationModel _pushNotificationObj =
              PushNotificationModel.fromJson(json);
          debugPrint(
              '[ServiceCubit][uploadFCMToken]: token upload successfull!!!!!');

          emit(ServiceFCMUploaded(pushNotificationModel: _pushNotificationObj));
        }
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][ServiceCubit][uploadFCMToken] :${e.toString()}");
    } on Exception catch (e) {
      debugPrint('[ServiceCubit][uploadFCMToken]: ${e.toString()}');
    }
  }

  Future<bool> _checkFCMUploadAccess() async {
    // decides whether to show course selection screen
    String screen = PrivilegeAccessConsts.SCREEN_LOGIN;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_PUSH_NOTIFICATION;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  Future<bool> _checkDeviceInfoAccess() async {
    // decides whether to show course selection screen
    String screen = PrivilegeAccessConsts.SCREEN_LOGIN;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_DEVICE_INFO;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  uploadDeviceInfo() async {
    try {
      if (await _checkDeviceInfoAccess()) {
        final _deviceInfoObj = await fetchDeviceInfo();

        if (_deviceInfoObj != null) {
          final _reqJson = _deviceInfoObj.toJson();
          if (USER_ID.isNotEmpty && ORG_ID.isNotEmpty) {
            final response = await _serviceRepository.uploadDeviceInfo(
                _reqJson, USER_ID, ORG_ID);
          }
        }
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][ServiceCubit][uploadDeviceInfo] :${e.toString()}");
    } on Exception catch (e) {
      debugPrint(
          "[Exception][ServiceCubit][uploadDeviceInfo] :${e.toString()}");
    }
  }

  fetchVideoViewStatus() async {
    try {
      if (ORG_ID.isNotEmpty) {
        // decides whether to show section details
        String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
        String accessRequiredFeature =
            PrivilegeAccessConsts.ACTION_GET_SECTION_DETAILS;
        bool shouldAccessResources =
            await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
                accessRequiredFeature: accessRequiredFeature);
        if (shouldAccessResources) {
          final response =
              await _serviceRepository.fetchVideoViewStatus(ORG_ID);

          dynamic responseBody = response;
          if (responseBody != null) {
            Map<String, dynamic> responseJson = responseBody;
            if (responseJson.containsKey('has_user_watched_any_videos')) {
              bool hasUserWatchedVideos =
                  responseJson['has_user_watched_any_videos'] ?? false;
              if (!hasUserWatchedVideos) {
                await initLocalNotification();
              }
              // emit(ServiceVideoViewStatus(
              //     shouldTriggerLocalNotifications:
              //         responseJson['has_user_watched_any_videos']));
            }
          }
          // emit(const ServiceVideoViewStatus(
          //     shouldTriggerLocalNotifications: false));
        }
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][ServiceCubit][fetchVideoViewStatus] :${e.toString()}");
    } on Exception catch (e) {
      debugPrint(
          "[Exception][ServiceCubit][fetchVideoViewStatus] :${e.toString()}");
    }
  }

  // init local notifications
  /// notify if the logged student not watched any video yet
  initLocalNotification() async {
    NotificationService _notificationService = NotificationService();
    await _notificationService.handleLocalNotifications();
  }
}
