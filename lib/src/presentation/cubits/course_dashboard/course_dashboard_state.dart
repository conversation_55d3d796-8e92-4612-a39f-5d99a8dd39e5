part of 'course_dashboard_cubit.dart';

class CourseDashboardState extends Equatable {
  final List<CourseDashboardInfo> courseDashboardInfo;

  const CourseDashboardState({required this.courseDashboardInfo});

  @override
  List<Object?> get props => [];
}

class CourseDashboardInitial extends CourseDashboardState {
  const CourseDashboardInitial({required super.courseDashboardInfo});
}

class CourseDashboardDetailsFetched extends CourseDashboardState {
  // final List<CoursesInfo> courseAllInfo;
  final CourseDetails? courseDetails;

  const CourseDashboardDetailsFetched(
      {required super.courseDashboardInfo, this.courseDetails});

  @override
  List<Object?> get props => [courseDashboardInfo, courseDetails];
}

class CourseDashboardDetailsError extends CourseDashboardState {
  final String error;
  const CourseDashboardDetailsError(
      {required super.courseDashboardInfo, required this.error});
  @override
  List<Object> get props => [error, courseDashboardInfo];
}

// class CourseDetailsFetched extends CourseDashboardState {
//   final CourseDetails? courseDetails;

//   const CourseDetailsFetched(
//       {required super.courseDashboardInfo, required this.courseDetails});

//   @override
//   List<Object?> get props => [courseDetails, courseDashboardInfo];
// }

class CourseDashBoardDetailsError extends CourseDashboardState {
  final String error;
  const CourseDashBoardDetailsError(
      {required super.courseDashboardInfo, required this.error});
  @override
  List<Object> get props => [error, courseDashboardInfo];
}

class DashboardCurrentAffairsFetched extends CourseDashboardState {
  final List<CurrentAffairs> currentAffairs;
  const DashboardCurrentAffairsFetched(
      {this.currentAffairs = const [], required super.courseDashboardInfo});

  @override
  List<Object> get props => [currentAffairs];
}

class DashboardCurrentAffairsFailure extends CourseDashboardState {
  final String error;
  const DashboardCurrentAffairsFailure(
      {required super.courseDashboardInfo, required this.error});

  @override
  List<Object> get props => [error];
}
