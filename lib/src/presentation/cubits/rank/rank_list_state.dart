part of 'rank_list_cubit.dart';

abstract class RankListState extends Equatable {
  List<Rank> rankList = [];
  RankListState({this.rankList = const []});

  @override
  List<Object> get props => [];
}

class RankListInitial extends RankListState {}

class RankListLoading extends RankListState {
  final bool isDataLoading;
  RankListLoading({required this.isDataLoading});

  @override
  List<Object> get props => [isDataLoading];
}

class RankListFetched extends RankListState {
  RankListFetched({super.rankList});

  List<Object> get props => [rankList];
}

class RankListError extends RankListState {
  final String error;
  RankListError({required this.error});
}
