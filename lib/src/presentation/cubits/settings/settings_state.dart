part of 'settings_cubit.dart';

abstract class SettingsState extends Equatable {
  final bool toggleVlaue;

  const SettingsState({
    this.toggleVlaue = false,
  });

  @override
  List<Object> get props => [];
}

class SettingsInitial extends SettingsState {}

class SettingstoggleNotification extends SettingsState {
  final bool toggleVlaue;
  SettingstoggleNotification({
    required this.toggleVlaue,
  });
  @override
  List<Object> get props => [toggleVlaue];
}
