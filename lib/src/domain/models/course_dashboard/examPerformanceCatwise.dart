// To parse this JSON data, do
//
//     final examPerformanceCatwise = examPerformanceCatwiseFromJson(jsonString);

import 'dart:convert';

ExamPerformanceCatwise examPerformanceCatwiseFromJson(String str) =>
    ExamPerformanceCatwise.fromJson(json.decode(str));

String examPerformanceCatwiseToJson(ExamPerformanceCatwise data) =>
    json.encode(data.toJson());

class ExamPerformanceCatwise {
  List<QuizSummary> quizSummary;
  String quizAttemptId;
  PerformanceSummary performanceSummary;

  ExamPerformanceCatwise({
    required this.quizSummary,
    required this.quizAttemptId,
    required this.performanceSummary,
  });

  factory ExamPerformanceCatwise.fromJson(Map<String, dynamic> json) =>
      ExamPerformanceCatwise(
        quizSummary: json["quiz_summary"] != null
            ? List<QuizSummary>.from(
                json["quiz_summary"].map((x) => QuizSummary.fromJson(x)))
            : [],
        quizAttemptId: json["quiz_attempt_id"] ?? "",
        performanceSummary:
            PerformanceSummary.fromJson(json["performance_summary"] ?? {}),
      );

  Map<String, dynamic> toJson() => {
        "quiz_summary": List<dynamic>.from(quizSummary.map((x) => x.toJson())),
        "quiz_attempt_id": quizAttemptId,
        "performance_summary": performanceSummary.toJson(),
      };
}

class PerformanceSummary {
  List<QuestionCategory> questionCategory;

  PerformanceSummary({
    required this.questionCategory,
  });

  factory PerformanceSummary.fromJson(Map<String, dynamic> json) =>
      PerformanceSummary(
        questionCategory: (json["question_category"] as List<dynamic>?)
                ?.map((x) => QuestionCategory.fromJson(x))
                .toList() ??
            [],
      );

  Map<String, dynamic> toJson() => {
        "question_category": questionCategory.map((x) => x.toJson()).toList(),
      };
}

class QuestionCategory {
  String name;
  Details details;

  QuestionCategory({
    required this.name,
    required this.details,
  });

  factory QuestionCategory.fromJson(Map<String, dynamic> json) =>
      QuestionCategory(
        name: json["name"] ?? "",
        details: Details.fromJson(json["details"] ?? {}),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "details": details.toJson(),
      };
}

class Details {
  String categoryId;
  int marksObtained;
  int correctAnswers;
  int totalQuestions;

  Details({
    required this.categoryId,
    required this.marksObtained,
    required this.correctAnswers,
    required this.totalQuestions,
  });

  factory Details.fromJson(Map<String, dynamic> json) => Details(
        categoryId: json["category_id"] ?? "",
        marksObtained: json["marks_obtained"] ?? 0,
        correctAnswers: json["correct_answers"] ?? 0,
        totalQuestions: json["total_questions"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "category_id": categoryId,
        "marks_obtained": marksObtained,
        "correct_answers": correctAnswers,
        "total_questions": totalQuestions,
      };
}

class QuizSummary {
  Course course;
  String quizId;
  String quizName;
  double passMarks;
  double totalMarks;
  int totalQuestions;
  double totalMarksObtained;

  QuizSummary({
    required this.course,
    required this.quizId,
    required this.quizName,
    required this.passMarks,
    required this.totalMarks,
    required this.totalQuestions,
    required this.totalMarksObtained,
  });

  factory QuizSummary.fromJson(Map<String, dynamic> json) => QuizSummary(
        course: Course.fromJson(json["course"] ?? {}),
        quizId: json["quiz_id"] ?? "",
        quizName: json["quiz_name"] ?? "",
        passMarks: (json["pass_marks"] ?? 0).toDouble(),
        totalMarks: (json["total_marks"] ?? 0).toDouble(),
        totalQuestions: json["total_questions"] ?? 0,
        totalMarksObtained: (json["total_marks_obtained"] ?? 0).toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "course": course.toJson(),
        "quiz_id": quizId,
        "quiz_name": quizName,
        "pass_marks": passMarks,
        "total_marks": totalMarks,
        "total_questions": totalQuestions,
        "total_marks_obtained": totalMarksObtained,
      };
}

class Course {
  String id;
  String name;
  String shortName;
  String description;

  Course({
    required this.id,
    required this.name,
    required this.shortName,
    required this.description,
  });

  factory Course.fromJson(Map<String, dynamic> json) => Course(
        id: json["id"] ?? "",
        name: json["name"] ?? "",
        shortName: json["short_name"] ?? "",
        description: json["description"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "short_name": shortName,
        "description": description,
      };
}
