import '/src/config/themes/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../utils/constants/strings.dart';
import '../cubits/connectivity/connectivity_cubit.dart';
import '../cubits/connectivity/connectivity_state.dart';

class ConnectivityStatusWidget extends StatelessWidget {
  int count = 0;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConnectivityCubit, ConnectivityState>(
      builder: (context, state) {
        if (state is InternetConnected &&
            state.connectionType == ConnectionType.wifi) {
          count = 0;
          return Container();
        } else if (state is InternetConnected &&
            state.connectionType == ConnectionType.mobile) {
          count = 0;
          return Container();
        } else if (state is InternetDisconnected) {
          count += 1;
          return errmsg(NO_NETWORK);
        }
        count = 0;
        return Container();
      },
    );
  }

  Widget errmsg(String text) {
    return Container(
      padding: const EdgeInsets.all(10.00),
      margin: const EdgeInsets.only(bottom: 10.00),
      color: AppTheme.sessionExpiredBgColor,
      child: Row(children: [
        Container(
          margin: const EdgeInsets.only(right: 6.00),
          child:
              const Icon(Icons.info, color: AppTheme.sessionExpiredTextColor),
        ),
        Text(text,
            style: const TextStyle(color: AppTheme.sessionExpiredTextColor)),
      ]),
    );
  }
}
