import 'package:equatable/equatable.dart';

class PushNotificationModel extends Equatable {
  final String fcmToken;
  final String pushNotificationId;

  const PushNotificationModel(
      {required this.fcmToken, required this.pushNotificationId});

  factory PushNotificationModel.fromJson(Map<String, dynamic> json) {
    return PushNotificationModel(
      fcmToken: json['fcm_token'],
      pushNotificationId: json['push_notification_id'],
    );
  }

  @override
  List<Object?> get props => [fcmToken, pushNotificationId];
}
