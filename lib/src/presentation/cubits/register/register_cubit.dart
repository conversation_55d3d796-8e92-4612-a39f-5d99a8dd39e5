import '/src/config/app_config/preference_config.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../domain/services/provider/language_provider.dart';
import '/src/domain/services/localizations/sl_strings.dart';

import 'package:provider/provider.dart' as provider;
import '/src/domain/repositories/register_repository.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:supabase/supabase.dart';

part 'register_state.dart';

class RegisterCubit extends Cubit<RegisterState> {
  RegisterRepository _registerRepository;

  RegisterCubit(this._registerRepository) : super(RegisterInitial());

  Future<void> register(
      String email, String password, Map<String, dynamic> data) async {
    try {
      final response =
          await _registerRepository.userRegistration(email, password, data);
      if (response == true) {
        emit(ExitUserError());
      } else {
        final jsonReq = {
          "mail_user": email,
          "mail_subject": "New Registration",
          "mail_content": "",
        };
        await _registerRepository.sentEmailNotification(jsonReq);
        emit(RegisterSuccess(user: response));
      }
    } on AuthException catch (e) {
      debugPrint("[AuthException][RegisterCubit][register] :${e.toString()}");
      emit(RegisterError(e.message));
    } on Exception catch (e) {
      debugPrint("[Exception][RegisterCubit][register] :${e.toString()}");
      emit(RegisterError(e.toString()));
    }
  }

  setLoading(bool isLoading) {
    emit(RegisterLoading(isLoading: isLoading));
  }

  handleLanguageSelection(BuildContext context, String selectedLang) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.setString(PREF_KEY_CURRENT_LANG, selectedLang);
    SLStrings.currentLanguage = selectedLang;
    SLStrings.currentLocale = Locale(selectedLang);
    provider.Provider.of<LanguageProvider>(context, listen: false)
        .changeCurrentLanguage(selectedLang);
    emit(ChangeLangauge(selectedLang: selectedLang));
  }

  refreshSignupWindow() {
    emit(Refresh());
  }
}
