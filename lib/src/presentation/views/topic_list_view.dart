import 'dart:ui';
// ignore_for_file: must_be_immutable

import '/src/domain/models/category/user_category.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/app_config/preference_config.dart';
import '../../config/enums/alert_types.dart';
import '../cubits/service_cubit/service_cubit.dart';
import '../cubits/splash_screen/splash_screen_cubit.dart';
import '../widgets/exit_on_double_tap.dart';
import '/src/utils/constants/nums.dart';

import '/src/config/themes/app_theme.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/presentation/widgets/empty_screen_view.dart';

import '../widgets/app_bar.dart';
import '../widgets/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/router/app_router.dart';
import '../../utils/constants/strings.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../cubits/topic_list/topic_list_cubit.dart';

@RoutePage()
class TopicListViewScreen extends HookWidget {
  final bool isFromLogin;
  TopicListViewScreen({Key? key, this.isFromLogin = false}) : super(key: key);

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  List<Map<String, dynamic>> topicList = [];
  List<UserCategory> categories = [];

  final ValueNotifier<bool> _isDataLoading = ValueNotifier(true);

  final TextEditingController _searchBarController = TextEditingController();
  final FocusNode _searchBarFocusNode = FocusNode();

  final ValueNotifier<List<UserCategory>> _searchResult =
      ValueNotifier<List<UserCategory>>([]);

  _resetTopicSelection() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.remove(PREF_KEY_TOPIC_ID);
    _prefs.remove(PREF_KEY_TOPIC_NAME);
    TOPIC_NAME = '';
  }

  _resetCourseSelection() async {
    COURSE_ID = '';
    COURSE_NAME = '';
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    _prefs.remove(PREF_KEY_COURSE_ID);
    _prefs.remove(PREF_KEY_COURSE_NAME);
  }

  // initialise category list fetch process
  _initCategoryList(TopicListCubit topicCubit) async {
    await topicCubit.getUserTopic();
    TopicListState state = topicCubit.state;
    if (state is TopicFetchSuccess) {
      categories = state.categoryList;
    }
  }

  @override
  Widget build(BuildContext context) {
    final topicCubit = BlocProvider.of<TopicListCubit>(context);
    final _serviceCubit = BlocProvider.of<ServiceCubit>(context);

    useEffect(() {
      Future<void>.microtask(() async {
        topicCubit.setLoading(true);
        _isDataLoading.value = true;
        await _initCategoryList(topicCubit);
        if (TOPIC_NAME.isEmpty) {
          // ensure fcm token uplaod is occurred every time user changes topic
          // upload only when user login for 1st time
          if (!kIsWeb) {
            await _serviceCubit.uploadFCMToken();
            await _serviceCubit.uploadDeviceInfo();
            await _serviceCubit.fetchVideoViewStatus();
          }
        }
        _searchResult.value = List.from(categories);

        topicCubit.setLoading(false);
        _isDataLoading.value = false;
      });
      return null;
    }, const []);

    return ExitOnDoubleTap(
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Scaffold(
          key: _scaffoldKey,
          resizeToAvoidBottomInset: false,
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(70),
              child: AppBarWidget(
                isFromLogin: true, //!isFromLogin,
                title: TOPIC_LIST_APP_TITLE,
                leadingIconName: BACK_ARROW,
                trailingWidget: Container(),
                leadingBtnAction: () async {
                  await _resetTopicSelection();
                  appRouter.popForced();
                },
              )),
          body: BlocBuilder<TopicListCubit, TopicListState>(
              builder: (context, state) {
            if (state is TopicFetchSuccess) {
              topicList = state.topicList;
            }
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              child: ValueListenableBuilder(
                  valueListenable: _searchResult,
                  builder: (context, searchData, __) {
                    return LayoutBuilder(builder: (context, constraints) {
                      return ValueListenableBuilder(
                          valueListenable: _isDataLoading,
                          builder: (context, __, _) {
                            return Stack(
                              children: [
                                _searchBarWidget(topicCubit, constraints),
                                (searchData.isNotEmpty)
                                    ? _mainContainer(
                                        context, constraints, searchData)
                                    : _isDataLoading.value
                                        ? Container()
                                        : const Positioned(
                                            top: 80,
                                            left: 0,
                                            right: 0,
                                            bottom: 0,
                                            child: EmptyScreenView(
                                                title: TOPICS_EMPTY),
                                          ),
                                _isDataLoading.value
                                    ? Positioned.fill(
                                        child: LoadingIndicatorClass())
                                    : Container(),
                              ],
                            );
                          });
                    });
                  }),
            );
          }),
        ),
      ),
    );
  }

  Widget _mainContainer(BuildContext context, BoxConstraints constraints,
      List<UserCategory> searchData) {
    return Align(
      child: SizedBox(
        width: constraints.maxWidth < 720 ? constraints.maxWidth : 800,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 80),
              ScrollConfiguration(
                behavior: ScrollConfiguration.of(context).copyWith(
                  scrollbars: false,
                  dragDevices: {
                    PointerDeviceKind.touch,
                    PointerDeviceKind.mouse,
                  },
                ),
                child: _categoryListView(context, searchData),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _categoryListView(
      BuildContext context, List<UserCategory> searchData) {
    return Flexible(
      child: ListView.builder(
        padding: const EdgeInsets.only(top: 5),
        itemCount: searchData.length,
        itemBuilder: (BuildContext context, int mainIndex) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: buildExpansionTile(
                context, searchData[mainIndex], true, mainIndex),
          );
        },
      ),
    );
  }

  Widget buildExpansionTile(BuildContext context, UserCategory category,
      bool isMainTopic, int itemIndex) {
    return Theme(
      data: Theme.of(context).copyWith(
        listTileTheme: ListTileTheme.of(context).copyWith(
            dense: true,
            minVerticalPadding: 0,
            horizontalTitleGap: 0,
            contentPadding: EdgeInsets.zero),
      ),
      child: ExpansionTileTheme(
        data: const ExpansionTileThemeData(
          iconColor: Colors.white,
          childrenPadding: EdgeInsets.zero,
          tilePadding: EdgeInsets.zero,
        ),
        child: ExpansionTile(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(isMainTopic ? 10.0 : 0)),
          collapsedShape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(isMainTopic ? 10.0 : 0)),
          collapsedBackgroundColor: isMainTopic
              ? AppTheme.secondaryAppColor
              : AppTheme.sectionCardBgColor,
          backgroundColor: isMainTopic
              ? AppTheme.secondaryAppColor
              : AppTheme.sectionCardBgColor,
          onExpansionChanged: (expansionStatus) async {
            if (isMainTopic) {
              _searchResult.value[itemIndex].isExpanded = expansionStatus;
            }
            _searchResult.notifyListeners();
          },
          trailing: category.subCategory.isNotEmpty
              ? Container(
                  height: double.infinity,
                  margin: EdgeInsets.only(bottom: isMainTopic ? 0 : 10),
                  child: Icon(
                      category.isExpanded || !isMainTopic
                          ? Icons.arrow_drop_down
                          : Icons.arrow_right,
                      size: 30,
                      color: isMainTopic
                          ? AppTheme.whiteColor
                          : AppTheme.okBtnGreen),
                )
              : const SizedBox(),
          title: isMainTopic
              ? _mainTopic(context, category, category.name, true)
              : _subTopicExpansionTile(
                  context,
                  category,
                ),
          tilePadding: EdgeInsets.only(
            right: 8,
            left: isMainTopic ? 0 : (10 * category.childIndex).toDouble(),
            top: isMainTopic
                ? 0
                : itemIndex == 0 && category.childIndex == 1
                    ? 12
                    : 0,
          ),
          clipBehavior: Clip.antiAlias,
          expandedCrossAxisAlignment: CrossAxisAlignment.start,
          expandedAlignment: Alignment.center,
          childrenPadding: EdgeInsets.zero,
          children: category.subCategory
              .map((subTopic) => buildExpansionTile(context, subTopic, false,
                  category.subCategory.indexOf(subTopic)))
              .toList(),
        ),
      ),
    );
  }

  Widget _mainTopic(BuildContext context, UserCategory category, String name,
      bool removePadding) {
    return Container(
      child: Material(
        color: AppTheme.secondaryAppColor,
        child: InkWell(
          splashColor: Colors.white.withOpacity(0.3),
          highlightColor: Colors.white.withOpacity(0.3),
          onTap: () {
            _navigateToCourseView(context, category);
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(),
            clipBehavior: Clip.antiAlias,
            child: Row(
              children: [
                Image.asset(
                  '$ASSETS_PATH/topic.png',
                  width: 30,
                  height: 30,
                ),
                const SizedBox(width: 10), // Space between image and text
                Text(
                  name,
                  style: LMSFonts.semiBoldFont(16, AppTheme.whiteColor),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _subTopicExpansionTile(
      BuildContext context, UserCategory subCategory) {
    return Container(
      color: AppTheme.sectionCardBgColor,
      child: _subTopicTile(context, subCategory, false),
    );
  }

  Widget _subTopicTile(
      BuildContext context, UserCategory subCategory, bool isExpanded) {
    return Container(
      margin: const EdgeInsets.only(left: 6, bottom: 10),
      decoration: BoxDecoration(
          color: AppTheme.topicExpandedViewColor,
          borderRadius: BorderRadius.circular(5.0)),
      clipBehavior: Clip.antiAlias,
      child: Material(
        color: AppTheme.topicExpandedViewColor,
        child: InkWell(
          splashColor: AppTheme.okBtnGreen.withOpacity(0.3),
          highlightColor: AppTheme.okBtnGreen.withOpacity(0.3),
          onTap: () {
            _navigateToCourseView(context, subCategory);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(
                horizontal: horizontalMargin, vertical: 5),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Image.asset(
                      isExpanded
                          ? '$ASSETS_PATH/sub_topic_open.png'
                          : '$ASSETS_PATH/sub_topic_closed.png',
                      height: 24,
                      width: 24),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(subCategory.name,
                            style: LMSFonts.regularFontWithHeight(
                                14.0, AppTheme.secondaryTextColor, 1.2)),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  _searchBarWidget(TopicListCubit topicListCubit, BoxConstraints constraints) {
    return Positioned(
      top: 20,
      left: 0,
      right: 0,
      child: Align(
        child: Container(
          width: constraints.maxWidth < 720 ? constraints.maxWidth : 770,
          decoration: BoxDecoration(
            color: AppTheme.searchBarBg,
            borderRadius: BorderRadius.circular(44.0),
          ),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: TextField(
            controller: _searchBarController,
            focusNode: _searchBarFocusNode,
            textInputAction: TextInputAction.search,
            decoration: InputDecoration(
              hintText: 'Search',
              hintStyle:
                  LMSFonts.regularFont(14, AppTheme.searchBarPlaceholderText),
              border: InputBorder.none,
              prefixIcon: Image.asset(
                '$ASSETS_PATH/search_icon.png',
                height: 24,
                width: 24,
              ),
              suffixIcon: GestureDetector(
                onTap: () {
                  _searchBarController.clear();
                  _searchResult.value = categories;

                  _searchBarFocusNode.unfocus();
                },
                child: const Icon(
                  Icons.close,
                  size: 24,
                  color: AppTheme.primaryAppColor,
                ),
              ),
            ),
            onChanged: (val) => onQueryChanged(topicListCubit, val),
          ),
        ),
      ),
    );
  }

  ///handle plan search
  void onQueryChanged(TopicListCubit topicListCubit, String newQuery) {
    _searchResult.value = [];

    for (var topic in categories) {
      if (topic.name.toLowerCase().contains(newQuery.trim().toLowerCase())) {
        // if category exists
        // result found
        _searchResult.value.add(topic);
      } else {
        //no result
      }
    }
  }

  _navigateToCourseView(
      BuildContext context, UserCategory selectedCategory) async {
    final topicCubit = BlocProvider.of<TopicListCubit>(context);
    List<Course> courseList = selectedCategory.courses;

    try {
      topicCubit.setLoading(true);
      _isDataLoading.value = true;

      final _splashScreenCubit = BlocProvider.of<SplashScreenCubit>(context);
      await _splashScreenCubit.getSubscriptionPlansforOrg(context);

      _resetCourseSelection();
      topicCubit.setLoading(false);
      _isDataLoading.value = false;

      if (courseList.isEmpty) {
        await showDialog(
          context: context,
          builder: (context) {
            return MultiActionDialogue(
                alertType: AlertType.warning,
                title: WARNING,
                content: EMPTY_COURSE,
                cancelBtnText: CLOSE,
                confirmBtnText: '',
                onCancelTap: () {
                  kIsWeb ? Navigator.of(context).pop() : appRouter.popForced();
                },
                onContinueTap: () {});
          },
        );
      } else {
        await _handleTopicSelection(context, selectedCategory);
        kIsWeb
            ? Beamer.of(context).beamToNamed('/course-list', data: {
                "topic": selectedCategory.name,
                "isFromLogin": true,
                "courses": courseList,
              })
            : await appRouter.push(
                CourseListViewRoute(
                    category: selectedCategory, isFromLogin: true),
              );
      }
    } on Exception catch (e) {
      debugPrint('[TopicListView][_navigateToCourseList]: ${e.toString()}');
    }
  }

  /// save selected topic details
  _handleTopicSelection(BuildContext context, UserCategory selectedCategory) async {
    await _resetTopicSelection();
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.setString(PREF_KEY_TOPIC_ID, selectedCategory.id);
    _prefs.setString(PREF_KEY_TOPIC_NAME, selectedCategory.name);
    TOPIC_NAME = selectedCategory.name;
    _searchBarFocusNode.unfocus();
  }
}
