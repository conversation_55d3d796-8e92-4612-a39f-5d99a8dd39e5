import 'package:equatable/equatable.dart';
// ignore: unused_import
import 'package:floor/floor.dart';

import '../../utils/constants/strings.dart';
import 'source.dart';

class TopicList extends Equatable {
  final String? id;
  final String? name;
  final String? description;
  final String? parent_id;
  final String? org_id;
  final int? sortorder;
  final bool? visible;
  final String? created_at;
  final String? updated_at;
  const TopicList(
      {this.id,
      this.name,
      this.description,
      this.parent_id,
      this.org_id,
      this.sortorder,
      this.visible = false,
      this.created_at,
      this.updated_at});

  factory TopicList.fromJson(Map<String, dynamic> json) {
    return TopicList(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      parent_id: json['parent_id'],
      org_id: json['org_id'],
      sortorder: json['sortorder'],
      visible: json['visible'],
      created_at: json['created_at'],
      updated_at: json['updated_at'],
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      id,
      name,
      description,
      parent_id,
      org_id,
      sortorder,
      visible,
      created_at,
      updated_at
    ];
  }
}
