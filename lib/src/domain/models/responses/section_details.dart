import 'package:equatable/equatable.dart';

import '../../../config/enums/section_status.dart';

class SectionDetails extends Equatable {
  final String? sectionId;
  final String? courseId;
  final String? name;
  final String? summary;
  final int? sectionOrder;
  final List<Resources> resources;
  final List<Modules> modules;

  const SectionDetails({
    this.sectionId,
    this.courseId,
    this.name,
    this.summary,
    this.sectionOrder,
    required this.resources,
    required this.modules,
  });

  factory SectionDetails.fromJson(Map<String, dynamic> json) {
    List<dynamic> jsonModuleList = json['modules'] ?? [];
    jsonModuleList.removeWhere((element) => element == null);
    jsonModuleList.removeWhere((element) =>
        element['module_name'] == null || element['module_name'] == '');
    List<Modules> moduleList = jsonModuleList.isNotEmpty
        ? jsonModuleList.map((e) => Modules.fromJson(e)).toList()
        : [];
    List<dynamic> jsonList = json['resources'] ?? [];
    jsonList.removeWhere((element) => element == null);
    jsonList.removeWhere(
        (element) => element['name'] == null || element['name'] == '');
    List<Resources> resourcesList = jsonList.isNotEmpty
        ? jsonList.map((e) => Resources.fromJson(e)).toList()
        : [];

    return SectionDetails(
      sectionId: json['section_id'] ?? '',
      courseId: json['course_id'] ?? '',
      name: json['name'] ?? '',
      summary: json['summary'] ?? '',
      sectionOrder: json['section_order'],
      resources: resourcesList,
      modules: moduleList,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      sectionId,
      courseId,
      name,
      summary,
      sectionOrder,
      resources,
      modules
    ];
  }
}

class Resources extends Equatable {
  final String id;
  final String name;
  final String type;
  final String url;
  final String createdAt;
  final String updatedAt;
  final String orgId;
  String status;
  bool expansionStatus = false;

  Resources({
    this.id = '',
    this.name = '',
    this.type = '',
    this.url = '',
    this.createdAt = '',
    this.updatedAt = '',
    this.orgId = '',
    this.status = '',
  });

  factory Resources.fromJson(Map<String, dynamic> json) {
    return Resources(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      url: json['url'] ?? '',
      createdAt: json['createdAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
      orgId: json['orgId'] ?? '',
      status: SectionStatus.notStarted.value,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      id,
      name,
      type,
      url,
      createdAt,
      updatedAt,
      orgId,
      status,
      expansionStatus
    ];
  }
}

class Modules extends Equatable {
  final String courseId;
  final String moduleId;
  final String instanceId;
  final String sectionId;
  final String moduleType;
  final String moduleResource;
  final String moduleName;
  final int remainingAttempts;
  bool didMarkedAsDone;
  int progress;
  String status;
  bool expansionStatus = false;

  Modules({
    this.courseId = '',
    this.moduleId = '',
    this.instanceId = '',
    this.sectionId = '',
    this.moduleType = '',
    this.moduleResource = '',
    this.moduleName = '',
    this.status = '',
    this.didMarkedAsDone = false,
    this.remainingAttempts = 0,
    this.progress = -1,
  });

  factory Modules.fromJson(Map<String, dynamic> json) {
    return Modules(
      courseId: json['course_id'] ?? '',
      moduleId: json['module_id'] ?? '',
      instanceId: json['instance'] ?? '',
      sectionId: json['section_id'] ?? '',
      moduleType: json['module_type'] ?? '',
      moduleResource: json['module_source'] == null
          ? ''
          : json['module_source'].toString().isEmpty
              ? json['module_type'] ?? ''
              : json['module_source'],
      moduleName: json['module_name'] ?? '',
      status: SectionStatus.notStarted.value,
      didMarkedAsDone: json['marked_as_done'] ?? false,
      remainingAttempts: json['attempts_remaining'] ?? 0,
      progress: json['progress'] != null ? (json['progress']).toInt() : -1,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      courseId,
      moduleId,
      instanceId,
      sectionId,
      moduleType,
      moduleResource,
      moduleName,
      status,
      didMarkedAsDone,
      progress
    ];
  }
}
