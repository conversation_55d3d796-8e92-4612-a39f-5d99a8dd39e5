import 'package:supabase_flutter/supabase_flutter.dart';

import '/src/domain/repositories/rank_list_repository.dart';
import '/src/utils/constants/strings.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../domain/models/rank.dart';

part 'rank_list_state.dart';

class RankListCubit extends Cubit<RankListState> {
  final RankListRepository _rankListRepository;
  RankListCubit(this._rankListRepository) : super(RankListInitial());

  fetchCourseRankList() async {
    try {
      String orgId = ORG_ID;
      String courseId = COURSE_ID;
      if (orgId.isNotEmpty && courseId.isNotEmpty) {
        final courseRankList =
            await _rankListRepository.getCourseRankList(orgId, courseId);

        emit(RankListFetched(rankList: courseRankList));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][RankListCubit][fetchCourseRankList] :${e.toString()}");
      emit(RankListError(error: e.details));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][RankListCubit][fetchCourseRankList] :${e.toString()}");
      emit(RankListError(error: e.toString()));
    }
  }

  fetchExamRankList(String examId) async {
    try {
      String orgId = ORG_ID;
      if (orgId.isNotEmpty && examId.isNotEmpty) {
        final examRankList =
            await _rankListRepository.getExamRankList(orgId, examId);
        emit(RankListFetched(rankList: examRankList));
      } else {
        emit(RankListError(error: RANK_LIST_FAILURE));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][RankListCubit][fetchExamRankList] :${e.toString()}");
      emit(RankListError(error: e.details));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][RankListCubit][fetchExamRankList] :${e.toString()}");
      emit(RankListError(error: e.toString()));
    }
  }

  setLoading(bool isDataLoading) {
    emit(RankListLoading(isDataLoading: isDataLoading));
  }
}
