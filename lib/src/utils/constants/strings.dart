// App
// ignore_for_file: lines_longer_than_80_chars, non_constant_identifier_names, constant_identifier_names

// Networking and APIs
// TO DO: Remove
const String baseUrl = 'https://newsapi.org/v2';
const String defaultApiKey = '********************************';
const String defaultSources = 'bbc-news, abc-news, al-jazeera-english';

// Storage and Databases
// TO DO: Remove when goes to production
const String articlesTableName = 'articles_table';
const String databaseName = 'app_database.db';

const String ASSETS_PATH = 'assets/enhancement';
const String APPBAR_ICON = 'menu';
const String BACK_ARROW = 'back_arrow';

const String IN_COUNTRY_CODE = '+91';

String USER_ID = "";
String COURSE_ID = '';
String COURSE_NAME = '';
String TOPIC_ID = '';
String TOPIC_NAME = '';
String ORG_ID = '';

// Profile screen

const String profileAppTitle = 'My Profile';
const String LoginPhotoInstruction = 'Click on picture to edit';
const String profileNickName = 'Nick Name';
const String profileFirstName = 'First Name';
const String profileLastName = 'Last Name';
const String COMMON_BUTTON_TEXT = 'Continue';
const String DELETE_ACCOUNT = 'Delete Account';
const String selectOptionText = 'Select Option';
const String opencamera = 'Open Camera';
const String openGallery = 'Open Gallery';
const String CAMERA_C = 'Camera';
const String GALLERY_C = 'Select from\ngallery';
const String REMOVE_PROFILE_PIC = 'Remove pr';
const String CANCEL_TEXT = 'Cancel';
const String ORGANIZATION_EMPTY_ALERT =
    "No organisation linked in your profile. Please contact admin";

const String FILE_SIZE_WARNING =
    "Please submit a valid file with a size not exceeding 1 MB";
const String FILE_TYPE_WARNING = "Please upload a valid image";
const String GALLERY = 'gallery';
const String CAMERA = 'camera';
const String STORAGE_PERMISSION_WARNING =
    "You have disabled storage permission";

const String CAMERA_PERMISSION_WARNING = "You have disabled camera permission";

const String GALLERY_PERMISSION_REQ =
    "Please enable permissions for using gallery access";
const String CAMERA_PERMISSION_REQ =
    "Please enable permissions for using camera services";
const String ENABLE_NOW = "Enable now";
const String DELETE_PROFILE_BTN = 'Delete';

const String PROFILE_UPDATION_SUCCESS = 'Profile details updated successfully!';
const String PROFILE_UPDATION_FAILED =
    'Sorry!\nFailed to update profile details.';
const String FAILED_TO_OPEN_CAMERA =
    "Sorry!\n Unable to open camera now.\n Please try again later";
const String FAILED_TO_OPEN_GALLERY =
    "Sorry!\n Unable to open gallery now.\n Please try again later";

const String ACCOUNT_DELETE_CONFIRM =
    'Are you sure you want to delete this account?';

const String FIELD_REQUIRED = 'Field cannot be empty! ';

//Topic List screen

const String TOPIC_LIST_APP_TITLE = 'Choose Topic';
const String EMPTY_COURSE = "Please enroll in this course to proceed.";
const String EMPTY_TOPIC_FOR_ORGANIZATION =
    "Please enroll in this organization to proceed.";
const String SELECT_COURSE = "Select Course";

//Login Screen

const String loginTitle = 'Hi';
const String loginSubTitle = 'Let\'s Get Started';
const String loginDescription = 'Login to the app with your mobile number';
const String loginMobile = 'Mobile Number';
const String loginButtonText = 'PROCEED';

//Otp Verification screen

const String verification = 'Verification code sent to';
const String editPhone = 'Edit phone Number';
const String recievedCode = 'Didn\'t recieved the code?';
const String resendCode = 'RESEND CODE';
const String otpButtonText = 'VERIFY OTP';

//Login Screen with Email

const String TITLE = 'Login';
const String EMAIL = 'Email';
const String PASSWORD = 'Password';
const String FORGOTPASSWORD = 'Forgot Password';
const String BUTTONTEXT = 'Sign In';
const String OR_CONTINUE_WITH = 'Or continue with';
const String NO_ACCOUNT = 'Don\'t have an account?';
const String SIGNUP = 'Sign Up';
const String RE_LOGIN = 'Back to Login';
const String INVALID_EMAIL = "Invalid email address";
const String EMAIL_FIELD_REQUIRED = "Email Field Required";
const String VALID_PASSWORD = "Please enter a valid password";

//Signup Screen

const String SIGNUPTITLE = 'Create an account';
const String FIRSTNAME = 'First name';
const String LASTNAME = 'Last name';
const String CONFIRMPASSWORD = 'Confirm Password';
const String PHONENUMBER = 'Phone Number';
const String SIGNUPBUTTONTEXT = 'Submit';
const String ACCOUNT = 'Already have an Account?';
const String LOGIN = 'Login';
const String REGISTER_SUCCESS_MSG =
    "Account created successfully, please check your mail and activate the account.";
const String EMAIL_EXIST =
    "Email already exists!\n Try to sign up with different email id.";

// Course Details View

const String EMPTY_VIEW_TEXT = 'No Data Found!';
const String COURSE_DETAILS_APPBAR_TITLE = 'Current Affairs';
const String CURRENT_AFFAIRS = 'Current Affairs';
const String VIEW_MORE = 'View More...';
const String CURRENT_AFFAIRS_EMPTY =
    'Current affairs are not available for the selected month.\nPlease select another month.';
const String AFFAIRS_CONTENT =
    '''Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam quis malesuada tellus. Duis feugiat porta lacinia. Curabitur purus sem, tincidunt eu efficitur sed, vulputate sit amet dui. Pellentesque sodales mattis tellus, ac porttitor justo semper eu. Aliquam rutrum aliquam mattis. Etiam scelerisque odio sit amet ultricies accumsan. Proin nec malesuada purus. Morbi diam nisi, cursus sed ultrices et, ullamcorper et ipsum. Nunc fringilla ornare dapibus. Sed molestie venenatis ligula, ut vestibulum enim varius scelerisque. Vivamus elementum posuere velit, vel finibus dui laoreet in. Vivamus augue diam, mollis vel dignissim vel, interdum eget sapien.''';
const String SUBJECTS = 'Subjects';
const String MODULE1 = 'module1';
const String MODULE2 = 'module2';
const String EXAM = 'Exam';
const String EXAMS = 'Exams';
const String VIDEOS = 'Videos';
const String NULL_PATH = '/dev/null';
const String YOUTUBE_URL = "https://www.youtube.com";
const String YOUTUBE_URL_BROKEN = "https://youtu.be";
const String VALID_VIDEO = "https";
const String COMMENT_BOX_TITLE = 'Type your comment here';
const String COMMENT_BOX_SUB_TITLE =
    'Leave a comment if you have any feedback or suggestion about the course.';
const String COMMENT_BOX_PLACEHOLDER = 'Type here...';
const String ADD_COMMENT = 'Add ';
const String COMMENT_SUBMITTED = 'Comment Added!';
const String SUBJECT_DETAILS_NOT_FOUND =
    'Subject details not found!\nPlease try again later.';
const String MODULE_DETAILS_NOT_FOUND =
    'Exam details not found!\nPlease try again later.';
const String AFFAIRS_DETAILS_NOT_FOUND =
    'Details not found!\nPlease try again later.';
const String EXIT_APP_MSG = 'Press Back Button Again to Exit';

const String HTML = 'html';

// const String LANGUAGE_CHANGE_ALERT =
//     'Language change detected. Please login again to continue.';

// const String LANGUAGE_CHANGE_ALERT2 = 'Language change detected';

// Daily Course View

const String APPBARTEXT = 'Daily Classes Module2';

// Organization View

const String ORGANIZATION_APPBAR_TITLE = 'Organization';

// Exam list View

const String EXAM_LIST_APPBAR_TITLE = 'Exams List';
const String NEW_EXAMS_TAB = 'New Exams';
const String ATTEMPTED_TAB = 'Attempted';
const String PASSED_TAB = 'Passed';
const String QUESTIONS = 'Questions';
const String PASSMARK = 'Passmark';
const String MARKS = 'Marks';
const String MIN = 'min';
const String CSEB = 'CSEB';
const String ATTEND_BTN = 'Attend';
const String VIEW_BUTTON = 'View Results';
const String RANK_BUTTON = 'Rank List';
const String RE_ATTEND_BTN = 'Re-Attend';

// Exam View

const String EXAM_APPBAR_TITLE = 'Exam View';
const String EXAM_APPBAR_ALERT_MESSAGE =
    "Are you sure you want to go back?\nYou will lose your exam Progress!";
const String CONFIRM = "Confirm";
const String EXAM_ALERT_OK = "OK";
const String EXAM_SUBMIT_ALERT_MESSAGE =
    "Are you sure you want to submit the exam?";
const String EXAM_ALERT_SEND_BUTTON = "Send";
const String EXAM_LABEL_TEXT = "Please type the issue here";
const String EXAM_MARK_LATER_ICON = "Flag for review";
const String EXAM_PROGRESS = "Exam Progress";
const String EXAM_QUICK_INFO = "Quick info";
const String EXAM_SKIPPED = "Skipped";
const String EXAM_ATTENDED = "Attended";
const String EXAM_FLAG_REVIEW = "Flagged for review";
const String EXAM_UNANSWERED = "Unanswered";
const String EXAM_SELECTED_QSTN = "Selected question";
const String COPYRIGHT = "@Smart Learn";
const String EXAM_REPORT_QUESTION = "Report Questions";
const String EXAM_ISSUE_TYPE = "Please select issue type";
const String EXAM_TIP = 'Exam Tip';
const String C_MINUTES = 'Minutes';
const String CONTINUE = 'Continue';
const String EXAM_ERROR = 'Error!';
const String EXAM_ALREADY_ATTEND =
    'You have already attended this exam.\n Please choose another exam.';
const String EXAM_COMPLETE = 'Exam Completed';
const String EXAM_COMPLETE_MSG = 'You have completed the exam.';
const String EXAM_PENALITY = "Penalty, if applicable:";
const String EXAM_MARK = "Mark:";
const String EXAM_NETWORK =
    'Network Disconnected!\n Don\'t worry, your exam will be submitted when the network is availble.';
const String EXAM_OOPS = 'Oops!';
const String EXAM_TIMES = 'Times up!';
const String FAILED_HOST_LOOKUP = 'Failed host lookup:';
const String EXAM_SUBMISSION_STATUS =
    'Exam submission in progress, please wait.';

// Onboarding Screen

const String SKIP = "Skip";
const String NEXT = "Next";
const String LEARN = "Learn to Excel";
const String LEARN_PROFESSIONAL =
    "Learn for professional as well as competitive exam for expert faculty's question";
const String PRACTICE = "Practice to Succeed";
const String PRACTICE_SESSION =
    "Try out practice session created by expert trainer and track your progress!";
const String STUDY = "Study to Win";
const String STUDY_MATERIALS =
    "Learn from experts recommended study materials for professional & competitive exams.";
const String WATCH = "Watch to Learn";
const String WATCH_FEATURES =
    "Watch our featured educational videos and learn like never before";
const String CONQUER = "Conquer your Dream";
const String UNLOCK_LEVEL =
    "You need to Unlock this Level to Get the Exams in this Level";
const String DONE = "Done";

/// Subjects detailed View
///

const String VIDEO = 'Video';
const String STUDY_MATERIAL = 'Study Material';
const String PRACTICE_SET = 'Practice Set';
const String EMPTY_VIDEO_TAB =
    'Sorry, no details found for this subject.\nPlease try again later.';
const String EMPTY_PRACTICE_TAB =
    'Sorry, no details found for this subject.\nPlease try again later.';
const String EMPTY_STUDY_MATERIALS_TAB =
    'Sorry, no details found for this subject.\nPlease try again later.';
// Settings Screen
const String SETTINGS_APPBAR_TITLE = "Settings";

// Rank List Screen

const String RANK_APPBAR_TITLE = "Rank List";
const String RANK_TITLE = "Top 10 Performers";

const String YOUR_RANK = "Your Rank";
const String YOUR_RANK_WITH_COMPETITORS = "Your rank with your competitors";
const String RANK_EXAM_ATTENDED = "Exam Attended";
const String MARK = "Mark";
const String LEVEL = "Level";

//  About Screen

const String ABOUT_APPBAR_TITLE = "About";
const String ABOUT_STATEMENT_ONE =
    "SmartLearn is a dynamic platform offering an interactive learning experience. It allows users to engage with video content through interspersed questions, promoting interactivity. This feature provides valuable insights for administrators, enabling them to assess user engagement and participation during training sessions. SmartLearn proves particularly beneficial for mandatory training, compliance requirements, and onboarding new users.\n\n";

const String ABOUT_ADDRESS_HEADING =
    "Competitor v 1.2 \n\n© Content Copyright owned and managed by:\n";
const String ABOUT_ADDRESS =
    "©Powered by:\nCitrus Informatics (India) Pvt Ltd.\nV/387-F, Kallupurackal Building,\nNear Pipeline Junction,\nThrikkakara P O,\nKochi, Kerala,\nIndia 682021\n";
const String ABOUT_EMAIL_HEADING = "Support Email";
const String ABOUT_EMAIL_ADDRESS =
    "<EMAIL> \nPhone: 9947642162\n\n";
// const String ABOUT_FACEBOOK =
//     "Facebook: https://www.facebook.com/competitorpsc/\n\n";

//Live Class Screen
const String LIVE_CLASS_APPBAR_TITLE = "Live Class";

// ExamPreview Screen

const String LEARNING_TIME = "This week's learning time";
const String KEEP_GOING = "Keep going!";
const String VIEW_SCHEDULE = "View Schedule";
const String COURSES = "Courses";
const String VIEW_ALL = "View all";
const String COURSE_DATA = "Course Data";
const String LEARNING_STATUS = "Advance physics learning";
const String LESSON = "Lesson2";

//Side drawer screen

const String PROFILE = "Profile";
const String SETTINGS = "Settings";
const String RANKING = "Ranking";
const String NEWS = "News";
const String NOTIFICATIONS = "Notifications";
const String ABOUT = "About";
const String SHARE = "Share";
const String FEEDBACK = "Feedback";
const String SUGGESTION = "Suggestion";
const String SIGNOUT = "Signout";

const String LOADING = 'Loading';
const String WARNING = 'Warning';
const String CLOSE = 'Close';
const String SUCCESS = 'Success';
const String ERROR = 'Error';
const String SUBMIT = 'Submit';

const String CHANGE_TOPIC = "Are you sure that you want to change the topic?";

///Empty Screens
const String TOPICS_EMPTY = 'Sorry, no topics found!\nPlease try again later.';
const String COURSE_EMPTY =
    'Sorry, no courses found for this topic!\nPlease try again later.';
const String ORGANIZATION_EMPTY =
    'Sorry, no organizations found for this user!\nPlease try again later.';
const String COURSE_DETAILS_EMPTY =
    'Sorry, no details found for this course!\nPlease try again later.';
const String SUBJECTS_EMPTY =
    'Sorry, no details found for this subject!\nPlease try again later.';
const String EXAMS_EMPTY =
    'Sorry, no exams found for this course!\nPlease try again later.';

const String RANK_LIST_EMPTY =
    'Sorry, Empty Rank List!\nCheck back later for updates!';

// Exam Result Screen

const EXAM_RESULT_APPBAR_TITLE = "Exam Result Analysis";
const FINISH = "Finish";
const REATTEND_EXAM =
    "Don't worry you can re-attend the exam from the home screen.";
const EXAM_ANALYSIS = "You can have a look at your exam result here.";
const SCORE_SHEET = "Score Sheet";
const TOTAL_MARK_SCORED = "Total Marks Scored";
const STATISTICS = "Topic-wise Statistics";
const CORRECT = "Correct";
const WRONG = "Wrong";
const SKIPPED = "Skipped";
const GENERAL = "General";
const VIEW_SOLUTION = "View Solutions";
const VIEW_RESULT = "View Results";
const FAILED_EXAMINATION = "You failed in the examination";
const PASSED_EXAMINATION =
    "Congratulations!!!\nYou have passed the examination.";
const ATTENDED = "Attended";
const REVIEW_ANSWER =
    "Here is your score sheet.You can click on the graph and review your answers and the score you've obtained.";
const QUESTION_NO = "Question No: 72";
const QUESTION = 'Which planet is the hottest in the solar system?';
const SKIPPED_QUESTION_STATUS = 'You skipped this question';
const WRONG_QUESTION_STATUS = 'You answered wrong';
const CORRECT_QUESTION_STATUS = 'You answered correctly';
const YOUR_ANSWER = 'Your Answer';
const CORRECT_ANSWER = 'Correct Answer';
const CORRECT_ANSWER_IS = 'Venus';
const OK = 'OK';
const String X_AXIS = 'Question No.';
const String Y_AXIS = 'Score';

///
/// Drawing pad view
///
const String TOOLTIP_EXPORT = 'Export Image';
const String TOOLTIP_UNDO = 'Undo';
const String TOOLTIP_REDO = 'Redo';
const String TOOLTIP_CLEAR = 'Clear';
const String TOOLTIP_PAUSE = 'Pause';
const String TOOLTIP_PLAY = 'Play';
const String EMPTY_CONTENT = 'No Content';
const String COMMENTS = 'Comments:';
const String ENTER_REMARKS = 'Enter Remarks';
const String FORMULA_WRITING_PAD = 'Formula Writing Pad';

///
/// Feedback
///
const String FEEDBACK_TITLE = 'We appreciate your feedback.';
const String FEEDBACK_CONTENT =
    '''We are always looking for ways to improve your experience.\nPlease take a moment to evaluate and tell us what you think about the course you chose.''';
const String FEEDBACK_PLACEHOLDER =
    'What can we do to improve your experience?';
const String FEEDBACK_BTN = 'Submit My Feedback';
const String FEEDBACK_SUBMITTED =
    'Your feedback has been successfully submitted. Thank You!';

// Study Material View

const STUDY_MATERIAL_APPBAR_TITLE = "Study Material";

// Section Detail Screen

const ALERT_TITLE = "Are you sure you want to mark this session as completed?";
const IMAGE_EXTENSIONJPEG = '.jpeg';
const IMAGE_EXTENSIONJPG = '.jpg';
const IMAGE_EXTENSIONPNG = '.png';
const INVALID_FILE_FORMAT = 'Invalid File Format';
const PPT_EXTENSION = 'ppt';
const PPTX_EXTENSION = 'pptx';
const PDF_EXTENSION = 'pdf';

const String EXAM_AVAILABLE_AFTER = "Exam will be available after";
const String SAMPLE_PDF_PATH =
    'https://www.africau.edu/images/default/sample.pdf';

const String IMAGE_VIEW_ERROR_TEXT = 'Unable to load image!';

// Exam Introduction Screen

const EXAM_INTRO_APPBAR_TITLE = "Exam";
const String DURATION = 'Duration';
const String DATE = 'Date';
const String INTRODUCTION = 'Introduction';
const String EXAM_AVAILABLE = "Exam Available in";
const String EXAM_TIPS = "Exam Tips";
const String EXAM_ATTEND = "Attend";
const String QUESTION_WITHIN = "question within";
const String EXAM_MINUTES = "minutes";
const String SORRY = "Sorry!";
const String CHOOSE_EXAM = "Choose another exam";
const String REQUEST_REJECTED = 'Your request has been rejected by admin!';
const String EXAM_TOTAL_MARK = "is the total mark and";
const String EXAM_PASS_MARK = "is the pass mark";
const String FROM = "From";
const String TO = "to";

///Popup
const String YES = 'Yes';
const String NO = 'No';
const EXAM_INTRO_BUTTON_TITLE = "Start";

// Exam Review Screen

const EXAM_REVIEW_APPBAR_TITLE = "Exam Review";
const EXAM_ATTENDED_ON = 'Exam Attended on';
const QUESTION_ANSWER = 'Questions and selected answers:';
const ANSWER_OPTED = 'Answer opted:';
const HOUR = 'hour';
const HOURS = 'hours';
const MINUTES = 'minutes';
const MINUTE = 'minute';
const SECOND = 'second';
const SECONDS = 'seconds';

const String DIALOGUE_CONTENT =
    "Unable to load exam results!\nplease try again later";

// Forgot Password Screen

const FORGOT_PASSWORD_TITLE = "Reset Password";
const FORGOT_PASSWORD_INTRO_TEXT =
    "Provide your email and we will send you a link to reset your password";
const FORGOT_PASSWORD_ALERT_MSG =
    "Please check your email to reset your password.";

const FORGOT_PASSWORD_APPBAR_TITLE = 'Password Recovery';

//Course Video Screen
const SAVE_PROGRESS_BTN = "Save Progress";
const PROGRESS_SUCESS_ALERT_CONTENT = "Your Progress Successfully saved";
const PROGRESS_ALERT_CONTENT = "Do you want to save Progress";
const VIDEO_COMMENTS = "Comments";

//Exam List Tab Screen

const EXAM_EXPIRED = 'Exam expired!';
const QUESTION_NOT_AVAILABLE =
    'Questions are not available for this exam right now.\n Please try again later.';
const RANK_NOT_AVAILABLE =
    'Rank list is not available for this exam right now.\n Please try another exam.';
const EXAM_AVAILABILITY = 'The exam will be accessible starting from';

// Image Screen
const MARK_AS_DONE = "Mark as done";
const PROGRESS_SAVED = "Your Progress Successfully saved";
const SUCESS = "Sucess";

// Welcome Screen

const SCHOOL_BANKING = "School of banking & PSC Coaching Center";
const WELCOME_SMART_LEARN = "Welcome to SmartLearn app";
const SMART_LEARN_ACCOUNT =
    "Your SmartLearn account has been created!\n Waiting for admin to activate your account.\n Please re-login once you receive confirmation email.\nContact +************ for more details.";

// no access screen

const NO_ACCESS = 'Access Restricted!';
const NO_ACCESS_CONTACT_ADMIN =
    "User has no access to perform the action. Please contact admin.";
// const WELCOME_SMART_LEARN = "Welcome to SmartLearn app";
// const SMART_LEARN_ACCOUNT =
//     "Your SmartLearn account has been created!\n Waiting for admin to activate your account.\n Please re-login once you receive confirmation email.\nContact +************ for more details.";

// Settings Screen

const NOTIFICATION = "Notification";
const PRIVACY_POLICY = "Privacy Policy";
const TERMS_CONDITION = "Terms and Condition";
const SHARE_FRIENDS = 'Share with your Friends';
const RATE_APP = "Rate this App";
const SIGNOUT_MSG = "Are you sure to want to logout?";
const TOKEN_EXPIRED =
    "Your session has expired!\nPlease login again to continue.";

// Network connectivity

const String NO_NETWORK = "No Internet Connection Available";

const String HTTP = 'http';
const String HTTPS = 'https';

// validation cubit
const String EMAIL_REQUIRED = 'Email is required';
const String INVALID_EMAIL_ADDRESS = 'Invalid email address';

const String PHONE_NUM_REQUIRED = 'Phone number is required';
const String INVALID_PHONE_NUM = 'Invalid phone number';

const String PASSWORD_REQUIRED = 'Password Required';
const String PASSWORD_LENGTH_MISMATCH =
    'Password should be at least 6 characters';
const String PASSWORDS_MISMATCH = 'Passwords doesn\'t match';
const String NO_NETWORK_TRY_LATER =
    "Network Disconnected!\nPlease try again when the network is available.";

const String VIEW_RESULTS_FAILURE =
    'Failed to fetch results!\nPlease try again later.';
const String VIEW_RESULTS_QUIZ_ATTEMPT_ID_EMPTY =
    'Server error!\nPlease try again later.'; // from backend the quiz attempt id is empty
const String RANK_LIST_FAILURE =
    'Failed to fetch rank list for this exam!\nPlease try again later.';

// Checkpoint Popup

// Empty Screen

const String TRY_AGAIN = "Try Again";

// Course Video Screen- checkpoint
const String CHECKPOINT_EXAM_NAVIGATION_MSG =
    'Check point reached. Now there will be a Quiz to check your understanding so far. You will be redirected now to the quiz associated with the checkpoint.';
const String SECTION_TITLE = "Subject Details";

const String POPUP_TITLE = "Custom Popup";
const String POPUP_MSG =
    " This is a custom popup that appears from 10 seconds to every 10 seconds.";
const String PROMPT_TEXT = "What is Natural Science";
const String PROMPT_VALIDATION_MSG = "Please Enter answer";

// subscriptions
const String SUBSCRIPTION = 'Subscription';
const String CHECKPOINT_COMPLETE_MSG =
    'Great job! You\'ve successfully completed this video session. Keep up the good work!';
const String RESOURCES = 'Resources';

// /// subscription
// const String SUBSCRIPTION_ADDED ='Subscription added. Waiting for approval from admin!';
// const String PENDING = 'Pending';
// const String SELECT = 'Select';
// const String APPROVED = 'Approved';
// const String UPGRADE_PREMIUM = "Upgrade your membership";
// const String SUBSCRIPTION_DESCRIPTION = "We offer pricing plans tailored to your needs. Find the perfect fit for your budget and goals.";

// const String PURCHASE_HISTORY = "Purchase History";
// const String PURCHASED_PLANS = "Purchased plans";
// const String ACTIVE_PLANS = 'Active plans';
// const String PLANS_WAITING_FOR_APPROVAL = "Plans waiting for approval";

// const String PLAN_AVAILABILITY_FROM = 'Plan Availability:\n';
// const String PLAN_VALIDITY = 'Plan Validity:\n';
// const String PLAN_AVAILABILITY_TO = ' up to ';
// const String PLAN_CONFIRMATION_TITLE = 'Confirm Subscription';
// const String PLAN_CONFIRMATION_CONTENT =  'Would you like to subscribe to the selected plan?';
// const String PLAN_EMPTY = 'No Plans found!';
// const String SEARCH_HINT_FOR_PLAN = 'Search by plan name';
