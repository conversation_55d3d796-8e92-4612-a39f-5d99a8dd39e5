import 'package:equatable/equatable.dart';

// no longer considerd.
// using CourseFileResource model instead
class ResourseFile extends Equatable {
  final String fileId;
  final String courseId;
  final String courseName;
  String instanceId;
  final String description;
  final String url;
  final String moduleResource;

  ResourseFile({
    required this.fileId,
    required this.courseId,
    required this.courseName,
    required this.instanceId,
    required this.description,
    required this.url,
    required this.moduleResource,
  });

  factory ResourseFile.fromJson(Map<String, dynamic> json) {
    return ResourseFile(
      fileId: json['id'] ?? '',
      courseName: json['name'] ?? '',
      courseId: json['course_id'] ?? '',
      instanceId: json['id'] ?? '',
      description: json['description'] ?? '',
      url: json['url'] ?? '',
      moduleResource: json['module_source'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'id': fileId,
        "course_id": courseId,
        'name': courseName,
        'url': url,
        "module_source": moduleResource
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [];
  }
}
