// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
      apiKey: "AIzaSyA2mzyIz-ZWf68CREXm5paDYknKeizwbBU",
      authDomain: "smartlearn-lms.firebaseapp.com",
      projectId: "smartlearn-lms",
      storageBucket: "smartlearn-lms.appspot.com",
      messagingSenderId: "464527218978",
      appId: "1:464527218978:web:75598ba1fbc08d4f756d7b",
      measurementId: "G-CWZ8DQVR35");

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDVa8HbWDwo4eVw2CUUJgtxvzi2Wy7KT7o',
    appId: '1:464527218978:android:b90844d61fe18df0756d7b',
    messagingSenderId: '464527218978',
    projectId: 'smartlearn-lms',
    storageBucket: 'smartlearn-lms.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBbO7LHTss87jxvjGtx17GZhAtgy4Yk7XA',
    appId: '1:46710193602:ios:c794d27c423e450bcbaa5b',
    messagingSenderId: '46710193602',
    projectId: 'mylms-e77bd',
    storageBucket: 'mylms-e77bd.appspot.com',
    iosBundleId: 'com.example.flutterCleanArchitecture',
  );
}
