// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_plan.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubscriptionPlanResult _$SubscriptionPlanResultFromJson(
        Map<String, dynamic> json) =>
    SubscriptionPlanResult(
      result: (json['result'] as List<dynamic>)
          .map((e) => SubscriptionPlan.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] as String,
    );

Map<String, dynamic> _$SubscriptionPlanResultToJson(
        SubscriptionPlanResult instance) =>
    <String, dynamic>{
      'result': instance.result,
      'status': instance.status,
    };

SubscriptionPlan _$SubscriptionPlanFromJson(Map<String, dynamic> json) =>
    SubscriptionPlan(
      id: json['id'] as String?,
      orgId: json['org_id'] as String?,
      name: SubscriptionPlan._uppercaseFirstName(json['name'] as String?),
      description: json['description'] as String?,
      subscriptionType:
          $enumDecode(_$SubscriptionTypeEnumMap, json['subscription_type']),
      targetId: json['target_id'],
      price: (json['price'] as num?)?.toDouble(),
      currency: $enumDecodeNullable(_$CurrencyEnumMap, json['currency']),
      subscriptionFrequencyType: $enumDecode(
          _$SubscriptionFrequencyTypeEnumMap, json['subscription_frequency']),
      validFrom: json['valid_from'] == null
          ? null
          : DateTime.parse(json['valid_from'] as String),
      validTo: json['valid_to'] == null
          ? null
          : DateTime.parse(json['valid_to'] as String),
      subscriptionPlanStatus: $enumDecode(
          _$SubscriptionPlanStatusEnumMap, json['subscription_plan_status']),
      isUserSubscribed: json['is_user_subscribed'] as bool,
      isHighLevelUser: json['is_high_level_user'] as bool,
      userSubscriptionStatus: $enumDecodeNullable(
          _$PlanStatusEnumEnumMap, json['subscription_usage_status']),
      userSubscriptionStartDate: json['user_subscription_start_date'] == null
          ? null
          : DateTime.parse(json['user_subscription_start_date'] as String),
      userSubscriptionEndDate: json['user_subscription_end_date'] == null
          ? null
          : DateTime.parse(json['user_subscription_end_date'] as String),
    );

Map<String, dynamic> _$SubscriptionPlanToJson(SubscriptionPlan instance) =>
    <String, dynamic>{
      'id': instance.id,
      'org_id': instance.orgId,
      'name': instance.name,
      'description': instance.description,
      'subscription_type':
          _$SubscriptionTypeEnumMap[instance.subscriptionType]!,
      'target_id': instance.targetId,
      'price': instance.price,
      'currency': _$CurrencyEnumMap[instance.currency],
      'subscription_frequency': _$SubscriptionFrequencyTypeEnumMap[
          instance.subscriptionFrequencyType]!,
      'valid_from': instance.validFrom?.toIso8601String(),
      'valid_to': instance.validTo?.toIso8601String(),
      'subscription_plan_status':
          _$SubscriptionPlanStatusEnumMap[instance.subscriptionPlanStatus]!,
      'is_user_subscribed': instance.isUserSubscribed,
      'is_high_level_user': instance.isHighLevelUser,
      'subscription_usage_status':
          _$PlanStatusEnumEnumMap[instance.userSubscriptionStatus],
      'user_subscription_start_date':
          instance.userSubscriptionStartDate?.toIso8601String(),
      'user_subscription_end_date':
          instance.userSubscriptionEndDate?.toIso8601String(),
    };

const _$SubscriptionTypeEnumMap = {
  SubscriptionType.COURSE_BASED: 'course_based',
  SubscriptionType.RESOURCE_BASED: 'resource_based',
  SubscriptionType.TIME_BASED: 'time_based',
};

const _$CurrencyEnumMap = {
  Currency.CURRENCY_INR: 'inr',
  Currency.INR: 'INR',
};

const _$SubscriptionFrequencyTypeEnumMap = {
  SubscriptionFrequencyType.ANNUAL: 'annual',
  SubscriptionFrequencyType.CUSTOM: 'custom',
  SubscriptionFrequencyType.MONTHLY: 'monthly',
};

const _$SubscriptionPlanStatusEnumMap = {
  SubscriptionPlanStatus.ACTIVE: 'active',
  SubscriptionPlanStatus.INACTIVE: 'inactive',
};

const _$PlanStatusEnumEnumMap = {
  PlanStatusEnum.UNSUBSCRIBED: 'none',
  PlanStatusEnum.PENDING: 'pending',
  PlanStatusEnum.APPROVED: 'approved',
};
