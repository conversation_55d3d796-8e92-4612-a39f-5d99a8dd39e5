import 'package:flutter/material.dart';

import '../../config/app_config/api_config.dart';

import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/models/organization.dart';
import '../../domain/models/privilege_access/privilege_access.dart';
import '../../domain/repositories/organization_repository.dart';

class OrganizationRepositoryImpl implements OrganizationRepository {
  OrganizationRepositoryImpl();

  @override
  Future<List<Organization>> getUserOrganizations(String userId) async {
    final organizationResponse = await Supabase.instance.client
        .from(APIConfig.V_USER_ORGS)
        .select()
        .eq("user_id", userId);

    List<dynamic> userList = organizationResponse;
    List<Map<String, dynamic>> mapList =
        userList.map((item) => item as Map<String, dynamic>).toList();
    List<Organization> organizationList =
        mapList.map((json) => Organization.fromJson(json)).toList();

    return organizationList;
  }

  @override
  Future<List<PrivilegeAccess>> getUserPrevileges(
      String userId, String orgId) async {
    final response = await Supabase.instance.client.rpc(
        APIConfig.GET_PRIVILEGE_ACCESS_LIST,
        params: {'user_id': userId, 'org_id': orgId});

    try {
      if (response != null) {
        Map<String, dynamic> responseData = response ?? {};

        PrivilegeAccess _privilegeAccess =
            privilegeAccessFromJson(responseData);
        return [_privilegeAccess];
      }
    } on Exception catch (e) {
      debugPrint(
          '[OrganizationRepositoryImpl][getUserPrevileges][Exception]: $e');
    }
    return [];
  }
}
