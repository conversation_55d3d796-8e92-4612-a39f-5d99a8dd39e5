import '/src/domain/models/purchase_history/purchase_history.dart';
import 'package:flutter/foundation.dart';

import '../../domain/models/subscription_plan/plan_course_result.dart';
import '/src/domain/models/subscription_plan/subscription_plan.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../config/app_config/api_config.dart';
import '../../domain/repositories/subscription_reporsitory.dart';

class SubscriptionRepositoryImpl implements SubscriptionRepository {
  @override
  Future<List<SubscriptionPlan>> getSubscriptionPlans(
      Map<String, dynamic> reqBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_SUBSCRIPTION_PLAN_LIST, params: reqBody);

    try {
      Map<String, dynamic> responseJson = response;
      SubscriptionPlanResult subscriptionPlanResult =
          SubscriptionPlanResult.fromJson(responseJson);
      List<SubscriptionPlan> plans = subscriptionPlanResult.result.isNotEmpty
          ? subscriptionPlanResult.result
          : [];

      return plans;
    } on Exception catch (e) {
      debugPrint(
          '[SubscriptionRepositoryImpl][getSubscriptionPlans][Exception]: $e');
    }
    return [];
  }

  @override
  Future<bool> submitSelectedPlan(Map<String, dynamic> reqBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.SUBMIT_SUBSCRIPTION_PLAN, params: reqBody);
    if (response != null) {
      Map<String, dynamic> responseJson = response;
      if (responseJson["status"] == 'success') {
        return true;
      }
    }
    return false;
  }

  @override
  Future<bool> getSubscriptionPlanStatus(Map<String, dynamic> reqBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_PLAN_STATUS, params: reqBody);
    if (response != null) {
      Map<String, dynamic> responseJson = response;
      if (responseJson["result"] == true) {
        return true;
      }
    }
    return false;
  }

  @override
  Future<List<PlanCourse>> getCourseForSelectedPlan(
      Map<String, dynamic> reqBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_COURSE_FOR_PLAN, params: reqBody);
    if (response != null) {
      try {
        Map<String, dynamic> responseJson = response;
        PlanCourseResult planCourseResult =
            PlanCourseResult.fromJson(responseJson);
        List<PlanCourse> planCourseList = planCourseResult.result != null &&
                planCourseResult.result!.isNotEmpty
            ? planCourseResult.result ?? []
            : [];

        return planCourseList;
      } on Exception catch (e) {
        debugPrint(
            '[SubscriptionRepositoryImpl][getCourseForSelectedPlan][Exception]: $e');
      }
    }
    return [];
  }

  @override
  Future<List<PurchaseHistoryResult>> getUserPurchaseHistory(
      Map<String, dynamic> reqBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_SUBSCRIPTION_HISTORY, params: reqBody);

    try {
      Map<String, dynamic> responseJson = response;
      PurchaseHistory purchaseHistory = PurchaseHistory.fromJson(responseJson);
      List<PurchaseHistoryResult> purchaseHistoryResult =
          purchaseHistory.result != null ? [purchaseHistory.result!] : [];

      return purchaseHistoryResult;
    } on Exception catch (e) {
      debugPrint(
          '[SubscriptionRepositoryImpl][getUserPurchaseHistory][Exception]: $e');
    }
    return [];
  }
}
