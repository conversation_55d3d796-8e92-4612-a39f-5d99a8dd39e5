import 'package:flutter/material.dart';

class LinearProgressIndicatorWidget extends StatelessWidget {
  const LinearProgressIndicatorWidget(
      {super.key,
      required this.progress,
      required this.progressColor,
      required this.backgroundColor});

  final double progress;
  final Color progressColor;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(4.0),
      child: LinearProgressIndicator(
        value: progress.isFinite
            ? progress
            : 0.0, // Set the progress value dynamically.
        backgroundColor: backgroundColor,
        valueColor: AlwaysStoppedAnimation<Color>(
          progressColor,
        ),
        minHeight: 8,
      ),
    );
  }
}
