// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_video_player.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveVideoPlayerDataAdapter extends TypeAdapter<HiveVideoPlayerData> {
  @override
  final int typeId = 1;

  @override
  HiveVideoPlayerData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveVideoPlayerData(
      id: fields[0] as int?,
      videoId: fields[1] as String,
      startTime: fields[2] as int,
      endTime: fields[3] as int,
      mileStonesCompleted: fields[4] as int,
      lastWatchTime: fields[5] as int,
      mileStoneDurations: fields[6] as String,
    );
  }

  @override
  void write(BinaryWriter writer, HiveVideoPlayerData obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.videoId)
      ..writeByte(2)
      ..write(obj.startTime)
      ..writeByte(3)
      ..write(obj.endTime)
      ..writeByte(4)
      ..write(obj.mileStonesCompleted)
      ..writeByte(5)
      ..write(obj.lastWatchTime)
      ..writeByte(6)
      ..write(obj.mileStoneDurations);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveVideoPlayerDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
