import 'dart:io';

import 'package:equatable/equatable.dart';

class DeviceInfo extends Equatable {
  final String device;
  final String manufacturer;
  final String deviceId; // alternative for serial number
  final String model;
  final String osVersion;
  final String userAgent;
  final String status;

  const DeviceInfo(
      {required this.device,
      required this.manufacturer,
      required this.deviceId,
      required this.model,
      required this.osVersion,
      required this.userAgent,
      required this.status});

  factory DeviceInfo.fromAndroidJson(
      Map<String, dynamic> json, String userAgent) {
    String devicePlatform = 'Android Phone';

    return DeviceInfo(
      device: devicePlatform,
      manufacturer: json["manufacturer"],
      deviceId: json["id"],
      model: json["model"],
      osVersion: json["version"]['release'],
      userAgent: userAgent,
      status: 'active',
    );
  }

  factory DeviceInfo.fromiOSJson(Map<String, dynamic> json, String userAgent) {
    String devicePlatform = 'iPhone';

    return DeviceInfo(
      device: devicePlatform,
      manufacturer: json["utsname"]["machine"],
      deviceId: json["identifierForVendor"],
      model: json["model"],
      osVersion: json["systemVersion"],
      userAgent: userAgent,
      status: 'active',
    );
  }

  factory DeviceInfo.fromWebJson(Map<String, dynamic> json, String userAgent) {
    String devicePlatform = Platform.isMacOS
        ? 'MacOS'
        : Platform.isWindows
            ? 'Windows'
            : Platform.isLinux
                ? 'Linux'
                : '';

    return DeviceInfo(
      device: devicePlatform,
      manufacturer: json["vendor"],
      deviceId: json["id"],
      model: json["model"],
      osVersion: json['appVersion'],
      userAgent: json['userAgent'],
      status: 'active',
    );
  }

  toJson() {
    return {
      "device": device,
      "manufacturer": manufacturer,
      "serial_no": deviceId,
      "model": model,
      "os_version": osVersion,
      "user_agent": userAgent,
      "status": status
    };
  }

  @override
  List<Object?> get props => [
        device,
        manufacturer,
        deviceId,
        model,
        osVersion,
        userAgent,
        status,
      ];

  @override
  bool get stringify => true;
}
