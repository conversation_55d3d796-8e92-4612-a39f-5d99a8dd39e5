import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/page_content.dart';
import '../../presentation/views/subscription_view.dart';
import '../../presentation/widgets/no_access_view.dart';
import '/src/presentation/views/welcome_view.dart';
import 'package:flutter/services.dart';

import '../../domain/models/course_progress.dart';
import '../../domain/models/exam_summary.dart';
import '../../domain/models/question_data.dart';
import '../../domain/models/rank.dart';
import '../../domain/models/resource_file.dart';
import '../../presentation/views/drawing_pad_view.dart';

import '../../presentation/views/exam_intro_view.dart';
import '../../presentation/views/exam_result_view.dart';
import '../../presentation/views/exam_review_view.dart';
import '../../presentation/views/image_view.dart';
import '../../presentation/views/pdf_view.dart';
import '../../presentation/views/reset_password_view.dart';
import '/src/presentation/views/news_view_more_view.dart';

import '../../presentation/views/section_details_view.dart';
import '../../presentation/views/study_material_view.dart';
import '/src/presentation/views/subjects_detailed_tab_view.dart';
import '/src/presentation/views/about_view.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import '../../../splash_screen.dart';
import '../../../src/presentation/views/signup_view.dart';

import '../../domain/models/course_details.dart';
import '../../presentation/views/course_details_view.dart';
import '../../presentation/views/course_list_view.dart';
import '../../presentation/views/course_module_view.dart';
import '../../presentation/views/course_news_view.dart';
import '../../presentation/views/course_video_view.dart';
import '../../presentation/views/daily_course_list_view.dart';
import '../../presentation/views/exam_view.dart';
import '../../presentation/views/exams_list_tab_view.dart';

import '../../presentation/views/login_email_view.dart';

import '../../presentation/views/onboarding_view.dart';
import '../../presentation/views/organization_view.dart';
import '../../presentation/views/profile_review_view.dart';
import '../../presentation/views/profile_view.dart';
import '../../presentation/views/rank_list_view.dart';
import '../../presentation/views/settings_view.dart';
import '../../presentation/views/topic_list_view.dart';

part 'app_router.gr.dart';

Widget Function(BuildContext, Animation<double>, Animation<double>, Widget)?
    customTransition = (BuildContext context, Animation<double> animation,
        Animation<double> secondaryAnimation, Widget child) {
  return FadeTransition(
    opacity: animation,
    child: child,
  );
};

@AutoRouterConfig()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
        AutoRoute(page: SplashRoute.page, initial: true),
        CustomRoute(
            page: ProfileReviewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: ExamListViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: DailyCourseListViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: OnboardingViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: LoginEmailViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: SignUpViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: ProfileViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: OrganizationViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: TopicListViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: CourseListViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
          page: CourseDetailsViewRoute.page,
          transitionsBuilder: customTransition,
        ),
        CustomRoute(
            page: ExamViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: CourseVideoViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: CourseNewsViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: NewsViewMoreViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: CourseModuleViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: SubjectsDetailedTabViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: SettingsViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: RankListViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: AboutViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: ExamResultViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: DrawingPadViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: DrawingReviewViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: StudyMaterialViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: SectionDetailsViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: ExamIntroductionViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: ExamReviewViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: ImageViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: PDFViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: WelcomeViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: ResetPasswordViewRoute.page,
            transitionsBuilder: customTransition),
        CustomRoute(
            page: NoAccessViewRoute.page, transitionsBuilder: customTransition),
        CustomRoute(
            page: SubscriptionViewRoute.page,
            transitionsBuilder: customTransition)
      ];
}

final appRouter = AppRouter();
