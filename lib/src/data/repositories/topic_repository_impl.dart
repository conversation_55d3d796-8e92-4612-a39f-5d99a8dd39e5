import '../../domain/models/category/user_category.dart';

import '../../config/app_config/api_config.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../config/app_config/api_constants.dart';
import '../../domain/models/topic.dart';
import '../../domain/repositories/topic_repository.dart';

class TopicRepositoryImpl implements TopicRepository {
  TopicRepositoryImpl();

  // TODO: Remove later
  // API unused
  // replaced with 'V_CATEGORY_BY_HIERARCHY'
  /* @override
  Future<List<TopicList>> getTopicList(String orgId) async {
    final response = await Supabase.instance.client
        .from(APIConfig.V_CATEGORY)
        .select()
        .eq("org_id", orgId)
        .eq('category_publish_status', PUBLISHED_STATUS);

    List<dynamic> topics = response;
    List<Map<String, dynamic>> mapList =
        topics.map((item) => item as Map<String, dynamic>).toList();

    List<TopicList> topicList =
        mapList.map((json) => TopicList.fromJson(json)).toList();
    return topicList;
  }*/

  /// get category by hierarchy including courses for each topic
  @override
  Future<List<UserCategory>> getCategoryByHierarchy(String orgId) async {
    ///  Note:
    /// 'filter_data' can have values:
    ///  0 => all
    ///  1 => published
    ///  2 => draft
    ///  for mobile end, always pass as 1

    final response = await Supabase.instance.client.rpc(
        APIConfig.V_CATEGORY_BY_HIERARCHY,
        params: {'org_id': orgId, 'filter_data': 1});

    List<dynamic> topics = response;
    List<Map<String, dynamic>> mapList =
        topics.map((item) => item as Map<String, dynamic>).toList();

    List<UserCategory> categories =
        mapList.map((json) => UserCategory.fromJson(json, 0)).toList();
    categories.sort((a, b) => a.name.compareTo(b.name));
    return categories;
  }
}
