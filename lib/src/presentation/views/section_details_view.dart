import '../../utils/resources/user_activity_res.dart';
import 'ppt_viewer.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../config/enums/alert_types.dart';
import '../../domain/models/course_dashboard/course_assignments.dart';
import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../domain/models/course_dashboard/course_page_resource.dart';
import '../../domain/models/course_dashboard/course_video_resource.dart';
import '../../domain/models/tabbar_item.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../../utils/constants/nums.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '../cubits/course_details/course_details_cubit.dart';
import '../cubits/course_list/course_list_cubit.dart';
import '../widgets/alert_popup/confirmation_popup.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/empty_screen_view.dart';
import '../widgets/tab_bar_widget.dart';
import '/src/presentation/widgets/connectivity_widget.dart';

import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/resource_file.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../widgets/debounced_button.dart';
import '../widgets/stepper_widget.dart' as stepper;
import '/src/config/enums/section_type.dart';
import '/src/config/themes/lms_fonts.dart';
import '../../config/enums/section_status.dart';
import '../../domain/models/question_data.dart';
import '../../utils/constants/strings.dart';
import '../cubits/exam/exam_cubit.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/domain/models/course_details.dart';
import '/src/presentation/cubits/section_details/section_details_cubit.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../domain/models/responses/section_details.dart';
import '../widgets/app_bar.dart';
import '../widgets/loading_indicator.dart';

@RoutePage()
// ignore: must_be_immutable
class SectionDetailsViewScreen extends HookWidget with WidgetsBindingObserver {
  final String sectionId;
  final String title;

  /// set to true while navigating from tab view to resourcses view
  /// when showing the resourses under selected folder
  final bool showFolderResources;
  final int folderIndex;
  final SectionDetails? sectionDetailsData;

  SectionDetailsViewScreen(
      {Key? key,
      required this.sectionId,
      required this.sectionDetailsData,
      required this.showFolderResources,
      required this.folderIndex,
      required this.title})
      : super(key: key);

  SectionDetails? sectionDetails;
  QuestionData? _questionData;

  List<Question> _questions = [];

  String? pathName;

  int _currentResourceStep = 0;
  int _currentFolderStep = 0;
  int _currentFolderResourceStep = 0;
  int remainingAttempts = 0;

  ValueNotifier<bool> _isDataLoading = ValueNotifier(false);
  ValueNotifier<bool> _hasValidContents = ValueNotifier(true);
  BuildContext? currentContext;

  // late TabController _tabController;

  final List<TabbarItem> _tabBarItems = [
    TabbarItem(
        id: 0,
        label: SLStrings.getTranslatedString('Resources'),
        icon: '$ASSETS_PATH/quiz_add.png'),
    TabbarItem(
        id: 1,
        label: SLStrings.getTranslatedString('Folders'),
        icon: '$ASSETS_PATH/dock_search.png'),
  ];

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    // TO DO: context should not be equal to null
    didChangeAppLocale(locales, currentContext);
  }

  // _initTabBar() {
  //   _tabController =
  //       useTabController(initialLength: _tabBarItems.length, initialIndex: 0);
  // }

  _checkForValidData(SectionDetailsCubit _sectionDetailsCubit) {
    _hasValidContents.value = (sectionDetails != null &&
            (showFolderResources
                ? (sectionDetails!.folders.isNotEmpty &&
                    sectionDetails?.folders[folderIndex] != null &&
                    sectionDetails!
                        .folders[folderIndex].folderModules.isNotEmpty)
                : true)) &&
        _sectionDetailsCubit.state is! SectionDetailsInitial;
  }

  _fetchData(
      BuildContext context, SectionDetailsCubit _sectionDetailsCubit) async {
    if (kIsWeb) {
      await _sectionDetailsCubit.fetchSectionDetails(sectionId);
      SectionDetailsState state = _sectionDetailsCubit.state;

      if (state is SectionDetailsError) {
        showPGExceptionPopup(context, state.error);
      } else if (state is SectionDetailsFetched) {
        sectionDetails = state.sectionDetailsData;
        _checkForValidData(_sectionDetailsCubit);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final _sectionDetailsCubit = BlocProvider.of<SectionDetailsCubit>(context);
    final _examCubit = BlocProvider.of<ExamCubit>(context);
    final _tabController =
        useTabController(initialLength: _tabBarItems.length, initialIndex: 0);
    sectionDetails = sectionDetailsData;
    useEffect(() {
      WidgetsBinding.instance.addObserver(this);

      sectionDetails = sectionDetailsData;
      _isDataLoading.value = true;
      _checkForValidData(_sectionDetailsCubit);
      Future<void>.microtask(() async {
        await _fetchData(context, _sectionDetailsCubit);

        _isDataLoading.value = false;
      });
      return () {
        currentContext = null;
        WidgetsBinding.instance.removeObserver(this);
      };
    }, [_tabController]);

    return Scaffold(
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: title,
              trailingWidget: Container(),
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {
                _sectionDetailsCubit.updateSetValue(false);
                kIsWeb
                    ? showFolderResources
                        ? Beamer.of(context).beamToReplacementNamed(
                            '/section-details',
                            data: {
                              'key': 'value',
                              "sectionId": sectionId,
                              'title': title + '  ',
                              'showFolders': false,
                              'folderIndex': 0,
                            },
                            beamBackOnPop: true,
                          )
                        : Beamer.of(context)
                            .beamToReplacementNamed('/course-details')
                    : Navigator.of(context).pop();
              },
            )),
        body: BlocListener<SectionDetailsCubit, SectionDetailsState>(
            listener: (context, state) async {
          if (state is SectionDetailsExapanding) {
            if (sectionDetails != null) {
              if (showFolderResources) {
                if (sectionDetails!.folders.isNotEmpty) {
                  sectionDetails
                      ?.folders[folderIndex]
                      .folderModules[state.index]
                      .expansionStatus = state.expand;
                }
              } else {
                sectionDetails?.modules[state.index].expansionStatus =
                    state.expand;
              }
            }
          } else if (state is SectionDetailschangeStatus) {
            if (sectionDetails != null) {
              if (showFolderResources) {
                if (sectionDetails!.folders.isNotEmpty) {
                  sectionDetails
                      ?.folders[folderIndex]
                      .folderModules[state.index]
                      .status = SectionStatus.done.value;
                }
              } else {
                sectionDetails?.modules[state.index].status =
                    SectionStatus.done.value;
              }
            }
          } else if (state is SectionDetailsFetched) {
            sectionDetails = SectionDetails(
                resources: state.sectionDetailsData?.resources ?? [],
                modules: state.sectionDetailsData?.modules ?? [],
                folders: state.sectionDetailsData?.folders ?? []);
            sectionDetails = state.sectionDetailsData;
            _checkForValidData(_sectionDetailsCubit);
          } else if (state is SectionDetailsListLoading) {
            // isDataLoading.value = state.isDataLoading;
          } else if (state is StepItemTapState) {
            _currentResourceStep = state.currentResourceStep;
            _currentFolderStep = state.currentFolderStep;
            _currentFolderResourceStep = state.currentFolderResourceStep;
          }
        }, child: BlocBuilder<SectionDetailsCubit, SectionDetailsState>(
                builder: (context, state) {
          if (state is SectionDetailsFetched) {
            sectionDetails = SectionDetails(
                resources: state.sectionDetailsData?.resources ?? [],
                modules: state.sectionDetailsData?.modules ?? [],
                folders: state.sectionDetailsData?.folders ?? []);
            sectionDetails = state.sectionDetailsData;
            _checkForValidData(_sectionDetailsCubit);
          }
          return ValueListenableBuilder(
              valueListenable: _isDataLoading,
              builder: (context, _, __) {
                return Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.whiteColor,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16)),
                  ),
                  child: LayoutBuilder(builder: (context, constraints) {
                    return ValueListenableBuilder(
                        valueListenable: _hasValidContents,
                        builder: (context, _, __) {
                          return Stack(
                            children: [
                              Visibility(
                                child: Align(
                                  child: Container(
                                    width: constraints.maxWidth < 720
                                        ? constraints.maxWidth
                                        : 800,
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 20),
                                    child: ScrollConfiguration(
                                      behavior: ScrollConfiguration.of(context)
                                          .copyWith(
                                        dragDevices: {
                                          PointerDeviceKind.touch,
                                          PointerDeviceKind.mouse,
                                        },
                                      ),
                                      child: showFolderResources
                                          ? _resourceWidget(context, _examCubit,
                                              _sectionDetailsCubit, state)
                                          : Column(
                                              children: [
                                                showFolderResources
                                                    ? Container()
                                                    : TabbarWidget(
                                                        tabBarItems:
                                                            _tabBarItems,
                                                        tabController:
                                                            _tabController),
                                                const SizedBox(height: 16),
                                                _tabBarBody(
                                                    context,
                                                    _examCubit,
                                                    _sectionDetailsCubit,
                                                    state,
                                                    _tabController),
                                              ],
                                            ),
                                    ),
                                  ),
                                ),
                              ),
                              ConnectivityStatusWidget(),
                              _isDataLoading.value == true
                                  ? Positioned.fill(
                                      child: LoadingIndicatorClass())
                                  : !_hasValidContents.value
                                      ? _emptyView(_sectionDetailsCubit)
                                      : Container()
                            ],
                          );
                        });
                  }),
                );
              });
        })));
  }

  Widget _emptyView(SectionDetailsCubit _sectionDetailsCubit) {
    return EmptyScreenView(
      title: SLStrings.getTranslatedString(KEY_SUBJECTS_EMPTY),
      showRetryButton:
          Supabase.instance.client.auth.currentSession?.isExpired == true
              ? true
              : false,
      retryAction: () async {
        _isDataLoading.value = true;
        await _sectionDetailsCubit.fetchSectionDetails(sectionId);
        _isDataLoading.value = false;
      },
    );
  }

  Widget _tabBarBody(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      TabController _tabController) {
    return Expanded(
      child: TabBarView(
        controller: _tabController,
        children: [
          sectionDetails != null && sectionDetails!.modules.isNotEmpty
              ? _resourceWidget(
                  context, _examCubit, _sectionDetailsCubit, state)
              : _emptyView(_sectionDetailsCubit),
          sectionDetails != null && sectionDetails!.folders.isNotEmpty
              ? _folderWidget(context, _examCubit, _sectionDetailsCubit, state)
              : _emptyView(_sectionDetailsCubit),
        ],
      ),
    );
  }

  Widget _folderWidget(BuildContext context, ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit, SectionDetailsState state) {
    return Visibility(
      visible: sectionDetails != null && sectionDetails!.folders!.isNotEmpty,
      child: Card(
        color: AppTheme.sectionCardBgColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: Column(
          children: [
            Container(
              decoration: const BoxDecoration(
                color: AppTheme.stepperColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
              width: MediaQuery.of(context).size.width,
              height: 50,
              child: Text(
                'Folders',
                style:
                    LMSFonts.sectionProgressTextStyle(16, AppTheme.whiteColor),
              ),
            ),
            Flexible(
              child: Theme(
                data: ThemeData(
                  colorScheme: Theme.of(context).colorScheme.copyWith(
                        primary: AppTheme.stepperColor,
                      ),
                ),
                child:
                    sectionDetails != null && sectionDetails!.folders.isNotEmpty
                        ? _folderStepperWidget(
                            context,
                            sectionDetails?.folders ?? [],
                            _sectionDetailsCubit,
                            _examCubit)
                        : Container(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _folderStepperWidget(BuildContext context, List<Folder> folders,
      SectionDetailsCubit _sectionDetailsCubit, ExamCubit _examCubit) {
    int length = folders.length;
    List<stepper.Step> stepList = [];

    for (int folderStepperIndex = 0;
        folderStepperIndex < length;
        folderStepperIndex++) {
      String moduleName = folders[folderStepperIndex].folderName.trim();
      stepList.add(
        stepper.Step(
          title: Row(children: [
            _imageWidget('file.png', 24.0, enableIcon: true),
            _moduleNameWidget(moduleName),
          ]),
          content: Container(),
          isActive: false,
        ),
      );
    }
    return length > 0
        ? stepper.Stepper(
            // key: ValueKey(getRandomValueForKey()),
            currentStep: length > 0 ? length - 1 : 0,
            controlsBuilder: (context, controller) {
              return Container();
            },
            stepIconBuilder: (stepIndex, stepState) {
              return Container(
                decoration: const BoxDecoration(
                  color: AppTheme.stepperColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    (stepIndex + 1).toString(),
                    style: LMSFonts.sectionProgressTextStyle(
                        14, AppTheme.whiteColor),
                  ),
                ),
              );
            },
            onStepTapped: (int index) {
              kIsWeb
                  ? Beamer.of(context).beamToNamed(
                      '/section-details',
                      data: {
                        'key': 'value',
                        "sectionId": sectionId,
                        'title': title +
                            ' ', // to differentiate routes as the navigation occurs to same screen
                        'showFolders': true,
                        'folderIndex': index,
                      },
                      beamBackOnPop: true,
                    )
                  : appRouter.push(SectionDetailsViewRoute(
                      sectionId: sectionId,
                      title: title + ' ',
                      showFolderResources: true,
                      folderIndex: index,
                      sectionDetailsData: sectionDetails));
              _sectionDetailsCubit.updateSetValue(true);
            },
            steps: stepList,
          )
        : Container();
  }

  Widget _resourceWidget(BuildContext context, ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit, SectionDetailsState state) {
    bool visibility = false;

    String title = SECTION_TITLE;

    List<Modules> modules = [];

    if (sectionDetails != null) {
      if (showFolderResources && sectionDetails!.folders.isNotEmpty) {
        visibility =
            sectionDetails!.folders[folderIndex].folderModules.isNotEmpty;
        title = sectionDetails!.folders[folderIndex].folderName;
        modules = sectionDetails!.folders[folderIndex].folderModules;
      } else {
        visibility = sectionDetails!.modules.isNotEmpty;
        modules = sectionDetails!.modules;
      }
    }

    return Visibility(
      visible: visibility,
      child: Card(
        color: AppTheme.sectionCardBgColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: Column(
          children: [
            Container(
              decoration: const BoxDecoration(
                color: AppTheme.stepperColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
              width: MediaQuery.of(context).size.width,
              child: Text(
                title,
                style:
                    LMSFonts.sectionProgressTextStyle(16, AppTheme.whiteColor),
              ),
            ),
            Flexible(
              child: Theme(
                data: ThemeData(
                  colorScheme: Theme.of(context).colorScheme.copyWith(
                        primary: AppTheme.stepperColor,
                      ),
                ),
                child: _stepperWidget(
                    context, modules, _examCubit, _sectionDetailsCubit, state),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getProgressval(int index) {
    double progressPercentage = sectionDetails != null
        ? showFolderResources && sectionDetails!.folders.isNotEmpty
            ? (sectionDetails
                    ?.folders[folderIndex].folderModules[index].progress
                    .toDouble() ??
                0.0)
            : (sectionDetails?.modules[index].progress.toDouble() ?? 0.0)
        : 0.0;
    progressPercentage /= 100;
    String progressvalue = progressPercentage != null
        ? progressPercentage == 0.0
            ? '0'
            : (progressPercentage * 100).toStringAsFixed(0)
        : '0';
    progressvalue = progressvalue == 'NaN' ? '0' : progressvalue;
    return progressvalue;
  }

  Widget _stepperWidget(
      BuildContext context,
      List<Modules> module,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state) {
    int length = module?.length ?? 0;
    List<stepper.Step> stepList = [];

    for (int index = 0; index < length; index++) {
      remainingAttempts = module?[index].remainingAttempts ?? 0;

      String progressvalue = _getProgressval(index);
      ResourceType? resoureType = module?[index].moduleType;
      bool _isTypeQuiz = resoureType == ResourceType.QUIZ;
      bool _isTypePage = resoureType == ResourceType.PAGE;

      bool _isTypeFileImage =
          resoureType == ResourceType.IMAGE || resoureType == ResourceType.FILE;
      bool _isTypePdf =
          module?[index].fileExtension.toLowerCase() == PDF_EXTENSION;

      String moduleName = module[index].moduleName.trim();
      bool _isActiveStep =
          module[index].isEnabled ? _currentResourceStep >= index : false;

      stepList.add(
        stepper.Step(
          title: Container(
            // color:
            //     (module!=null &&module?[index].isEnabled) ? Colors.amber : Colors.red,
            child: Row(
              children: [
                _isTypeQuiz
                    ? _imageWidget('question_light.png', 24.0, enableIcon: true)
                    : _isTypePage
                        ? _imageWidget('file_light.png', 24.0, enableIcon: true)
                        : _isTypeFileImage
                            ? _isTypePdf
                                ? _imageWidget('pdf.png', 20.0,
                                    enableIcon: true)
                                : _imageWidget('file_light.png', 24.0,
                                    enableIcon: true)
                            : _imageWidget('video_light.png', 24.0,
                                enableIcon: true),
                _moduleNameWidget(moduleName),
                const SizedBox(width: 5),
                module[index].didMarkedAsDone
                    ? _imageWidget('done_button.png', 24.0, enableIcon: true)
                    : module != null && module![index].isSkipped
                        ? _skipWidget()
                        : _skipIcon(context, module[index]),
              ],
            ),
          ),
          content: _stepperContentWidget(context, _sectionDetailsCubit, state,
              _examCubit, index, progressvalue),
          isActive: _isActiveStep,
        ),
      );
    }
    return length > 0
        ? stepper.Stepper(
            // key: ValueKey(getRandomValueForKey()),
            currentStep: _currentResourceStep,
            controlsBuilder: (context, controller) {
              return Container();
            },
            stepIconBuilder: (stepIndex, stepState) {
              return Container(
                decoration: BoxDecoration(
                  color: (module[stepIndex].isEnabled)
                      ? AppTheme.stepperColor
                      : AppTheme.transparentColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    (stepIndex + 1).toString(),
                    style: LMSFonts.sectionProgressTextStyle(
                        14, AppTheme.whiteColor),
                  ),
                ),
              );
            },
            onStepTapped: (int index) {
              if (module[index] != null && module[index].isEnabled) {
                _sectionDetailsCubit.stepItemTapped(
                    index, _currentFolderStep, _currentFolderResourceStep);
              } else {
                debugPrint('disabled!!!!!');
              }
            },
            steps: stepList,
          )
        : Container();
  }

  Widget _moduleNameWidget(String name) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(left: 8),
        child: _moduleNameText(name),
      ),
    );
  }

  Widget _skipWidget() {
    return Material(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
      ),
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          color: Colors.grey.shade300,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
        alignment: Alignment.center,
        child: Text(
          'Skipped',
          textAlign: TextAlign.center,
          style: LMSFonts.mediumItalicFont(12, AppTheme.primaryTextColorBlack),
        ),
      ),
    );
  }

  Widget _skipIcon(BuildContext context, Modules module) {
    return Visibility(
      visible: module.isEnabled,
      child: GestureDetector(
        onTap: () {
          _showSkipResourceAlert(context, module);
        },
        child: Material(
          elevation: 2,
          shape: const CircleBorder(),
          color: Colors.transparent,
          child: Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.9),
            ),
            padding: const EdgeInsets.all(2.5),
            child: Align(
                child: Image.asset(
              '$ASSETS_PATH/fast-forward.png',
              height: height,
              color: AppTheme.iconEnabledColor,
            )),
          ),
        ),
      ),
    );
  }

  _showSkipResourceAlert(BuildContext context, Modules module) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return MultiActionDialogue(
          title: 'Skip',
          content: 'Do you want to skip this resource?',
          alertType: AlertType.confirm,
          onContinueTap: () async {
            final _userActivityLog = UserActivityLog.instance;
            kIsWeb ? Beamer.of(context).beamBack() : Navigator.pop(context);
            final _sectionDetailsCubit =
                BlocProvider.of<SectionDetailsCubit>(context);
            _isDataLoading.value = true;
            await _sectionDetailsCubit.setSkippedResource(module);
            SectionDetailsState state = _sectionDetailsCubit.state;
            if (state is SkipModuleResourceSuccess) {
              // skipped resource
              int index = -1;
              if (sectionDetails != null) {
                if (showFolderResources) {
                  if (sectionDetails!.folders.isNotEmpty) {
                    index = sectionDetails?.folders[folderIndex].folderModules
                            .indexWhere((element) =>
                                element.instanceId == module.instanceId) ??
                        -1;
                  }
                } else {
                  index = sectionDetails?.modules.indexWhere((element) =>
                          element.instanceId == module.instanceId) ??
                      -1;
                }
              }

              _updateResourceStatusAfterSkip(index);
            }

            ///
            /// activity log update
            ///
            bool isSuccess = state is SkipModuleResourceSuccess;
            String error =
                state is SkipModuleResourceFailure ? state.error : '';
            await _userActivityLog.setResSkipActivityLog(
                context: context,
                screen: 'Section Details View',
                resourceType: module.moduleType.name,
                id: module.moduleId,
                result: isSuccess ? 'success' : 'error',
                responseStatus:
                    isSuccess ? 'skipped' : 'failed with error: $error');

            ///
            _isDataLoading.value = false;
          },
          onCancelTap: () =>
              kIsWeb ? Beamer.of(context).beamBack() : Navigator.pop(context),
          cancelBtnText: 'Cancel',
          confirmBtnText: 'Skip',
        );
      },
    );
  }

  _updateResourceStatus(int itemIndex) {
    if (sectionDetails == null) {
      return;
    }

    if (showFolderResources) {
      if (sectionDetails!.folders.isNotEmpty) {
        sectionDetails?.folders[folderIndex].folderModules[itemIndex].progress =
            100.0;

        final nextInprogressItem =
            sectionDetails?.folders[folderIndex].folderModules.indexWhere(
                    (element) => !element.isEnabled && !element.isSkipped) ??
                -1;
        final hasInProgressItems =
            sectionDetails?.folders[folderIndex].folderModules.any(
                  (e) =>
                      (e.isEnabled &&
                          e.progress != null &&
                          e.progress! < 100) &&
                      !e.isSkipped,
                ) ??
                false;
        if (nextInprogressItem != -1) {
          sectionDetails?.folders[folderIndex].folderModules[nextInprogressItem]
              .isEnabled = !hasInProgressItems;
        }
      }
    } else {
      sectionDetails?.modules[itemIndex].progress = 100.0;

      final nextInprogressItem = sectionDetails?.modules.indexWhere(
              (element) => !element.isEnabled && !element.isSkipped) ??
          -1;
      final hasInProgressItems = sectionDetails?.modules.any(
            (e) =>
                (e.isEnabled && e.progress != null && e.progress! < 100) &&
                !e.isSkipped,
          ) ??
          false;
      if (nextInprogressItem != -1) {
        sectionDetails?.modules[nextInprogressItem].isEnabled =
            !hasInProgressItems;
      }
    }
  }

  _updateResourceStatusAfterSkip(int itemIndex) {
    if (sectionDetails == null) {
      return;
    }

    if (showFolderResources) {
      if (sectionDetails!.folders.isNotEmpty) {
        sectionDetails
            ?.folders[folderIndex].folderModules[itemIndex].isSkipped = true;

        final nextInprogressItem =
            sectionDetails?.folders[folderIndex].folderModules.indexWhere(
                    (element) => !element.isEnabled && !element.isSkipped) ??
                -1;
        final hasInProgressItems =
            sectionDetails?.folders[folderIndex].folderModules.any(
                  (e) =>
                      (e.isEnabled &&
                          e.progress != null &&
                          e.progress! < 100) &&
                      !e.isSkipped,
                ) ??
                false;
        if (nextInprogressItem != -1) {
          sectionDetails?.folders[folderIndex].folderModules[nextInprogressItem]
              .isEnabled = !hasInProgressItems;
        }
      }
    } else {
      sectionDetails?.modules[itemIndex].isSkipped = true;

      final nextInprogressItem = sectionDetails?.modules.indexWhere(
              (element) => !element.isEnabled && !element.isSkipped) ??
          -1;
      final hasInProgressItems = sectionDetails?.modules.any(
            (e) =>
                (e.isEnabled && e.progress != null && e.progress! < 100) &&
                !e.isSkipped,
          ) ??
          false;
      if (nextInprogressItem != -1) {
        sectionDetails?.modules[nextInprogressItem].isEnabled =
            !hasInProgressItems;
      }
    }
  }

  Widget _moduleNameText(String name) {
    return Text(
      name,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: LMSFonts.mediumFont(14, AppTheme.progressContainerColor, 1.0),
    );
  }

  Widget _stepperContentWidget(
      BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      ExamCubit _examCubit,
      int index,
      String progressvalue) {
    Modules? moduleObj;

    if (sectionDetails != null) {
      if (showFolderResources) {
        if (sectionDetails!.folders.isNotEmpty) {
          moduleObj = sectionDetails?.folders[folderIndex].folderModules[index];
        }
      } else {
        moduleObj = sectionDetails?.modules[index];
      }
    }

    ResourceType? resourceType = moduleObj?.moduleType;

    bool _isTypeQuiz = resourceType == ResourceType.QUIZ;

    bool _isTypeVideo =
        resourceType == ResourceType.VIDEO || resourceType == ResourceType.URL;
    bool _isTypePage = resourceType == ResourceType.PAGE;
    bool _isTypeFileImage =
        resourceType == ResourceType.IMAGE || resourceType == ResourceType.FILE;
    String moduleName = moduleObj?.moduleName.trim() ?? '';

    if (moduleObj == null) {
      return Container();
    } else {
      return Column(
        children: <Widget>[
          DebouncedButton(
            disableButton: false,
            onPressed: () {
              if (moduleObj != null && moduleObj.isEnabled) {
                _onTapNavigationInfo(context, _examCubit, _sectionDetailsCubit,
                    state, index, moduleObj);
              } else {
                debugPrint('disabled!!!!!');
              }
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppTheme.sectionDetailsBgColor,
              ),
              padding: const EdgeInsets.symmetric(
                  horizontal: horizontalMargin, vertical: 10),
              child: Row(
                children: [
                  _videoProgressWidget(progressvalue),
                  // : Container(),
                  Expanded(
                    child: _moduleNameText(moduleName),
                  ),
                  _isTypeQuiz
                      ? _examAttendBtn(
                          context, _sectionDetailsCubit, _examCubit, moduleObj)
                      : _isTypePage || _isTypeFileImage
                          ? _imageWidget('view.png', 24.0, enableIcon: true)
                          : _imageWidget('arrow.png', 16.0, enableIcon: true)
                ],
              ),
            ),
          ),
        ],
      );
    }
  }

  Widget _videoProgressWidget(String progressvalue) {
    return Container(
        width: 45,
        height: 30,
        margin: const EdgeInsets.only(right: 15),
        decoration: BoxDecoration(
          color: AppTheme.progressContainerColor,
          borderRadius: BorderRadius.circular(5.0),
        ),
        child: Center(
          child: Text(
            progressvalue + '%',
            style: LMSFonts.sectionProgressTextStyle(12, AppTheme.whiteColor),
          ),
        ));
  }

  Widget _examAttendBtn(
      BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit,
      ExamCubit _examCubit,
      Modules moduleObj) {
    return GestureDetector(
      onTap: remainingAttempts > 0
          ? () async {
              await _fetchQuestionsForExam(
                _examCubit,
                context,
                _sectionDetailsCubit,
                moduleObj.instanceId ?? '',
              );
            }
          : null,
      child:
          _imageWidget('quiz_add.png', 24.0, enableIcon: remainingAttempts > 0),
    );
  }

  Widget _imageWidget(String path, double height, {required bool enableIcon}) {
    return Image.asset(
      '$ASSETS_PATH/$path',
      height: height,
      color:
          enableIcon ? AppTheme.iconEnabledColor : AppTheme.iconDisableddColor,
    );
  }

  _navigationInfo(BuildContext context) async {
    try {
      kIsWeb
          ? Beamer.of(context).beamToNamed('/exam-intro', data: {
              'questionData': _questionData!,
              'isFromSectionDetails': true
            })
          : appRouter.push(ExamIntroductionViewRoute(
              questionData: _questionData!,
              isFromSectionDetails: true,
              navigateBackTo: NavigationTo.section_details,
            ));
    } on Exception catch (e) {
      debugPrint("exception => _navigationInfo => $e");
    }
  }

  _onTapNavigationInfo(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      int index,
      Modules moduleObj) async {
    _isDataLoading.value = true;
    ResourceType resoureType = moduleObj.moduleType;

    bool _isTypeQuiz = resoureType == ResourceType.QUIZ;

    bool _isTypeVideo =
        resoureType == ResourceType.VIDEO || resoureType == ResourceType.URL;
    bool _isTypePage = resoureType == ResourceType.PAGE;
    bool _isTypeFileImage =
        resoureType == ResourceType.IMAGE || resoureType == ResourceType.FILE;

    String instanceId = moduleObj.instanceId;

    bool _hasAccessToGetResource = await _checkAccessForFetchingResource();

    if (_isTypeQuiz) {
      if (moduleObj.remainingAttempts > 0) {
        _isDataLoading.value = false;

        await _fetchQuestionsForExam(
          _examCubit,
          context,
          _sectionDetailsCubit,
          moduleObj.instanceId ?? '',
        );
      }
    } else if (state is PageDetailsError) {
      _isDataLoading.value = false;
      showPGExceptionPopup(context, state.error);
    } else {
      ResourceProgress? _courseProgress =
          await _fetchCourseProgress(context, _sectionDetailsCubit, instanceId);

      ///
      /// _courseProgress == null --> if the _fetchCourseProgress() method returned null,
      /// happens if the privilege access is denied.
      ///
      /// _courseProgress != null -> check is added to
      /// prevent multiple privilege restricted popup
      ///
      _isDataLoading.value = false;
      if (_courseProgress != null) {
        if (_isTypeVideo) {
          await _handleVideoTypeNavigation(context, _examCubit,
              _sectionDetailsCubit, state, index, _courseProgress);
        } else if (_isTypePage) {
          await _handlePageTypeNavigation(context, _examCubit,
              _sectionDetailsCubit, state, index, _courseProgress);
        } else if (_isTypeFileImage) {
          await _handleFileImageTypeNavigation(context, _examCubit,
              _sectionDetailsCubit, state, index, _courseProgress);
        }
      }
    }
    _isDataLoading.value = false;
  }

  Future<bool> _checkAccessForFetchingResource() async {
    // decides whether to fetch course resource- url, video, image, file
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_RESOURCE;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _handleVideoTypeNavigation(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      int index,
      ResourceProgress courseProgress) async {
    CourseVideoResource? _courseVideoObj;
    List<CheckPoint> _checkPoints = [];
    String? id;
    String instanceId = '';
    String courseModuleId = '';

    if (sectionDetails != null) {
      if (showFolderResources) {
        if (sectionDetails!.folders.isNotEmpty) {
          instanceId = sectionDetails
                  ?.folders[folderIndex].folderModules[index].instanceId ??
              '';
          courseModuleId = sectionDetails
                  ?.folders[folderIndex].folderModules[index].courseModuleId ??
              '';
        }
      } else {
        instanceId = sectionDetails!.modules[index].instanceId;
        courseModuleId = sectionDetails!.modules[index].courseModuleId ?? '';
      }
    }

    _isDataLoading.value = true;

    await _sectionDetailsCubit.fetchVideoContent(instanceId, courseModuleId);
    // await _sectionDetailsCubit
    //     .fetchCheckPointData("9bf89423-3b9b-4b83-a654-b4a6f6c05b2a");
    state = _sectionDetailsCubit.state;

    if (state is VideoResourceError) {
      // await _sectionDetailsCubit.setLoading(false);
      _isDataLoading.value = false;

      showPGExceptionPopup(context, state.error);
    } else if (state is VideoResourceFetched) {
      _courseVideoObj = state.courseVideo;
      _courseVideoObj.instanceId = instanceId;
      _checkPoints.addAll(state.checkPoints);
      state = _sectionDetailsCubit.state;

      _isDataLoading.value = false;

      // await _sectionDetailsCubit.setLoading(false);

      id = sectionDetails?.courseId;
      await _sectionDetailsCubit.getCourseprogress(_courseVideoObj.instanceId);
      state = _sectionDetailsCubit.state;
      if (state is GetCourseProgressState) {
        kIsWeb
            ? Beamer.of(context).beamToNamed(
                "/video-view",
                data: {
                  "courseVideo": _courseVideoObj,
                  "isFromSectionDetails": true,
                  "checkPoints": _checkPoints
                },
              )
            : appRouter
                .push(CourseVideoViewRoute(
                    courseVideo: _courseVideoObj,
                    courseProgress: courseProgress,
                    isFromSectionDetails: true,
                    navigateBackTo: NavigationTo.section_details,
                    checkPoints: _checkPoints))
                .then((value) async {
                if (value != null) {
                  Map<String, dynamic> args = value as Map<String, dynamic>;

                  double progress = double.parse(args['progress'].toString());
                  if (sectionDetails != null) {
                    if (showFolderResources &&
                        sectionDetails!.folders.isNotEmpty) {
                      sectionDetails?.folders[folderIndex].folderModules[index]
                          .progress = progress;
                      sectionDetails?.folders[folderIndex].folderModules[index]
                          .didMarkedAsDone = (progress == 100);
                    } else {
                      sectionDetails?.modules[index]?.progress = progress;
                      sectionDetails?.modules[index].didMarkedAsDone =
                          (progress == 100);
                    }
                  }
                  _updateResourceStatus(index);
                  await _sectionDetailsCubit.refreshScreen();
                }
              });
      }
    }
    _isDataLoading.value = false;
  }

  _handlePageTypeNavigation(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      int index,
      ResourceProgress? courseProgress) async {
    String instanceId = '';
    String courseModuleId = '';
    int folderResourceLength = 0;

    if (sectionDetails != null) {
      if (showFolderResources) {
        if (sectionDetails!.folders.isNotEmpty) {
          instanceId = sectionDetails
                  ?.folders[folderIndex].folderModules[index].instanceId ??
              '';
          folderResourceLength =
              sectionDetails?.folders[folderIndex].folderModules.length ?? 0;
          courseModuleId = sectionDetails
                  ?.folders[folderIndex].folderModules[index].courseModuleId ??
              '';
        }
      } else {
        instanceId = sectionDetails!.modules[index].instanceId;
        courseModuleId = sectionDetails?.modules[index].courseModuleId ?? '';
      }
    }
    _isDataLoading.value = true;
    // await _sectionDetailsCubit.setLoading(true);
    await _sectionDetailsCubit.fetchPageContent(instanceId, courseModuleId);
    state = _sectionDetailsCubit.state;
    if (state is PageContentsFetched) {
      // await _sectionDetailsCubit.getCourseprogress(instanceId);
      // state = _sectionDetailsCubit.state;
      // if (state is GetCourseProgressState) {
      //   CourseProgress courseProgress = state.progress;
      //  } else if (state is GetCourseProgressFailureState) {
      //   showPGExceptionPopup(context, state.error);
      // }
      CoursePageResource pageContent = state.pageContentData;
      _isDataLoading.value = false;
      await _sectionDetailsCubit.setLoading(false);
      kIsWeb
          ? Beamer.of(context).beamToNamed('/study-materials', data: {
              'pageContent': pageContent,
              'instance': instanceId,
              'courseId': sectionDetails?.courseId ?? COURSE_ID,
              'progess': courseProgress,
              'moduleLength': folderResourceLength,
            })
          : appRouter
              .push(StudyMaterialViewRoute(
                  pageContent: pageContent,
                  moduleLength: folderResourceLength,
                  resourceProgress: courseProgress))
              .then((value) async {
              if (value != null && value == true) {
                // Map<String, dynamic> args = value as Map<String, dynamic>;

                double studyProgress = 100.0;
                if (sectionDetails != null) {
                  if (showFolderResources &&
                      sectionDetails!.folders.isNotEmpty) {
                    sectionDetails?.folders[folderIndex].folderModules[index]
                        .progress = studyProgress;
                    sectionDetails?.folders[folderIndex].folderModules[index]
                        .didMarkedAsDone = true;
                  } else {
                    sectionDetails?.modules[index]?.progress = studyProgress;
                    sectionDetails?.modules[index].didMarkedAsDone = true;
                  }
                }
                _updateResourceStatus(index);
                await _sectionDetailsCubit.refreshScreen();
              }
            });
    } else if (state is PageDetailsError) {
      _isDataLoading.value = false;

      // await _sectionDetailsCubit.setLoading(false);
      showPGExceptionPopup(context, state.error);
    }
    _isDataLoading.value = false;
  }

  _handleFileImageTypeNavigation(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      int index,
      ResourceProgress? courseProgress) async {
    try {
      CourseFileResource? _resourceFile;
      String instance = '';
      String courseModuleId = '';
      if (sectionDetails != null) {
        if (showFolderResources) {
          if (sectionDetails!.folders.isNotEmpty) {
            instance = sectionDetails
                    ?.folders[folderIndex].folderModules[index].instanceId ??
                '';
            courseModuleId = sectionDetails?.folders[folderIndex]
                    .folderModules[index].courseModuleId ??
                '';
          }
        } else {
          instance = sectionDetails!.modules[index].instanceId;
          courseModuleId = sectionDetails!.modules[index].courseModuleId;
        }
      }

      _isDataLoading.value = true;

      //  await _sectionDetailsCubit.setLoading(true);
      await _sectionDetailsCubit.fetchFileContent(instance, courseModuleId);
      // For Image View

      state = _sectionDetailsCubit.state;

      if (state is FileResourceFetched) {
        _resourceFile = state.resourseFile;
        _resourceFile.id = instance;

        bool isNetworkUrl = await validateImage(_resourceFile.url ?? '');

        if (sectionDetails?.modules[index].fileExtension == PPT_EXTENSION ||
            sectionDetails?.modules[index].fileExtension == PPTX_EXTENSION) {
          ///
          /// PPT
          ///

          _resourceFile.progress =
              double.tryParse(_getProgressval(index)) ?? 0.0;
          // courseProgress?.progress ?? 0.0;

          await _handleNavigationToPPTReader(context, _resourceFile, index);
        } else if (_resourceFile != null && !_resourceFile.url.endsWith(HTML)) {
          if (_resourceFile.url.endsWith(IMAGE_EXTENSIONJPG) ||
              _resourceFile.url.endsWith(IMAGE_EXTENSIONJPEG) ||
              _resourceFile.url.endsWith(IMAGE_EXTENSIONPNG) ||
              (isNetworkUrl)) {
            _handleNavigationToImageView(
              context,
              index,
              courseProgress,
              _resourceFile,
            );
          } else {
            _isDataLoading.value = false;
            //   await _sectionDetailsCubit.setLoading(false);
            _showAlertDialog(context,
                SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
          }
        } else {
          _isDataLoading.value = false;
          _showAlertDialog(
              context, SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
        }
      } else if (state is FileResourceError) {
        _isDataLoading.value = false;
        showPGExceptionPopup(context, state.error);
      } else {
        _isDataLoading.value = false;
        _showAlertDialog(
            context, SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
      }
    } on Exception catch (_) {
      // TODO
      _isDataLoading.value = false;
    }
    _isDataLoading.value = false;
  }

  _handleNavigationToPPTReader(
      BuildContext context, CourseFileResource _resourceFile, int index) async {
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);
    final _sectionDetailsCubit = BlocProvider.of<SectionDetailsCubit>(context);

    _isDataLoading.value = true;

    await _courseDetailsCubit.fetchFileResource(
        _resourceFile.id, _resourceFile.courseModuleId);
    // For Image View

    CourseDetailsState state = _courseDetailsCubit.state;

    if (state is CourseDetailsFileResFetched) {
      if (state.courseFileResource.isNotEmpty) {
        final CourseFileResource _fileRes = state.courseFileResource.first;
        _sectionDetailsCubit.courseFileobj = _fileRes;
        _fileRes.progress = _resourceFile.progress;

        final CheckPointData checkPointData = state.checkPointData;

        Future.delayed(const Duration(milliseconds: 500), () {
          _isDataLoading.value = false;
        });
        appRouter
            .push(PPTViewerRoute(
          courseFileResource: _fileRes,
          checkPoints: checkPointData.checkPoints ?? [],
          isFromExamScreen: false,
          navigateBackTo: NavigationTo.section_details,
        ))
            .then((value) async {
          if (value != null && _fileRes.progress < 100) {
            final progress = (value as double).toInt();
            if (sectionDetails != null) {
              if (showFolderResources && sectionDetails!.folders.isNotEmpty) {
                sectionDetails?.folders[folderIndex].folderModules[index]
                    .progress = progress.toDouble();
                sectionDetails?.folders[folderIndex].folderModules[index]
                    .didMarkedAsDone = (progress == 100);
              } else {
                sectionDetails?.modules[index]?.progress = progress.toDouble();
                sectionDetails?.modules[index].didMarkedAsDone = (value == 100);
              }
            }
          }
          _updateResourceStatus(index);
          _isDataLoading.value = false;
        });
      }
    }
  }

  _handleNavigationToImageView(
    BuildContext context,
    int index,
    ResourceProgress? courseProgress,
    CourseFileResource _resourceFile,
  ) async {
    String instanceId = '';
    String courseId = '';
    int folderResourceLength = 0;

    if (sectionDetails != null) {
      if (showFolderResources) {
        if (sectionDetails!.folders.isNotEmpty) {
          instanceId = sectionDetails
                  ?.folders[folderIndex].folderModules[index].instanceId ??
              '';
          folderResourceLength =
              sectionDetails?.folders[folderIndex].folderModules.length ?? 0;
        }
      } else {
        instanceId = sectionDetails!.modules[index].instanceId;
      }
    }
    courseId = sectionDetails!.courseId ?? COURSE_ID;

    kIsWeb
        ? Beamer.of(context).beamToNamed('/image-view', data: {
            'imagePath': _resourceFile.url,
            'resourceFile': _resourceFile,
            'instance': instanceId,
            'courseId': courseId,
            'progess': courseProgress,
            'moduleLength': folderResourceLength,
          })
        : appRouter
            .push(ImageViewRoute(
                resourceFile: _resourceFile,
                moduleLength: folderResourceLength,
                resourceProgress: courseProgress))
            .then((value) async {
            if (value != null) {
              _isDataLoading.value = true;
              Map<String, dynamic> args = value as Map<String, dynamic>;

              double imageProgress = double.parse(args['progress'].toString());
              _isDataLoading.value = true;
              if (sectionDetails != null) {
                if (showFolderResources && sectionDetails!.folders.isNotEmpty) {
                  sectionDetails?.folders[folderIndex].folderModules[index]
                      .progress = imageProgress;
                  sectionDetails?.folders[folderIndex].folderModules[index]
                      .didMarkedAsDone = (imageProgress == 100);
                } else {
                  sectionDetails?.modules[index]?.progress = imageProgress;
                  sectionDetails?.modules[index].didMarkedAsDone =
                      (imageProgress == 100);
                }
              }
              _updateResourceStatus(index);

              // await _sectionDetailsCubit.refreshScreen();
              // if (_courseDetailsDashboard.isNotEmpty) {
              //   int itemIndex = _courseDetailsDashboard.first.courseAssignments
              //       .indexWhere((element) =>
              //           element.resourceId == courseAssignment.resourceId);
              //   _courseDetailsDashboard
              //       .first.courseAssignments[itemIndex].progress = progress;
              //   bool isCompleted =
              //       double.parse(args['progress'].toString()) == 100.0;
              //   if (isCompleted) {
              //     updateResourceStatus(itemIndex, isCompleted);
              //   }
              // }
              _isDataLoading.value = false;
            }
          });
  }

  Future<bool> _checkAccessToFetchQuestions() async {
    // decides whether to disable/enable attend  button
    String screen = PrivilegeAccessConsts.SCREEN_EXAM;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_GET_QUIZ_QUESTIONS;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  Future<bool> _checkAccessToFetchCourseProgress() async {
    // decides whether to fetch course progress
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_GET_COURSE_PROGRESS;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  ///fetch the progress value for any type resource
  Future<ResourceProgress?> _fetchCourseProgress(BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit, String instanceId) async {
    // bool _hasAccess = await _checkAccessToFetchCourseProgress();
    // if (_hasAccess) {
    SectionDetailsState state = _sectionDetailsCubit.state;
    await _sectionDetailsCubit.getCourseprogress(instanceId);
    state = _sectionDetailsCubit.state;
    if (state is GetCourseProgressState) {
      ResourceProgress courseProgress = state.progress;
      return courseProgress;
    } else if (state is GetCourseProgressFailureState) {
      ResourceProgress courseProgress = ResourceProgress(
          progress: 0.0,
          timeSpent: '',
          instanceId: instanceId,
          markedAsDone: false);
      bool isActiveRouteInWeb = false;
      if (kIsWeb) {
        var beamerRouter = Beamer.of(context);
        isActiveRouteInWeb = beamerRouter.active;
      }

      bool isThereCurrentDialogShowing = kIsWeb
          ? isActiveRouteInWeb
          : ModalRoute.of(context)?.isCurrent != true;
      if (!isThereCurrentDialogShowing) {
        showPGExceptionPopup(context, state.error);
      }
      return courseProgress;
    }
    // } else {
    //   bool _shouldShowPopUp = checkForActiveRoute(context,
    //       webCurrentRouteName: '/section-details',
    //       mobileCurrentRouteName: SectionDetailsViewRoute.name);
    //   if (_shouldShowPopUp) {
    //     showNoAccessPopup(context, '');
    //   }
    // }
    return null;
  }

  void _showAlertDialog(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: msg,
          iconWidget: const Icon(Icons.error, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  pdfScreenNavigation(
      SectionDetailsCubit _sectionDetailsCubit, BuildContext context) async {
    try {
      _isDataLoading.value = true;
      // _sectionDetailsCubit.setLoading(true);
      String path = await _sectionDetailsCubit.getFileFromUrl(SAMPLE_PDF_PATH);

      // if (path != null) {
      //   kIsWeb
      //       ? Beamer.of(context)
      //           .beamToNamed('/pdf-view', data: {'urlPDFPath': path})
      //       : await appRouter.push(PDFViewRoute(urlPDFPath: path));
      // }
      _isDataLoading.value = false;

      // _sectionDetailsCubit.setLoading(false);
    } on Exception catch (e) {
      debugPrint('[section_details_view.dart][pdfScreenNavigation]: $e');
    }
  }

  _fetchQuestionsForExam(ExamCubit _examCubit, BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit, String examId) async {
    try {
      bool _hasAccess = await _checkAccessToFetchQuestions();

      if (_hasAccess) {
        _isDataLoading.value = true;
        //    _sectionDetailsCubit.setLoading(true);
        await _examCubit.fetchQuestions(examId);
        _isDataLoading.value = false;
        // _sectionDetailsCubit.setLoading(false);

        ExamState state = _examCubit.state;

        if (state is ExamsQuestionsFailureState) {
          bool _shouldShowPopUp = checkForActiveRoute(context,
              webCurrentRouteName: '/section-details',
              mobileCurrentRouteName: SectionDetailsViewRoute.name);
          if (_shouldShowPopUp) {
            showPGExceptionPopup(context, state.error);
          }
        } else {
          if (_examCubit.state.examData.isNotEmpty) {
            _questionData = _examCubit.state.examData.first;
          }
          _questions = _examCubit.state.examData.isEmpty
              ? []
              : _examCubit.state.examData.first.questions;

          DateTime currentTime = DateTime.now();
          DateTime startTime = _questionData?.startTime ?? currentTime;
          DateTime endTime = _questionData?.endTime ?? currentTime;

          if ((currentTime.isAfter(startTime) &&
                  currentTime.isBefore(endTime)) ||
              (currentTime.isAtSameMomentAs(startTime))) {
            // check if the exam is valid
            if (_questions.isNotEmpty) {
              _navigationInfo(context);
            } else {
              // no questions available for the exam
              _showQuestionsEmptyPopup(context);
            }
          } else {
            // not valid
            // exam expired
            // TO DO: remove after- modify list tile UI and show as invalid
            if (currentTime.isBefore(startTime)) {
              String startTimeFormatted =
                  AppDateFormatter().formatForExamAvailabilityPopup(startTime);
              _showInvalidExamPopup(context,
                  '${SLStrings.getTranslatedString(KEY_EXAM_AVAILABLE_POPUP)} $startTimeFormatted');
            } else {
              _showInvalidExamPopup(
                  context, SLStrings.getTranslatedString(KEY_EXAM_EXPIRED));
            }
          }
        }
      } else {
        bool _shouldShowPopUp = checkForActiveRoute(context,
            webCurrentRouteName: '/section-details',
            mobileCurrentRouteName: SectionDetailsViewRoute.name);
        if (_shouldShowPopUp) {
          showNoAccessPopup(context, '');
        }
      }
    } on Exception catch (e) {
      debugPrint('[section_details_view][_fetchQuestionsForExam]: $e');
    }
  }

  _showInvalidExamPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: msg,
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showQuestionsEmptyPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        // ignore: lines_longer_than_80_chars
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: SLStrings.getTranslatedString(KEY_QUESTION_NOT_AVAILABLE),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              return;
            }
          },
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            alertType: AlertType.error,
            onContinueTap: () =>
                kIsWeb ? Navigator.of(context).pop() : appRouter.popForced(),
            onCancelTap: () {
              kIsWeb
                  ? Beamer.of(context).beamToNamed('/section-details')
                  : appRouter.popUntil((route) =>
                      route.settings.name == SectionDetailsViewRoute.name);
            },
            cancelBtnText: SLStrings.getTranslatedString(KEY_OK),
            confirmBtnText: '',
          ),
        );
      },
    );
  }
}
