part of 'exam_clock_cubit.dart';

abstract class ExamClockState extends Equatable {
  final String currentTime;
  const ExamClockState({required this.currentTime});

  @override
  List<Object> get props => [currentTime];
}

class ExamClockInitial extends ExamClockState {
  const ExamClockInitial({required super.currentTime});
}

class ExamCurrentTime extends ExamClockState {
  const ExamCurrentTime({required super.currentTime});
}
