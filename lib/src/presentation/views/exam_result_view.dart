import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/app_config/preference_config.dart';
import '../../config/enums/question_answer_type.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../../utils/constants/nums.dart';
import '/src/presentation/widgets/app_bar.dart';
import '/src/presentation/widgets/html_content.dart';

import '/src/presentation/cubits/exam/exam_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/models/exam_summary.dart';
import '../../domain/models/question_data.dart';
import '../widgets/loading_indicator.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/presentation/widgets/button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/theme_colors.dart';
import '../../utils/constants/strings.dart';

@RoutePage()
// ignore: must_be_immutable
class ExamResultViewScreen extends HookWidget with WidgetsBindingObserver {
  final ExamSummary examSummary;
  final int tabIndex;

  ExamResultViewScreen(
      {super.key, required this.examSummary, required this.tabIndex});

  final markDetails = <MarkDetails>[];

  final double _horizontalMargin = 16.0;

  BuildContext? currentContext;

  /// calculate score range needed for y axis in the line graph
  _calculateGraphicalData(ExamCubit _examCubit) async {
    try {
      Future.sync(() {
        MarkDetails _markDetailsObj;

        for (var question in examSummary.questions) {
          List<Answer> chosenAnswers = question.options
              .where((element) =>
                  question.selectedAnswerIds.contains(element.answerId))
              .toList();
          if (chosenAnswers.isEmpty) {
            _markDetailsObj =
                MarkDetails(qustionNum: question.id, scoreRange: 0);

            markDetails.add(_markDetailsObj);
          } else {
            _markDetailsObj = MarkDetails(
                qustionNum: question.id,
                scoreRange: question.markScored ?? 0.0);

            markDetails.add(_markDetailsObj);
          }
        }
      });
      await _updateExamsList(_examCubit);
    } on Exception catch (e) {
      debugPrint('[exam_result_view.dart][_calculateGraphicalData]: $e');
    }
    return;
  }

  _updateExamsList(ExamCubit _examCubit) async {
    if (examSummary.didPassExam) {
      await _examCubit.fetchExamList();
    }
  }

  @override
  void didChangeLocales(List<Locale>? locales) async {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('de_DE')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    await didChangeAppLocale(locales, currentContext);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final _examCubit = BlocProvider.of<ExamCubit>(context);
    final screenWidth = MediaQuery.of(context).size.width;

    final qustionNum = useState<int>(1);
    final isVisible = useState<bool>(false);

    final zoomPanBehavior = useState<ZoomPanBehavior>(
      ZoomPanBehavior(
        enablePanning: true,
        enablePinching: true,
      ),
    );

    useEffect(() {
      Future<void>.microtask(() async {
        _examCubit.setLoading(true);
        await _calculateGraphicalData(_examCubit);
        _examCubit.setLoading(false);
      });
      WidgetsBinding.instance.addObserver(this);

      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
      },
      child: Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title:
                  SLStrings.getTranslatedString(KEY_EXAM_RESULT_APPBAR_TITLE),
              trailingWidget: Container(),
              leadingIconName: '',
              isFromLogin: true,
              leadingBtnAction: () {},
            )),
        body: BlocBuilder<ExamCubit, ExamState>(
          builder: (context, state) {
            return LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
              return Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16)),
                ),
                child: state is ExamsLoading && state.loadingStatus
                    ? LoadingIndicatorClass()
                    : Stack(
                        children: [
                          ScrollConfiguration(
                            behavior: ScrollConfiguration.of(context).copyWith(
                              dragDevices: {
                                PointerDeviceKind.touch,
                                PointerDeviceKind.mouse,
                              },
                            ),
                            child: SingleChildScrollView(
                              child: Align(
                                child: Container(
                                  width: constraints.maxWidth < 720
                                      ? constraints.maxWidth
                                      : 800,
                                  margin: EdgeInsets.symmetric(
                                      horizontal: _horizontalMargin,
                                      vertical: 10),
                                  child: Column(
                                    children: [
                                      const SizedBox(height: 56),
                                      _resultWidget(),
                                      const SizedBox(height: 20),
                                      Text(
                                          examSummary.didPassExam
                                              ? SLStrings.getTranslatedString(
                                                  KEY_EXAM_ANALYSIS)
                                              : SLStrings.getTranslatedString(
                                                  KEY_REATTEND_EXAM),
                                          textAlign: TextAlign.center,
                                          style: LMSFonts.regularFontWithHeight(
                                              14,
                                              AppTheme.resultDescriptionColor,
                                              1.3)),
                                      const SizedBox(height: 30),
                                      _topicStatictics(),
                                      const SizedBox(height: 30),
                                      _dividerWidget(5.0),
                                      _doughnutGraph(context),
                                      _lineGraph(context, qustionNum, isVisible,
                                          markDetails, zoomPanBehavior),
                                      const SizedBox(height: 20),
                                      _titleWidget(
                                          SLStrings.getTranslatedString(
                                              KEY_SCORE_SHEET)),
                                      const SizedBox(height: 15),
                                      _subTitleWidget(
                                          SLStrings.getTranslatedString(
                                              KEY_REVIEW_ANSWER)),
                                      const SizedBox(height: 20),
                                      _dividerWidget(5.0),
                                      const SizedBox(height: 15),
                                      _totalMarkWidget(context),
                                      const SizedBox(height: 15),
                                      _markWidget(),
                                      const SizedBox(height: 15),
                                      _topicTitleWidget(
                                          SLStrings.getTranslatedString(
                                              KEY_STATISTICS)),
                                      const SizedBox(height: 15),
                                      _topicStatictics(),
                                      const SizedBox(height: 20),
                                      _topicStaticticsDiagram(context),
                                      const SizedBox(height: 25),
                                      Container(
                                        width: double.infinity,
                                        margin:
                                            const EdgeInsets.only(bottom: 15),
                                        child: _buttonWidget(
                                            context, screenWidth, _examCubit),
                                      ),
                                      // ])
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          ExamFinishButton(
                            constraints: constraints,
                            examCubit: _examCubit,
                          ),
                        ],
                      ),
              );
            });
          },
        ),
      ),
    );
  }

  Widget _topicStatictics() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _squareIconWithTitle(SLStrings.getTranslatedString(KEY_CORRECT),
            AppTheme.correctColor, examSummary.correctAnswerCount),
        _squareIconWithTitle(SLStrings.getTranslatedString(KEY_WRONG),
            AppTheme.wrongColor, examSummary.wrongAnswerCount),
        _squareIconWithTitle(SLStrings.getTranslatedString(KEY_SKIPPED),
            AppTheme.skippColor, examSummary.skippedAnsCount),
      ],
    );
  }

  Widget _topicStaticticsDiagram(BuildContext context) {
    String title = examSummary.examName;
    int totalNoOfQstns = examSummary.numOfQuestions;
    int correct = examSummary.correctAnswerCount;
    int wrong = examSummary.wrongAnswerCount;
    int skipped = examSummary.skippedAnsCount;
    double totalWidth = MediaQuery.sizeOf(context).width - 32;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppTheme.topicBgColor,
        ),
        borderRadius: BorderRadius.circular(10),
        color: AppTheme.topicBgColor,
      ),
      child: Padding(
        padding: EdgeInsets.only(
            left: _horizontalMargin,
            top: 9,
            right: _horizontalMargin,
            bottom: _horizontalMargin),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _categoryNameRowWidget(title, correct, totalNoOfQstns),
            const SizedBox(height: 13),
            _expansionTileStatusWidget(correct, wrong, skipped, totalWidth),
            // ExpansionTile(
            //   title: _expansionTileStatusWidget(
            //       correct, wrong, skipped, totalWidth),
            //   trailing: //examSummary.categoeyWiseSummary.isNotEmpty
            //       Container(width: 1),
            //   children: List.generate(
            //     examSummary.categoeyWiseSummary.length,
            //     (categoryIndex) {
            //       String title = examSummary
            //           .categoeyWiseSummary[categoryIndex].questionCategoryName;
            //       int correct = examSummary
            //           .categoeyWiseSummary[categoryIndex].correctAnswerCount;
            //       int wrong = examSummary
            //           .categoeyWiseSummary[categoryIndex].wrongAnswerCount;
            //       int skipped = examSummary
            //           .categoeyWiseSummary[categoryIndex].skippedAnsCount;
            //       int totalNoOfQstns = correct + wrong + skipped;

            //       /// categoryWiseSummary
            //       return Padding(
            //         padding: const EdgeInsets.symmetric(horizontal: 5),
            //         child: ListTile(
            //           title: Column(
            //             mainAxisAlignment: MainAxisAlignment.center,
            //             children: [
            //               _categoryNameRowWidget(
            //                   title, correct, totalNoOfQstns),
            //               _expansionTileStatusWidget(
            //                   correct, wrong, skipped, totalWidth),
            //               const SizedBox(height: 10),
            //             ],
            //           ),
            //         ),
            //       );
            //     },
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _categoryStatusBar(Color color, double value) {
    return Flexible(
      child: Container(
        color: color,
        width: value,
        height: 9,
      ),
    );
  }

  Widget _categoryNameRowWidget(String title, int correct, int totalNoOfQstns) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(title,
              maxLines: null,
              style: LMSFonts.mediumFont(16, AppTheme.whiteTextColor, 0)),
        ),
        const SizedBox(width: 10),
        Text(correct.toString() + '/' + totalNoOfQstns.toString(),
            style: LMSFonts.mediumFont(16, AppTheme.whiteTextColor, 0))
      ],
    );
  }

  Widget _expansionTileStatusWidget(
      int correct, int wrong, int skipped, double totalWidth) {
    return Container(
        height: 9,
        decoration: BoxDecoration(
            color: AppTheme.whiteColor,
            borderRadius: BorderRadius.circular(25)),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: Row(
            children: [
              _squareWidget(correct, AppTheme.correctColor),
              _squareWidget(wrong, AppTheme.wrongColor),
              _squareWidget(skipped, AppTheme.skippColor),
            ],
          ),
        ));
  }

  Widget _squareWidget(int width, Color color) {
    return Visibility(
      visible: width > 0,
      child: Flexible(
        flex: width,
        child: Container(
          decoration: BoxDecoration(
            color: color,
          ),
        ),
      ),
    );
  }

  Widget _squareIconWithTitle(String title, Color color, int count) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
          ),
        ),
        const SizedBox(width: 10),
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            title + ' ($count)',
            style: LMSFonts.regularFontWithHeight(
                14, AppTheme.resultDescriptionColor, 0),
          ),
        ),
      ],
    );
  }

  Widget _buttonWidget(
      BuildContext context, double screenWidth, ExamCubit _examCubit) {
    return Align(
        alignment: Alignment.bottomCenter,
        child: ButtonWidget(
          child: SizedBox(
            width: double.infinity,
            child: Text(
              SLStrings.getTranslatedString(KEY_VIEW_SOLUTION),
              style: LMSFonts.buttonStyle(16.0),
              textAlign: TextAlign.center,
            ),
          ),
          textColor: LmsColors.white,
          color: AppTheme.buttonBgColour,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          onPressed: () async {
            List<Question> _questions = examSummary.questions;
            if (_questions.isNotEmpty) {
              QuestionData _questionData =
                  QuestionData.fromExamSummary(examSummary);

              /// disable/enable navigation
              _handleBackNavigations(_examCubit);

              _questions = examSummary.questions;
              kIsWeb
                  ? Beamer.of(context).beamToNamed('/exam-view', data: {
                      'examDuration': _questionData.duration,
                      'shouldShowTimer': false,
                      'isFromViewResult': true,
                      'questionData': _questionData,
                      'quizAttemptId': ''
                    })
                  : appRouter.push(
                      ExamViewRoute(
                          examDuration: _questionData.duration,
                          shouldShowTimer: false,
                          isFromViewResult: true,
                          questionData: _questionData,
                          quizAttemptId: ''),
                    );
            }
          },
        ));
  }

  _handleBackNavigations(ExamCubit _examCubit) {
    _examCubit.updateNavigationStatustoReview(disableBackNavigation: false);
    _examCubit.updateNavigationStatustoResult(disableBackNavigation: true);
    _examCubit.updateNavigationStatustoList(disableBackNavigation: false);
    _examCubit.updateNavigationStatustoExamView(disableBackNavigation: false);
  }

  Widget _resultWidget() {
    if (examSummary.didPassExam) {
      return passedResultWidget();
    } else {
      return failedResultWidget();
    }
  }

  Widget failedResultWidget() {
    return Column(
      children: [
        Center(
          child: Image.asset(
            "$ASSETS_PATH/fail.png",
            width: 110,
            height: 110,
            fit: BoxFit.fill,
          ),
        ),
        const SizedBox(height: 15),
        Text(SLStrings.getTranslatedString(KEY_FAILED_EXAMINATION),
            textAlign: TextAlign.center,
            style: LMSFonts.mediumFont(20, AppTheme.failColor, 1))
      ],
    );
  }

  Widget passedResultWidget() {
    return Column(
      children: [
        Center(
          child: Image.asset(
            "$ASSETS_PATH/pass.png",
            width: 110,
            height: 110,
            fit: BoxFit.fill,
          ),
        ),
        // ),
        const SizedBox(height: 20),
        Text(SLStrings.getTranslatedString(KEY_PASSED_EXAMINATION),
            textAlign: TextAlign.center,
            style: LMSFonts.mediumFont(20, AppTheme.correctColor, 1)),
      ],
    );
  }

  Widget _imageContainer(String path) {
    return Container(
        padding: const EdgeInsets.all(10),
        height: 60,
        width: 60,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: AppTheme.containerBorderColor,
            width: 2,
          ),
        ),
        child: Center(
          child: Image.asset(
            '$ASSETS_PATH/${path}',
          ),
        ));
  }

  Widget _totalMarkWidget(BuildContext context) {
    String studentScore = examSummary.scoredMark.toStringAsFixed(1);
    String totalScore = examSummary.totalMark.toInt().toString();
    print("studentScore => $studentScore");
    return Row(
      children: [
        _imageContainer("mark.png"),
        const SizedBox(width: 15),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              SLStrings.getTranslatedString(KEY_MARK),
              style: LMSFonts.examMarkHeadStyle(),
            ),
            const SizedBox(height: 5),
            Text(
              studentScore + '/' + totalScore,
              style: LMSFonts.examMarkStyle(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _titleWidget(String title) {
    return Text(title, style: LMSFonts.semiBoldFont(16, AppTheme.titleColor));
  }

  Widget _topicTitleWidget(String title) {
    return Align(
        alignment: Alignment.center,
        child:
            Text(title, style: LMSFonts.semiBoldFont(16, AppTheme.titleColor)));
  }

  Widget _markTitleWidget(String title) {
    return Align(
        alignment: Alignment.topLeft,
        child: Text(title,
            style: LMSFonts.semiBoldFont(16, AppTheme.sectionCardBgColor)));
  }

  Widget _dividerWidget(double margin) {
    return const Divider(
      color: AppTheme.dividerColor,
      thickness: 1,
      height: 1,
    );
  }

  Widget _subTitleWidget(String subTitle) {
    return Text(
      subTitle,
      textAlign: TextAlign.center,
      style:
          const TextStyle(fontSize: 14, color: AppTheme.scoreDescriptionColor),
    );
  }

  Widget _markWidget() {
    String attended =
        (examSummary.correctAnswerCount + examSummary.wrongAnswerCount)
            .toString();
    String corrected = examSummary.correctAnswerCount.toString();
    String wrong = examSummary.wrongAnswerCount.toString();
    String skipped = examSummary.skippedAnsCount.toString();
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.stepperColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppTheme.stepperColor,
          width: 2,
        ),
      ),
      child: Column(children: [
        Padding(
            padding: const EdgeInsets.only(top: 10, left: 20),
            child: _markTitleWidget(
              SLStrings.getTranslatedString(KEY_TOTAL_MARK_SCORED),
            )),
        // const SizedBox(height: 5),
        Container(
            margin: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: LmsColors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: LmsColors.white,
              ),
            ),
            child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                child: Column(
                  children: [
                    markItemWidget(
                        SLStrings.getTranslatedString(KEY_ATTENDED),
                        AppTheme.attendedColor,
                        '$ASSETS_PATH/attend.png',
                        attended),
                    _dividerWidget(5.0),
                    markItemWidget(
                        SLStrings.getTranslatedString(KEY_CORRECT),
                        AppTheme.correctAnswerColor,
                        "$ASSETS_PATH/correct.png",
                        corrected),
                    _dividerWidget(5.0),
                    markItemWidget(SLStrings.getTranslatedString(KEY_WRONG),
                        AppTheme.wrongColor, "$ASSETS_PATH/wrong.png", wrong),
                    _dividerWidget(5.0),
                    markItemWidget(
                        SLStrings.getTranslatedString(KEY_SKIPPED),
                        AppTheme.skippColor,
                        "$ASSETS_PATH/skipped.png",
                        skipped),
                  ],
                )))
      ]),
    );
  }

  Widget _divider(Color color) {
    return Container(
      margin: const EdgeInsets.only(
        bottom: 10,
        top: 10,
      ),
      color: color,
      height: 0.8,
    );
  }

  Widget questionCountWidget(String mark) {
    return Text(
      mark,
      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
    );
  }

  Widget markItemWidget(
      String title, Color color, String imageTitle, String count) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15),
      child: Row(
        children: [
          Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: AppTheme.dividerColor,
                width: 2,
              ),
            ),
            padding: const EdgeInsets.all(3),
            child: Image.asset(
              imageTitle,
              fit: BoxFit.fill,
            ),
          ),
          const SizedBox(width: 15),
          Text(title, style: LMSFonts.regularFontWithHeight(16, color, 0)),
          const Spacer(),
          Text(count, style: LMSFonts.regularFontWithHeight(16, color, 0))
        ],
      ),
    );
  }

  Widget questionCountTitle(String title, Color color) {
    return Padding(
        padding: const EdgeInsets.only(top: 5),
        child: Text(title,
            style: LMSFonts.regularFontWithHeight(18, LmsColors.amber, 0)));
  }

  Widget _doughnutGraph(BuildContext context) {
    /* double correctPercentage =
        (examSummary.correctAnswerCount / examSummary.numOfQuestions) * 100;
    double wrongPercentage =
        (examSummary.wrongAnswerCount / examSummary.numOfQuestions) * 100;
    double skippedPercentage =
        (examSummary.skippedAnsCount / examSummary.numOfQuestions) * 100;*/
    return SizedBox(
      height: 200,
      child: MediaQuery.removePadding(
        context: context,
        removeTop: true,
        removeBottom: true,
        child: SfCircularChart(
          selectionGesture: ActivationMode.none,
          onLegendTapped: null,
          legend: Legend(
              isVisible: true,
              position: LegendPosition.right,
              toggleSeriesVisibility: true),
          series: <CircularSeries>[
            DoughnutSeries<ChartData, String>(
              dataSource: <ChartData>[
                ChartData(
                    SLStrings.getTranslatedString(KEY_CORRECT) +
                        ' (${examSummary.correctAnswerCount})',
                    examSummary.correctAnswerCount * 1.0,
                    AppTheme.correctColor),
                ChartData(
                    SLStrings.getTranslatedString(KEY_WRONG) +
                        ' (${examSummary.wrongAnswerCount})',
                    examSummary.wrongAnswerCount * 1.0,
                    AppTheme.wrongColor),
                ChartData(
                    SLStrings.getTranslatedString(KEY_SKIPPED) +
                        ' (${examSummary.skippedAnsCount})',
                    examSummary.skippedAnsCount * 1.0,
                    AppTheme.skippColor),
              ],
              radius: '70%', // Set the outer radius of the ut chart
              innerRadius: '75%',
              xValueMapper: (ChartData data, _) => data.category,
              yValueMapper: (ChartData data, _) => data.value,
              explode: true,
              explodeIndex: 0,
              pointColorMapper: (ChartData data, _) => data.pointColor,
              explodeOffset: '1.7%',
              enableTooltip: false,
              onPointTap: null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _lineGraph(
      BuildContext context,
      ValueNotifier<int> questionNum,
      ValueNotifier<bool> isVisible,
      List<MarkDetails> markDetails,
      ValueNotifier<ZoomPanBehavior> zoomPanBehavior) {
    return Center(
      // child: Container(
      //   color: AppTheme.secondaryBlue,
      //   child: Stack(
      //     children: [
      //       SfCartesianChart(
      //         zoomPanBehavior: zoomPanBehavior.value,
      //         primaryYAxis: NumericAxis(
      //           title: AxisTitle(text: 'Score'),
      //           decimalPlaces: 1,
      //         ),
      //         primaryXAxis: NumericAxis(
      //             title: AxisTitle(text: 'Question No.'),
      //             interval: markDetails.length > 22 ? 10 : 1,
      //             decimalPlaces: 0,
      //             enableAutoIntervalOnZooming: false),
      //         legend: Legend(isVisible: false),
      //         series: <SplineSeries<MarkDetails, double>>[
      //           SplineSeries<MarkDetails, double>(
      //             markerSettings: const MarkerSettings(
      //                 color: LmsColors.white, isVisible: true),
      //             onPointTap: (pointInteractionDetails) {
      //               if (pointInteractionDetails.pointIndex != null) {
      //                 final index = pointInteractionDetails.pointIndex!;
      //                 // mark.value = markDetails[index].scoreRange;
      //                 questionNum.value = markDetails[index].qustionNum;
      //                 isVisible.value = true;

      //                 _showDetailCard(context, isVisible, questionNum.value);
      //               }
      //             },
      //             splineType: SplineType.monotonic,
      //             isVisible: true,
      //             dataSource: markDetails,
      //             xValueMapper: (MarkDetails questions, _) =>
      //                 questions.qustionNum * 1.0,
      //             xAxisName: SLStrings.getTranslatedString(KEY_X_AXIS),
      //             yValueMapper: (MarkDetails questions, _) =>
      //                 questions.scoreRange,
      //             yAxisName: SLStrings.getTranslatedString(KEY_Y_AXIS),
      //           ),
      //         ],
      //       ),
      //       // isVisible.value
      //       //     ? _detailCard(isVisible, questionNum.value)
      //       //     : Container()
      //     ],
      //   ),
      // ),
      child: SfCartesianChart(
        zoomPanBehavior: zoomPanBehavior.value,
        primaryYAxis: NumericAxis(
          title: AxisTitle(text: SLStrings.getTranslatedString(KEY_Y_AXIS)),
          decimalPlaces: 1,
        ),
        primaryXAxis: NumericAxis(
            title: AxisTitle(text: SLStrings.getTranslatedString(KEY_X_AXIS)),
            interval: markDetails.length > 22 ? 10 : 1,
            decimalPlaces: 0,
            enableAutoIntervalOnZooming: false),
        legend: Legend(isVisible: false),
        series: <SplineSeries<MarkDetails, double>>[
          SplineSeries<MarkDetails, double>(
            markerSettings:
                const MarkerSettings(color: LmsColors.white, isVisible: true),
            onPointTap: (pointInteractionDetails) {
              if (pointInteractionDetails.pointIndex != null) {
                final index = pointInteractionDetails.pointIndex!;
                // mark.value = markDetails[index].scoreRange;
                questionNum.value = markDetails[index].qustionNum;
                isVisible.value = true;

                _showDetailCard(context, isVisible, questionNum.value);
              }
            },
            splineType: SplineType.monotonic,
            isVisible: true,
            dataSource: markDetails,
            xValueMapper: (MarkDetails questions, _) =>
                questions.qustionNum * 1.0,
            xAxisName: SLStrings.getTranslatedString(KEY_X_AXIS),
            yValueMapper: (MarkDetails questions, _) => questions.scoreRange,
            yAxisName: SLStrings.getTranslatedString(KEY_Y_AXIS),
          ),
        ],
      ),
    );
  }

  _showDetailCard(BuildContext context, ValueNotifier<bool> isVisible,
      int questionNumIndex) {
    Question poppedQuestion = examSummary.questions
        .where((element) => element.id == questionNumIndex)
        .toList()
        .first;
    double okWidth = kIsWeb ? 100 : MediaQuery.sizeOf(context).width / 2.5;
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return LayoutBuilder(builder: (context, constraints) {
            double webWidth = 400;
            // constraints.maxWidth / 5;
            double mobileWidth = constraints.maxWidth;
            double allowedWidth = constraints.maxWidth > 1200
                ? webWidth
                : constraints.maxWidth >= 500
                    ? webWidth
                    : mobileWidth;
            return Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: allowedWidth,
                  constraints:
                      BoxConstraints(maxHeight: constraints.maxHeight / 1.2),
                  padding: const EdgeInsets.all(25),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Card(
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                    elevation: 0,
                    color: AppTheme.whiteColor,
                    child: Padding(
                      padding: const EdgeInsets.all(23.0),
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              SLStrings.getTranslatedString(KEY_X_AXIS) +
                                  ' ' +
                                  questionNumIndex.toString(),
                              style: LMSFonts.semiBoldFont(
                                  16, AppTheme.popupQstnNum),
                            ),
                            const SizedBox(height: 14),
                            _examInfoQstn(poppedQuestion),
                            const SizedBox(height: 9),
                            _examInfoAnswerStatus(poppedQuestion),
                            const SizedBox(height: 14),
                            const Divider(
                              height: 1,
                              thickness: 1,
                              color: AppTheme.dividerColorInReview,
                              indent: 5,
                              endIndent: 5,
                            ),
                            const SizedBox(height: 14),
                            _examInfoStudentAnswerField(poppedQuestion),
                            _studentANswerValue(poppedQuestion),
                            _examInfoCorrectAnswerField(poppedQuestion),
                            _correctANswerValue(poppedQuestion),
                            const SizedBox(height: 10),
                            Align(
                              child: ButtonWidget(
                                  elevation: 1.0,
                                  minimumSize: Size(okWidth, 35),
                                  shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(20))),
                                  color: AppTheme.okBtnGreen,
                                  child: Text(
                                    SLStrings.getTranslatedString(KEY_OK),
                                    style: LMSFonts.mediumFont(
                                        16, AppTheme.whiteColor, 0),
                                    textAlign: TextAlign.center,
                                  ),
                                  onPressed: () {
                                    isVisible.value = false;
                                    Navigator.pop(context);
                                  }),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          });
        });
  }

  Widget _examInfoQstn(Question poppedQuestion) {
    bool isHTML = poppedQuestion.questionType == QuestAnswerType.html.value;
    String qstn = poppedQuestion.questionText ?? '';
    qstn = qstn.replaceFirst('${poppedQuestion.id}. ', '');
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          poppedQuestion.id.toString() + '. ',
          style: LMSFonts.regularFont(14, AppTheme.questionAnsText),
        ),
        isHTML
            ? Expanded(
                child: HTMLContent(
                  id: poppedQuestion.questionId ?? '',
                  text: qstn,
                  enableRippleEffect: false,
                  contentColor: AppTheme.questionAnsText,
                  backgroundColor: const Color.fromARGB(0, 0, 0, 0),
                  borderColor: Colors.transparent,
                  onSelectionAction: () {},
                ),
              )
            : Expanded(
                child: Text(
                  qstn,
                  style: LMSFonts.regularFont(14, AppTheme.questionAnsText),
                ),
              ),
      ],
    );
  }

  Widget _examInfoAnswerStatus(Question poppedQuestion) {
    bool isSkipped = poppedQuestion.selectedAnswerIds.isEmpty;
    bool isCorrectAnswer = poppedQuestion.didAnswerCorrectly;

    String imageName = isCorrectAnswer ? 'right' : 'wrong';
    return Row(
      children: [
        const SizedBox(width: 14),
        isSkipped
            ? Container()
            : Image.asset('$ASSETS_PATH/$imageName.png', height: 20, width: 20),
        SizedBox(width: isSkipped ? 2 : 5),
        Text(
          isCorrectAnswer
              ? SLStrings.getTranslatedString(KEY_CORRECT_QUESTION_STATUS)
              : isSkipped
                  ? SLStrings.getTranslatedString(KEY_SKIPPED_QUESTION_STATUS)
                  : SLStrings.getTranslatedString(KEY_WRONG_QUESTION_STATUS),
          style: LMSFonts.mediumFont(
              14,
              isCorrectAnswer
                  ? AppTheme.correctColor
                  : isSkipped
                      ? AppTheme.skippColor
                      : AppTheme.wrongColor,
              0),
        ),
      ],
    );
  }

  Widget _examInfoStudentAnswerField(Question poppedQuestion) {
    bool isSkipped = poppedQuestion.selectedAnswerIds.isEmpty;
    return Visibility(
        visible: !isSkipped,
        child: _answerType(SLStrings.getTranslatedString(KEY_YOUR_ANSWER)));
  }

  Widget _examInfoCorrectAnswerField(Question poppedQuestion) {
    bool isCorrectAnswer = poppedQuestion.didAnswerCorrectly;

    return Visibility(
        visible: !isCorrectAnswer,
        child: _answerType(SLStrings.getTranslatedString(KEY_CORRECT_ANSWER)));
  }

  Widget _answerType(String type) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        type,
        style: LMSFonts.mediumFont(14, AppTheme.popupYourAnswr, 0),
      ),
    );
  }

  Widget _studentANswerValue(Question poppedQuestion) {
    bool isSkipped = poppedQuestion.selectedAnswerIds.isEmpty;
    bool isWrong = !poppedQuestion.didAnswerCorrectly;
    bool isHTML = poppedQuestion.questionType == QuestAnswerType.html.value;
    return Visibility(
        visible: !isSkipped,
        child: Container(
            margin: const EdgeInsets.only(bottom: 15),
            child: _answerValue(poppedQuestion.chosenAnswer,
                AppTheme.questionAnsText, isHTML)));
  }

  Widget _correctANswerValue(Question poppedQuestion) {
    bool isCorrectAnswer = poppedQuestion.didAnswerCorrectly;
    bool isHTML = poppedQuestion.questionType == QuestAnswerType.html.value;
    return Visibility(
        visible: !isCorrectAnswer,
        child: Container(
          child: _answerValue(
              poppedQuestion.correctAnswer, AppTheme.questionAnsText, isHTML),
        ));
  }

  Widget _answerValue(String answer, Color textColor, bool isHTML) {
    return Container(
      padding: const EdgeInsets.only(top: 5),
      child: Align(
        alignment: Alignment.centerLeft,
        child: isHTML
            ? HTMLContentTexView(
                id: answer,
                text: answer,
                enableRippleEffect: false,
                contentColor: textColor,
                backgroundColor: Colors.transparent,
                borderColor: Colors.transparent,
                onSelectionAction: () {})
            : Text(
                answer,
                style: LMSFonts.regularFont(14, AppTheme.questionAnsText),
              ),
      ),
    );
  }
}

class ChartData {
  final String category;
  final double value;
  final Color pointColor;

  ChartData(this.category, this.value, this.pointColor);
}

class MarkDetails {
  MarkDetails({required this.qustionNum, required this.scoreRange});

  final int qustionNum;
  final double scoreRange;
}

class ExamFinishButton extends StatelessWidget {
  final BoxConstraints constraints;
  final ExamCubit examCubit;
  const ExamFinishButton(
      {super.key, required this.constraints, required this.examCubit});

  @override
  Widget build(BuildContext context) {
    return examFinishButton(context);
  }

  /// common to exam review and exam result view screens
  Widget examFinishButton(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        width: constraints.maxWidth < 720 ? constraints.maxWidth : 800,
        decoration: const BoxDecoration(
          borderRadius:
              BorderRadiusDirectional.vertical(top: Radius.circular(16)),
          color: Colors.white,
        ),
        padding: const EdgeInsets.only(right: 16, top: 16, bottom: 10),
        height: 60,
        child: Align(
          alignment: Alignment.topRight,
          child: ButtonWidget(
              minimumSize: const Size(10, 10),
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(buttonRadius),
                  side: const BorderSide(
                    color: AppTheme.examInfoTextColor,
                  )),
              color: AppTheme.whiteTextColor,
              child: Text(SLStrings.getTranslatedString(KEY_EXAM_FINISH),
                  style: LMSFonts.semiBoldFont(16, AppTheme.examInfoTextColor)),
              onPressed: () async {
                examCubit.updateNavigationStatustoList(
                    disableBackNavigation: false);

                SharedPreferences _pref = await SharedPreferences.getInstance();
                bool navigationStatus =
                    _pref.getBool(PREF_KEY_FROM_SECTION_DETAILS) ?? false;
                if (navigationStatus) {
                  await _pref.setBool(PREF_KEY_FROM_SECTION_DETAILS, false);
                  kIsWeb
                      ? Beamer.of(context)
                          .beamToReplacementNamed('/section-details')
                      : appRouter.popUntil((route) =>
                          route.settings.name == SectionDetailsViewRoute.name);
                } else {
                  kIsWeb
                      ? Beamer.of(context).beamToReplacementNamed(
                          '/exam-list-tab',
                          data: {'tabIndex': 1})
                      : appRouter.push(ExamListViewRoute(tabIndex: 1));

                  // appRouter.pushAndPopUntil(ExamListViewRoute(tabIndex: 1),
                  //     predicate: (Route<dynamic> route) {
                  //   return route.settings.name == CourseDetailsViewRoute.name;
                  // });
                  // appRouter.popUntil(
                  //     (route) => route.settings.name == ExamListViewRoute.name);
                }
              }),
        ),
      ),
    );
  }
}
