import '../../config/app_config/preference_config.dart';
import '../../utils/constants/locale_change_helper.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';

import '/src/config/enums/exam_status.dart';

import '/src/utils/constants/helper.dart';
import 'package:auto_route/auto_route.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../widgets/app_bar.dart';
import '../widgets/modal_side_sheet.dart';
import '/src/domain/models/course_progress.dart';
import '/src/presentation/cubits/section_details/section_details_cubit.dart';
import 'package:flutter_tex/flutter_tex.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/enums/question_answer_type.dart';
import '../widgets/debounced_button.dart';
import '../widgets/session_expired_widget.dart';

import '../widgets/connectivity_widget.dart';
import '/src/config/themes/lms_fonts.dart';
import 'package:timer_count_down/timer_controller.dart';
import 'package:timer_count_down/timer_count_down.dart';

import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/presentation/cubits/exam_clock/exam_clock_cubit.dart';
import '/src/utils/helper/app_date_formatter.dart';

import '/src/config/themes/theme_colors.dart';
import '/src/presentation/widgets/loading_indicator.dart';

import '/src/config/themes/app_theme.dart';

import '/src/config/enums/question_status.dart';
import '/src/utils/constants/exam_list_json.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';
import '../../domain/models/question_data.dart';
import '../../utils/constants/strings.dart';
import '../cubits/exam/exam_cubit.dart';
import '../widgets/button_widget.dart';

@RoutePage()

// ignore: must_be_immutable
class ExamViewScreen extends HookWidget with WidgetsBindingObserver {
  final int examDuration;
  final bool shouldShowTimer;
  final bool isFromViewResult;
  final String quizAttemptId;
  final QuestionData questionData;
  final bool isFromVideoView;

  ExamViewScreen({
    super.key,
    required this.examDuration,
    required this.shouldShowTimer,
    required this.isFromViewResult,
    required this.quizAttemptId,
    required this.questionData,
    this.isFromVideoView = false,
  });

  late PageController _pageController;

  final CountdownController _examCountdownTimerController =
      CountdownController(autoStart: true);
  final _scrollController = ScrollController();

  late QuestionData _questionData;

  int _currentPageIndex = 0;
  int _selectedRadio = 0;

  bool _showPrevious = false;
  bool _showNextIcon = true;
  bool _isSelected = true;
  bool _enableSubmitButton = true;
  bool _showMoreOptions = false;
  bool _isExamSubmissionInProgress = false;
  bool _didCompleteExam = false;
  bool _isDataLoading = false;

  List<Question> _questions = [];

  /// corresponding answer/option index of the question
  final List<int> _selectedAnswerIndexes = [];

  /// list of all the answers student attended
  final List<Map<String, dynamic>> _answeredQuestionList = [];

  /// json to submit to api
  Map<String, dynamic> _examJson = {};

  BuildContext? currentContext;
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  final ValueNotifier<bool> _examSubmitted = ValueNotifier<bool>(false);

  final double _horizontalMargin = 16.0;
  final double _sideSheetPadding = 20.0;

  /// scroll to the selected question in both page view and top question number view
  void _scrollToIndex(BuildContext context, int index) {
    // final double itemPos = index * 60;
    _pageController.animateToPage(index,
        duration: const Duration(milliseconds: 200),
        curve: Curves.fastEaseInToSlowEaseOut);
    // context.read<ExamCubit>().scrollToIndex(index);
  }

  /// question selection action
  _changeIndex(BuildContext context, int index,
      ValueNotifier<int> selectedQuestionIndex) {
    for (int i = selectedQuestionIndex.value; i <= index; i++) {
      if (_questions[i].selectedAnswerIds.isEmpty &&
          _questions[i].questionsStatus != QuestionStatus.markedForLater &&
          _questions[i].questionsStatus != QuestionStatus.answered) {
        _questions[i].questionsStatus = QuestionStatus.skipped;
      }
    }
    selectedQuestionIndex.value = index;

    context.read<ExamCubit>().changeIndex(index);
  }

  ///
  /// add unanswered question details to the submit json
  /// as empty list of slected answer ids
  ///
  _finalizeSubmitJson() {
    String quizId = _questionData.questions[0].quizId ?? '';
    Map<String, dynamic> _newQuestion = {};
    List<String> _answeredQuestionIds =
        _answeredQuestionList.map((e) => e["question_id"].toString()).toList();

    for (var unAnsweredQstn in _questionData.questions) {
      _answeredQuestionIds.toSet().toList();
      if (_answeredQuestionIds.contains(unAnsweredQstn.questionId)) {
        continue;
      } else {
        if (unAnsweredQstn.questionsStatus != QuestionStatus.answered) {
          _newQuestion = {
            "order_id": unAnsweredQstn.id,
            "question_id": unAnsweredQstn.questionId,
            "question_with_options": unAnsweredQstn.questionText,
            "response_summary": '',
            "selected_answer_ids": unAnsweredQstn.selectedAnswerIds,
          };
          _answeredQuestionList.add(_newQuestion);
          _answeredQuestionIds.add(unAnsweredQstn.questionId ?? '');
        }
      }
    }
    _answeredQuestionList.sort(
      (a, b) {
        int orderID1 = a["order_id"];
        int orderID2 = b["order_id"];
        return orderID1.compareTo(orderID2);
      },
    );
    _examJson = {
      "org_id": ORG_ID,
      "quiz_id": quizId,
      "user_id": USER_ID,
      "submit_datas": _answeredQuestionList,
    };
  }

  /// Submit the selected details to server
  _submitExam(BuildContext parentContext, ExamCubit _examCubit) async {
    var route =
        ModalRoute.of(_scaffoldKey.currentState?.context ?? parentContext);
    _examCountdownTimerController.pause();
    ExamState state = _examCubit.state;

    _finalizeSubmitJson();
    _examCubit.setLoading(true);
    _isDataLoading = true;
    if (isFromVideoView) {
      await _examCubit.submitCheckPointExam(_examJson, _questionData);
    } else {
      await _examCubit.submitExam(_examJson, _questionData);
    }
    state = _examCubit.state;
    _examCubit.setLoading(true);
    if (state is ExamsSubmitted) {
      _didCompleteExam = true;
      _examCubit.enableSubmitBtn(false);
      _examCubit.setLoading(true);
      String quizId = _questionData.questions.first.quizId ?? '';
      await _examCubit.calculateQuizGrades(quizId, quizAttemptId);

      state = _examCubit.state;

      if (state is ExamsQuizGradeState) {
        if (!kIsWeb) {
          if (route != null && route.isActive) {
            await _showExamCompleted(parentContext, _examCubit, true);
          }
        } else {
          await _showExamCompleted(parentContext, _examCubit, true);
        }
      } else {
        _examCubit.setLoading(false);
        _isDataLoading = false;
        if (!kIsWeb) {
          if (route != null && route.isActive) {
            await _showExamCompleted(parentContext, _examCubit, false);
          }
        } else {
          await _showExamCompleted(parentContext, _examCubit, false);
        }
      }
    } else if (state is ExamsQuestionsFailureState) {
      _showMoreOptions = true;
      if (state.error
          .contains(SLStrings.getTranslatedString(KEY_FAILED_HOST_LOOKUP))) {
        _examCubit.setLoading(false);
        _isDataLoading = false;
        _showExamError(parentContext, _examCubit,
            SLStrings.getTranslatedString(KEY_EXAM_NETWORK));
      } else {
        _examCubit.setLoading(false);
        _isDataLoading = false;
        _showExamError(parentContext, _examCubit, state.error);
      }
    } else if (state is CheckPointQuizSubmitted) {
      _didCompleteExam = true;
      _examCubit.setLoading(false);
      _isDataLoading = false;
      await _showExamCompleted(parentContext, _examCubit, false);
    } else {}
  }

  ///
  /// Save selected answer
  /// calls every time when an answer is selected
  ///
  _saveAnswer(Question answeredQuestion, int selectedQuestionIndex,
      ExamCubit examCubit) async {
    examCubit.saveAnswer(_questionData, answeredQuestion, selectedQuestionIndex,
        _answeredQuestionList, _examJson);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // This method is called when the app's lifecycle state changes.
    // You can respond to the different lifecycle states here.
    switch (state) {
      case AppLifecycleState.resumed:
        // The app is in the foreground.

        break;
      case AppLifecycleState.inactive:
        // The app is in an inactive state (e.g., switching apps).
        break;
      case AppLifecycleState.paused:
        if (!_didCompleteExam &&
            !isFromVideoView &&
            !isFromViewResult &&
            !_examSubmitted.value) {
          _examCountdownTimerController.pause();
          // submit exam when app is taken background
          if (currentContext != null && currentContext!.routeData.isActive) {
            final _examCubit = BlocProvider.of<ExamCubit>(currentContext!);
            _didCompleteExam = true;
            if (Navigator.of(currentContext!).canPop()) {
              Navigator.pop(currentContext!);
            }
            _submitCallback(currentContext!, _examCubit, _examCubit.state);
          } // The app is in the background.
        }

        break;
      case AppLifecycleState.detached:
        // The app is detached (not running).
        break;
    }
  }

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    if (isFromViewResult) {
      didChangeAppLocale(locales, currentContext);
    }
  }

  @override
  // ignore: avoid_renaming_method_parameters
  Widget build(BuildContext parentContext) {
    currentContext = parentContext;

    final _examCubit = BlocProvider.of<ExamCubit>(parentContext);
    final _clockCubit = BlocProvider.of<ExamClockCubit>(parentContext);
    ValueNotifier<List<String>> selectedAnswerIds = useState<List<String>>([]);
    ValueNotifier<int> selectedQuestionIndex = useState<int>(0);

    _pageController = usePageController(initialPage: 0);

    String _examStartTimeString =
        AppDateFormatter().formatToDateTimeString(DateTime.now().toLocal());

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      _questionData = questionData;
      _questions = _questionData.questions;
      _clockCubit.updateCurrentTime();
      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
      },
      child: BlocBuilder<ExamCubit, ExamState>(builder: (context, state) {
        if (state is ExamListChangeIndex) {
          _currentPageIndex = state.changedIndex;
          _isSelected = true;

          if (state.changedIndex != 0) {
            _showPrevious = true;
          } else {
            _showPrevious = false;
          }

          if (_questions.length > 1 &&
              state.changedIndex < _questions.length - 1) {
            _showNextIcon = true;
          } else {
            _showNextIcon = false;
          }
        } else if (state is ExamListChangeRadioButton) {
          _selectedRadio = state.RadioValue;
        } else if (state is ExamSubmitBtnState) {
          _enableSubmitButton = state.enableSubmitBtn;
        } else if (state is ExamSpeedDialState) {
          _showMoreOptions = state.enableSpeedDial;
        } else if (state is ExamSubmissionStatus) {
          _isExamSubmissionInProgress = state.status;
        }
        if (state is ExamSaveAnswer) {
          _questionData = state.questionData;
          _examJson = state.examJsonBody;
        }
        if (state is! ExamSpeedDialState) {
          _showMoreOptions = false;
        }

        return Scaffold(
          key: _scaffoldKey,
          backgroundColor: AppTheme.bgColor,
          resizeToAvoidBottomInset: false,
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(70),
              child: AppBarWidget(
                title: SLStrings.getTranslatedString(KEY_EXAM_APPBAR_TITLE),
                trailingWidget: Container(),
                leadingIconName: '',
                isFromLogin: true,
                leadingBtnAction: () {},
              )),
          body: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              child: GestureDetector(
                onTap: () {
                  context.read<ExamCubit>().setMoreOptionsVisibility(false);
                },
                child: Stack(
                  children: [
                    Align(
                      child: SizedBox(
                        width: constraints.maxWidth < 720
                            ? constraints.maxWidth
                            : 800,
                        child: Column(
                          children: [
                            const SizedBox(height: 23),
                            _topContainer(
                                context,
                                _examCubit,
                                _examStartTimeString,
                                selectedQuestionIndex,
                                constraints),
                            SizedBox(height: isFromViewResult ? 0 : 20),
                            isFromViewResult
                                ? Container()
                                : Divider(
                                    height: 1,
                                    indent: _horizontalMargin,
                                    endIndent: _horizontalMargin),
                            SizedBox(height: isFromViewResult ? 10 : 20),
                            ScrollConfiguration(
                              behavior:
                                  ScrollConfiguration.of(context).copyWith(
                                dragDevices: {
                                  PointerDeviceKind.touch,
                                  PointerDeviceKind.mouse,
                                },
                              ),
                              child: _pageViewWidget(context, selectedAnswerIds,
                                  selectedQuestionIndex, _examCubit),
                            ),
                          ],
                        ),
                      ),
                    ),
                    _optionsDropDownWidget(context, _examCubit,
                        selectedQuestionIndex, constraints),
                    ConnectivityStatusWidget(),
                    _isExamSubmissionInProgress
                        ? const ExamUploadingStatusWidget()
                        : Container(),
                    //    state is ExamsLoading && state.loadingStatus
                    _isDataLoading ? LoadingIndicatorClass() : Container(),
                  ],
                ),
              ),
            );
          }),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: _bottomBtnContainer(parentContext, _examCubit),
        );
      }),
    );
  }

  /// exam submission callback
  _submitCallback(
      BuildContext parentContext, ExamCubit _examCubit, ExamState state) async {
    if (!isFromViewResult) {
      _examSubmitted.value = true;
      await _examCubit.updateExamSubmissionStatus(true);

      await _submitExam(parentContext, _examCubit);
      await _examCubit.updateExamSubmissionStatus(false);
    }
  }

  Widget _topContainer(
      BuildContext parentContext,
      ExamCubit _examCubit,
      String _examStartTimeString,
      ValueNotifier<int> selectedQuestionIndex,
      BoxConstraints constraints) {
    return Visibility(
        visible: !isFromViewResult,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: _horizontalMargin),
          child: Row(
            children: [
              _timerWidget(parentContext, _examCubit),
              const Spacer(),
              _currentDateTimeWidget(_examStartTimeString),
              const Spacer(),
              _moreOptionsWidget(_examCubit),
              const SizedBox(width: 15),
              _listAllQstnsWidget(
                  parentContext, selectedQuestionIndex, constraints),
            ],
          ),
        ));
  }

  Widget _timerWidget(BuildContext parentContext, ExamCubit _examCubit) {
    return Visibility(
      visible: shouldShowTimer,
      child: BlocBuilder<ExamClockCubit, ExamClockState>(
        builder: (context, state) {
          return Countdown(
            controller: _examCountdownTimerController,
            seconds: examDuration * 60,
            build: (_, double time) {
              Duration hours = Duration(seconds: time.toInt());
              String countDownValue = hours.toString();
              countDownValue = countDownValue.split('.').first;
              return Text(
                countDownValue,
                style: LMSFonts.semiBoldFont(18, AppTheme.examInfoTextColor),
              );
            },
            onFinished: () async {
              await _showTimerCompletedPopup(
                  parentContext, _examCubit, _examCubit.state);
            },
          );
        },
      ),
    );
  }

  Widget _currentDateTimeWidget(String _examStartTimeString) {
    return BlocBuilder<ExamClockCubit, ExamClockState>(
      builder: (context, state) {
        _examStartTimeString = state.currentTime;
        return Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0)),
            child: Text(
              _examStartTimeString,
              style: LMSFonts.regularFontWithHeight(
                  14, AppTheme.examDateTimeText, 0),
            ));
      },
    );
  }

  Widget _moreOptionsWidget(ExamCubit _examCubit) {
    return GestureDetector(
      onTap: () => {_examCubit.setMoreOptionsVisibility(!_showMoreOptions)},
      child: Container(
        color: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.only(bottom: 3),
          child: Image.asset('$ASSETS_PATH/exam_more_options.png',
              height: 35, width: 35),
        ),
      ),
    );
  }

  Widget _optionsDropDownWidget(BuildContext context, ExamCubit _examCubit,
      ValueNotifier<int> selectedQuestionIndex, BoxConstraints constraints) {
    double startFrom =
        (constraints.maxWidth < 720 ? constraints.maxWidth : 800);
    return Visibility(
      visible: _showMoreOptions,
      child: Positioned(
        top: 55,
        right: ((constraints.maxWidth - startFrom) / 2) + 45,
        // width: startFrom,
        child: Align(
          child: SizedBox(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  margin: const EdgeInsets.only(right: 29),
                  child: Image.asset('$ASSETS_PATH/traingle.png', width: 9),
                ),
                Container(
                    decoration: BoxDecoration(
                      color: AppTheme.primaryAppColor,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 13, vertical: 10),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // _optionsText(context, EXAM_REPORT_QUESTION,
                        //     'report_qstns', _examCubit, selectedQuestionIndex),
                        // const Divider(
                        //   height: 1,
                        //   thickness: 5.0,
                        //   color: AppTheme.whiteColor,
                        // ),
                        _optionsText(
                            context,
                            SLStrings.getTranslatedString(KEY_EXAM_FLAG_REVIEW),
                            'flag_for_review',
                            _examCubit,
                            selectedQuestionIndex),
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _optionsText(BuildContext context, String title, String image,
      ExamCubit _examCubit, ValueNotifier<int> selectedQuestionIndex) {
    return GestureDetector(
      onTap: () {
        if (title == SLStrings.getTranslatedString(KEY_EXAM_FLAG_REVIEW)) {
          _flaggedForReviewAction(selectedQuestionIndex);
        } else {
          // TO DO: Future implementation
          // _showReportQuestionDialogue(context);
        }
        _examCubit.setMoreOptionsVisibility(!_showMoreOptions);
      },
      child: Container(
        padding: const EdgeInsets.only(top: 10, bottom: 10),
        child: Row(
          children: [
            Image.asset(
              '$ASSETS_PATH/$image.png',
              width: 24,
              height: 24,
            ),
            const SizedBox(width: 5),
            Text(
              title,
              style: LMSFonts.semiBoldFont(14, AppTheme.whiteTextColor),
            ),
          ],
        ),
      ),
    );
  }

  Widget _listAllQstnsWidget(BuildContext context,
      ValueNotifier<int> selectedQuestionIndex, BoxConstraints constraints) {
    return GestureDetector(
      onTap: () {
        context.read<ExamCubit>().setMoreOptionsVisibility(false);
        modalSideSheet(
          context,
          header: '',
          addCloseIconButton: false,
          barrierDismissible: true,
          body: _examProgressDetailWidget(selectedQuestionIndex, context),
          addActions: false,
          addDivider: false,
          confirmActionTitle: '',
          cancelActionTitle: '',
          confirmActionOnPressed: () {},

          // If null, Navigator.pop(context) will used
          cancelActionOnPressed: () {},
        );
      },
      child: Container(
        color: Colors.transparent,
        child: Image.asset('$ASSETS_PATH/exam_list_menu.png',
            height: 30, width: 30),
      ),
    );
  }

  Widget _pageViewWidget(
      BuildContext context,
      ValueNotifier<List<String>> selectedAnswerIds,
      ValueNotifier<int> selectedQuestionIndex,
      ExamCubit examCubit) {
    return Flexible(
      child: PageView.builder(
        onPageChanged: (int index) {
          // Scroll to the corresponding question in the top view
          //  _scrollToSelectedQuestionNum(index);
          _changeIndex(context, index, selectedQuestionIndex);
        },
        controller: _pageController,
        itemCount: _questions.length,
        itemBuilder: (BuildContext context, int questionIndex) {
          return _questionCard(
              _questions[questionIndex].questionText ?? '',
              _questions[questionIndex],
              context,
              selectedAnswerIds,
              questionIndex,
              examCubit,
              _pageController,
              selectedQuestionIndex);
        },
      ),
    );
  }

  Widget _questionCard(
      String question,
      Question quest,
      BuildContext context,
      ValueNotifier<List<String>> selectedAnswerIds,
      int questionIndex,
      ExamCubit examCubit,
      PageController _pageController,
      ValueNotifier<int> selectedQuestionIndex) {
    return SingleChildScrollView(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _examQstnWidget(quest, question),
        const SizedBox(height: 5),
        _examMarksRow(quest),
        const SizedBox(height: 5),
        _answerWidget(examCubit, quest, questionIndex, selectedAnswerIds,
            selectedQuestionIndex),
        _statusIndicatorRow(),
        const SizedBox(height: 100),
      ],
    ));
  }

  Widget _examQstnWidget(Question quest, String question) {
    bool isHTML = (quest.questionType == QuestAnswerType.html.value);
    return Container(
      margin: EdgeInsets.symmetric(horizontal: _horizontalMargin),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            quest.id.toString() + '. ',
            style: LMSFonts.regularFont(14, AppTheme.examDateTimeText),
          ),
          isHTML
              ? Flexible(
                  child: TeXView(
                    renderingEngine: const TeXViewRenderingEngine.katex(),
                    child: TeXViewDocument(question,
                        style: TeXViewStyle(
                            fontStyle: LMSFonts.questionText(),
                            contentColor: AppTheme.examDateTimeText,
                            padding: const TeXViewPadding.only(top: 2))),
                  ),
                )
              : Flexible(
                  child: Text(
                    question,
                    style: LMSFonts.regularFont(14, AppTheme.examDateTimeText),
                  ),
                ),
        ],
      ),
    );
  }

  Widget _examMarksRow(Question quest) {
    return Visibility(
      visible: !isFromViewResult,
      child: Align(
        alignment: Alignment.topRight,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: _horizontalMargin),
          padding: const EdgeInsets.only(top: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                  EXAM_MARK + ' ' + (quest.defaultMark ?? 0).toStringAsFixed(1),
                  style: LMSFonts.semiBoldFont(16, AppTheme.secondaryAppColor)),
              quest.penalty != 0
                  ? Expanded(
                      child: Container(
                        padding: const EdgeInsets.only(left: 5),
                        child: Text(
                          EXAM_PENALITY +
                              ' ' +
                              (quest.penalty ?? 0).toStringAsFixed(1),
                          textAlign: TextAlign.end,
                          style: LMSFonts.regularFont(
                            16,
                            AppTheme.penaltyText,
                          ),
                        ),
                      ),
                    )
                  : Container()
            ],
          ),
        ),
      ),
    );
  }

  Widget _answerWidget(
      ExamCubit examCubit,
      Question quest,
      int questionIndex,
      ValueNotifier<List<String>> selectedAnswerIds,
      ValueNotifier<int> selectedQuestionIndex) {
    return Column(
        children: List.generate(quest.options.length, (answerItemIndex) {
      bool isSelected = selectedAnswerIds.value
          .contains(quest.options[answerItemIndex].answerId);
      bool isWrongAnswerSelected = (quest.selectedAnswerIds
          .contains(quest.options[answerItemIndex].answerId));
      bool isCorrectAnswer =
          quest.options[answerItemIndex].correctAnswer == true;

      Color backgroundColor = isFromViewResult
          ? isCorrectAnswer
              ? AppTheme.selectedCorrectAnswerColor
              : isWrongAnswerSelected
                  ? AppTheme.selectedWrongAnswerColor
                  : AppTheme.whiteColor
          : isSelected
              ? AppTheme.selectedAnswerColor
              : AppTheme.whiteColor;

      Color borderColor = isFromViewResult
          ? isCorrectAnswer
              ? AppTheme.selectedCorrectAnswerColor
              : isWrongAnswerSelected
                  ? AppTheme.selectedWrongAnswerColor
                  : AppTheme.unAnsweredColor
          : isSelected
              ? AppTheme.selectedCorrectAnswerColor
              : AppTheme.unAnsweredColor;
      Color answerTextColor = isFromViewResult
          ? (isCorrectAnswer || isWrongAnswerSelected)
              ? AppTheme.whiteColor
              : AppTheme.unAnsweredColor
          : isSelected
              ? AppTheme.whiteColor
              : AppTheme.unAnsweredColor;

      return _answerBox(
          _questions[questionIndex].questionText ?? '',
          _questions[questionIndex],
          selectedAnswerIds,
          questionIndex,
          examCubit,
          selectedQuestionIndex,
          answerItemIndex,
          backgroundColor,
          borderColor,
          answerTextColor);
    }));
  }

  Widget _answerBox(
      String question,
      Question quest,
      ValueNotifier<List<String>> selectedAnswerIds,
      int questionIndex,
      ExamCubit examCubit,
      ValueNotifier<int> selectedQuestionIndex,
      int answerItemIndex,
      Color backgroundColor,
      Color borderColor,
      Color answerTextColor) {
    Color answerIconColor = backgroundColor;
    Color answerBorderColor = borderColor;

    return Container(
      margin: EdgeInsets.only(
          bottom: 20, right: _horizontalMargin, left: _horizontalMargin),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.0),
        border: Border.all(color: borderColor),
      ),
      child: ListTile(
          visualDensity: const VisualDensity(vertical: -3),
          shape: RoundedRectangleBorder(
            side: BorderSide(color: borderColor),
            borderRadius: BorderRadius.circular(8.0),
          ),
          tileColor: backgroundColor,
          title: Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              children: [
                _answerNumberWidget(answerItemIndex, answerIconColor,
                    answerBorderColor, answerTextColor),
                const SizedBox(width: 11),
                Expanded(
                    child: (quest.options[answerItemIndex].answerType ==
                            QuestAnswerType.html.value)
                        ? _answerWithHtml(
                            question,
                            quest,
                            selectedAnswerIds,
                            questionIndex,
                            examCubit,
                            selectedQuestionIndex,
                            answerItemIndex,
                            backgroundColor,
                            borderColor)
                        : _answerWithText(quest, answerItemIndex)),
              ],
            ),
          ),
          onTap: () async {
            await _onAnswerSelected(examCubit, quest, answerItemIndex,
                questionIndex, selectedQuestionIndex, selectedAnswerIds);
          }),
    );
  }

  Widget _answerNumberWidget(int id, Color answerIconColor,
      Color answerBorderColor, Color answerTextColor) {
    String alphabet = convertIntToAlphabet(id + 1);
    return Container(
      height: 35,
      width: 35,
      decoration: BoxDecoration(
        color: answerIconColor,
        border: Border.all(color: answerBorderColor),
        borderRadius: BorderRadius.circular(35),
      ),
      child: Align(
        child: Text(
          alphabet,
          textAlign: TextAlign.center,
          style: LMSFonts.semiBoldFont(18, answerTextColor),
        ),
      ),
    );
  }

  Widget _answerWithText(Question quest, int answerItemIndex) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        quest.options[answerItemIndex].answerVal,
        style: LMSFonts.regularFont(14, AppTheme.examDateTimeText),
      ),
    );
  }

  Widget _answerWithHtml(
      String question,
      Question quest,
      ValueNotifier<List<String>> selectedAnswerIds,
      int questionIndex,
      ExamCubit examCubit,
      ValueNotifier<int> selectedQuestionIndex,
      int answerItemIndex,
      Color backgroundColor,
      Color borderColor) {
    return Align(
      alignment: Alignment.centerLeft,
      child: TeXView(
        renderingEngine: const TeXViewRenderingEngine.katex(),
        style: TeXViewStyle(
          fontStyle: LMSFonts.answerText(),
          contentColor: AppTheme.examDateTimeText,
        ),
        child: TeXViewInkWell(
          rippleEffect: false,
          onTap: (_) async => await _onAnswerSelected(
              examCubit,
              quest,
              answerItemIndex,
              questionIndex,
              selectedQuestionIndex,
              selectedAnswerIds),
          child: TeXViewDocument(quest.options[answerItemIndex].answerVal,
              style: TeXViewStyle(
                fontStyle: LMSFonts.answerText(),
                contentColor: AppTheme.examDateTimeText,
              )),
          id: quest.options[answerItemIndex].answerId,
        ),
      ),
    );
  }

  Widget _statusIndicatorRow() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: _horizontalMargin),
      child: Row(
        children: [
          _statusIndicatorWidget(
              'right_aswer',
              isFromViewResult
                  ? SLStrings.getTranslatedString(KEY_CORRECT_ANSWER_EXAM_VIEW)
                  : SLStrings.getTranslatedString(KEY_ANSWERED),
              true),
          SizedBox(width: _horizontalMargin),
          _statusIndicatorWidget(
              'wrong_answer',
              SLStrings.getTranslatedString(KEY_WRONG_ANSWER_EXAM_VIEW),
              isFromViewResult),
        ],
      ),
    );
  }

  Widget _statusIndicatorWidget(String image, String text, bool isVisible) {
    return Visibility(
      visible: isVisible,
      child: Row(
        children: [
          Image.asset(
            '$ASSETS_PATH/$image.png',
            height: 23,
            width: 23,
          ),
          const SizedBox(width: 3),
          Text(
            text,
            style: LMSFonts.regularFont(14, AppTheme.examDateTimeText),
          ),
        ],
      ),
    );
  }

  Widget _examProgressDetailWidget(
      ValueNotifier<int> selectedQuestionIndex, BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          top: 13,
          bottom: _sideSheetPadding,
          left: _sideSheetPadding - 15,
          right: _sideSheetPadding -
              15), // -15 => padding added to the grdi view for scrollbar.
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _examProgressDetailTitle(),
          const SizedBox(height: 25),
          Expanded(
              child: _examProgressDetailGrid(selectedQuestionIndex, context)),
          const SizedBox(height: 10),
          _examProgressDetailQuickInfo()
        ],
      ),
    );
  }

  Widget _examProgressDetailTitle() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Text(
        SLStrings.getTranslatedString(KEY_EXAM_PROGRESS),
        style: LMSFonts.semiBoldFont(16, AppTheme.previousBtnColor),
      ),
    );
  }

  Widget _examProgressDetailGrid(
      ValueNotifier<int> selectedQuestionIndex, BuildContext context) {
    List<Widget> _qstnNumbers = [];
    double qstnItemSize = _questions.length > 150 ? 45 : 40;
    for (var i = 0; i < _questions.length; i++) {
      Widget _qstnItem =
          _questionNumberItem(i, selectedQuestionIndex, qstnItemSize);
      _qstnNumbers.add(_qstnItem);
    }

    return ScrollConfiguration(
      behavior: ScrollConfiguration.of(context).copyWith(dragDevices: {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      }, scrollbars: false),
      child: Scrollbar(
        controller: _scrollController,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Wrap(
              children: _qstnNumbers,
              spacing: 15,
              runSpacing: 15,
            ),
          ),
        ),
      ),
    );
  }

  Widget _questionNumberItem(
      index, ValueNotifier<int> selectedQuestionIndex, double size) {
    return BlocBuilder<ExamCubit, ExamState>(
      builder: (context, state) {
        Question quest = _questions[index];
        bool isCurrentQstnSelected =
            (index == _currentPageIndex && _isSelected);
        bool isAnswered = quest.questionsStatus == QuestionStatus.answered;
        double qstnItemFont = _questions.length > 150 ? 16 : 18;
        return GestureDetector(
          onTap: () {
            _scrollToIndex(context, index);
            _changeIndex(context, index, selectedQuestionIndex);
          },
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: isCurrentQstnSelected
                  ? AppTheme.selectedQstnFillColor
                  : isAnswered
                      ? AppTheme.attendedFillColor
                      : quest.questionsStatus == QuestionStatus.markedForLater
                          ? AppTheme.flaggedForReviewFillColor
                          : _questions[index].questionsStatus ==
                                  QuestionStatus.skipped
                              ? AppTheme.skippedFillColor
                              : AppTheme.transparentColor,
              shape: BoxShape.circle,
              border: Border.all(
                color: isCurrentQstnSelected
                    ? AppTheme.selectedQstnFillColor
                    : isAnswered
                        ? AppTheme.attendedFillColor
                        : _questions[index].questionsStatus ==
                                QuestionStatus.skipped
                            ? AppTheme.skippedFillColor
                            : quest.questionsStatus ==
                                    QuestionStatus.markedForLater
                                ? AppTheme.flaggedForReviewFillColor
                                : AppTheme.questionNumUnanswered,
                width: 2,
              ),
            ),
            child: Center(
              child: Text('${_questions[index].id}',
                  style: LMSFonts.semiBoldFont(
                      qstnItemFont,
                      isCurrentQstnSelected
                          ? AppTheme.questionNumAnswered
                          : (quest.questionsStatus == QuestionStatus.unAnswered)
                              ? AppTheme.questionNumUnanswered
                              : AppTheme.questionNumAnswered)),
            ),
          ),
        );
      },
    );
  }

  Widget _examProgressDetailQuickInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      color: AppTheme.whiteColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(SLStrings.getTranslatedString(KEY_EXAM_QUICK_INFO),
              style: LMSFonts.semiBoldFont(14, AppTheme.previousBtnColor)),
          const SizedBox(height: 15.0),
          _squareIconWithTitle(
              SLStrings.getTranslatedString(KEY_EXAM_SELECTED_QSTN),
              AppTheme.selectedQstnFillColor,
              removeFill: false),
          const SizedBox(height: 16.0),
          _squareIconWithTitle(SLStrings.getTranslatedString(KEY_EXAM_ATTENDED),
              AppTheme.attendedFillColor,
              removeFill: false),
          const SizedBox(height: 16.0),
          _squareIconWithTitle(SLStrings.getTranslatedString(KEY_EXAM_SKIPPED),
              AppTheme.skippedFillColor,
              removeFill: false),
          const SizedBox(height: 16.0),
          _squareIconWithTitle(
              SLStrings.getTranslatedString(KEY_EXAM_FLAG_REVIEW),
              AppTheme.flaggedForReviewFillColor,
              removeFill: false),
          const SizedBox(height: 16.0),
          _squareIconWithTitle(
              SLStrings.getTranslatedString(KEY_EXAM_UNANSWERED),
              AppTheme.unAnsweredColor,
              removeFill: true),
        ],
      ),
    );
  }

  Widget _squareIconWithTitle(String title, Color color,
      {required bool removeFill}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 18,
          height: 18,
          decoration: BoxDecoration(
            color: removeFill ? AppTheme.transparentColor : color,
            shape: BoxShape.circle,
            border: Border.all(
              width: 2,
              color: color,
            ),
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          child: Text(
            title,
            style: LMSFonts.regularFont(14, AppTheme.sectionDetailsTextColor),
          ),
        ),
      ],
    );
  }

  /* TO DO: Remove later
  Widget _gridItem(int questionNumber, BuildContext context,
      ValueNotifier<int> selectedQuestionIndex) {
    int currentSelectedIndex = questionNumber - 1;
    return GestureDetector(
        onTap: () {
          _scrollToIndex(context, currentSelectedIndex);
          context.read<ExamCubit>().changeIndex(currentSelectedIndex);
        },
        child: Container(
          decoration: BoxDecoration(
            color: _questions[currentSelectedIndex].questionsStatus ==
                    QuestionStatus.markedForLater
                ? AppTheme.markedForLaterBg
                : AppTheme.questionNumberBg,
            shape: BoxShape.circle,
            border: Border.all(
              color: (currentSelectedIndex == selectedQuestionIndex.value &&
                      _isSelected)
                  ? AppTheme.markedForLaterBg
                  : AppTheme.primaryColor,
              width: 3,
            ),
          ),
          child: Center(
            child: Text(
              '$questionNumber',
              style: const TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ));
  }


  Widget _floatingActionWidgets(ValueNotifier<int> selectedQuestionIndex) {
    return BlocListener<ExamCubit, ExamState>(
      listener: (context, state) async {
        if (state is ExamListChangeIndex) {
          _currentPageIndex = state.changedIndex;
          _isSelected = true;

          if (state.changedIndex != 0) {
            _showPrevious = true;
          } else {
            _showPrevious = false;
          }

          if (state.changedIndex < _questions.length - 1) {
            _showNextIcon = true;
          } else {
            _showNextIcon = false;
          }
          _enableSpeedDial = true;
        } else if (state is ExamListShowIcon) {
          _showIcon = state.button;
          _enableSpeedDial = true;
        } else if (state is ExamListChangeRadioButton) {
          _enableSpeedDial = true;
          _selectedRadio = state.RadioValue;
        } else if (state is ExamsLoading) {
          _enableSpeedDial = false;
        }
      },
      child: BlocBuilder<ExamCubit, ExamState>(builder: (context, state) {
        return SafeArea(
            child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              _previousBtnWidget(),
              _drawingPadWidget(),
              Expanded(child: Container()),
              !isFromViewResult
                  ? _optionsBtnWidget(context, selectedQuestionIndex)
                  : Container(),
              _nextBtnWidget(),
            ],
          ),
        ));
      }),
    );
  }

  Widget _previousBtnWidget() {
    return Visibility(
      visible: _showPrevious && _enableSpeedDial,
      child: Container(
          width: 40,
          height: 40,
          margin: const EdgeInsets.only(right: 16),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: AppTheme.primaryColor,
              width: 3.0,
            ),
          ),
          child: FloatingActionButton(
            heroTag: 'btn1',
            backgroundColor: Colors.white,
            onPressed: () {
              if (_pageController.page != 0) {
                _pageController.previousPage(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.ease,
                );
              }
            },
            child: const Icon(
              Icons.arrow_back_ios_new_outlined,
              color: AppTheme.iconColor,
            ),
          )),
    );
  }*/

// Future:
  Widget _drawingPadWidget() {
    return Visibility(
      visible: false,
      child: Container(
          width: 45,
          height: 45,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: AppTheme.primaryBlue,
              width: 3.0,
            ),
          ),
          child: FloatingActionButton(
            heroTag: 'draw',
            backgroundColor: AppTheme.primaryBlue,
            elevation: 0,
            onPressed: () => appRouter.push(const DrawingPadViewRoute()),
            child: const Icon(Icons.draw),
          )),
    );
  }

  _onAnswerSelected(
    ExamCubit examCubit,
    Question quest,
    int answerItemIndex,
    int questionIndex,
    ValueNotifier<int> selectedQuestionIndex,
    ValueNotifier<List<String>> selectedAnswerIds,
  ) async {
    {
      if (!isFromViewResult) {
        String tempSelectedAnswerId = quest.options[answerItemIndex].answerId;

        if (selectedAnswerIds.value.contains(tempSelectedAnswerId)) {
          // remove if answer is already selected()- toggle action
          selectedAnswerIds.value.remove(tempSelectedAnswerId);
          _selectedAnswerIndexes.remove(answerItemIndex);
          // ignore: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member
          selectedAnswerIds.notifyListeners();
          quest.selectedAnswerIndexes.remove(answerItemIndex);
          quest.selectedAnswerIds.remove(tempSelectedAnswerId);
        } else {
          // add into list if answer is not selected()toggle action
          selectedAnswerIds.value.add(quest.options[answerItemIndex].answerId);
          // ignore: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member
          selectedAnswerIds.notifyListeners();
          _selectedAnswerIndexes.add(answerItemIndex);
          quest.selectedAnswerIndexes.add(answerItemIndex);
          quest.selectedAnswerIds.add(tempSelectedAnswerId);
        }
        if (quest.selectedAnswerIds.isNotEmpty) {
          quest.questionsStatus = QuestionStatus.answered;
        } else {
          quest.questionsStatus = QuestionStatus.unAnswered;
        }
        examCubit.updateColor(
            quest.options[answerItemIndex].answerVal, _selectedAnswerIndexes);
        _saveAnswer(quest, selectedQuestionIndex.value, examCubit);
      }
    }
  }

  _showExamCompleted(
      BuildContext context, ExamCubit examCubit, bool isGradeCalculated) {
    final _clockCubit = BlocProvider.of<ExamClockCubit>(context);

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context1) {
        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              return;
            }
          },
          child: SingleActionDialogue(
            title: SLStrings.getTranslatedString(KEY_EXAM_COMPLETE_TITLE),
            message: SLStrings.getTranslatedString(KEY_EXAM_COMPLETE_MSG),
            iconWidget:
                Image.asset('assets/images/assignment.png', height: 150),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: true,
            handleOkCallback: () {},
          ),
        );
      },
    ).then((value) async {
      if (questionData.questions[0].quizId != null) {
        _clockCubit.cancelTimer();
        _examCountdownTimerController.pause();
        // disable/enable back navigation
        examCubit.updateNavigationStatustoIntro(disableBackNavigation: false);
        examCubit.updateNavigationStatustoExamView(disableBackNavigation: true);

        await examCubit.checkNetworkAvailability();
        ExamState state = examCubit.state;
        if (state is NetworkStatus && !state.isNetworkAvailable) {
          // in case of internet disconnection, navigate back to exam list screen
          //or videoView screen based on Conditions
          if (isFromVideoView) {
            examCubit.updateNavigationStatustoExamView(
                disableBackNavigation: false);
            kIsWeb
                ? Beamer.of(context).beamToReplacementNamed('/section-details')
                : appRouter.popUntil((route) =>
                    route.settings.name == SectionDetailsViewRoute.name);
          } else {
            examCubit.updateNavigationStatustoExamView(
                disableBackNavigation: false);
            kIsWeb
                ? Beamer.of(context).beamToReplacementNamed('/exam-list-tab')
                : appRouter.push(ExamListViewRoute(tabIndex: 1));
            //  .then((value) => _examSubmitted.value = false)
          }
        } else {
          if (questionData.questions[0].quizId != null) {
            if (isFromVideoView) {
              // examCubit.setLoading(true);
              final examResult = examCubit.checkPointResponseData['result'];
              final examResultType = examResult != null
                  ? examResult.toString().toLowerCase() == 'failed'
                      ? ExamResult.failed
                      : ExamResult.passed
                  : ExamResult.notStarted;
              _showResult(context, examResultType);
            } else {
              kIsWeb
                  ? Beamer.of(context).beamToNamed('/exam-review', data: {
                      'quizId': questionData.questions[0].quizId ?? '',
                      'quizAttemptId': quizAttemptId,
                      'isGradeCalculated': isGradeCalculated
                    })
                  : appRouter.push(ExamReviewViewRoute(
                      quizId: questionData.questions[0].quizId ?? '',
                      quizAttemptId: quizAttemptId,
                      isGradeCalculated: isGradeCalculated));
              // .then((value) => _examSubmitted.value = false);
            }
          } else {
            kIsWeb
                ? Beamer.of(context)
                    .beamToNamed('/exam-list-tab', data: {'tabIndex': 1})
                : appRouter
                    .push(ExamListViewRoute(tabIndex: 1))
                    .then((value) => _examSubmitted.value = false);
          }
        }
      }
    });
  }

  _showResult(BuildContext context, ExamResult examResult) {
    final sectionDetails = BlocProvider.of<SectionDetailsCubit>(context);
    final _examCubit = BlocProvider.of<ExamCubit>(context);

    showDialog(
      context: context,
      builder: (ctx) {
        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              return;
            }
          },
          child: SingleActionDialogue(
            title: SLStrings.getTranslatedString(KEY_EXAM_RESULT),
            message: examResult == ExamResult.failed
                ? SLStrings.getTranslatedString(KEY_FAILED_EXAMINATION)
                : SLStrings.getTranslatedString(KEY_PASSED_EXAMINATION),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: true,
            handleOkCallback: () {},
          ),
        );
      },
    ).then((value) async {
      if (sectionDetails.content != null) {
        _examCubit.setLoading(true);

        _isDataLoading = true;
        // appRouter.popUntil(
        //     (route) => route.settings.name == CourseVedioViewRoute.name);
        // await appRouter.pop();
        Future.delayed(const Duration(milliseconds: 0), () async {
          _examCubit.setLoading(false);
          _isDataLoading = false;
          _examCubit.updateNavigationStatus(false);
          _examCubit.updateNavigationStatustoExamView(
              disableBackNavigation: false);
          kIsWeb
              ? Beamer.of(context).beamToReplacementNamed('/video-view', data: {
                  'courseVideo': sectionDetails.content!,
                  'courseId': COURSE_ID,
                  'checkPoints':
                      sectionDetails.checkPointData?.checkPoints ?? [],
                  'progess': CourseProgress(
                      progress: 0,
                      timeSpent: "time_spent",
                      instanceId: "instance_id",
                      markedAsDone: false),
                  'isFromExamScreen': true,
                  'isExamPassed': examResult == ExamResult.passed,
                })
              : await appRouter.pushAndPopUntil(
                  CourseVideoViewRoute(
                    courseVideo: sectionDetails.content!,
                    checkPoints:
                        sectionDetails.checkPointData?.checkPoints ?? [],
                    progess: CourseProgress(
                        progress: 0,
                        timeSpent: "time_spent",
                        instanceId: "instance_id",
                        markedAsDone: false),
                    isFromExamScreen: true,
                    isExamPassed: examResult == ExamResult.passed,
                  ),
                  predicate: (route) =>
                      route.settings.name == SectionDetailsViewRoute.name);
          // .then((value) => _examSubmitted.value = false);
        });
      } else {
        debugPrint(
            "[_showResult][sectionDetails] => ${sectionDetails.content}");
      }
    });
  }

  _showExamError(BuildContext context, ExamCubit examCubit, String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_WARNING),
          message: error.isNotEmpty
              ? error
              : SLStrings.getTranslatedString(KEY_EXAM_ALREADY_ATTEND),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    ).then((value) async {
      _examSubmitted.value = false;
      kIsWeb ? Navigator.of(context).pop() : appRouter.pop();
    });
  }

  _showTimerCompletedPopup(
      BuildContext parentContext, ExamCubit _examCubit, ExamState state) {
    var route = appRouter.current;

    if (route.name == ExamViewRoute.name || kIsWeb) {
      _examCubit.setMoreOptionsVisibility(false);
      showDialog(
        barrierDismissible: false,
        context: parentContext,
        builder: (context) {
          return SingleActionDialogue(
            title: SLStrings.getTranslatedString(KEY_OOPS_ALERT),
            message: SLStrings.getTranslatedString(KEY_EXAM_TIME_UP),
            iconWidget: Image.asset('assets/images/alarm.png', height: 150),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: kIsWeb ? false : true,
            handleOkCallback: () {
              Navigator.of(context).pop();
            },
          );
        },
      ).then((value) async {
        kIsWeb ? () {} : appRouter.pop();
        await _submitExam(parentContext, _examCubit);
      });
    }
  }

  Widget _bottomBtnContainer(BuildContext parentContext, ExamCubit _examCubit) {
    ExamState state = _examCubit.state;
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      return Align(
        alignment: Alignment.bottomCenter,
        child: SizedBox(
          width: constraints.maxWidth < 720 ? constraints.maxWidth : 800,
          child: Container(
              height: 70,
              padding: EdgeInsets.only(
                  left: _horizontalMargin,
                  right: _horizontalMargin,
                  bottom: _horizontalMargin),
              color: AppTheme.whiteColor,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _previousBtn(),
                  _submitBtn(parentContext, _examCubit),
                  _nextBtn(),
                ],
              )),
        ),
      );
    });
  }

  Widget _previousBtn() {
    return Expanded(
      child: ValueListenableBuilder(
          valueListenable: _examSubmitted,
          builder: (context, isExamSubmitted, _) {
            return Container(
              // decoration: BoxDecoration(
              // color: AppTheme.whiteColor,
              //     borderRadius: BorderRadius.circular(3)),
              child: ButtonWidget(
                child: Text(
                  SLStrings.getTranslatedString(KEY_PREVIOUS),
                  style: LMSFonts.semiBoldFont(15, AppTheme.whiteTextColor),
                ),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(3)),
                textColor: LmsColors.white,
                color: AppTheme.previousBtnColor,
                onPressed: isExamSubmitted ||
                        _isExamSubmissionInProgress ||
                        !_enableSubmitButton
                    ? null
                    : _showPrevious
                        ? () {
                            if (_pageController.page != 0) {
                              _pageController.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.ease,
                              );
                            }
                          }
                        : null,
              ),
            );
          }),
    );
  }

  Widget _nextBtn() {
    return Expanded(
      child: ValueListenableBuilder(
          valueListenable: _examSubmitted,
          builder: (context, isExamSubmitted, _) {
            return Container(
              margin: EdgeInsets.only(left: _horizontalMargin),
              child: ButtonWidget(
                child: Text(
                  SLStrings.getTranslatedString(KEY_NEXT),
                  style: LMSFonts.semiBoldFont(15, AppTheme.whiteTextColor),
                ),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(3)),
                color: AppTheme.nextBtnColor,
                onPressed: isExamSubmitted ||
                        _isExamSubmissionInProgress ||
                        !_enableSubmitButton
                    ? null
                    : _showNextIcon
                        ? () {
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.ease,
                            );
                          }
                        : null,
              ),
            );
          }),
    );
  }

  Widget _submitBtn(BuildContext parentContext, ExamCubit _examCubit) {
    return Expanded(
      child: ValueListenableBuilder(
          valueListenable: _examSubmitted,
          builder: (context, isExamSubmitted, _) {
            return DebouncedButton(
              child: Container(
                  height: kIsWeb ? 40 : 50,
                  margin: EdgeInsets.only(left: _horizontalMargin),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    //  padding: EdgeInsets.symmetric(
                    //       horizontal: horizontalPadding ?? 12.0,
                    //       vertical: verticalPadding ?? 5),

                    borderRadius: BorderRadius.circular(3),
                    color: isExamSubmitted
                        ? AppTheme.continueBtnDisabled
                        : AppTheme.submitBtnColor,
                  ),
                  child: Text(
                    isFromViewResult
                        ? SLStrings.getTranslatedString(KEY_FINISH)
                        : SLStrings.getTranslatedString(KEY_SUBMIT),
                    style: LMSFonts.semiBoldFont(15, AppTheme.whiteTextColor),
                  )),
              borderRadius: 3,
              disableButton: isExamSubmitted ||
                  _isExamSubmissionInProgress ||
                  !_enableSubmitButton,
              onPressed: _isExamSubmissionInProgress
                  ? () {}
                  : _enableSubmitButton
                      ? !isFromViewResult
                          ? () async => await _submitCallback(
                              parentContext, _examCubit, _examCubit.state)
                          : () async {
                              SharedPreferences _pref =
                                  await SharedPreferences.getInstance();
                              bool _navigationStatus = _pref
                                      .getBool(PREF_KEY_FROM_SECTION_DETAILS) ??
                                  false;

                              /// disable navigation
                              _examCubit.updateNavigationStatustoExamView(
                                  disableBackNavigation: true);
                              _examCubit.updateNavigationStatustoResult(
                                  disableBackNavigation: false);

                              if (_navigationStatus) {
                                _pref.setBool(
                                    PREF_KEY_FROM_SECTION_DETAILS, false);
                                kIsWeb
                                    ? Beamer.of(context)
                                        .beamToNamed('/section-details')
                                    : appRouter.popUntil((route) =>
                                        route.settings.name ==
                                        SectionDetailsViewRoute.name);
                              } else {
                                kIsWeb
                                    ? Beamer.of(context).beamToReplacementNamed(
                                        '/exam-list-tab',
                                        data: {
                                            'tabIndex': 1,
                                          })
                                    : appRouter
                                        .push(ExamListViewRoute(tabIndex: 1));

                                // appRouter.popUntil((route) =>
                                //     route.settings.name ==
                                //     ExamListViewRoute.name);
                              }
                            }
                      : () {},
            );
          }),
    );
  }

  _flaggedForReviewAction(ValueNotifier<int> selectedQuestionIndex) {
    if (_questions.isNotEmpty) {
      _questions[selectedQuestionIndex.value].questionsStatus =
          QuestionStatus.markedForLater;
      int answeredQuestionIndex = _answeredQuestionList.indexWhere((element) =>
          element.values
              .contains(_questions[selectedQuestionIndex.value].questionId));

      if (answeredQuestionIndex != -1) {
        _answeredQuestionList.removeAt(answeredQuestionIndex);
      }
    }
  }

  _showReportQuestionDialogue(BuildContext context) {
    List<String> _options = NewExams.reportQuestionsOptions;
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return Material(
          type: MaterialType.transparency,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.0),
                    color: Colors.white),
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    Text(
                      SLStrings.getTranslatedString(KEY_EXAM_REPORT_QUESTION),
                      style: const TextStyle(
                          fontSize: 20, color: Colors.lightBlueAccent),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Align(
                        alignment: Alignment.topLeft,
                        child: Padding(
                            padding: const EdgeInsets.only(left: 5),
                            child: Text(
                              SLStrings.getTranslatedString(
                                  KEY_EXAM_ISSUE_TYPE),
                              style: const TextStyle(fontSize: 18),
                            ))),
                    const SizedBox(height: 16),
                    BlocBuilder<ExamCubit, ExamState>(
                        builder: (context, state) {
                      return _reportOptionsListView(_options);
                    }),
                    _reportMsgTextField(),
                    const SizedBox(height: 16),
                    _reportActions(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _reportOptionsListView(List<String> _options) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: _options.length,
      itemBuilder: (context, index) {
        return RadioListTile(
          title: Text(_options[index]),
          value: index,
          groupValue: _selectedRadio,
          onChanged: (value) {
            if (value != null) {
              context.read<ExamCubit>().selectOption(value);
            }
          },
        );
      },
    );
  }

  Widget _reportMsgTextField() {
    return TextField(
      keyboardType: TextInputType.multiline,
      minLines: 2,
      maxLines: 5,
      decoration: InputDecoration(
        hintText: SLStrings.getTranslatedString(KEY_EXAM_LABEL_TEXT),
        filled: true,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: const BorderSide(
            color: Colors.grey,
          ),
        ),
        border: OutlineInputBorder(
          borderSide: const BorderSide(
            color: Colors.grey,
          ),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _reportActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _sendReportBtn(),
        _cancelReportBtn(),
      ],
    );
  }

  Widget _sendReportBtn() {
    return Container(
        width: 100,
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.black,
        ),
        alignment: Alignment.center,
        child: InkWell(
          onTap: () {
            appRouter.pop();
            _selectedRadio = 0;
          },
          child: Text(
            SLStrings.getTranslatedString(KEY_SEND),
            style: const TextStyle(fontSize: 18, color: Colors.white),
          ),
        ));
  }

  Widget _cancelReportBtn() {
    return Container(
        width: 100,
        height: 40,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.black,
        ),
        alignment: Alignment.center,
        child: InkWell(
          onTap: () {
            appRouter.pop();
            _selectedRadio = 0;
          },
          child: Text(
            SLStrings.getTranslatedString(KEY_CANCEL),
            style: const TextStyle(fontSize: 18, color: Colors.white),
          ),
        ));
  }
}
