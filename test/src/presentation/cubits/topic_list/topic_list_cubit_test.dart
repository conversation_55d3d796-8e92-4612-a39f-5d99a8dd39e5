import 'package:SmartLearn/src/data/repositories/topic_repository_impl.dart';
import 'package:SmartLearn/src/domain/models/topic.dart';

import 'package:SmartLearn/src/presentation/cubits/topic_list/topic_list_cubit.dart';
import 'package:SmartLearn/src/utils/constants/api_response.dart';

import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter_test/flutter_test.dart';

import 'package:shared_preferences/shared_preferences.dart';

class MockTopicListRepository implements TopicRepositoryImpl {
  @override
  Future<List<TopicList>> getTopicList(String orgId) {
    return Future(() => TOPIC_LIST);
  }
}

void main() {
  late TopicRepositoryImpl _topicRepositoryImpl;
  late TopicListCubit _topicListCubit;

  setUp(() {
    SharedPreferences.setMockInitialValues({});
    _topicRepositoryImpl = MockTopicListRepository();
    _topicListCubit = TopicListCubit(_topicRepositoryImpl);
  });

  tearDown(() {
    _topicListCubit.close();
  });

  group('TopicList', () {
    WidgetsFlutterBinding.ensureInitialized();

    blocTest<TopicListCubit, TopicListState>(
        'emit [change colour] when TopicListItemColour event is added',
        build: () => _topicListCubit,
        act: (cubit) => cubit.updateItemColor("yes"),
        expect: () => [const TopicListItemColour(color: true)]);
  });
}
