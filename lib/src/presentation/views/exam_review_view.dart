import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';

import '../../utils/constants/locale_change_helper.dart';
import '/src/config/enums/question_answer_type.dart';
import 'package:flutter_tex/flutter_tex.dart';
import '../../domain/models/question_data.dart';
import '../../utils/constants/nums.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/presentation/widgets/button_widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/models/exam_review.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../cubits/exam/exam_cubit.dart';
import '../widgets/loading_indicator.dart';
import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';

import '../widgets/app_bar.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import 'exam_result_view.dart';
import 'ppt_viewer.dart';

@RoutePage()

// ignore: must_be_immutable
class ExamReviewViewScreen extends HookWidget with WidgetsBindingObserver {
  final String quizId;
  final String quizAttemptId;
  final bool isGradeCalculated;
  final NavigationTo navigateBackTo;

  ExamReviewViewScreen({
    Key? key,
    required this.quizId,
    required this.quizAttemptId,
    required this.isGradeCalculated,
    required this.navigateBackTo,
  }) : super(key: key);

  List<ExamReview> _examReviewData = [];

  DateTime _startTimeDateTime = DateTime.now();
  DateTime _durationDateTime = DateTime.now();

  String? _timeString;
  String formattedTime = '';

  bool loading = false;

  final _horizontalMargin = 16.0;

  BuildContext? currentContext;

  @override
  void didChangeLocales(List<Locale>? locales) async {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('de_DE')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    await didChangeAppLocale(locales, currentContext);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;

    final screenWidth = MediaQuery.of(context).size.width;
    final _examCubit = BlocProvider.of<ExamCubit>(context);

    const _topMargin = 56.0;

    useEffect(() {
      Future<void>.microtask(() async {
        _examCubit.examReviewLoading(true);
        await _examCubit.fetchReviewList(quizId, quizAttemptId);
        _examCubit.examReviewLoading(false);
      });

      WidgetsBinding.instance.addObserver(this);

      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
      },
      child: Scaffold(
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title:
                  SLStrings.getTranslatedString(KEY_EXAM_REVIEW_APPBAR_TITLE),
              isFromLogin: true, // to disable back action
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {},
              trailingWidget: Container(),
            )),
        body: BlocListener<ExamCubit, ExamState>(
          listener: (context, state) async {
            if (state is ExamReviewLoading) {
              loading = state.loadingStatus;
            }
          },
          child: BlocBuilder<ExamCubit, ExamState>(
            builder: (context, state) {
              if (state is ExamReviewFetched) {
                if (state.examReviewData.isNotEmpty) {
                  _examReviewData = state.examReviewData;

                  _startTimeDateTime = _examReviewData[0].startTime != null
                      ? DateTime.parse(_examReviewData[0].startTime!).toLocal()
                      : DateTime.now();

                  _timeString = _examReviewData[0].duration;
                  _durationDateTime = _timeString != null
                      ? DateTime.parse("1970-01-01 " + _timeString!)
                      : DateTime.now();
                }

                int hours = _durationDateTime.hour;
                int minutes = _durationDateTime.minute;
                int seconds = _durationDateTime.second;

                String hoursString = hours > 1
                    ? SLStrings.getTranslatedString(KEY_HOURS)
                    : SLStrings.getTranslatedString(KEY_HOUR);
                String minutesString = minutes > 1
                    ? SLStrings.getTranslatedString(KEY_MINUTES)
                    : SLStrings.getTranslatedString(KEY_MINUTE);
                String secondsString = seconds > 1
                    ? SLStrings.getTranslatedString(KEY_SECONDS)
                    : SLStrings.getTranslatedString(KEY_SECOND);

                formattedTime =
                    '$hours $hoursString $minutes $minutesString $seconds $secondsString';
                if (hours == 0) {
                  if (minutes == 0) {
                    formattedTime = '$seconds $secondsString';
                  } else {
                    formattedTime =
                        '$minutes $minutesString $seconds $secondsString';
                  }
                }
              }
              return LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16)),
                    ),
                    child: Stack(
                      children: [
                        state is ExamReviewLoading && state.loadingStatus
                            ? Positioned.fill(child: LoadingIndicatorClass())
                            : _examReviewData.isNotEmpty
                                ? Align(
                                    child: Container(
                                      width: constraints.maxWidth < 720
                                          ? constraints.maxWidth
                                          : 800,
                                      margin: EdgeInsets.only(
                                          left: _horizontalMargin,
                                          right: _horizontalMargin,
                                          top: _topMargin),
                                      child: ScrollConfiguration(
                                        behavior:
                                            ScrollConfiguration.of(context)
                                                .copyWith(
                                          dragDevices: {
                                            PointerDeviceKind.touch,
                                            PointerDeviceKind.mouse,
                                          },
                                        ),
                                        child: Column(
                                          children: [
                                            Expanded(
                                              child: ListView(
                                                // crossAxisAlignment:
                                                //     CrossAxisAlignment.start,
                                                children: [
                                                  _pageHeading(),
                                                  _rowWidget(
                                                      AppDateFormatter()
                                                          .formatToDateTimeString(
                                                              _startTimeDateTime),
                                                      SLStrings.getTranslatedString(
                                                          KEY_EXAM_ATTENDED_ON),
                                                      'review_calendar'),
                                                  const SizedBox(height: 16),
                                                  _rowWidget(
                                                      formattedTime,
                                                      SLStrings
                                                          .getTranslatedString(
                                                              KEY_DURATION),
                                                      'review_clock'),
                                                  const SizedBox(height: 15),
                                                  _listViewWidget(constraints),
                                                ],
                                              ),
                                            ),
                                            _buttonWidget(
                                                context, 800, _examCubit),
                                          ],
                                        ),
                                      ),
                                    ),
                                  )
                                : Container(
                                     child: Center(
                                  child: Text(
                                    SLStrings.getTranslatedString(
                                        KEY_DIALOGUE_CONTENT_REVIEW_FAILURE_MESSAGE),
                                    style: LMSFonts.regularFont(16, AppTheme.examTitleColorInReview),
                                  ),
                                ),
                                ),
                        state is ExamReviewLoading && state.loadingStatus
                            ? Container()
                            : ExamFinishButton(
                                constraints: constraints,
                                examCubit: _examCubit,
                              ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _pageHeading() {
    return Align(
      child: Column(children: [
        _titleWidget(_examReviewData[0].quizName ?? '', 18,
            AppTheme.examTitleColorInReview),
        const SizedBox(height: 10),
        _subTitleWidget(
            _examReviewData[0].courseName ?? '', 16, AppTheme.courseNameColor),
        const SizedBox(height: 20),
      ]),
    );
  }

  Widget _rowWidget(String title, String content, String image) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.durationBoxBgColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        children: [
          _imageWidget(image),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _subTitleWidget(content, 14, AppTheme.whiteColor),
              _titleWidget(title, 16, AppTheme.whiteColor),
            ],
          )
        ],
      ),
    );
  }

  Widget _titleWidget(String title, double size, Color textColor) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: LMSFonts.semiBoldFont(size, textColor),
    );
  }

  Widget _subTitleWidget(String title, double size, Color textColor) {
    return Text(
      title,
      style: LMSFonts.regularFontWithHeight(size, textColor, 0),
    );
  }

  Widget _imageWidget(String title) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(width: 2, color: AppTheme.whiteColor),
      ),
      margin: const EdgeInsets.all(15),
      width: 55,
      height: 55,
      child: Padding(
        padding: const EdgeInsets.all(6),
        child: Image.asset(
          '$ASSETS_PATH/$title.png',
        ),
      ),
    );
  }

  Widget _listViewWidget(BoxConstraints constraints) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          SLStrings.getTranslatedString(KEY_QUESTION_ANSWER),
          style: LMSFonts.semiBoldFont(16, AppTheme.examTitleColorInReview),
        ),
        const SizedBox(height: 15),
        if (_examReviewData.isNotEmpty)
          ..._examReviewData.map(
            (item) => _listItemWIdget(item, constraints),
          ),
      ],
    );
  }

  Widget _listItemWIdget(ExamReview item, BoxConstraints constraints) {
    int index = _examReviewData.indexOf(item);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        questionWidget(item.questionWithOptions ?? '', index, constraints,
            item.questionType == QuestAnswerType.html.value),
        const SizedBox(height: 5),
        answerWidget(index, item.answerType == QuestAnswerType.html.value),
        const SizedBox(height: 10),
        index != _examReviewData.length - 1 ? _dividerWidget() : Container(),
        const SizedBox(height: 12),
      ],
    );
  }

  Widget _dividerWidget() {
    return Container(
      height: 0.8,
      color: AppTheme.dividerColorInReview,
    );
  }

  Widget questionWidget(
      String title, int index, BoxConstraints constraints, bool isHtml) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: Alignment.topLeft,
          child: Text(
            (index + 1).toString() + '. ',
            style: LMSFonts.regularFontWithHeight(
              14,
              AppTheme.examDateTimeText,
              isHtml ? 1.3 : 1.2,
            ),
          ),
        ),
        Flexible(
          child: Align(
            alignment: Alignment.topLeft,
            child: isHtml
                ? TeXView(
                    renderingEngine: const TeXViewRenderingEngine.katex(),
                    child: TeXViewDocument(
                      title,
                      style: LMSFonts.questionAnswerTextInReview(isQstn: true),
                    ),
                  )
                : Text(title,
                    style: LMSFonts.regularFontWithHeight(
                        14, AppTheme.examTitleColorInReview, 1.2)),
          ),
        )
      ],
    );
  }

  Widget answerWidget(int index, bool isHtml) {
    bool _isValidSummary = _examReviewData[index].responseSummary != null &&
        _examReviewData[index].responseSummary!.isNotEmpty;
    String _summaryVal =
        _isValidSummary ? _examReviewData[index].responseSummary! : 'N/A';
    String ANSWER_OPTED = SLStrings.getTranslatedString(KEY_ANSWER_OPTED);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _summaryVal == 'N/A'
            ? RichText(
                text: TextSpan(children: <TextSpan>[
                TextSpan(
                    text: ANSWER_OPTED,
                    style: LMSFonts.regularFont(14, AppTheme.answerOptedColor)),
                TextSpan(
                    text: ' ' + _summaryVal,
                    style: LMSFonts.regularFont(
                        14, AppTheme.examTitleColorInReview)),
              ]))
            : Text(ANSWER_OPTED,
                style: LMSFonts.regularFont(14, AppTheme.answerOptedColor)),
        _isValidSummary && _summaryVal != 'N/A'
            ? Padding(
                padding: const EdgeInsets.only(top: 5.0),
                child: isHtml
                    ? TeXView(
                        renderingEngine: const TeXViewRenderingEngine.katex(),
                        child: TeXViewDocument(
                          _summaryVal,
                          style: LMSFonts.questionAnswerTextInReview(
                              isQstn: false),
                        ),
                      )
                    : Text(_summaryVal,
                        style: LMSFonts.regularFontWithHeight(
                            14, AppTheme.examTitleColorInReview, 1.2)),
              )
            : Container()
      ],
    );
  }

  Widget _buttonWidget(
      BuildContext context, double screenWidth, ExamCubit _examCubit) {
    return Align(
        alignment: Alignment.bottomCenter,
        child: Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            child: ButtonWidget(
                child: SizedBox(
                  width: double.infinity,
                  child: Text(
                    SLStrings.getTranslatedString(KEY_VIEW_RESULTS_BTN),
                    style: LMSFonts.buttonStyle(16.0),
                    textAlign: TextAlign.center,
                  ),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(buttonRadius),
                ),
                onPressed: isGradeCalculated == true
                    ? () async {
                        _examCubit.updateNavigationStatustoExamView(
                            disableBackNavigation: false);
                        _examCubit.updateNavigationStatustoReview(
                            disableBackNavigation: true);

                        if (_examCubit.state.examData.isNotEmpty) {
                          QuestionData _questionData =
                              _examCubit.state.examData.first;

                          // _questions = _examCubit.state.examData.isEmpty
                          //     ? []
                          //     : _examCubit.state.examData.first.questions;
                          kIsWeb
                              ? Beamer.of(context)
                                  .beamToNamed('/exam-view', data: {
                                  'examDuration': _questionData.duration,
                                  'shouldShowTimer': true,
                                  'isFromViewResult': true,
                                  'questionData': _questionData,
                                  'quizAttemptId': quizAttemptId
                                })
                              : appRouter.push(
                                  ExamViewRoute(
                                    examDuration: _questionData.duration,
                                    shouldShowTimer: true,
                                    isFromViewResult: true,
                                    questionData: _questionData,
                                    quizAttemptId: quizAttemptId,
                                    navigateBackTo: NavigationTo.exam_list,
                                  ),
                                );
                        }

                        if (_examCubit.examSummaryResult.isNotEmpty) {
                          kIsWeb
                              ? Beamer.of(context)
                                  .beamToNamed('/exam-result', data: {
                                  'examSummary':
                                      _examCubit.examSummaryResult.first,
                                  'tabIndex': _examCubit
                                          .examSummaryResult.first.didPassExam
                                      ? 2
                                      : 1
                                })
                              : appRouter.push(ExamResultViewRoute(
                                  examSummary:
                                      _examCubit.examSummaryResult.first,
                                  tabIndex: _examCubit
                                          .examSummaryResult.first.didPassExam
                                      ? 2
                                      : 1,
                                  navigateBackTo: navigateBackTo));
                        } else {
                          _showAlertDialog(
                              context,
                              SLStrings.getTranslatedString(
                                  KEY_DIALOGUE_CONTENT));
                          kIsWeb
                              ? Beamer.of(context).beamToNamed('/exam-list-tab',
                                  data: {'tabIndex': 1})
                              : appRouter.push(ExamListViewRoute(tabIndex: 1));
                          // appRouter.pushAndPopUntil(
                          //     CourseDetailsViewRoute(
                          //       courseId: COURSE_ID,
                          //       courseName: COURSE_NAME,
                          //     ), predicate: (Route<dynamic> route) {
                          //   return route.settings.name ==
                          //       CourseDetailsViewRoute.name;
                          // });
                        }
                      }
                    : null)));
  }

  void _showAlertDialog(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: msg,
          iconWidget: const Icon(Icons.error, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }
}

class CustomScrollBehaviour extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    return child;
  }
}
