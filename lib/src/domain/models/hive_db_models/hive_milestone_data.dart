import 'package:hive_flutter/hive_flutter.dart';
part 'hive_milestone_data.g.dart';

@HiveType(typeId: 2)
class HiveMileStoneData {
  @HiveField(0)
  final int? id;
  @HiveField(1)
  final String videoId;
  @HiveField(2)
  final String milestoneId;
  @HiveField(3)
  final int sessionStart;
  @HiveField(4)
  final int sessionEnd;
  @HiveField(5)
  final String sessionResponse;
  @HiveField(6)
  final int sessionStatus;

  HiveMileStoneData({
    this.id,
    required this.videoId,
    required this.milestoneId,
    required this.sessionStart,
    required this.sessionEnd,
    required this.sessionResponse,
    required this.sessionStatus,
  });
}
