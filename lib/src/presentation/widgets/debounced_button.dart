import 'dart:async';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class DebouncedButton extends HookWidget {
  final VoidCallback onPressed;
  final Widget child;
  final Duration debounceDuration;
  final double borderRadius;
  final bool disableButton;

  const DebouncedButton({
    Key? key,
    required this.onPressed,
    required this.child,
    required this.disableButton,
    this.borderRadius = 0,
    this.debounceDuration = const Duration(milliseconds: 1500),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ValueNotifier<bool> _isProcessing = useState(false);
    Timer? _debounceTimer;

    void _handleTap() {
      if (_isProcessing.value) return;

      _isProcessing.value = true;
      onPressed();

      _debounceTimer = Timer(debounceDuration, () {
        if (context.mounted) {
          _isProcessing.value = false;
        }
      });
    }

    useEffect(() {
      return () {
        _debounceTimer?.cancel();
      };
    }, []);

    return InkWell(
      onTap: disableButton ? null : _handleTap,
      radius: borderRadius,
      child: child,
    );
  }
}
