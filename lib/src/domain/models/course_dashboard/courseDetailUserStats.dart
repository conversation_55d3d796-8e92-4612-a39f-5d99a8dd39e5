// To parse this JSON data, do
//
//     final courseDetailUserStatiModel = courseDetailUserStatiModelFromJson(jsonString);

import 'dart:convert';

import 'course_assignments.dart';

List<CourseDetailUserStatiModel> courseDetailUserStatiModelFromJson(
        String str) =>
    List<CourseDetailUserStatiModel>.from(
        json.decode(str).map((x) => CourseDetailUserStatiModel.fromJson(x)));

String courseDetailUserStatiModelToJson(
        List<CourseDetailUserStatiModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CourseDetailUserStatiModel {
  double progress;
  String? quizType;
  double percentage;
  String timeSpent;
  double totalMarks;
  String resourceId;
  int achievements;
  String resourceName;
  ResourceType resourceType;

  CourseDetailUserStatiModel({
    required this.progress,
    required this.quizType,
    required this.percentage,
    required this.timeSpent,
    required this.totalMarks,
    required this.resourceId,
    required this.achievements,
    required this.resourceName,
    required this.resourceType,
  });

  factory CourseDetailUserStatiModel.fromJson(Map<String, dynamic> json) =>
      CourseDetailUserStatiModel(
        progress: (json["progress"] ?? 0) * 1.0,
        quizType: json["quiz_type"] ?? "",
        percentage: (json["percentage"] ?? 0) * 1.0,
        timeSpent: json["time_spent"] ?? "",
        totalMarks: (json["totalMarks"] ?? 0) * 1.0,
        resourceId: json["resource_id"] ?? "",
        achievements: json["achievements"] ?? 0,
        resourceName: json["resource_name"] ?? "",
        resourceType: json["resource_type"] != null
            ? resourceTypeValues.fromString(json["resource_type"])!
            : ResourceType.FILE,
      );

  Map<String, dynamic> toJson() => {
        "progress": progress,
        "quiz_type": quizType,
        "percentage": percentage,
        "time_spent": timeSpent,
        "totalMarks": totalMarks,
        "resource_id": resourceId,
        "achievements": achievements,
        "resource_name": resourceName,
        "resource_type": resourceTypeValues.reverse[resourceType],
      };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
