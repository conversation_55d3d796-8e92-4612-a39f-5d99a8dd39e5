import '../models/check_point_data/check_point_data.dart';
import '../models/course_dashboard/courseDetailUserStats.dart';
import '../models/course_dashboard/course_assignments.dart';
import '../models/course_dashboard/course_file_resource.dart';
import '../models/course_dashboard/course_page_resource.dart';
import '../models/course_dashboard/course_video_resource.dart';
import '../models/course_dashboard/examPerformanceCatwise.dart';
import '../models/course_dashboard/userCourseProgress.dart';
import '../models/course_progress.dart';

abstract class CourseDetailsRepository {
  Future<List<CourseDetailUserStatiModel>> fetchCoursewiseStats(
      Map<String, dynamic> jsonReq);
  Future<UserCourseProgress> fetchGraphicalProgress(
      Map<String, dynamic> jsonReq);
  Future<ExamPerformanceCatwise> fetchExamPerformancecatWise(
      Map<String, dynamic> jsonReq);
  Future<List<CourseAssignments>> fetchCourseAssignments(
      Map<String, dynamic> jsonReq);

  Future<List<CourseVideoResource>> fetchCourseVideoResource(
      Map<String, dynamic> jsonReq);

  Future<CheckPointData?> fetchVideoCheckPointInfo(
      Map<String, dynamic> jsonReq);
  Future<List<CourseFileResource>> fetchCourseFileResource(
      Map<String, dynamic> jsonReq);
  Future<CoursePageResource?> fetchCoursePageResource(
      Map<String, dynamic> jsonReq);

  Future<ResourceProgress?> getVideoResProgress(
      Map<String, dynamic> jsonReq); //
  Future<dynamic> setFileProgress(Map<String, dynamic> jsonReq);
}
