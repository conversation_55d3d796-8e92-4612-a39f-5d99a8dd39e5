import '/src/config/app_config/preference_config.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '/src/domain/models/organization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../config/themes/theme_colors.dart';
import '../../utils/constants/nums.dart';
import '../../utils/constants/strings.dart';
import '../cubits/organization/organization_cubit.dart';
import '../cubits/topic_list/topic_list_cubit.dart';
import '../widgets/app_bar.dart';
import '../widgets/button_widget.dart';
import '../widgets/empty_screen_view.dart';
import '../widgets/loading_indicator.dart';

@RoutePage()
// ignore: must_be_immutable
class OrganizationViewScreen extends HookWidget with WidgetsBindingObserver {
  final bool isFromLogin;
  OrganizationViewScreen({Key? key, this.isFromLogin = false})
      : super(key: key);

  String organizationId = '';

  List<Organization> orgList = [];

  BuildContext? currentContext;

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    didChangeAppLocale(locales, currentContext);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final organizationsCubit = BlocProvider.of<OrganizationCubit>(context);
    final topicCubit = BlocProvider.of<TopicListCubit>(context);
    final screenWidth = MediaQuery.of(context).size.width;

    ValueNotifier<String?> selectedOrg = useState<String?>(null);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      Future<void>.microtask(() async {
        organizationsCubit.setLoading(true);
        await organizationsCubit.getUserOrganizations();
        organizationsCubit.setLoading(false);
      });
      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, const []);

    return PopScope(
        canPop: false,
        onPopInvoked: (bool didPop) {
          if (didPop) {
            return;
          }
        },
        child: Scaffold(
          backgroundColor: AppTheme.bgColor,
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(70),
              child: AppBarWidget(
                isFromLogin: !isFromLogin,
                title: SLStrings.getTranslatedString(
                    KEY_ORGANIZATION_APPBAR_TITLE),
                leadingIconName: BACK_ARROW,
                trailingWidget: Container(),
                leadingBtnAction: () async {
                  kIsWeb
                      ? Beamer.of(context).beamToNamed('/profile-view')
                      : await appRouter.push(ProfileViewRoute());
                },
              )),
          body: BlocBuilder<OrganizationCubit, OrganizationState>(
              builder: (context, state) {
            if (state is OrganizationSuccess) {
              orgList = state.organizations;
            }
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              child: SafeArea(
                child: (state is OrganizationLoading)
                    ? LoadingIndicatorClass()
                    : (state is! OrganizationLoading && orgList.isEmpty)
                        ? EmptyScreenView(
                            title: SLStrings.getTranslatedString(
                                KEY_ORGANIZATION_EMPTY))
                        : LayoutBuilder(builder: (context1, constraints) {
                            return Align(
                              child: Container(
                                width: constraints.maxWidth < 720
                                    ? double.infinity
                                    : 800,
                                padding: const EdgeInsets.all(horizontalMargin),
                                child: Column(
                                  children: [
                                    Flexible(
                                      child: ListView.builder(
                                        itemCount: orgList.length,
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          return _orgTile(
                                              context, selectedOrg, index);
                                        },
                                      ),
                                    ),
                                    _continueBtn(
                                        context,
                                        screenWidth,
                                        organizationsCubit,
                                        organizationId,
                                        topicCubit)
                                  ],
                                ),
                              ),
                            );
                          }),
              ),
            );
          }),
        ));
  }

  Widget _orgTile(
      BuildContext context, ValueNotifier<String?> selectedOrg, int index) {
    bool isSelected = selectedOrg.value == orgList[index].orgName!;
    return GestureDetector(
      onTap: () async => _onOrgSelected(context, selectedOrg, index),
      child: Container(
          margin: const EdgeInsets.only(top: 14),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
                width: isSelected ? 2 : 1,
                color: isSelected
                    ? AppTheme.primaryAppColor
                    : AppTheme.examIntroTextColor),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 5),
            child: Row(
              children: [
                Expanded(
                  child: Text(orgList[index].orgName!,
                      style: LMSFonts.regularFont(
                          16.0, AppTheme.examIntroTextColor)),
                ),
                Container(
                  height: 15,
                  width: 15,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryAppColor
                        : AppTheme.transparentColor,
                    borderRadius: BorderRadius.circular(3),
                    border: Border.all(
                        color: isSelected
                            ? AppTheme.primaryAppColor
                            : AppTheme.examIntroTextColor),
                  ),
                  child: Image.asset(
                    '$ASSETS_PATH/tick.png',
                    color: isSelected
                        ? AppTheme.whiteColor
                        : AppTheme.transparentColor,
                  ),
                ),
              ],
            ),
          )),
    );
  }

  _onOrgSelected(BuildContext context, ValueNotifier<String?> selectedOrg,
      int index) async {
    selectedOrg.value = orgList[index].orgName!;
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    organizationId = orgList[index].orgId ?? "";
    _prefs.setString(PREF_KEY_ORG_ID, organizationId);
    ORG_ID = organizationId;
    context
        .read<OrganizationCubit>()
        .handleOrgSelection(orgList[index].orgName);
  }

  Widget _continueBtn(BuildContext context, double screenWidth,
      OrganizationCubit orgCubit, String orgId, TopicListCubit topicCubit) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        child: ButtonWidget(
          child: SizedBox(
            width: double.infinity,
            child: Text(
              SLStrings.getTranslatedString(KEY_CONTINUE),
              style: LMSFonts.buttonStyle(16.0),
              textAlign: TextAlign.center,
            ),
          ),
          textColor: LmsColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
          onPressed: organizationId.isNotEmpty
              ? () async =>
                  await _handleOrgConfirmation(context, orgCubit, topicCubit)
              : null,
        ),
      ),
    );
  }

  _handleOrgConfirmation(BuildContext context, OrganizationCubit orgCubit,
      TopicListCubit topicCubit) async {
    await orgCubit.getPrivilegeAccessList();
    bool _hasAccessToFetchTopic = await _initAccessDetails();
    // if (_hasAccessToFetchTopic) {
    orgCubit.setLoading(true);
    await topicCubit.getUserTopic();
    TopicListState state = topicCubit.state;

    if (state is TopicListSuccess && state.topicList.isNotEmpty) {
      orgCubit.setLoading(false);
      kIsWeb
          ? Beamer.of(context)
              .beamToNamed('/topic-view', data: {'isFromLogin': true})
          : appRouter.push(TopicListViewRoute(isFromLogin: true));
    } else {
      orgCubit.setLoading(false);
      _showTopicEmptyDialogue(context);
    }
    // } else {
    //   // showNoAccessPopup(context, '');
    //   appRouter.push(const NoAccessViewRoute());
    // }
  }

  Future<bool> _initAccessDetails() async {
    // TODO: ACTION_GET_CATEGORY_LIST- corresponds to api 'get_category_hierarchy'. Complete once api is done
    // decides whether to show topic selection screen

    // String screen = PrivilegeAccessConsts.SCREEN_TOPIC;
    // String accessRequiredFeature =
    //     PrivilegeAccessConsts.ACTION_GET_CATEGORY_LIST;
    // return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
    //     accessRequiredFeature: accessRequiredFeature);

    return true;
  }

  // show if the selected organization does not have any topics
  _showTopicEmptyDialogue(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return MultiActionDialogue(
              alertType: AlertType.warning,
              title: SLStrings.getTranslatedString(KEY_WARNING),
              content: SLStrings.getTranslatedString(
                  KEY_EMPTY_TOPIC_FOR_ORGANIZATION),
              cancelBtnText: SLStrings.getTranslatedString(KEY_CLOSE),
              confirmBtnText: '',
              onCancelTap: () {
                kIsWeb ? Navigator.of(context).pop : appRouter.pop();
              },
              onContinueTap: () {});
        });
  }
}
