import 'dart:async';
import 'dart:ui';

import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../utils/helper/privilege_access_mapper.dart';
import '/src/domain/services/network_services.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:carousel_slider/carousel_slider.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';

import '../../utils/constants/helper.dart';
import '../../utils/constants/locale_change_helper.dart';

import '../widgets/exit_on_double_tap.dart';
import '../../domain/models/course_progress.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '../widgets/connectivity_widget.dart';
import '/src/presentation/widgets/empty_screen_view.dart';
import '/src/presentation/widgets/vedio_thumbnail_widget.dart';
import '/src/config/enums/course_grid_item.dart';
import '../../config/themes/app_theme.dart';
import '/src/config/router/app_router.dart';
import '/src/presentation/cubits/course_list/course_list_cubit.dart';
import '/src/config/enums/scroll_direction.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../src/config/themes/lms_fonts.dart';
import '../../../src/config/themes/theme_colors.dart';
import '../../../src/presentation/widgets/app_bar.dart';
import '../../../src/utils/constants/strings.dart';
import '../../../src/utils/helper/app_date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../domain/models/course_details.dart';
import '../cubits/profile/profile_cubit.dart';
import '../widgets/drawer_widget.dart';
import '../widgets/loading_indicator.dart';

@RoutePage()
// ignore: must_be_immutable
class CourseDetailsViewScreen extends HookWidget with WidgetsBindingObserver {
  final Map<String, dynamic> sampleJson = {
    'current_affairs_size': 5,
    'videos_scroll_direction': GridScrollDirection.horizontal,
    'videos_crossaxis_count': 20,
    'subjects_scroll_direction': GridScrollDirection.horizontal,
    'subjects_crossaxis_count': 20,
    'exams_scroll_direction': GridScrollDirection.vertical,
    'exams_crossaxis_count': 20,
  };

  final String courseId;
  final String courseName;
  final bool isFromLogin;

  // CourseDetailsViewScreen(requi this.courseId, this.courseName, {Key? key, this.isFromLogin = false})
  //     // : courseId = COURSE_ID,
  //     //   courseName = COURSE_NAME;

  CourseDetailsViewScreen(
      {Key? key,
      required this.courseId,
      required this.courseName,
      this.isFromLogin = false})
      : super(key: key);

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController _affairsScrollController = ScrollController();
  final CarouselController _controller = CarouselController();

  static const double _affairsHeight = 150;
  static const double _videoItemHeight = 151;
  static const double _subjectItemHeight = 130;
  static const double _horizontalMargin = 16;
  static const double _verticalGapBwSections = 30;
  static const double _verticalSpaceAfterTitle = 15;

  CourseDetails? courseDetails;

  /// set to true when the
  /// horizontal scroll for affairs list reaches the last item.
  /// used to set the visibility of the arrow.
  bool _didReachEndofScroll = false;

  /// decides whether to show the course in sidemenu
  late ValueNotifier<bool> isCourseAccessGiven;
  late ValueNotifier<bool> hasSubscriptionAccess;
  late ValueNotifier<bool> shouldFetchCourseDetails;
  late ValueNotifier<bool> shouldFetchCurrentAffairs;
  late ValueNotifier<bool> shouldFetchSectionDetails;
  late ValueNotifier<bool> shouldFetchNewExams;
  late ValueNotifier<bool> shouldFetchAttemptedExams;

  int _currentCarouselItemIndex = 0;

  BuildContext? currentContext;

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    // TO DO: context should not be equal to null
    didChangeAppLocale(locales, currentContext);
  }

  _reloadData(CourseListCubit _courseCubit, UserCubit _userCubit) async {
    if (await checkNetworkStatus()) {
      _courseCubit.setLoading(true);
      await _userCubit.getUserInfo(USER_ID);
      await _courseCubit.fetchCourseDetails(COURSE_ID);
    }
  }

  _initAccessDetails() async {
    // decides whether to show course in sidemenu
    String screen = PrivilegeAccessConsts.SCREEN_COURSE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_COURSE_LIST;
    isCourseAccessGiven.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show course details
    screen = PrivilegeAccessConsts.SCREEN_COURSE;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_COURSE_DETAILS;
    shouldFetchCourseDetails.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show current affairs
    screen = PrivilegeAccessConsts.SCREEN_CURRENT_AFFAIRS;
    accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_GET_CURRENT_AFFAIR_lIST;
    shouldFetchCurrentAffairs.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show section details
    screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_SECTION_DETAILS;
    shouldFetchSectionDetails.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show new exams
    screen = PrivilegeAccessConsts.SCREEN_EXAM;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_EXAM_LIST;
    shouldFetchNewExams.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show attempted exams
    screen = PrivilegeAccessConsts.SCREEN_EVALUATION;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_ATTENDED_EXAM_LIST;
    shouldFetchAttemptedExams.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show subscription tab
    screen = PrivilegeAccessConsts.SCREEN_SUBSCRIPTION;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_USER_SUBSCRIPTION_LIST;
    bool subAccessCheck1 = await PrivilegeAccessMapper.checkPrivilegeAccessFor(
        screen,
        accessRequiredFeature: accessRequiredFeature);
    hasSubscriptionAccess.value = subAccessCheck1;
  }

  _initCourseDetailsInfo(CourseListCubit _courseCubit) async {
    courseDetails = const CourseDetails(
        courseModule: [], courseVideos: [], currentAffairs: [], exams: []);
    if (shouldFetchCourseDetails.value) {
      _courseCubit.setLoading(true);
      await _courseCubit.fetchCourseDetails(courseId);
    }
    // Future.delayed(const Duration(seconds: 1), () {
    //   _courseCubit.setLoading(false);
    // });
    _affairsScrollController.addListener(() {
      if (_affairsScrollController.offset ==
          _affairsScrollController.position.maxScrollExtent) {
        //reached end of scroll
        _didReachEndofScroll = true;
      } else {
        _didReachEndofScroll = false;
      }

      _courseCubit.removeArrow(!_didReachEndofScroll);
    });
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    isCourseAccessGiven = useState<bool>(false);
    hasSubscriptionAccess = useState<bool>(false);
    shouldFetchCourseDetails = useState<bool>(false);
    shouldFetchCurrentAffairs = useState<bool>(false);
    shouldFetchNewExams = useState<bool>(false);
    shouldFetchAttemptedExams = useState<bool>(false);
    shouldFetchSectionDetails = useState<bool>(false);

    final _courseCubit = BlocProvider.of<CourseListCubit>(context);
    final _userCubit = BlocProvider.of<UserCubit>(context);

    String? firstName = _userCubit.userFirstName ?? "";
    String? lastName = _userCubit.userLastName ?? "";
    String? fullName = firstName + ' ' + lastName;
    String? email = _userCubit.state.profile?.email ?? '';
    String? avatarUrl = _userCubit.state.profile?.avatarUrl ?? '';
    String _courseId = '';
    String _courseName = '';

    Future<void> _fetchRequiredData() async {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      _courseId = _prefs.getString('courseID') ?? '';
      COURSE_ID = _courseId;
      _courseName = _prefs.getString('courseName') ?? '';
      COURSE_NAME = _courseName;
      USER_ID = _prefs.getString("userId") ?? '';
    }

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      Future<void>.microtask(() async {
        await _initAccessDetails();
        await _initCourseDetailsInfo(_courseCubit);
      });

      return () {
        currentContext = null;
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return ExitOnDoubleTap(
      scaffoldKey: _scaffoldKey,
      child: Scaffold(
        drawerEnableOpenDragGesture: false,
        key: _scaffoldKey,
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(85),
            child: AppBarWidget(
              title: COURSE_NAME,
              toolbarHeight: 85,
              isHomeScreen: true,
              // trailingWidget: Image.asset(
              //   '$ASSETS_PATH/notifications.png',
              //   width: 36,
              //   height: 31,
              // ),
              trailingWidget: Container(),
              leadingIconName: APPBAR_ICON,
              leadingBtnAction: () async {
                // final result = await InternetConnectionCheckerPlus().hasConnection;
                final scaffoldState = _scaffoldKey.currentState;
                if (scaffoldState != null) {
                  scaffoldState.openDrawer();
                }
              },
            )),
        drawer: Drawer(
            width: kIsWeb ? 350 : MediaQuery.of(context).size.width * 0.8,
            child: DrawerWidget(
              name: fullName,
              email: email,
              avatarUrl: avatarUrl,
              courseName: courseName,
              hasCourseAccess: isCourseAccessGiven.value,
              hasProfileAccess: true,
              hasSubscriptionAccess: hasSubscriptionAccess.value,
            )),
        body: Container(
          // padding: const EdgeInsets.symmetric(horizontal: 5),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          ),
          child: SafeArea(
            child: BlocBuilder<CourseListCubit, CourseListState>(
              builder: (context, state) {
                CourseDetails _courseDetails = CourseDetails(
                  courseModule: state.courseDetailsData?.courseModule ?? [],
                  courseVideos: state.courseDetailsData?.courseVideos ?? [],
                  currentAffairs: state.courseDetailsData?.currentAffairs ?? [],
                  exams: state.courseDetailsData?.exams ?? [],
                );

                if (state is CourseDetailsFetched) {
                  _courseDetails = CourseDetails(
                    courseModule: state.courseDetailsData?.courseModule ?? [],
                    courseVideos: state.courseDetailsData?.courseVideos ?? [],
                    currentAffairs:
                        state.courseDetailsData?.currentAffairs ?? [],
                    exams: state.courseDetailsData?.exams ?? [],
                  );
                  courseDetails = _courseDetails;
                  _currentCarouselItemIndex = kIsWeb
                      ? (courseDetails?.currentAffairs.length ?? 0) > 1
                          ? 1
                          : 0
                      : 0;
                } else if (state is CarouselChangeIndex) {
                  _currentCarouselItemIndex = state.carouselIndex;
                }
                bool isEmptyData = courseDetails != null
                    ? courseDetails!.courseModule.isEmpty &&
                        courseDetails!.courseVideos.isEmpty &&
                        courseDetails!.currentAffairs.isEmpty &&
                        courseDetails!.exams.isEmpty
                    : _courseDetails.courseModule.isEmpty &&
                        _courseDetails.courseVideos.isEmpty &&
                        _courseDetails.currentAffairs.isEmpty &&
                        _courseDetails.exams.isEmpty;
                return LayoutBuilder(builder:
                    (BuildContext context, BoxConstraints constraints) {
                  return Stack(
                    children: [
                      isEmptyData && state is CourseDetailsEmpty
                          ? EmptyScreenView(
                              title: SLStrings.getTranslatedString(
                                  KEY_COURSE_DETAILS_EMPTY),
                              retryAction: () {
                                _reloadData(_courseCubit, _userCubit);
                              },
                              buttonText:
                                  SLStrings.getTranslatedString(KEY_TRY_AGAIN),
                              showRetryButton: NetworkServices.client?.client
                                              .auth.currentSession ==
                                          null ||
                                      NetworkServices.client?.client.auth
                                              .currentSession?.isExpired ==
                                          true
                                  ? true
                                  : false,
                            )
                          : Align(
                              child: Container(
                                height: constraints.maxHeight,
                                width: constraints.maxWidth < 720
                                    ? double.infinity
                                    : 800,
                                decoration: const BoxDecoration(
                                  // color: Colors.white,
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(16),
                                      topRight: Radius.circular(16)),
                                ),
                                child: ScrollConfiguration(
                                  behavior: ScrollConfiguration.of(context)
                                      .copyWith(
                                          scrollbars: false,
                                          dragDevices: {
                                        PointerDeviceKind.touch,
                                        PointerDeviceKind.mouse,
                                      }),
                                  child: SingleChildScrollView(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(
                                            height: _verticalGapBwSections),

                                        _currentAffairsView(
                                            constraints,
                                            context,
                                            courseDetails ?? _courseDetails,
                                            _courseCubit),
                                        // : Container(),
                                        _videosView(
                                            context,
                                            constraints,
                                            courseDetails ?? _courseDetails,
                                            state,
                                            _courseCubit),
                                        // shouldFetchSectionDetails.value
                                        //     ?
                                        _subjectsView(
                                            context,
                                            constraints,
                                            courseDetails ?? _courseDetails,
                                            state,
                                            _courseCubit),
                                        // : Container(),
                                        shouldFetchNewExams.value ||
                                                shouldFetchAttemptedExams.value
                                            ? _examsView(
                                                context,
                                                constraints,
                                                courseDetails ?? _courseDetails,
                                                state,
                                                _courseCubit)
                                            : Container(),
                                        const SizedBox(height: 5),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                      ConnectivityStatusWidget(),
                      state is CourseDetailsLoading && state.isDataLoading
                          ? Positioned.fill(child: LoadingIndicatorClass())
                          : Container(),
                    ],
                  );
                });
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _titleWidget(BoxConstraints constraints, String title) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: _horizontalMargin),
      child: Text(
        title,
        style: LMSFonts.semiBoldFont(22, AppTheme.headerTextColor),
      ),
    );
  }

  Widget _currentAffairsView(BoxConstraints constraints, BuildContext context,
      CourseDetails courseDetails, CourseListCubit courseCubit) {
    bool showMore = courseDetails.currentAffairs.length >
        sampleJson['current_affairs_size'];
    int listLength = showMore
        ? sampleJson['current_affairs_size']
        : courseDetails.currentAffairs.length;
    return Visibility(
      visible: listLength > 0,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: _titleWidget(constraints,
                    SLStrings.getTranslatedString(KEY_CURRENT_AFFAIRS)),
              ),
              _seeAllWidget(context, courseCubit),
            ],
          ),
          const SizedBox(height: _verticalSpaceAfterTitle),
          _affairsCarouselWidget(
              context, courseCubit, courseDetails.currentAffairs, constraints),
        ],
      ),
    );
  }

  Widget _seeAllWidget(BuildContext context, CourseListCubit courseCubit) {
    return Visibility(
      visible: true, // shouldFetchCurrentAffairs.value,
      child: InkWell(
        onTap: () async {
          courseCubit.setLoading(true);
          await courseCubit.fetchMoreCurrentAffairs();
          CourseListState state = courseCubit.state;
          if (state is CurrentAffairsFetched) {
            kIsWeb
                ? Beamer.of(context).beamToNamed('/news-more',
                    data: {'affairsList': state.currentAffairs})
                : appRouter.push(
                    NewsViewMoreViewRoute(affairsList: state.currentAffairs));
            courseCubit.setLoading(false);
          }
        },
        child: Container(
          margin: const EdgeInsets.only(right: _horizontalMargin),
          color: Colors.white,
          child: Row(
            children: [
              Text(
                SLStrings.getTranslatedString(KEY_SEE_ALL),
                style: LMSFonts.semiBoldFont(14, AppTheme.primaryAppColor),
              ),
              Image.asset(
                '$ASSETS_PATH/see_all_btn.png',
                width: 20,
                height: 10,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _affairsCarouselWidget(
      BuildContext context,
      CourseListCubit courseCubit,
      List<CurrentAffairs?> affairsList,
      BoxConstraints constraints) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: kIsWeb ? 16 : 0),
      child: Column(children: [
        _carouselSliderWidget(context, courseCubit, affairsList, constraints),
        const SizedBox(height: 12),
        _carouselDotsWidget(affairsList, courseCubit),
      ]),
    );
  }

  _carouselSliderWidget(BuildContext context, CourseListCubit courseCubit,
      List<CurrentAffairs?> affairsList, BoxConstraints constraints) {
    double _affairsWidth = MediaQuery.sizeOf(context).width;
    final List<Widget> imageSliders = [];
    for (var i = 0; i < affairsList.length; i++) {
      CurrentAffairs? item = affairsList[i];
      Widget newsWidget = item != null
          ? InkWell(
              onTap: () {
                int index = i;
                if (courseDetails!.currentAffairs[index] != null &&
                    courseDetails!
                        .currentAffairs[index]!.newsContent.isNotEmpty) {
                  kIsWeb
                      ? Beamer.of(context).beamToNamed('/course-news', data: {
                          'currentAffairs':
                              courseDetails!.currentAffairs[index]!
                        })
                      : appRouter.push(CourseNewsViewRoute(
                          currentAffairs: courseDetails!.currentAffairs[index]!,
                        ));
                } else {
                  _showAlertDialog(
                      context,
                      SLStrings.getTranslatedString(
                          KEY_AFFAIRS_DETAILS_NOT_FOUND));
                }
              },
              child: Container(
                height: constraints.maxWidth < 720 ? 150 : 300,
                margin:
                    const EdgeInsets.symmetric(horizontal: _horizontalMargin),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    image: DecorationImage(
                        fit: BoxFit.cover,
                        image: AssetImage(item.backgroundImage))),
                child: Stack(
                  children: <Widget>[
                    _carouselContentWidget(item, i),
                  ],
                ),
              ),
            )
          : Container();

      imageSliders.add(newsWidget);
    }

    return Container(
      color: Colors.transparent,
      child: CarouselSlider(
        items: imageSliders,
        carouselController: _controller,
        options: CarouselOptions(
            initialPage: _currentCarouselItemIndex,
            enlargeCenterPage: kIsWeb ? true : false,
            aspectRatio: constraints.maxWidth < 720
                ? _affairsWidth / _affairsHeight
                : 25 / 4,
            viewportFraction: constraints.maxWidth < 720 ? 1 : 0.6,
            enableInfiniteScroll: false,
            onPageChanged: (index, reason) =>
                courseCubit.changeCarouselIndex(index)),
      ),
    );
  }

  _carouselContentWidget(CurrentAffairs item, int index) {
    return Positioned(
      bottom: 0.0,
      left: 0.0,
      right: 0.0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: const LinearGradient(
            colors: [Color.fromRGBO(0, 0, 0, 1), Color.fromRGBO(0, 0, 0, 0)],
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _affairsTitleWidget(item.newsTitle),
            _dateItemWidget(index),
          ],
        ),
      ),
    );
  }

  _affairsTitleWidget(String newsTitle) {
    return Text(
      newsTitle,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: LMSFonts.mediumFont(16, AppTheme.whiteTextColor, 1.0),
    );
  }

  _dateItemWidget(int index) {
    String date = courseDetails != null &&
            courseDetails!.currentAffairs[index] != null
        ? AppDateFormatter()
            .formatDatedMy(courseDetails!.currentAffairs[index]!.publishedDate)
        : AppDateFormatter().formatDatedMy(DateTime.now());
    return Text(
      date,
      textAlign: TextAlign.center,
      style: LMSFonts.mediumFont(12, AppTheme.whiteTextColor, 1),
    );
  }

  _carouselDotsWidget(
      List<CurrentAffairs?> affairsList, CourseListCubit courseCubit) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: affairsList.asMap().entries.map((entry) {
        return GestureDetector(
          onTap: () => _updateCarousalIndex(entry, courseCubit),
          child: Container(
            width: 6.0,
            height: 6.0,
            margin: const EdgeInsets.symmetric(horizontal: 6.0),
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentCarouselItemIndex == entry.key
                    ? AppTheme.carousalSelectedDotColor
                    : AppTheme.carousalUnSelectedDotColor),
          ),
        );
      }).toList(),
    );
  }

  _updateCarousalIndex(
      MapEntry<int, CurrentAffairs?> entry, CourseListCubit courseCubit) {
    _controller.jumpToPage(entry.key);
    courseCubit.changeCarouselIndex(entry.key);
  }

  Widget _videosView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails _courseDetails,
      CourseListState state,
      CourseListCubit _courseCubit) {
    int itemCount = _courseDetails.courseVideos.length;

    return Visibility(
      visible: itemCount > 0,
      child: Container(
        margin: const EdgeInsets.only(top: _verticalGapBwSections),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _titleWidget(
                constraints, SLStrings.getTranslatedString(KEY_VIDEOS)),
            const SizedBox(height: _verticalSpaceAfterTitle),
            sampleJson['videos_scroll_direction'] ==
                    GridScrollDirection.vertical
                ? _verticalGridView(context, constraints, _courseDetails,
                    itemCount, CourseGridItem.videos, state, _courseCubit)
                : _horizontalVideoView(
                    context,
                    constraints,
                    _courseDetails,
                    _courseCubit,
                    itemCount,
                    CourseGridItem.videos,
                  ),
          ],
        ),
      ),
    );
  }

  Widget _horizontalVideoView(
    BuildContext context,
    BoxConstraints constraints,
    CourseDetails? _courseDetails,
    CourseListCubit _courseCubit,
    int itemCount,
    CourseGridItem gridItem,
  ) {
    List<CourseVideo?> _courseVideos = _courseDetails?.courseVideos ?? [];
    final List<Widget> videoSliders = _courseVideos
        .map((item) => item != null
            ? _videoItem(context, constraints, item, _courseCubit,
                _courseVideos.indexOf(item), _courseVideos.length)
            : Container())
        .toList();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: _horizontalMargin),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: videoSliders,
        ),
      ),
    );
    // List<CourseVideo?> _courseVideos = _courseDetails?.courseVideos ?? [];
    // final List<Widget> videoSliders = _courseVideos
    //     .map((item) => item != null
    //         ? _videoItem(context, item, _courseVideos.indexOf(item),
    //             constraints, _courseCubit, _courseVideos.length)
    //         : Container())
    //     .toList();
    // return SingleChildScrollView(
    //   scrollDirection: Axis.horizontal,
    //   child: Row(
    //     crossAxisAlignment: CrossAxisAlignment.start,
    //     children: videoSliders,
    //   ),
    // );
  }

  Widget _verticalGridView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails? _courseDetails,
      int itemCount,
      CourseGridItem gridItem,
      CourseListState state,
      CourseListCubit _courseCubit) {
    switch (gridItem) {
      case CourseGridItem.videos:
        itemCount = itemCount > sampleJson['videos_crossaxis_count']
            ? sampleJson['videos_crossaxis_count']
            : itemCount;
        break;
      case CourseGridItem.subjects:
        itemCount = itemCount > sampleJson['subjects_crossaxis_count']
            ? sampleJson['subjects_crossaxis_count']
            : itemCount;
        break;
      case CourseGridItem.exams:
        itemCount = itemCount > sampleJson['exams_crossaxis_count']
            ? sampleJson['exams_crossaxis_count']
            : itemCount;
        break;
      default:
    }

    return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        primary: false,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // Number of columns in one view
          childAspectRatio: gridItem == CourseGridItem.videos ? 2 / 1.4 : 2 / 1,
          //  (constraints.maxWidth / 2)/  (constraints.maxWidth / 2.88),
          mainAxisSpacing: 20,
          crossAxisSpacing: 20,
        ),
        itemCount: itemCount,
        itemBuilder: (BuildContext ctx, index) {
          String moduleName = gridItem == CourseGridItem.subjects
              ? _courseDetails != null &&
                      _courseDetails.courseModule[index] != null
                  ? _courseDetails.courseModule[index]!.moduleName
                  : ''
              : gridItem == CourseGridItem.exams
                  ? _courseDetails != null &&
                          _courseDetails.exams[index] != null
                      ? _courseDetails.exams[index]!.moduleName
                      : ''
                  : '';
          return gridItem == CourseGridItem.videos
              ? _videoItem(
                  context,
                  constraints,
                  _courseDetails?.courseVideos[index],
                  _courseCubit,
                  index,
                  _courseDetails?.courseVideos.length ?? 0)
              : gridItem == CourseGridItem.subjects
                  ? _subjectNames(
                      context,
                      moduleName,
                      constraints,
                      _courseDetails?.courseModule[index],
                      index,
                      _courseDetails?.courseModule.length ?? 0)
                  : gridItem == CourseGridItem.exams
                      ? _examContainer(context, _courseDetails?.exams ?? [],
                          constraints) //_examTitles(context, constraints,_courseDetails?.exams[index], moduleName, index)
                      : Container();
        });
  }

  Widget _videoItem(
      BuildContext context,
      BoxConstraints constraints,
      CourseVideo? courseVideo,
      CourseListCubit _courseCubit,
      int index,
      int totalCount) {
    /// _horizontalMargin * 3 -> left, right padding gap fo the view,
    /// and the gap in between each item
    /// -10 -> left and right padding of the entire view
    double screenWidth = MediaQuery.sizeOf(context).width;
    double _videoItemWidth = totalCount == 1 || totalCount > 2
        ? (screenWidth / 2) - (_horizontalMargin * 3)
        : (screenWidth - (_horizontalMargin * 3)) / 2;
    CourseVideo courseVideoArg = courseVideo ??
        CourseVideo(
            videoId: '',
            videoName: '',
            videoURL: '',
            progress: 0,
            description: '',
            videoThumbnail: '',
            instanceId: '');
    bool _isYouTubeVideo = courseVideoArg.videoURL != '' &&
        courseVideoArg.videoURL.startsWith(YOUTUBE_URL);

    CourseProgress? progress = CourseProgress(
        progress: 0, timeSpent: "", instanceId: "", markedAsDone: false);
    return InkWell(
      // for showing hand icon for web while hovering
      onTap: () => debugPrint("object"),
      child: Container(
        margin: const EdgeInsets.only(right: _horizontalMargin),
        width: constraints.maxWidth < 720 ? _videoItemWidth : 200,
        alignment: Alignment.topLeft,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: VideoThumbnailWidget(
                videoUrl: courseVideoArg.videoURL,
                isYoutubeVideo: _isYouTubeVideo,
                removeShadow: true,
                videoTitle: courseVideoArg.videoName,
                progress: courseDetails?.courseVideos[index]?.progress ?? 0.0,
                constraints: constraints,
                shouldShowVideoProgress: true,
                thumbnail: courseVideoArg.videoThumbnail,
                // futureInst: _isYouTubeVideo
                //     ? generateYouTubeThumbnail(courseVideoArg.videoURL)
                //     : generateVideoThumbnail(courseVideoArg.videoURL),
                onTapCallback: () => kIsWeb
                    ? Beamer.of(context).beamToNamed('/video-view', data: {
                        'courseVideo': courseVideoArg,
                        'progess': progress
                      })
                    : appRouter
                        .push(CourseVideoViewRoute(
                            courseVideo: courseVideoArg, progess: progress))
                        .then((value) {
                        if (value != null) {
                          Map<String, dynamic> args =
                              value as Map<String, dynamic>;
                          String progressVal = args['progress'] ?? '0.0';
                          double progressDouble = double.parse(progressVal);
                          String durationStr = args['total_duration'] ?? '0';
                          int durationInt = int.parse(durationStr);
                          if (courseDetails?.courseVideos[index] != null) {
                            courseDetails?.courseVideos[index]!.progress =
                                progressDouble;
                            courseDetails?.courseVideos[index]!.totalDuration =
                                durationInt;
                          }
                        }

                        _courseCubit
                            .emit(CourseVideoStatus(isFullSCreen: true));
                      }),
              ),
            ),
            courseVideo != null
                ? _videoTitleRow(courseVideo.videoName)
                : Container(),
          ],
        ),
      ),
    );
  }

  Widget _videoTitleRow(String videoName) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        height: 50,
        // color: Colors.amber,
        margin: const EdgeInsets.only(top: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              '$ASSETS_PATH/video_play.png',
              height: 35,
              width: 35,
            ),
            const SizedBox(width: 5),
            Expanded(
              child: Container(
                // color: Colors.red,
                alignment: Alignment.centerLeft,
                child: Text(
                  videoName,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: LMSFonts.mediumFont(14, AppTheme.headerTextColor, 21),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _subjectsView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails _courseDetails,
      CourseListState state,
      CourseListCubit _courseCubit) {
    int courseModulesLength = _courseDetails.courseModule.length;

    return courseModulesLength > 0
        ? Container(
            margin: const EdgeInsets.only(top: _verticalGapBwSections - 5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _titleWidget(
                    constraints, SLStrings.getTranslatedString(KEY_SUBJECTS)),
                const SizedBox(height: _verticalSpaceAfterTitle),
                sampleJson['subjects_scroll_direction'] ==
                        GridScrollDirection.vertical
                    ? _verticalGridView(
                        context,
                        constraints,
                        _courseDetails,
                        courseModulesLength,
                        CourseGridItem.subjects,
                        state,
                        _courseCubit)
                    : _horizontalSubjectsView(
                        context,
                        constraints,
                        _courseDetails,
                        _courseCubit,
                        courseModulesLength,
                        CourseGridItem.subjects,
                      ),
              ],
            ),
          )
        : Container();
  }

  Widget _horizontalSubjectsView(
    BuildContext context,
    BoxConstraints constraints,
    CourseDetails? _courseDetails,
    CourseListCubit _courseCubit,
    int itemCount,
    CourseGridItem gridItem,
  ) {
    List<CourseModule?> _subjects = _courseDetails?.courseModule ?? [];
    final List<Widget> subjectSliders = _subjects
        .map((item) => item != null
            ? _subjectNames(
                context,
                item.moduleName,
                constraints,
                item,
                _subjects.indexOf(item),
                _courseDetails?.courseModule.length ?? 0)
            : Container())
        .toList();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: _horizontalMargin),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: subjectSliders,
        ),
      ),
    );
  }

  Widget _horizontalGridView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails? _courseDetails,
      int courseModulesLength,
      CourseGridItem gridItem,
      CourseListCubit _courseCubit) {
    switch (gridItem) {
      case CourseGridItem.videos:
        courseModulesLength =
            courseModulesLength > sampleJson['videos_crossaxis_count']
                ? sampleJson['videos_crossaxis_count']
                : courseModulesLength;
        break;
      case CourseGridItem.subjects:
        courseModulesLength =
            courseModulesLength > sampleJson['subjects_crossaxis_count']
                ? sampleJson['subjects_crossaxis_count']
                : courseModulesLength;
        break;
      case CourseGridItem.exams:
        courseModulesLength =
            courseModulesLength > sampleJson['exams_crossaxis_count']
                ? sampleJson['exams_crossaxis_count']
                : courseModulesLength;
        break;
      default:
    }

    final gridViewHeight = constraints.maxHeight * 0.16;
    return Container(
      height: courseModulesLength > 2 ? gridViewHeight * 2 : gridViewHeight,
      color: Colors.transparent,
      child: GridView.builder(
          scrollDirection: Axis.horizontal,
          gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: 200,
            childAspectRatio: 0.969 / 1.4,
            mainAxisSpacing: 20,
            crossAxisSpacing: 20,
          ),
          itemCount: courseModulesLength,
          itemBuilder: (BuildContext ctx, index) {
            String moduleName = gridItem == CourseGridItem.subjects
                ? _courseDetails != null &&
                        _courseDetails.courseModule[index] != null
                    ? _courseDetails.courseModule[index]!.moduleName
                    : ''
                : gridItem == CourseGridItem.exams
                    ? _courseDetails != null &&
                            _courseDetails.exams[index] != null
                        ? _courseDetails.exams[index]!.moduleName
                        : ''
                    : '';
            return
                /*gridItem == CourseGridItem.videos
                ? _videoItem(
                    context,
                    constraints,
                    _courseDetails?.courseVideos[index],
                    _courseCubit,
                    index,
                    _courseDetails?.courseVideos.length ?? 0)
                : gridItem == CourseGridItem.subjects
                    ? _subjectNames(
                        context,
                        moduleName,
                        constraints,
                        _courseDetails?.courseModule[index],
                        index,
                        _courseDetails?.courseModule.length ?? 0)
                    :*/
                gridItem == CourseGridItem.exams
                    ? _examContainer(context, _courseDetails?.exams ?? [],
                        constraints) //_examTitles(context, constraints,_courseDetails?.exams[index], moduleName, index)
                    : Container();
          }),
    );
  }

  Widget _subjectNames(
      BuildContext context,
      String text,
      BoxConstraints constraints,
      CourseModule? courseModule,
      int index,
      int totalCount) {
    double screenWidth = MediaQuery.sizeOf(context).width;
    double _subjectItemWidth = totalCount == 1 || totalCount > 2
        ? (screenWidth / 2) - (_horizontalMargin * 3)
        : (screenWidth - (_horizontalMargin * 3)) / 2;

    return InkWell(
      onTap: () {
        if (courseModule != null) {
          kIsWeb
              ? Beamer.of(context).beamToNamed(
                  '/section-details',
                  data: {
                    "sectionId": courseModule.moduleId,
                    'title': courseModule.moduleName,
                  },
                  beamBackOnPop: true,
                )
              : appRouter.push(SectionDetailsViewRoute(
                  sectionId: courseModule.moduleId,
                  title: courseModule.moduleName));
        } else {
          _showAlertDialog(context,
              SLStrings.getTranslatedString(KEY_SUBJECT_DETAILS_NOT_FOUND));
        }
      },
      child: Container(
        height: _subjectItemHeight,
        margin: EdgeInsets.only(
            right: index == totalCount - 1 ? 0 : _horizontalMargin),
        width: constraints.maxWidth < 720 ? _subjectItemWidth : 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: const LinearGradient(
            colors: [
              AppTheme.subjectsLinearGradientBottom,
              AppTheme.subjectsLinearGradientTop
            ],
            begin: Alignment.bottomRight,
            end: Alignment.topLeft,
          ),
        ),
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              '$ASSETS_PATH/subjects_general.png',
              height: 40,
            ),
            const SizedBox(height: 11),
            Flexible(
              child: Text(
                text,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: LMSFonts.mediumFont(16, AppTheme.whiteTextColor, 1.0),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _examsView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails _courseDetails,
      CourseListState state,
      CourseListCubit _courseCubit) {
    int examsLength = _courseDetails != null ? _courseDetails.exams.length : 0;
    return examsLength > 0
        ? Container(
            margin: const EdgeInsets.only(top: _verticalGapBwSections),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _titleWidget(
                    constraints, SLStrings.getTranslatedString(KEY_EXAMS)),
                const SizedBox(height: _verticalSpaceAfterTitle),
                sampleJson['exams_scroll_direction'] ==
                        GridScrollDirection.vertical
                    ? _verticalExamsView(
                        context,
                        constraints,
                        _courseDetails,
                        _courseCubit,
                        examsLength,
                      )
                    : _horizontalGridView(context, constraints, _courseDetails,
                        examsLength, CourseGridItem.exams, _courseCubit),
              ],
            ),
          )
        : Container();
  }

  _verticalExamsView(
    BuildContext context,
    BoxConstraints constraints,
    CourseDetails? _courseDetails,
    CourseListCubit _courseCubit,
    int _examsLength,
  ) {
    List<CourseExam?> _exams = _courseDetails?.exams ?? [];
    // final List<Widget> examsSliders = _exams
    //     .map((item) => item != null
    //         ? _examTitles(context, constraints, item, item.moduleName,
    //             _exams.indexOf(item))
    //         : Container())
    //     .toList();

    return SingleChildScrollView(
      child: Container(
          margin: const EdgeInsets.symmetric(
            horizontal: _horizontalMargin,
          ),
          // child: Column(
          //   children: examsSliders,
          // ),
          child: _examContainer(context, _exams, constraints)),
    );
  }

  Widget _examContainer(BuildContext context, List<CourseExam?> _exams,
      BoxConstraints constraints) {
    double _itemWidth =
        (MediaQuery.sizeOf(context).width / 2) - _horizontalMargin * 3;
    return Container(
      width: constraints.maxWidth < 720 ? _itemWidth : 200,
      margin: const EdgeInsets.only(bottom: _verticalSpaceAfterTitle * 2),
      decoration: BoxDecoration(
        // color: AppTheme.secondaryAppColor,
        borderRadius: BorderRadius.circular(10),
      ),
      alignment: Alignment.center,
      child: TextButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          backgroundColor: AppTheme.secondaryAppColor,
          foregroundColor: AppTheme.secondaryAppColor,
        ),
        onPressed: () {
          if (_exams.isNotEmpty) {
            kIsWeb
                ? Beamer.of(context)
                    .beamToNamed('/exam-list-tab', data: {'tabIndex': 0})
                : appRouter.push(ExamListViewRoute(tabIndex: 0));
          } else {
            _showAlertDialog(context,
                SLStrings.getTranslatedString(KEY_MODULE_DETAILS_NOT_FOUND));
          }
        },
        child: Container(
          height: _subjectItemHeight - 16,
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                '$ASSETS_PATH/question_light.png',
                color: AppTheme.whiteTextColor,
                height: 40,
              ),
              const SizedBox(height: 5),
              Flexible(
                child: Text(
                  SLStrings.getTranslatedString(KEY_NEW_EXAMS_TAB),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: LMSFonts.semiBoldFont(16, AppTheme.whiteTextColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

/* Removed for now
  Widget _examTitles(BuildContext context, BoxConstraints constraints,
      CourseExam? courseModule, String text, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: _verticalSpaceAfterTitle),
      decoration: BoxDecoration(
        // color: AppTheme.secondaryAppColor,
        borderRadius: BorderRadius.circular(10),
      ),
      alignment: Alignment.center,
      child: TextButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          backgroundColor: AppTheme.secondaryAppColor,
          foregroundColor: AppTheme.secondaryAppColor,
        ),
        onPressed: () {
          if (courseModule != null) {
            appRouter.push(ExamListViewRoute(updatedExamList: []));
          } else {
            _showAlertDialog(context,
                SLStrings.getTranslatedString(KEY_MODULE_DETAILS_NOT_FOUND));
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: _horizontalMargin),
          child: Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 5.0),
                  child: Text(
                    text,
                    textAlign: TextAlign.left,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    style: LMSFonts.semiBoldFont(16, AppTheme.examsTitleColor),
                  ),
                ),
              ),
              Image.asset(
                '$ASSETS_PATH/arrow_right.png',
                height: 32,
                width: 32,
                color: AppTheme.secondaryAppColor,
              ),
            ],
          ),
        ),
      ),
    );
  }*/

  Widget _divider() {
    return Container(
      color: LmsColors.black.withOpacity(0.8),
      height: 0.8,
      margin: const EdgeInsets.only(bottom: 20),
    );
  }

  void _showAlertDialog(BuildContext context, String msg) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: msg,
          iconWidget: const Icon(Icons.error, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }
/*
  _updateCurrentLanguageStatus(BuildContext context) async {
    String languageCode = SLConfig.DEFAULT_LOCALE_EN;
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.setString('current_lang', languageCode);
    _updateAppLocale(context, [Locale(languageCode)]);
  }

  _updateAppLocale(BuildContext context, List<Locale>? locale) {
    provider.Provider.of<LanguageProvider>(context, listen: false)
        .changeCurrentLanguage(SLStrings.getCurrentLangauge(locale));
  }

  logoutUserOnLanguageChangeDetection(String lang) async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      // Future.delayed(const Duration(seconds: 2), () {
      _prefs.setBool("signout", true);
      _prefs.setString('current_lang', lang);
      SLStrings.currentLanguage = lang;
      SLStrings.currentLocale = Locale(lang);

      if (scaffoldMessengerKey.currentContext != null) {
        final _userCubit =
            BlocProvider.of<UserCubit>(scaffoldMessengerKey.currentContext!);
        _userCubit.userSignout();
      }

      clearCurrentUserInfo();
      appRouter.pushAndPopUntil(LoginEmailViewRoute(isTokenExpired: false),
          predicate: (Route<dynamic> route) {
        return route.settings.name == ExamListViewRoute.name;
      });
      // });
    } on Exception catch (_) {
      // TODO
    }
  }
*/
  // void _showLanguageChangedAlert(
  //     BuildContext context, bool wasDefaultLangEN, String lang) {
  //   if (context != null && context.routeData.isActive) {
  //     showDialog(
  //       context: context,
  //       barrierDismissible: false,
  //       builder: (BuildContext context) {
  //         return CustomAlertDialog(
  //           title: '',
  //           message: wasDefaultLangEN
  //               ? LANGUAGE_CHANGE_ALERT2
  //               : LANGUAGE_CHANGE_ALERT,
  //           iconWidget: const Icon(Icons.error, size: 60),
  //         );
  //       },
  //     ).then((value) {
  //       if (wasDefaultLangEN) {
  //         //no change
  //         //dismiss the popuo
  //       } else {
  //         Navigator.pop(context);
  //         // logoutUserOnLanguageChangeDetection(lang);
  //       }
  //     });
  //   }
  // }
}
