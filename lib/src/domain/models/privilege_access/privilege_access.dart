// To parse this JSON data, do
//
//     final privilegeAccess = privilegeAccessFromJson(jsonString);

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'privilege_access.g.dart';

PrivilegeAccess privilegeAccessFromJson(Map<String, dynamic> json) =>
    PrivilegeAccess.fromJson(json);

String privilegeAccessToJson(PrivilegeAccess data) =>
    json.encode(data.toJson());

@JsonSerializable()
class PrivilegeAccess extends Equatable {
  @JsonKey(name: "org_id")
  final String? orgId;
  @Json<PERSON>ey(name: "status")
  final String? status;
  @Json<PERSON>ey(name: "user_id")
  final String? userId;
  @JsonKey(name: "user_roles")
  final List<String?> userRoles;
  @JsonKey(name: "role_privileges")
  final List<RolePrivilege?>? rolePrivileges;

  const PrivilegeAccess({
    required this.orgId,
    required this.status,
    required this.userId,
    required this.userRoles,
    required this.rolePrivileges,
  });

  factory PrivilegeAccess.fromJson(Map<String, dynamic> json) =>
      _$PrivilegeAccessFromJson(json);

  Map<String, dynamic> toJson() => _$PrivilegeAccessToJson(this);

  @override
  List<Object?> get props => [orgId, status, userId, userRoles, rolePrivileges];
}

@JsonSerializable()
class RolePrivilege extends Equatable {
  @JsonKey(name: "screen")
  final String? screen;
  @JsonKey(name: "actions")
  final Map<String, bool> actions;

  const RolePrivilege({
    required this.screen,
    required this.actions,
  });

  factory RolePrivilege.fromJson(Map<String, dynamic> json) =>
      _$RolePrivilegeFromJson(json);

  Map<String, dynamic> toJson() => _$RolePrivilegeToJson(this);

  @override
  List<Object?> get props => [screen, actions];
}
