part of 'register_cubit.dart';

abstract class RegisterState extends Equatable {
  final bool isLoading;
  const RegisterState({this.isLoading = false});

  @override
  List<Object> get props => [];
}

class RegisterInitial extends RegisterState {}

class RegisterLoading extends RegisterState {
  const RegisterLoading({super.isLoading});
}

class RegisterSuccess extends RegisterState {
//final User? user;
  final User? user;

  RegisterSuccess({required this.user});
  @override
  List<Object> get props => [];
}

class RegisterError extends RegisterState {
  final String message;

  RegisterError(this.message);

  @override
  List<Object> get props => [message];
}

class ExitUserError extends RegisterState {
  ExitUserError();

  @override
  List<Object> get props => [];
}

class ChangeLangauge extends RegisterState {
  final String selectedLang;

  const ChangeLangauge({required this.selectedLang});

  @override
  List<Object> get props => [selectedLang];
}

class Refresh extends RegisterState {
  Refresh();

  @override
  List<Object> get props => [];
}
