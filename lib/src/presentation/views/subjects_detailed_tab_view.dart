import 'package:SmartLearn/src/domain/models/tabbar_item.dart';
import 'package:auto_route/auto_route.dart';

import '../../utils/helper/thumbnail_generator.dart';
import '/src/domain/models/course_details.dart';
import '/src/presentation/widgets/tab_bar_widget.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/presentation/widgets/vedio_thumbnail_widget.dart';
import '../../config/router/app_router.dart';
import '../widgets/app_bar.dart';
import '/src/config/themes/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../utils/constants/strings.dart';

@RoutePage()

// ignore: must_be_immutable
class SubjectsDetailedTabViewScreen extends HookWidget {
  final String title;
  SubjectsDetailedTabViewScreen(this.title);

  late TabController _tabController;
  List<TabbarItem> _tabBarItems = [
    TabbarItem(id: 0, label: VIDEO),
    TabbarItem(id: 1, label: STUDY_MATERIAL),
    TabbarItem(id: 2, label: PRACTICE_SET)
  ];

  List<String> _videos = [];
  List<String> _studyMaterials = [];
  List<String> _practiceSet = [];

  @override
  Widget build(BuildContext context) {
    // final screenWidth = MediaQuery.of(context).size.width;
    _tabController = useTabController(initialLength: _tabBarItems.length);

    useEffect(() {
      _videos = List<String>.generate(
              7,
              (index) =>
                  "https://kfpwszxuivydjftikxts.supabase.co/storage/v1/object/sign/temp/LMS_Test_video.mp4?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJ0ZW1wL0xNU19UZXN0X3ZpZGVvLm1wNCIsImlhdCI6MTY4NjAyOTYwMCwiZXhwIjoxNjg2NjM0NDAwfQ.p5d-zmQkx116s226eyekg0JYUj9B6vvwB7TPwRA8R9s&t=2023-06-06T05%3A33%3A20.531Z")
          .toList();

      _studyMaterials =
          List<String>.generate(7, (index) => "Chemistry -Class ${index + 1}")
              .toList();
      _practiceSet =
          List<String>.generate(7, (index) => "Class ${index + 1}").toList();
      return null;
    }, const []);

    return Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: title,
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () => appRouter.pop(),
              trailingWidget: Container(),
            )),
        body: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: Column(
              children: [
                const SizedBox(height: 20),
                TabbarWidget(
                    tabBarItems: _tabBarItems, tabController: _tabController),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _videoGridView(constraints),
                      _studyMaterialListView(),
                      _practiceSetListView(),
                    ],
                  ),
                ),
              ],
            ),
          );
        }));
  }

  Widget _videoGridView(BoxConstraints constraints) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: _videos.isNotEmpty
          ? GridView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(vertical: 20),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2, // Number of columns in one view
                childAspectRatio: 2 / 1.4,
                //  (constraints.maxWidth / 2)/  (constraints.maxWidth / 2.88),
                mainAxisSpacing: 20,
                crossAxisSpacing: 20,
              ),
              itemCount: _videos.length,
              itemBuilder: (BuildContext ctx, index) {
                bool _isYouTubeVideo = _videos[index] != '' &&
                    checkForValidYoutubeUrls(_videos[index]);

                return VideoThumbnailWidget(
                    thumbnail: '', // TO DO: consider later
                    // futureInst: _isYouTubeVideo
                    //     ? generateYouTubeThumbnail(_videos[index])
                    //     : generateVideoThumbnail(_videos[index]),
                    videoUrl: _videos[index],
                    videoTitle: 'Title',
                    isYoutubeVideo: _isYouTubeVideo,
                    progress: 0.0,
                    constraints: constraints,
                    shouldShowVideoProgress: false,
                    onTapCallback: () {
                      CourseVideo _courseVideoObj = CourseVideo(
                          videoId: '',
                          videoName: '',
                          description: '',
                          instanceId: '',
                          videoThumbnail: '',
                          videoURL:
                              'https://kfpwszxuivydjftikxts.supabase.co/storage/v1/object/sign/temp/video.mp4?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJ0ZW1wL3ZpZGVvLm1wNCIsImlhdCI6MTY4NjIyNDExNSwiZXhwIjoxNjg2ODI4OTE1fQ.RlCV4yZ_lVx030tisntVNA3mzzFSNDDWU0btm1wTGWM&t=2023-06-08T11%3A35%3A13.856Z');

                      // appRouter.push(
                      //     CourseVideoViewRoute(courseVideo: _courseVideoObj));
                    });
              })
          : const EmptyTabView(tabItem: EMPTY_STUDY_MATERIALS_TAB),
    );
  }

  Widget _studyMaterialListView() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: _studyMaterials.isNotEmpty
          ? ListView.builder(
              itemCount: _studyMaterials.length,
              padding: const EdgeInsets.symmetric(vertical: 20),
              itemBuilder: (BuildContext context, int index) {
                return CardView(
                  title: _studyMaterials[index],
                  index: index,
                  leadingWidget: Image.asset(
                    'assets/images/sm2.png',
                    height: 30,
                    width: 30,
                    color: AppTheme.iconColor,
                  ),
                  trailingWidget: const Icon(
                    Icons.arrow_forward_ios,
                  ),
                  onTapCallback: () {
                    // appRouter.push(
                    //     StudyMaterialViewRoute(title: _studyMaterials[index]));
                  },
                );
              })
          : const EmptyTabView(tabItem: EMPTY_STUDY_MATERIALS_TAB),
    );
  }

  Widget _practiceSetListView() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: _practiceSet.isNotEmpty
          ? ListView.builder(
              itemCount: _practiceSet.length,
              padding: const EdgeInsets.symmetric(vertical: 20),
              itemBuilder: (BuildContext context, int index) {
                return CardView(
                  title: _practiceSet[index],
                  index: index,
                  leadingWidget: Image.asset(
                    'assets/images/sm1.png',
                    height: 30,
                    width: 30,
                    color: AppTheme.iconColor,
                  ),
                  trailingWidget: const Icon(
                    Icons.arrow_forward_ios,
                  ),
                  onTapCallback: () {
                    // appRouter.push(ExamViewRoute(
                    //     showTimer: false,
                    //     examDuration: 0,
                    //     fromViewResult: true,
                    //     questions: []));
                  },
                );
              })
          : const EmptyTabView(tabItem: EMPTY_PRACTICE_TAB),
    );
  }
}

class CardView extends StatelessWidget {
  final String title;
  final Widget leadingWidget;
  final Widget trailingWidget;
  final int index;
  final VoidCallback onTapCallback;

  const CardView(
      {super.key,
      required this.title,
      required this.leadingWidget,
      required this.trailingWidget,
      required this.index,
      required this.onTapCallback});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTapCallback(),
      child: Card(
        color: AppTheme.secondaryBlue,
        elevation: 2.0,
        margin: const EdgeInsets.only(bottom: 15),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 18.0, horizontal: 18),
          alignment: Alignment.center,
          child: Row(
            children: [
              leadingWidget,
              const SizedBox(width: 10),
              Text(
                title,
                style: LMSFonts.mediumFont(18, Colors.black, 1.0),
              ),
              Expanded(child: Container()),
              trailingWidget
            ],
          ),
        ),
      ),
    );
  }
}

class EmptyTabView extends StatelessWidget {
  final String tabItem;

  const EmptyTabView({super.key, required this.tabItem});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      padding: EdgeInsets.symmetric(horizontal: 20),
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: AppTheme.borderColor, width: 0.5)),
      child: Center(
        child: Text(
          tabItem,
          textAlign: TextAlign.center,
          style: LMSFonts.regularFontWithHeight(
              25, AppTheme.primaryTextColorBlack, 1.0),
        ),
      ),
    );
  }
}
