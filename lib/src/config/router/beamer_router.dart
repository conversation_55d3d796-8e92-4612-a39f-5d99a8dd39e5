import 'dart:convert';

import '/src/presentation/views/ppt_viewer.dart';

import '../../../src/domain/models/course_dashboard/course_page_resource.dart';
import '../../../src/domain/models/course_dashboard/course_video_resource.dart';

import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../presentation/views/course_details_dashboard.dart';
import '/splash_screen.dart';
import '/src/config/app_config/sl_config.dart';
import '/src/domain/models/responses/section_details.dart';
import '/src/presentation/cubits/exam/exam_cubit.dart';
import '/src/presentation/views/about_view.dart';
import '/src/presentation/views/course_description_view.dart';
import '/src/presentation/views/course_details_view.dart';
import '/src/presentation/views/course_list_view.dart';
import '/src/presentation/views/course_news_view.dart';
import '/src/presentation/views/course_video_view.dart';
import '/src/presentation/views/exam_intro_view.dart';
import '/src/presentation/views/exam_result_view.dart';
import '/src/presentation/views/exam_review_view.dart';
import '/src/presentation/views/exam_view.dart';
import '/src/presentation/views/exams_list_tab_view.dart';
import '/src/presentation/views/image_view.dart';
import '/src/presentation/views/login_email_view.dart';
import '/src/presentation/views/news_view_more_view.dart';
import '/src/presentation/views/onboarding_view.dart';
import '/src/presentation/views/organization_view.dart';
import '/src/presentation/views/pdf_view.dart';
import '/src/presentation/views/profile_review_view.dart';
import '/src/presentation/views/profile_view.dart';
import '/src/presentation/views/rank_list_view.dart';
import '/src/presentation/views/reset_password_view.dart';
import '/src/presentation/views/section_details_view.dart';
import '/src/presentation/views/settings_view.dart';
import '/src/presentation/views/signup_view.dart';
import '/src/presentation/views/study_material_view.dart';
import '/src/presentation/views/topic_list_view.dart';
import '/src/presentation/views/welcome_view.dart';
import '/src/presentation/widgets/no_access_view.dart';
import '/src/utils/resources/shared_preference_utils.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/models/category/user_category.dart';
import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_details.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/exam_summary.dart';
import '../../domain/models/page_content.dart';
import '../../domain/models/question_data.dart';
import '../../domain/models/rank.dart';
import '../../domain/models/resource_file.dart';
import '../../presentation/cubits/section_details/section_details_cubit.dart';
import '../../presentation/views/subscription_view.dart';

import 'dart:developer';

class BeamerRouter {
  final routerDelegate = BeamerDelegate(
    guards: [
      BeamGuard(
        pathPatterns: ['/'],
        check: (context, location) {
          final locations = location.history;
          print('location => $locations');
          return false;
        },
      ),
      BeamGuard(
        pathPatterns: ['/video-view'],
        check: ((context, location) {
          final _examCubit =
              BlocProvider.of<ExamCubit>(context).navigatedToExamIntro;
          if (_examCubit) {
            return false;
          } else {
            return true;
          }
        }),
      ),
      BeamGuard(
        pathPatterns: ['/exam-list-tab'],
        check: ((context, location) {
          final disableNavigationBackToList =
              BlocProvider.of<ExamCubit>(context).disableNavigationBackToList;
          if (disableNavigationBackToList) {
            // block navigation
            return false;
          } else {
            return true;
          }
        }),
      ),
      BeamGuard(
        pathPatterns: ['/exam-intro'],
        check: ((context, location) {
          final disableNavigationBackToIntro =
              BlocProvider.of<ExamCubit>(context).disableNavigationBackToIntro;
          if (disableNavigationBackToIntro) {
            // block navigation
            return false;
          } else {
            return true;
          }
        }),
      ),
      BeamGuard(
        pathPatterns: ['/exam-view'],
        check: ((context, location) {
          final disableNavigationBackToExam =
              BlocProvider.of<ExamCubit>(context).disableNavigationBackToExam;
          if (disableNavigationBackToExam) {
            // block navigation
            return false;
          } else {
            return true;
          }
        }),
      ),
      BeamGuard(
        pathPatterns: ['/exam-review'],
        check: ((context, location) {
          final disableNavigationBackToReview =
              BlocProvider.of<ExamCubit>(context).disableNavigationBackToReview;
          if (disableNavigationBackToReview) {
            // block navigation
            return false;
          } else {
            return true;
          }
        }),
      ),
      BeamGuard(
        pathPatterns: ['/exam-result'],
        check: ((context, location) {
          final disableNavigationBackToResult =
              BlocProvider.of<ExamCubit>(context).disableNavigationBackToResult;
          if (disableNavigationBackToResult) {
            // block navigation
            return false;
          } else {
            return true;
          }
        }),
      ),
      BeamGuard(
        pathPatterns: ['/course-details'],
        check: (context, loc) {
          final data = BlocProvider.of<SectionDetailsCubit>(context).setValue;
          if (data) {
            return false;
          } else {
            return true;
          }
        },
        // beamToNamed: (origin, target) => '/section-details',
      ),
      BeamGuard(
        pathPatterns: [
          '/course-details',
          '/profile-view',
          '/profile-review',
          '/section-details',
          '/video-view',
          '/exam-intro',
          '/exam-view',
          '/exam-list-tab',
          '/course-news',
          '/news-more',
          '/exam-review',
          '/exam-result',
          '/rank-list',
          '/study-materials',
          '/subscription-view',
          '/no-access-view',
        ],
        check: ((context, location) =>
            Supabase.instance.client.auth.currentSession?.isExpired == false),
        beamToNamed: (origin, target) => '/login-view',
      )
    ],
    locationBuilder: RoutesLocationBuilder(routes: {
      '/': (context, state, data) => BeamPage(
            title: SLConfig.APP_TITLE,
            key: const ValueKey('splash'),
            child: SplashScreen(),
          ),
      '/onboarding': (context, state, data) => BeamPage(
          title: SLConfig.APP_TITLE,
          key: const ValueKey('onboarding'),
          child: OnboardingViewScreen()),
      '/login-view': (context, state, data) {
        bool isTokenExpired = false;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          isTokenExpired = args['isTokenExpired'] ?? false;
        }
        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: const ValueKey('login-view'),
          child: LoginEmailViewScreen(
            isTokenExpired: isTokenExpired,
          ),
        );
      },
      '/course-list': (context, state, data) {
        UserCategory topic = UserCategory(
            id: '',
            name: '',
            orgId: '',
            courses: [],
            subCategory: [],
            childIndex: 0,
            isPremium: false,
            description: '',
            publishStatus: PublishStatus.PUBLISHED,
            isExpanded: false);
        bool isFromLogin = true;
        List<Course> courses = [];
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('topic') &&
              args.containsKey('isFromLogin') &&
              args.containsKey('courses')) {
            topic = args['topic'] ?? '';
            isFromLogin = args['isFromLogin'] ?? false;
            courses = args['courses'] ?? [];
            SharedPreferencesUtils.instance.setRouteData('course-list', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('course-list');
            topic = args['topic'];
            isFromLogin = false;
            courses = args['courses'] ?? [];
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('course-list');
          topic = args['topic'];
          isFromLogin = false;
          courses = args['courses'] ?? [];
        }
        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: const ValueKey('course-list'),
          child: CourseListViewScreen(
            category: topic,
            isFromLogin: isFromLogin,
          ),
        );
      },
      '/course-details': (context, state, data) {
        String courseId = '';
        String courseName = '';
        bool isFromLogin = false;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('courseId') && args.containsKey('courseName')) {
            courseId = args['courseId'] ?? '';
            courseName = args['courseName'] ?? '';
            isFromLogin = args['isFromLogin'] == true;
            SharedPreferencesUtils.instance
                .setRouteData('course-details', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('course-details');
            courseId = args['courseId'];
            courseName = args['courseName'];
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('course-details');
          courseId = args['courseId'];
          courseName = args['courseName'];
        }
        return BeamPage(
            title: SLConfig.APP_TITLE,
            key: ValueKey(courseId),
            child: CourseDetailsViewScreen(
              courseId: courseId,
              courseName: courseName,
              isFromLogin: isFromLogin,
            ));
      },
      '/profile-view': (context, state, data) {
        bool isFromLogin = false;
        bool isFromSideMenu = false;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          isFromLogin = args['isFromLogin'] ?? false;
          isFromSideMenu = args['isFromSideMenu'] ?? false;
        }

        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: const ValueKey('profile-view'),
          child: ProfileViewScreen(
            isFromLogin: isFromLogin,
            isFromSideMenu: isFromSideMenu,
          ),
        );
      },
      '/profile-review': (context, state, data) {
        return BeamPage(
          title: SLConfig.APP_TITLE,
          child: ProfileReviewScreen(),
        );
      },
      '/organization-view': (context, state, data) {
        final isFromLogin = state.queryParameters['isFromLogin'] == 'true';
        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: const ValueKey('organization-view'),
          child: OrganizationViewScreen(
            isFromLogin: isFromLogin,
          ),
        );
      },
      '/topic-view': (context, state, data) {
        bool isFromLogin = false;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          isFromLogin = args['isFromLogin'] ?? false;
        }
        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: const ValueKey('topic-view'),
          child: TopicListViewScreen(
            isFromLogin: isFromLogin,
          ),
        );
      },
      '/welcome': (context, state, data) => const BeamPage(
            key: ValueKey('welcome'),
            child: WelcomeViewScreen(),
            title: SLConfig.APP_TITLE,
          ),
      '/reset-password': (context, state, data) => BeamPage(
            key: const ValueKey("reset-password"),
            child: ResetPasswordViewScreen(),
            title: SLConfig.APP_TITLE,
          ),
      '/sign-up': (context, state, data) => BeamPage(
            key: const ValueKey("sign-up"),
            child: SignUpViewScreen(),
            title: SLConfig.APP_TITLE,
          ),
      '/section-details': (context, state, data) {
        String sectionId = '';
        String title = '';
        SectionDetails sectionDetailsData =
            SectionDetails(resources: [], modules: [], folders: []);
        bool showFolders = false;
        int folderIndex = 0;

        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('sectionId') &&
              args.containsKey('title') &&
              args.containsKey('showFolders') &&
              args.containsKey('folderIndex')) {
            sectionId = args['sectionId'];
            title = args['title'];
            showFolders = args['showFolders'];
            folderIndex = args['folderIndex'];
            SharedPreferencesUtils.instance
                .setRouteData('section-details', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('section-details');
            sectionId = args['sectionId'];
            title = args['title'];
            showFolders = args['showFolders'];
            folderIndex = args['folderIndex'];
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('section-details');
          sectionId = args['sectionId'];
          title = args['title'];
          sectionDetailsData = args['sectionDetailsData'] ?? sectionDetailsData;
          showFolders = args['showFolders'];
          folderIndex = args['folderIndex'];
        }
        return BeamPage(
            title: SLConfig.APP_TITLE,
            key: ValueKey(title),
            child: SectionDetailsViewScreen(
                sectionId: sectionId,
                title: title,
                sectionDetailsData: sectionDetailsData,
                showFolderResources: showFolders,
                folderIndex: folderIndex));
      },
      '/video-view': (context, state, data) {
        CourseVideoResource courseVideo;
        String? courseId;
        ResourceProgress progress;
        bool isFromSectionDetails;
        bool isFromExamScreen;
        bool isExamPassed;
        List<CheckPoint> checkPoints;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('courseVideo') && args.containsKey('progess')) {
            courseVideo = args['courseVideo'];
            courseId = args['courseId'];
            progress = args['progess'];
            isFromSectionDetails = args['isFromSectionDetails'] ?? false;
            isFromExamScreen = args['isFromExamScreen'] ?? false;
            isExamPassed = args['isExamPassed'] ?? false;
            checkPoints = args['checkPoints'] ?? <CheckPoint>[];
            SharedPreferencesUtils.instance.setRouteData('video-view', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('video-view');
            courseVideo = CourseVideoResource.fromJson(args['courseVideo']);
            courseId = args['courseId'];
            progress = args['progess'];
            isFromSectionDetails = args['isFromSectionDetails'] ?? false;
            isFromExamScreen = args['isFromExamScreen'] ?? false;
            isExamPassed = args['isExamPassed'] ?? false;
            checkPoints = args['checkPoints'] = <CheckPoint>[];
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('video-view');
          courseVideo = CourseVideoResource.fromJson(args['courseVideo']);
          courseId = args['courseId'];
          progress = args['progess'];
          isFromSectionDetails = args['isFromSectionDetails'] ?? false;
          isFromExamScreen = args['isFromExamScreen'] ?? false;
          isExamPassed = args['isExamPassed'] ?? false;
          checkPoints = args['checkPoints'] = <CheckPoint>[];
        }

        return BeamPage(
            title: SLConfig.APP_TITLE,
            key: ValueKey(courseVideo.id),
            child: CourseVideoViewScreen(
              courseVideo,
              courseProgress: progress,
              isFromSectionDetails: isFromSectionDetails,
              isFromExamScreen: isFromExamScreen,
              isExamPassed: isExamPassed,
              checkPoints: checkPoints,
              navigateBackTo: NavigationTo.details_dashboard,
            ));
      },
      '/exam-intro': (context, state, data) {
        QuestionData questionData;
        bool isFromSectionDetails;
        bool isFromVideoScreen;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('questionData')) {
            questionData = args['questionData'];
            isFromSectionDetails = args['isFromSectionDetails'] ?? false;
            isFromVideoScreen = args['isFromVideoScreen'] ?? false;
            SharedPreferencesUtils.instance.setRouteData('exam-intro', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('exam-intro');
            questionData =
                QuestionData.fromJson(args['questionData'], args['examId']);
            isFromSectionDetails = args['isFromSectionDetails'] ?? false;
            isFromVideoScreen = args['isFromVideoScreen'] ?? false;
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('exam-intro');
          questionData = QuestionData.fromJson(
              args['questionData'], args['questionData']['examId']);
          isFromSectionDetails = args['isFromSectionDetails'] ?? false;
          isFromVideoScreen = args['isFromVideoScreen'] ?? false;
        }
        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: ValueKey(questionData.examId),
          child: ExamIntroductionViewScreen(
            questionData: questionData,
            isFromSectionDetails: isFromSectionDetails,
            isFromVideoScreen: isFromVideoScreen,
            navigateBackTo: NavigationTo.exam_list,
          ),
        );
      },
      '/exam-view': (context, state, data) {
        int examDuration;
        bool shouldShowTimer;
        bool isFromViewResult;
        String quizAttemptId;
        QuestionData questionData;
        bool isFromVideoView = false;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('examDuration') &&
              args.containsKey('questionData')) {
            examDuration = args['examDuration'];
            shouldShowTimer = args['shouldShowTimer'];
            isFromViewResult = args['isFromViewResult'];
            quizAttemptId = args['quizAttemptId'];
            questionData = args['questionData'];
            isFromVideoView = args['isFromVideoView'] ?? false;
            SharedPreferencesUtils.instance.setRouteData('exam-view', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('exam-view');
            examDuration = args['examDuration'];
            shouldShowTimer = args['shouldShowTimer'];
            isFromViewResult = args['isFromViewResult'];
            quizAttemptId = args['quizAttemptId'];
            questionData = args['questionData'];
            isFromVideoView = args['isFromVideoView'];
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('exam-view');
          examDuration = args['examDuration'];
          shouldShowTimer = args['shouldShowTimer'];
          isFromViewResult = args['isFromViewResult'];
          quizAttemptId = args['quizAttemptId'];
          questionData = args['questionData'];
          isFromVideoView = args['isFromVideoView'];
        }
        return BeamPage(
            title: SLConfig.APP_TITLE,
            key: ValueKey(quizAttemptId),
            child: ExamViewScreen(
              examDuration: examDuration,
              shouldShowTimer: shouldShowTimer,
              isFromViewResult: isFromViewResult,
              quizAttemptId: quizAttemptId,
              questionData: questionData,
              isFromVideoView: isFromVideoView,
              navigateBackTo: NavigationTo.details_dashboard,
            ));
      },
      '/exam-list-tab': (context, state, data) {
        int tabIndex;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('tabIndex')) {
            tabIndex = args['tabIndex'];
          } else {
            tabIndex = 0;
          }
        } else {
          tabIndex = 0;
        }
        return BeamPage(
            title: SLConfig.APP_TITLE,
            child: ExamListViewScreen(tabIndex),
            key: const ValueKey('exam-list-tab'));
      },
      '/course-news': (context, state, data) {
        CurrentAffairs currentAffairs;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('currentAffairs')) {
            currentAffairs = args['currentAffairs'];
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('course-news');
            currentAffairs = args['currentAffairs'];
          }

          SharedPreferencesUtils.instance.setRouteData('course-news', args);
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('course-news');
          currentAffairs = args['currentAffairs'];
        }
        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: ValueKey(currentAffairs.newsId),
          child: CourseNewsViewScreen(currentAffairs: currentAffairs),
        );
      },
      '/news-more': (context, state, data) {
        List<CurrentAffairs> affairsList = [];
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('affairsList')) {
            affairsList = args['affairsList'];
            SharedPreferencesUtils.instance.setRouteData('news-more', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('news-more');
            final List<dynamic> routeArgs = args['affairsList'];
            final currentAffairs =
                routeArgs.map((e) => CurrentAffairs.fromJson(e)).toList();
            affairsList = currentAffairs;
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('news-more');
          final List<dynamic> routeArgs = args['affairsList'];
          final currentAffairs =
              routeArgs.map((e) => CurrentAffairs.fromJson(e)).toList();
          affairsList = currentAffairs;
        }
        return BeamPage(
          child: NewsViewMoreViewScreen(affairsList),
          title: SLConfig.APP_TITLE,
        );
      },
      '/exam-review': (context, state, data) {
        String quizId;
        String quizAttemptId;
        bool isGradeCalculated;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('quizId') &&
              args.containsKey('quizAttemptId') &&
              args.containsKey('isGradeCalculated')) {
            quizId = args['quizId'];
            quizAttemptId = args['quizAttemptId'];
            isGradeCalculated = args['isGradeCalculated'];
            SharedPreferencesUtils.instance.setRouteData('exam-review', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('exam-review');
            quizId = args['quizId'];
            quizAttemptId = args['quizAttemptId'];
            isGradeCalculated = args['isGradeCalculated'];
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('exam-review');
          quizId = args['quizId'];
          quizAttemptId = args['quizAttemptId'];
          isGradeCalculated = args['isGradeCalculated'];
        }
        return BeamPage(
          title: SLConfig.APP_TITLE,
          child: ExamReviewViewScreen(
              quizId: quizId,
              quizAttemptId: quizAttemptId,
              isGradeCalculated: isGradeCalculated, navigateBackTo: NavigationTo.exam_list,),
        );
      },
      '/exam-result': (context, state, data) {
        ExamSummary examSummary;
        int tabIndex;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('examSummary') && args.containsKey('tabIndex')) {
            examSummary = args['examSummary'];
            tabIndex = args['tabIndex'];
            SharedPreferencesUtils.instance.setRouteData('exam-result', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('exam-result');
            examSummary = ExamSummary.fromJson(args['examSummary']);
            tabIndex = args['tabIndex'];
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('exam-result');
          examSummary = ExamSummary.fromJson(args['examSummary']);
          tabIndex = args['tabIndex'];
        }
        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: const ValueKey('exam-result'),
          child: ExamResultViewScreen(
              examSummary: examSummary, tabIndex: tabIndex,navigateBackTo: NavigationTo.exam_list),
        );
      },
      '/rank-list': (context, state, data) {
        bool isCourseRankList;
        List<Rank> rankListForExam;
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('isCourseRankList') &&
              args.containsKey('isCourseRankList')) {
            isCourseRankList = args['isCourseRankList'];
            rankListForExam = args['rankListForExam'];
            SharedPreferencesUtils.instance.setRouteData('rank-list', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('rank-list');
            isCourseRankList = args['isCourseRankList'];
            rankListForExam = args['rankListForExam'];
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('rank-list');
          isCourseRankList = args['isCourseRankList'];
          rankListForExam = args['rankListForExam'];
        }
        return BeamPage(
          child: RankListViewScreen(isCourseRankList, rankListForExam),
          title: SLConfig.APP_TITLE,
        );
      },
      '/settings': (context, state, data) {
        return BeamPage(
          child: SettingsViewScreen(),
          title: SLConfig.APP_TITLE,
        );
      },
      '/about': (context, state, data) {
        return const BeamPage(
          child: AboutViewScreen(),
          title: SLConfig.APP_TITLE,
        );
      },
      '/study-materials': (context, state, data) {
        CoursePageResource pageContent;
        String instance;
        String courseId;
        ResourceProgress? progess;
        int moduleLength = 0;

        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('pageContent') &&
              args.containsKey('instance') &&
              args.containsKey('courseId') &&
              args.containsKey('moduleLength')) {
            pageContent = args['pageContent'];
            instance = args['instance'];
            courseId = args['courseId'];
            progess = args['progess'];
            moduleLength = args['moduleLength'] ?? 0;
            SharedPreferencesUtils.instance
                .setRouteData('/study-materials', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('study-materials');
            pageContent = CoursePageResource.fromJson(args['pageContent']);
            instance = args['instance'];
            courseId = args['courseId'];
            progess = args['progess'];
            moduleLength = args['moduleLength'] ?? 0;
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('study-materials');
          pageContent = CoursePageResource.fromJson(args['pageContent']);
          instance = args['instance'];
          courseId = args['courseId'];
          progess = args['progess'];
          moduleLength = args['moduleLength'] ?? 0;
        }
        return BeamPage(
            title: SLConfig.APP_TITLE,
            child: StudyMaterialViewScreen(
              pageContent: pageContent,
              resourceProgress: progess,
              moduleLength: moduleLength,
            ));
      },
      '/image-view': (context, state, data) {
        CourseFileResource resourceFile;
        ResourceProgress? progess;
        int moduleLength = 0;

        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('imagePath') &&
              args.containsKey('resourceFile')) {
            resourceFile = args['resourceFile'];
            progess = args['progess'];
            moduleLength = args['moduleLength'] ?? 0;
            SharedPreferencesUtils.instance.setRouteData('image-view', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('/image-view');
            resourceFile = args['resourceFile'];
            progess = args['progess'];
            moduleLength = args['moduleLength'] ?? 0;
          }
        } else {
          final args =
              SharedPreferencesUtils.instance.getRouteData('/image-view');
          resourceFile = args['resourceFile'];
          progess = args['progess'];
          moduleLength = args['moduleLength'] ?? 0;
        }
        return BeamPage(
            title: SLConfig.APP_TITLE,
            child: ImageViewScreen(
              resourceFile: resourceFile,
              resourceProgress: progess,
              moduleLength: moduleLength,
            ));
      },
      '/pdf-view': (context, state, data) {
        CourseFileResource resourceFile;

        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('resourceFile')) {
            resourceFile = args['resourceFile'];
            SharedPreferencesUtils.instance.setRouteData('pdf-view', args);
          } else {
            final args =
                SharedPreferencesUtils.instance.getRouteData('pdf-view');
            resourceFile = args['resourceFile'];
          }
        } else {
          final args = SharedPreferencesUtils.instance.getRouteData('pdf-view');
          resourceFile = args['resourceFile'];
        }
        return BeamPage(
          child: PDFViewScreen(
            courseFileResource: resourceFile,
            checkPoints: const [], // Replace with actual list if available
            isFromExamScreen:
                false, // Set to true if navigating from exam screen
            isExamPassed: false, // Set to true if exam is passed
            navigateBackTo: NavigationTo
                .details_dashboard, // Replace with the correct NavigationTo value if needed
          ),
          title: SLConfig.APP_TITLE,
        );
      },
      '/subscription-view': (context, state, data) {
        return BeamPage(
          title: SLConfig.APP_TITLE,
          key: const ValueKey('subscription-view'),
          child: SubscriptionViewScreen(),
        );
      },
      '/no-access-view': (context, state, data) => const BeamPage(
            key: ValueKey("no-access-view"),
            child: NoAccessViewScreen(),
            title: SLConfig.APP_TITLE,
          ),
      '/course-description-view': (context, state, data) {
        String summary = '';
        if (data != null) {
          final args = data as Map<String, dynamic>;
          if (args.containsKey('summary')) {
            summary = args['summary'];
            SharedPreferencesUtils.instance
                .setRouteData('course-description-view', args);
          } else {
            final args = SharedPreferencesUtils.instance
                .getRouteData('course-description-view');
            summary = args['summary'];
          }
        } else {
          final args = SharedPreferencesUtils.instance
              .getRouteData('course-description-view');
          summary = args['summary'];
        }
        return BeamPage(
          key: ValueKey("course-description-view"),
          child: CourseDescriptionViewScreen(
            summary: summary,
          ),
          title: SLConfig.APP_TITLE,
        );
      },
      // '/course-details-dashboard': (context, state, data) => const BeamPage(
      //       key: ValueKey("course-details-dashboard"),
      //       child: CourseDetailsDashboardScreen(coursesInfo: []),
      //       title: SLConfig.APP_TITLE,
      //     ),
    }),
  );
}
