// ignore_for_file: must_be_immutable

import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart' as provider;

import '../../config/app_config/preference_config.dart';
import '../../config/enums/alert_types.dart';
import '../../domain/services/provider/language_provider.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../cubits/organization/organization_cubit.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/domain/services/localizations/sl_strings.dart';

import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/lists.dart';
import '/src/config/app_config/sl_config.dart';
import '/src/presentation/widgets/session_expired_widget.dart';
import '/src/utils/helper/regex_strings.dart';

import '../../domain/services/db_helper.dart';
import '/src/domain/models/organization.dart';

import '../../config/themes/app_theme.dart';
import '../../utils/constants/nums.dart';
import '/src/config/themes/lms_fonts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../src/config/router/app_router.dart';
import '../../../src/presentation/cubits/login_email/login_cubit.dart';
import '../../../src/presentation/cubits/profile/profile_cubit.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/constants/strings.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/button_widget.dart';
import '../widgets/connectivity_widget.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/textfield_widget.dart';

@RoutePage()
class LoginEmailViewScreen extends HookWidget with WidgetsBindingObserver {
  final bool isTokenExpired;

  LoginEmailViewScreen({Key? key, this.isTokenExpired = false})
      : super(key: key);

  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final DbHelper _dbHelper = DbHelper.instance;

  String? errorMessage;
  String? passwordErrorMessage;
  String _selectedLanguage = SLConfig.DEFAULT_LOCALE_EN;

  bool showEmailError = false;
  bool showPasswordError = false;
  bool enableSubmitButton = false;
  bool inputDataSuccessValidation = false;
  bool shouldShowLoading = false;
  bool showSessionExpiredAlert = true;

  BuildContext? currentContext;

  @override
  Future<void> didChangeLocales(List<Locale>? locales) async {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    if (locales != null && locales.isNotEmpty) {
      String newLang = locales[0].languageCode;
      _selectedLanguage = newLang;
      if (SLStrings.currentLanguage.contains(newLang)) {
        // language not changed
      } else {
        // language changed
        _selectedLanguage = await getUpdatedLang(_selectedLanguage);

        bool wasDefaultLangEN =
            SLStrings.currentLanguage.contains(SLConfig.DEFAULT_LOCALE_EN) &&
                _selectedLanguage.contains(SLConfig.DEFAULT_LOCALE_EN);

        if (currentContext != null) {
          if (localizationLangauges.contains(_selectedLanguage)) {
            // new language is supported by app
            // i.e. exists in the list of locales supported by app

            if (wasDefaultLangEN) {
            } else {
              //_showLanguageChangedAlert(currentContext!, _selectedLanguage);
            }
          } else {
            /// changed language from system settings
            /// language not in the list of locales supported
            /// by app
            /// set to default lang- 'en'
            ///

            if (wasDefaultLangEN) {
              // if the app language was already 'en',
              // then no need to show popup and change to 'en'
              _selectedLanguage = SLConfig.DEFAULT_LOCALE_EN;
            } else {
              // if the app language was not 'en',
              // then show popup and change language to 'en'
              _selectedLanguage = SLConfig.DEFAULT_LOCALE_EN;
              //_showLanguageChangedAlert(currentContext!, _selectedLanguage);
            }
          }
          if (currentContext!.routeData.isActive) {
            // Future.delayed(const Duration(milliseconds: 500), () {
            _updateAppLocale(currentContext!);
            // });
          }
        }
      }
    }
  }

  _updateAppLocale(BuildContext context) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    bool isNavigatingAfterLogout =
        _prefs.getBool(PREF_KEY_NAVIGATING_AFTER_LOGOUT) ?? false;

    _selectedLanguage = (!isNavigatingAfterLogout && !isTokenExpired)
        ? _selectedLanguage
        : SLStrings.currentLanguage;

    provider.Provider.of<LanguageProvider>(context, listen: false)
        .changeCurrentLanguage(_selectedLanguage);
    _onLangaugeChanged(context, _selectedLanguage);
  }

  _setStatus() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.setBool(PREF_KEY_NAVIGATING_AFTER_LOGOUT, false);

    ///
    /// if 'isNavigatingAfterLogout' is true, then
    /// user has signed out manualy, so need to
    /// maintain the app's chosen locale in preference
    /// without resetting app locale based on device locale.
    ///
    /// set to false when user reaches login screen
    /// so that, the app can take
    /// device's locale as the app locale from next time onwards
    /// (next restart/ relaunch)
    ///
  }

  void _clearTextFieldValidation(LoginEmailCubit _loginCubit) {
    _loginCubit.clearEmailError();
    _loginCubit.clearPasswordError();
  }

  @override
  Widget build(BuildContext mainContext) {
    currentContext = mainContext;
    FocusNode _emailFocusNode = useFocusNode();
    FocusNode _passwordFocusNode = useFocusNode();

    final _loginCubit = BlocProvider.of<LoginEmailCubit>(mainContext);

    _emailController.addListener(() {
      _loginCubit.changeEmail(_emailController.text);
    });

    _passwordController.addListener(() {
      _loginCubit.changePassword(_passwordController.text);
    });

    final screenWidth = MediaQuery.of(mainContext).size.width;

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      //_clearTextFieldValidation(_loginCubit);
      Future<void>.microtask(() async {
        _loginCubit.setLoading(true);
        await clearCurrentUserInfo();
        if (!kIsWeb) {
          _selectedLanguage = await getUpdatedLang(_selectedLanguage);
          await _updateAppLocale(mainContext);
        }
        _loginCubit.setLoading(false);
        _loginCubit.dismissionSessionExpiredAlert();
        await _setStatus();
      });
      return () {
        currentContext = null;
        _emailController.clear();
        _passwordController.clear();
        _emailController.dispose();
        _passwordController.dispose();

        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
      },
      child: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(mainContext);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Scaffold(
          backgroundColor: AppTheme.primaryBlue,
          body: Container(
            // margin: const EdgeInsets.only(bottom: 36),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('$ASSETS_PATH/background.png'),
                fit: BoxFit.cover,
              ),
            ),
            child: BlocListener<LoginEmailCubit, LoginEmailState>(
              listener: (context, state) async {
                if (state is LoginEmailLoading) {
                  shouldShowLoading = state.isLoading;
                } else if (state is LoginEmailLoading) {
                } else if (state is LoginEmailErrorValidation) {
                  errorMessage = state.error;
                  showEmailError = state.showError;
                } else if (state is LoginPasswordValidation) {
                  passwordErrorMessage = state.PasswordError;

                  showPasswordError = state.showError;
                } else if (state is LoginButtonColor) {
                  enableSubmitButton = state.buttonColor;
                } else if (state is AlertMessageVisibility) {
                  showSessionExpiredAlert = state.showAlert;
                }
              },
              child: BlocBuilder<LoginEmailCubit, LoginEmailState>(
                builder: (context, state) {
                  if (state is ChangeAppLangauge) {
                    _selectedLanguage = state.selectedLang;
                  }
                  return SafeArea(
                    child: Stack(
                      children: [
                        LayoutBuilder(builder: (context, constraints) {
                          return Align(
                            child: SizedBox(
                              width: constraints.maxWidth < 720
                                  ? constraints.maxWidth
                                  : 800,
                              height: constraints.maxHeight,
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  kIsWeb
                                      ? Container()
                                      : _languageSelectionWidget(context),
                                  SingleChildScrollView(
                                    clipBehavior: Clip.none,
                                    child: Container(
                                      // height: MediaQuery.of(context).size.height -
                                      //     MediaQuery.of(context).padding.top -
                                      //     80,
                                      decoration: const BoxDecoration(
                                          // image: DecorationImage(
                                          //   image:
                                          //       AssetImage('$ASSETS_PATH/background.png'),
                                          //   fit: BoxFit.cover,
                                          // ),
                                          ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Image.asset('$ASSETS_PATH/logo.png',
                                              height: 80, width: 100),
                                          const SizedBox(height: 11),
                                          _appTitle(SLConfig.APP_TITLE),
                                          const SizedBox(height: 10),
                                          Card(
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(20.0),
                                            ),
                                            color: AppTheme.cardColor,
                                            margin: const EdgeInsets.all(20.0),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 22),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: <Widget>[
                                                  const SizedBox(height: 30.0),
                                                  _loginTitle(SLStrings
                                                      .getTranslatedString(
                                                          KEY_LOGIN)),
                                                  const SizedBox(height: 15.0),
                                                  _emailWidget(
                                                      screenWidth,
                                                      _emailController,
                                                      _emailFocusNode,
                                                      context,
                                                      errorMessage,
                                                      _loginCubit),
                                                  const SizedBox(height: 20.0),
                                                  _passwordWidget(
                                                      screenWidth,
                                                      _passwordController,
                                                      _passwordFocusNode,
                                                      context,
                                                      passwordErrorMessage,
                                                      _loginCubit),
                                                  const SizedBox(height: 16.0),
                                                  _forgotPasswordWidget(
                                                      context),
                                                  const SizedBox(height: 20.0),
                                                  ButtonWidget(
                                                      // color: enableSubmitButton
                                                      //     ? shouldShowLoading
                                                      //         ? AppTheme
                                                      //             .courseNameColor
                                                      //         : AppTheme
                                                      //             .buttonBgColour
                                                      //     : AppTheme
                                                      //         .courseNameColor,
                                                      shape:
                                                          RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                                buttonRadius),
                                                      ),
                                                      child: Text(
                                                          SLStrings
                                                              .getTranslatedString(
                                                                  KEY_BUTTON_TEXT),
                                                          style: LMSFonts
                                                              .buttonStyle(16)),
                                                      onPressed: enableSubmitButton
                                                          ? () async =>
                                                              await _submitLogin(
                                                                  context)
                                                          : null),
                                                  const SizedBox(height: 20),
                                                  // _continueWidget(),
                                                  // const SizedBox(height: 20),
                                                  // _socialWidget(),
                                                  // const SizedBox(height: 20),
                                                  _signupOptionsWidget(
                                                      _loginCubit, context),
                                                  const SizedBox(
                                                      height: kIsWeb ? 10 : 30),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                        shouldShowLoading
                            ? Positioned.fill(child: LoadingIndicatorClass())
                            : Container(),
                        Positioned(
                          top: 0,
                          right: 0,
                          left: 0,
                          child: ConnectivityStatusWidget(),
                        ),
                        isTokenExpired && showSessionExpiredAlert
                            ? const Positioned(
                                top: 0,
                                right: 0,
                                left: 0,
                                child: SessionExpiredWidget(),
                              )
                            : Container(),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  void loginValidation(BuildContext context) {
    String _email = _emailController.text;
    final bool emailValid = RegExp(emailValidationRegex).hasMatch(_email);

    if (emailValid == false) {
      context.read<LoginEmailCubit>().setEmailError(
          SLStrings.getTranslatedString(KEY_INVALID_EMAIL), true);
    }
    if (_email.isEmpty) {
      context.read<LoginEmailCubit>().setEmailError(
          SLStrings.getTranslatedString(KEY_EMAIL_FIELD_REQUIRED), true);
    }

    if (_email.isNotEmpty && emailValid == true) {
      context.read<LoginEmailCubit>().setEmailError("", false);
    }
    if (emailValid == true) {
      context.read<LoginEmailCubit>().setEmailError("", false);
    }
    String _password = _passwordController.text;
    if (_password.isEmpty) {
      context.read<LoginEmailCubit>().setPasswordError(
          SLStrings.getTranslatedString(KEY_VALID_PASSWORD), true);
    } else {
      context.read<LoginEmailCubit>().setPasswordError("", false);
    }
  }

  Future<void> _submitLogin(BuildContext context) async {
    LoginEmailCubit _loginCubit = context.read<LoginEmailCubit>();
    FocusScopeNode currentFocus = FocusScope.of(context);
    LoginEmailState state = _loginCubit.state;
    if (!currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }
    _loginCubit.setLoading(true);
    await _loginCubit.loginEmail(
      _emailController.text,
      _passwordController.text,
    );

    state = _loginCubit.state;

    await _handleNavigation(context, state, _loginCubit);
    _loginCubit.setLoading(false);
  }

  _handleNavigation(BuildContext context, LoginEmailState state,
      LoginEmailCubit _loginCubit) async {
    final _orgCubit = BlocProvider.of<OrganizationCubit>(context);

    if (state is LoginEmailSuccess) {
      if (state.userId != null && state.userId != '') {
        if (!kIsWeb) {
          _dbHelper.clearTable();
        }
        SharedPreferences _prefs = await SharedPreferences.getInstance();
        // context.read<LoginEmailCubit>().setLoading(false);
        final _userCubit = BlocProvider.of<UserCubit>(context);
        await _userCubit.getUserInfo(USER_ID);
        // set to true if the user has a valid profile image
        bool isValidProfile = _userCubit.userProfilePic?.isNotEmpty ?? false;

        List<Organization> orgList = state.orgList;
        // _loginCubit.setLoading(false);

        /// set signout status to flase when new user logged in
        _prefs.setBool(PREF_KEY_SIGNOUT, false);
        _prefs.setBool(PREF_KEY_FIRST_LAUNCH, false);

        ///
        /// check if the student is enrolled into any
        /// organizations 1st
        ///
        if (orgList.isNotEmpty) {
          ///
          /// student has organizations
          ///
          if (orgList.length > 1) {
            ///
            /// if the student has more than 1 organization,
            /// let the student choose the org.
            ///
            ORG_ID = '';
            _prefs.setString(PREF_KEY_ORG_ID, '');
          } else {
            ///
            /// if the student has only one org,
            /// take the 1st org id as the student's current org
            ///
            ORG_ID = orgList.first.orgId ?? '';
            _prefs.setString(PREF_KEY_ORG_ID, ORG_ID);
            await _orgCubit.getPrivilegeAccessList();
          }

          // !isValidProfile
          //     ? kIsWeb
          //         ? Beamer.of(context)
          //             .beamToReplacementNamed('/profile-view', data: {
          //             "isFromLogin": true,
          //           })
          //         : appRouter.push(ProfileViewRoute(isFromLogin: true))
          //     :
          state.orgList.length > 1
              ? kIsWeb
                  ? Beamer.of(context)
                      .beamToReplacementNamed('/organization-view')
                  : appRouter.push(OrganizationViewRoute())
              : kIsWeb
                  ? Beamer.of(context).beamToReplacementNamed('/topic-view')
                  : appRouter.push(TopicListViewRoute());
        } else {
          ///
          /// student does not have any organizations
          /// wait for admin to add the student to an organization
          /// navigate to welcome screen and wait till
          /// student recieves the confirmation email
          ///
          kIsWeb
              ? Beamer.of(context).beamToReplacementNamed('/welcome')
              : appRouter.push(const WelcomeViewRoute());
        }
      }
    } else if (state is LoginEmailError) {
      showDialog(
        context: context,
        builder: (context) {
          return MultiActionDialogue(
              alertType: AlertType.warning,
              title: SLStrings.getTranslatedString(KEY_WARNING),
              content: state.error,
              cancelBtnText: SLStrings.getTranslatedString(KEY_CLOSE),
              confirmBtnText: '',
              onCancelTap: () {
                kIsWeb ? Navigator.of(context).pop() : appRouter.pop();
              },
              onContinueTap: () {});
        },
      );
    }
  }

  Widget _emailWidget(
      double screenWidth,
      TextEditingController _emailController,
      FocusNode _emailFocusNode,
      BuildContext context,
      String? error,
      LoginEmailCubit loginCubit) {
    return StreamBuilder<String>(
        stream: loginCubit.email,
        builder: (context, snapshot) {
          errorMessage = snapshot.error != null && snapshot.error != ''
              ? snapshot.error.toString()
              : null;
          if (_emailFocusNode.hasFocus) {
            context.read<LoginEmailCubit>().changeBtnStatus(
                _emailController.text,
                _passwordController.text,
                errorMessage,
                passwordErrorMessage);
          }

          return SLTextFieldWidget(
            focusNode: _emailFocusNode,
            obscureText: false,
            controller: _emailController,
            showSuffixIcon: true,
            errorMsg: _emailFocusNode.hasFocus ? null : errorMessage,
            hint: SLStrings.getTranslatedString(KEY_EMAIL),
            textInputType: TextInputType.emailAddress,
            onTap: () => _refreshWindow(loginCubit),
            onChange: (val) {
              context.read<LoginEmailCubit>().changeBtnStatus(
                  _emailController.text,
                  _passwordController.text,
                  errorMessage,
                  passwordErrorMessage);
            },
            onSufixIconTapped: () {
              // TO DO: clear textfield
              _emailController.clear();
              if (_emailController.text.isNotEmpty) {
                loginCubit.email.drain();
              }
            },
            onSubmitted: (val) {},
          );
        });
  }

  Widget _passwordWidget(
      double screenWidth,
      TextEditingController _passwordController,
      FocusNode _passwordFocusNode,
      BuildContext context,
      String? error,
      LoginEmailCubit loginCubit) {
    return StreamBuilder<String>(
        stream: loginCubit.password,
        builder: (context, snapshot) {
          passwordErrorMessage = snapshot.error != null && snapshot.error != ''
              ? snapshot.error.toString()
              : null;
          if (_passwordFocusNode.hasFocus) {
            context.read<LoginEmailCubit>().changeBtnStatus(
                _emailController.text,
                _passwordController.text,
                errorMessage,
                passwordErrorMessage);
          }
          return SLTextFieldWidget(
            focusNode: _passwordFocusNode,
            obscureText: true,
            showSuffixIcon: true,
            controller: _passwordController,
            errorMsg: _passwordFocusNode.hasFocus ? null : passwordErrorMessage,
            hint: SLStrings.getTranslatedString(KEY_PASSWORD),
            textInputType: TextInputType.visiblePassword,
            isPassword: true,
            onTap: () => _refreshWindow(loginCubit),
            onChange: (val) {
              context.read<LoginEmailCubit>().changeBtnStatus(
                  _emailController.text,
                  _passwordController.text,
                  errorMessage,
                  passwordErrorMessage);
            },
            onSufixIconTapped: () {
              _passwordController.clear();
              if (_passwordController.text.isNotEmpty) {
                loginCubit.password.drain();
              }
            },
            onSubmitted: (val) {},
          );
        });
  }

  Widget _appTitle(String title) {
    return Container(
        alignment: Alignment.center,
        child: Text(title,
            textAlign: TextAlign.center,
            style: LMSFonts.semiBoldFont(
              30,
              AppTheme.whiteColor,
            )));
  }

  Widget _loginTitle(String title) {
    return Text(SLStrings.getTranslatedString(KEY_TITLE),
        textAlign: TextAlign.center, style: LMSFonts.loginSignupTitle());
  }

  Widget _forgotPasswordWidget(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
          onTap: () {
            kIsWeb
                ? Beamer.of(context).beamToNamed('/reset-password')
                : appRouter.push(ResetPasswordViewRoute());
          },
          child: Text(SLStrings.getTranslatedString(KEY_FORGOT_PASSWORD),
              style: LMSFonts.regularFont(16, AppTheme.primaryBlue))),
    );
  }

  Widget _continueWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 25.0),
      child: Row(
        children: [
          Expanded(
            child: Divider(
              thickness: 0.5,
              color: Colors.grey[400],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Text(
              SLStrings.getTranslatedString(KEY_OR_CONTINUE_WITH),
              style:
                  LMSFonts.regularFontWithHeight(14, AppTheme.whiteColor, 1.0),
            ),
          ),
          Expanded(
            child: Divider(
              thickness: 0.5,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  Widget _socialWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        _socialButtonWidget("$ASSETS_PATH/soc.png"),
        const SizedBox(width: 10),
        _socialButtonWidget("$ASSETS_PATH/facebook.png"),
        const SizedBox(width: 10),
        _socialButtonWidget("$ASSETS_PATH/google.png"),
      ],
    );
  }

  Widget _socialButtonWidget(String path) {
    return Image.asset(
      path,
      height: 40,
    );
  }

  Widget _signupOptionsWidget(
      LoginEmailCubit loginCubit, BuildContext context) {
    return Visibility(
      visible: !kIsWeb,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _noAccountText(),
          const SizedBox(width: 8),
          _registerWidget(loginCubit, context)
        ],
      ),
    );
  }

  Widget _noAccountText() {
    return Flexible(
      child: Text(
        SLStrings.getTranslatedString(KEY_NO_ACCOUNT),
        style: LMSFonts.regularFont(14, AppTheme.whiteColor),
      ),
    );
  }

  Widget _registerWidget(LoginEmailCubit loginCubit, BuildContext context) {
    return GestureDetector(
        onTap: () {
          kIsWeb
              ? Beamer.of(context).beamToNamed('/sign-up')
              : appRouter
                  .push(SignUpViewRoute())
                  .then((value) => _refreshWindow(loginCubit));
        },
        child: Container(
            alignment: Alignment.centerRight,
            child: Text(
              SLStrings.getTranslatedString(KEY_SIGNUP),
              style: LMSFonts.regularFont(14, AppTheme.primaryBlue),
            )));
  }

  ///
  /// localization ui
  ///

  Widget _languageSelectionWidget(BuildContext context) {
    return Positioned(
      top: 5,
      right: 5,
      child: _languageOptions(context),
    );
  }

  Widget _languageOptions(BuildContext context) {
    return DropdownButton<String>(
      value: _selectedLanguage,
      onChanged: (String? newValue) {
        if (newValue != null) _onLangaugeChanged(context, newValue);
      },
      items: localizationLangauges.map((String item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(
            item,
            style: LMSFonts.regularFontWithHeight(
                14,
                kIsWeb ? AppTheme.whiteTextColor : AppTheme.headerTextColor,
                1.0),
          ),
        );
      }).toList(),
    );
  }

  void _showLanguageChangedAlert(BuildContext context, String newLang) {
    bool isThereCurrentDialogShowing =
        ModalRoute.of(context)?.isCurrent != true;

    if (context != null &&
        context.routeData.isActive &&
        !isThereCurrentDialogShowing) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return SingleActionDialogue(
            title: '',
            message:
                SLStrings.getTranslatedString(KEY_LANGUAGE_CHANGE_ALERT_EN),
            iconWidget: const Icon(Icons.error, size: 60),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: true,
            handleOkCallback: () {},
          );
        },
      ).then((value) {
        _onLangaugeChanged(context, SLStrings.currentLanguage);
      });
    }
  }

  _onLangaugeChanged(BuildContext context, String newLang) {
    final loginCubit = BlocProvider.of<LoginEmailCubit>(context);
    loginCubit.handleLanguageSelection(context, newLang);
  }

  _refreshWindow(LoginEmailCubit loginCubit) {
    //  _clearTextFieldValidation(loginCubit);

    loginCubit.refreshWindow();
  }
}
