// To parse this JSON data, do
//
//     final pageContent = pageContentFromJson(jsonString);

import 'dart:convert';

PageContent pageContentFromJson(String str) =>
    PageContent.fromJson(json.decode(str));

String pageContentToJson(PageContent data) => json.encode(data.toJson());

class PageContent {
  String? id;
  String? name;
  String? orgId;
  String? status;
  String? content;
  int? display;
  dynamic comments;
  int? revision;
  String? courseId;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic approvedBy;
  dynamic description;
  int? introformat;
  int? legacyfiles;
  dynamic approvedDate;
  int? contentformat;
  dynamic displayoptions;
  dynamic legacyfileslast;

  PageContent({
    this.id,
    this.name,
    this.orgId,
    this.status,
    this.content,
    this.display,
    this.comments,
    this.revision,
    this.courseId,
    this.createdAt,
    this.updatedAt,
    this.approvedBy,
    this.description,
    this.introformat,
    this.legacyfiles,
    this.approvedDate,
    this.contentformat,
    this.displayoptions,
    this.legacyfileslast,
  });

  factory PageContent.fromJson(Map<String, dynamic> json) => PageContent(
        id: json["id"],
        name: json["name"],
        orgId: json["org_id"],
        status: json["status"],
        content: json["content"],
        display: json["display"],
        comments: json["comments"],
        revision: json["revision"],
        courseId: json["course_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        approvedBy: json["approved_by"],
        description: json["description"],
        introformat: json["introformat"],
        legacyfiles: json["legacyfiles"],
        approvedDate: json["approved_date"],
        contentformat: json["contentformat"],
        displayoptions: json["displayoptions"],
        legacyfileslast: json["legacyfileslast"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "org_id": orgId,
        "status": status,
        "content": content,
        "display": display,
        "comments": comments,
        "revision": revision,
        "course_id": courseId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "approved_by": approvedBy,
        "description": description,
        "introformat": introformat,
        "legacyfiles": legacyfiles,
        "approved_date": approvedDate,
        "contentformat": contentformat,
        "displayoptions": displayoptions,
        "legacyfileslast": legacyfileslast,
      };
}
