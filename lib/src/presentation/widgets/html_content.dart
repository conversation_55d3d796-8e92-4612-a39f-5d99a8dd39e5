import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_tex/flutter_tex.dart';

import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';

class HTMLContentTexView extends StatelessWidget {
  final String id;
  final String text;
  final bool enableRippleEffect;
  final Color contentColor;
  final Color backgroundColor;
  final Color borderColor;
  final VoidCallback onSelectionAction;

  const HTMLContentTexView({
    super.key,
    required this.id,
    required this.text,
    required this.enableRippleEffect,
    required this.contentColor,
    required this.backgroundColor,
    required this.borderColor,
    required this.onSelectionAction,
  });

  @override
  Widget build(BuildContext context) {
    return TeXView(
      renderingEngine: const TeXViewRenderingEngine.mathjax(),
      child: TeXViewInkWell(
          rippleEffect: enableRippleEffect,
          onTap: (_) async {},
          child: TeXViewDocument(text,
              style: TeXViewStyle(
                fontStyle: LMSFonts.questionText(),
                contentColor: AppTheme.questionAnsText,
              )),
          id: id,
          style: TeXViewStyle(
            backgroundColor: backgroundColor,
            contentColor: contentColor,
            // margin: const TeXViewMargin.all(5),
            // padding: const TeXViewPadding.all(0),
            // borderRadius: const TeXViewBorderRadius.all(8),
            // border: TeXViewBorder.all(
            //   TeXViewBorderDecoration(
            //       borderColor: borderColor,
            //       borderStyle: TeXViewBorderStyle.solid,
            //       borderWidth: 2),
            // ),
          )),
    );
  }
}

class HTMLContent extends StatelessWidget {
  final String id;
  final String text;
  final bool enableRippleEffect;
  final Color contentColor;
  final Color backgroundColor;
  final Color borderColor;
  final VoidCallback onSelectionAction;

  const HTMLContent({
    super.key,
    required this.id,
    required this.text,
    required this.enableRippleEffect,
    required this.contentColor,
    required this.backgroundColor,
    required this.borderColor,
    required this.onSelectionAction,
  });

  @override
  Widget build(BuildContext context) {
    return Html(data: text);
  }
}
