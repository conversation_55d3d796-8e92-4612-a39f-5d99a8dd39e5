part of 'section_details_cubit.dart';

abstract class SectionDetailsState extends Equatable {
  final String? path;
  final bool doneStatus;
  const SectionDetailsState({this.path, this.doneStatus = false});

  @override
  List<Object> get props => [path!];
}

class SectionDetailsInitial extends SectionDetailsState {
  const SectionDetailsInitial();
}

class SectionDetailsFetched extends SectionDetailsState {
  final SectionDetails? sectionDetailsData;

  const SectionDetailsFetched({required this.sectionDetailsData});

  @override
  List<Object> get props => [sectionDetailsData!];
}

class SectionDetailsError extends SectionDetailsState {
  final String error;
  const SectionDetailsError({required this.error});

  @override
  List<Object> get props => [error];
}

class SectionDetailsListLoading extends SectionDetailsState {
  final bool isDataLoading;
  const SectionDetailsListLoading({required this.isDataLoading});

  @override
  List<Object> get props => [isDataLoading];
}

class SectionDetailschangeStatus extends SectionDetailsState {
  final int index;
  const SectionDetailschangeStatus({required this.index});
  @override
  List<Object> get props => [index];
}

class SectionDetailsExapanding extends SectionDetailsState {
  final int index;
  final bool expand;
  const SectionDetailsExapanding({required this.index, required this.expand});
  @override
  List<Object> get props => [index, expand];
}

//
// Section Details UI Enhancement
//
class StepItemTapState extends SectionDetailsState {
  final int currentResourceStep;
  final int currentFolderStep;
  final int currentFolderResourceStep;
  const StepItemTapState(
      {this.currentFolderStep = 0,
      this.currentFolderResourceStep = 0,
      this.currentResourceStep = 0});

  @override
  List<Object> get props =>
      [currentResourceStep, currentFolderStep, currentFolderResourceStep];
}
// Study Material View
// Fetch Page Content

class PageContentsFetched extends SectionDetailsState {
  final CoursePageResource pageContentData;

  const PageContentsFetched({required this.pageContentData});

  @override
  List<Object> get props => [pageContentData];
}

// Study Material View
// Fetch Page Content

class PageDetailsError extends SectionDetailsState {
  final String error;
  const PageDetailsError({required this.error});

  @override
  List<Object> get props => [error];
}

class PdfViewer extends SectionDetailsState {
  const PdfViewer({super.path});

  @override
  List<Object> get props => [path!];
}

class VideoResourceFetched extends SectionDetailsState {
  final CourseVideoResource courseVideo;
  final List<CheckPoint> checkPoints;
  const VideoResourceFetched(
      {required this.courseVideo, required this.checkPoints});

  @override
  List<Object> get props => [courseVideo, checkPoints];
}

class VideoResourceError extends SectionDetailsState {
  final String error;
  const VideoResourceError({required this.error});

  @override
  List<Object> get props => [error];
}

class FileResourceFetched extends SectionDetailsState {
  final CourseFileResource resourseFile;

  const FileResourceFetched({required this.resourseFile});

  @override
  List<Object> get props => [resourseFile];
}

class FileResourceError extends SectionDetailsState {
  final String error;
  const FileResourceError({required this.error});

  @override
  List<Object> get props => [error];
}

// get course progress
class GetCourseProgressState extends SectionDetailsState {
  final ResourceProgress progress;
  const GetCourseProgressState({required this.progress});

  @override
  List<Object> get props => [progress];
}

class GetCourseProgressFailureState extends SectionDetailsState {
  final String error;
  const GetCourseProgressFailureState({required this.error});

  @override
  List<Object> get props => [error];
}

class RefreshState extends SectionDetailsState {
  const RefreshState();

  @override
  List<Object> get props => [];
}

class SectionDetailschangeDoneStatus extends SectionDetailsState {
  const SectionDetailschangeDoneStatus({super.doneStatus});
  @override
  List<Object> get props => [doneStatus];
}

class SetProgressState extends SectionDetailsState {
  const SetProgressState();

  @override
  List<Object> get props => [];
}

class ResourceFailureState extends SectionDetailsState {
  final String error;
  const ResourceFailureState({required this.error});

  @override
  List<Object> get props => [error];
}

// set resourcse as skipped
class SkipModuleResourceSuccess extends SectionDetailsState {
  SkipModuleResourceSuccess();

  @override
  List<Object> get props => [];
}

class SkipModuleResourceFailure extends SectionDetailsState {
  final String error;
  SkipModuleResourceFailure({required this.error});

  @override
  List<Object> get props => [error];
}
