import 'package:flutter/material.dart';
import 'package:jiffy/jiffy.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../config/app_config/preference_config.dart';
import '../../../config/app_config/sl_config.dart';
import '../localizations/sl_strings.dart';

class LanguageProvider extends ChangeNotifier {
  String currentLanguage;
  Locale? currentLocale = const Locale(SLConfig.DEFAULT_LOCALE_EN);

  LanguageProvider({
    this.currentLanguage = SLConfig.DEFAULT_LOCALE_EN,
    this.currentLocale,
  });

  changeCurrentLanguage(String newLanguage) async {
    currentLanguage = newLanguage;
    currentLocale = Locale(newLanguage);
    SLStrings.currentLanguage = newLanguage;
    SLStrings.currentLocale = currentLocale!;
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    _prefs.setString(PREF_KEY_CURRENT_LANG, currentLanguage);
    Jiffy.setLocale(currentLanguage);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }
}
