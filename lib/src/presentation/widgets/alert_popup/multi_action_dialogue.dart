import 'package:flutter/foundation.dart';

import '../../../config/enums/alert_types.dart';
import '../../../config/themes/app_dynamic_theme.dart';
import '/src/config/themes/app_theme.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/config/themes/theme_colors.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart' as provider;
import '../button_widget.dart';

/// with yes,no buttons(2 buttons)
class MultiActionDialogue extends StatelessWidget {
  final String title;
  final String content;
  final bool showGoback;
  final String cancelBtnText;
  final String confirmBtnText;
  final AlertType alertType;

  final VoidCallback onCancelTap;
  final VoidCallback onContinueTap;
  final bool isFromVideoScreen;

  const MultiActionDialogue({
    super.key,
    required this.title,
    required this.content,
    this.showGoback = false,
    required this.cancelBtnText,
    required this.confirmBtnText,
    required this.alertType,
    required this.onCancelTap,
    required this.onContinueTap,
    this.isFromVideoScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    return dialogueContent(context);
  }

  Widget dialogueContent(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);

    return Material(
      type: MaterialType.transparency,
      child: LayoutBuilder(builder: (context, constraints) {
        double webWidth = 379.6;
        // constraints.maxWidth / 5;
        double mobileWidth = constraints.maxWidth;
        double allowedWidth = constraints.maxWidth > 1200
            ? webWidth
            : constraints.maxWidth >= 500
                ? webWidth
                : mobileWidth;
        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: allowedWidth,
              margin: const EdgeInsets.symmetric(horizontal: 22),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.0),
                  color: Colors.white),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  isFromVideoScreen
                      ? Container()
                      : Text(
                          title,
                          style: LMSFonts.mediumFont(
                              18,
                              alertType == AlertType.confirm
                                  ? AppTheme.primaryColor
                                  : alertType == AlertType.warning
                                      ? appDynamicTheme.toastWarningColor
                                      : alertType == AlertType.error
                                          ? appDynamicTheme.toastErrorColor
                                          : alertType == AlertType.success
                                              ? appDynamicTheme
                                                  .toastSuccessColor
                                              : AppTheme.commentBoxContent,
                              1.1),
                          textAlign: TextAlign.center,
                        ),
                  const SizedBox(height: 20),
                  Text(
                    content,
                    textAlign: TextAlign.center,
                    style: LMSFonts.regularFontWithHeight(
                        16, AppTheme.primaryTextColorBlack, 1.3),
                  ),
                  const SizedBox(height: 30),
                  _btnWidgets(screenWidth, appDynamicTheme),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _btnWidgets(double screenWidth, AppDynamicTheme? appDynamicTheme) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: kIsWeb ? 20 : 0),
        child: Row(
          mainAxisAlignment: cancelBtnText != '' && confirmBtnText != ''
              ? MainAxisAlignment.spaceBetween
              : MainAxisAlignment.center,
          children: [
            cancelBtnText != ''
                ? _cancelBtn(screenWidth, appDynamicTheme)
                : const SizedBox(),
            kIsWeb && cancelBtnText.isNotEmpty && confirmBtnText.isNotEmpty
                ? const SizedBox(width: 30)
                : Container(),
            confirmBtnText != '' ? _confirmBtn(screenWidth) : const SizedBox(),
          ],
        ),
      ),
    );
  }

  Widget _cancelBtn(double screenWidth, AppDynamicTheme? appDynamicTheme) {
    return Flexible(
      child: Container(
        // margin: const EdgeInsets.symmetric(horizontal: kIsWeb ? 50 : 0),
        child: ButtonWidget(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
            side: BorderSide(
              color: appDynamicTheme?.buttonDismissBgColor ??
                  AppTheme.buttonBgColour,
            ),
          ),
          color: AppTheme.whiteColor,
          child: Text(
            cancelBtnText,
            textAlign: TextAlign.center,
            style: LMSFonts.mediumFont(
                14,
                appDynamicTheme?.buttonDismissTextColor ??
                    AppTheme.buttonBgColour,
                1.0),
          ),
          minimumSize: Size(screenWidth / 3, 38),
          onPressed: () {
            onCancelTap();
          },
        ),
      ),
    );
  }

  Widget _confirmBtn(double screenWidth) {
    return Flexible(
      child: Container(
        // margin: const EdgeInsets.symmetric(horizontal: kIsWeb ? 50 : 0),
        child: ButtonWidget(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Text(
            confirmBtnText,
            textAlign: TextAlign.center,
            style: LMSFonts.mediumFont(14, AppTheme.whiteColor, 1.0),
          ),
          minimumSize: Size(screenWidth / 3, 38),
          onPressed: () {
            onContinueTap();
          },
        ),
      ),
    );
  }
}
