String emailValidationRegex =
    r'^[^@\s]+@[a-zA-Z]+(\.{1}[a-zA-Z]+(\.{0,1}[a-zA-Z]+)?)$';
String phoneNumValidationRegex = r'^\+?[1-9]\d{9}$';

// Regular expression patterns for various YouTube URL formats
final youtubeURLPatterns = [
  RegExp(
      r"^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|embed)\/|watch\?(?:(?=.*v=([^#\&\?]+))|.*)(?:\S+)?))(?:(?=.*[^\w\s])\S)*$"),
  RegExp(
      r'^https://www\.youtube\.com/watch\?\app\=\desktop\v=([a-zA-Z0-9_-]+)'),
  RegExp(r'^https://www\.youtube\.com/watch\?v=([a-zA-Z0-9_-]+)'),
  RegExp(r'^https://youtu\.be/([a-zA-Z0-9_-]+)'),
  RegExp(r'^https://www\.youtube\.com/embed/([a-zA-Z0-9_-]+)'),
  RegExp(r'^https://www\.youtube\.com/v/([a-zA-Z0-9_-]+)'),
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtube\.com/watch\?v=)([\w-]+)$'), //Standard Video URL - format https://www.youtube.com/watch?v=VIDEO_ID
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtu\.be/)([\w-]+)$'), //Shortened Video URL - format https://youtu.be/VIDEO_ID
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtube\.com/channel/)([\w-]+)$'), //Channel URL (ID-based) - format https://www.youtube.com/channel/CHANNEL_ID
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtube\.com/c/)([\w-]+)$'), //Custom Channel URL - format https://www.youtube.com/c/ChannelName
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtube\.com/embed/)([\w-]+)$'), //Embed Video URL - format https://www.youtube.com/embed/VIDEO_ID
  googleSearchUrlRegex, // Google Search URL - format https://www.google.com/search?q=vid:VIDEO_ID || https://www.google.com/search?sca_esv=5d466d0b992978df&rlz=1C1CHZN_enIN1150IN1150&sxsrf=AHTn8zokbWxE8M77nw5QSEaxkWB7EyJqPg:1740584888719&q=indian+history+introduction+psc+class&udm=7&fbs=ABzOT_CWdhQLP1FcmU5B0fn3xuWp6IcynRBrzjy_vjxR0KoDMuRqhRxkdGgKzs52-1TZixdYkeUgOjz9KxcNKskor3Wf2rIxNx6KOIGfUnDaMqwoFCjfvwrqUbp2v4j-H0s4UGCDC7JGNxEJCujrhN0fcoPnSehFtQ6Ot5y5xitVmIPkZ7qJ1hSnz90xLzzxZjGVY45h2nvqGrqR_ikWuqpxX-f9gWUARw&sa=X&ved=2ahUKEwixjvn_1-GLAxUVUfUHHcGyCdgQtKgLegQIFhAB&biw=1192&bih=613&dpr=1#fpstate=ive&vld=cid:1dd1de23,vid:eGr161tAgEM,st:0
];

RegExp googleSearchUrlRegex = RegExp(
  r'^https:\/\/www\.google\.com\/search\?.*q=[^&]+.*$',
  caseSensitive: false,
);

String inputNameRegex = '[a-z A-Z]';
