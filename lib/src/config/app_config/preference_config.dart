///
/// stores the keys for data stored in the shared prefrence
///

// ignore_for_file: constant_identifier_names

const String PREF_KEY_ACCESS_TOKEN = 'token';
const String PREF_KEY_USER_ID = 'userId';
const String PREF_KEY_ORG_ID = 'orgId';
const String PREF_KEY_TOPIC_ID = 'topicId';
const String PREF_KEY_TOPIC_NAME = 'topicValue';
const String PREF_KEY_COURSE_ID = 'courseID';
const String PREF_KEY_COURSE_NAME = 'courseName';

const String PREF_KEY_FIRST_LAUNCH = 'first_launch';

const String PREF_KEY_SHOW_WELCOME_SCREEN = 'showWelcomeScreen';
const String PREF_KEY_CURRENT_LANG = 'current_lang';
const String PREF_KEY_REFRESH_TOKEN = 'refreshToken';
const String PREF_KEY_FCM_TOKEN = 'fcm_token';

const String PREF_KEY_NETWORK_STATUS = 'networkStatus';

const String PREF_KEY_SIGNOUT = 'signout';
const String PREF_KEY_NAVIGATING_AFTER_LOGOUT = 'isNavigatingAfterLogout';

const String PREF_KEY_FROM_SECTION_DETAILS = 'isFromSectionDetails';
const String PREF_KEY_FROM_SUBJECT_TAB_VIEW = 'isFromSubjectTabView';

const String PREF_KEY_QUIZ_ATTEMPT_ID = 'quiz_attempt_id';

const String PREF_KEY_PREVILEGE_ACCESS_LIST = 'privilege_access';

const String PREF_KEY_SUBSCRIBED_PLANS_LIST = 'subscribed_plans';
