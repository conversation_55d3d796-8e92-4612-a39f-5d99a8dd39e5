// To parse this JSON data, do
//
//     final subscriptionPlan = subscriptionPlanFromJson(jsonString);

import 'package:SmartLearn/src/utils/constants/helper.dart';
import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'subscription_plan.g.dart';

SubscriptionPlanResult subscriptionPlanResultFromJson(String str) =>
    SubscriptionPlanResult.fromJson(json.decode(str));

String subscriptionPlanResultToJson(SubscriptionPlanResult data) =>
    json.encode(data.toJson());

// SubscriptionPlan subscriptionPlanFromJson(String str) =>
//     SubscriptionPlan.fromJson(json.decode(str));

// String subscriptionPlanToJson(SubscriptionPlan data) =>
//     json.encode(data.toJson());

@JsonSerializable()
class SubscriptionPlanResult {
  @JsonKey(name: "result")
  List<SubscriptionPlan> result;
  @JsonKey(name: "status")
  String status;

  SubscriptionPlanResult({
    required this.result,
    required this.status,
  });

  factory SubscriptionPlanResult.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionPlanResultFromJson(json);

  Map<String, dynamic> toJson() => _$SubscriptionPlanResultToJson(this);
}

@JsonSerializable()
class SubscriptionPlan {
  @JsonKey(name: "id")
  String? id;
  @JsonKey(name: "org_id")
  String? orgId;
  @JsonKey(fromJson: SubscriptionPlan._uppercaseFirstName)
  String? name;
  @JsonKey(name: "description")
  String? description;
  @JsonKey(name: "subscription_type")
  SubscriptionType subscriptionType;
  @JsonKey(name: "target_id")
  dynamic targetId;
  @JsonKey(name: "price")
  double? price;
  @JsonKey(name: "currency")
  Currency? currency;
  @JsonKey(name: "subscription_frequency")
  SubscriptionFrequencyType subscriptionFrequencyType;
  @JsonKey(name: "valid_from")
  DateTime? validFrom;
  @JsonKey(name: "valid_to")
  DateTime? validTo;
  @JsonKey(name: "subscription_plan_status")
  SubscriptionPlanStatus subscriptionPlanStatus;
  @JsonKey(name: "is_user_subscribed")
  bool isUserSubscribed;
  @JsonKey(name: "subscription_usage_status")
  PlanStatusEnum? userSubscriptionStatus;
  @JsonKey(name: "user_subscription_start_date")
  DateTime? userSubscriptionStartDate;
  @JsonKey(name: "user_subscription_end_date")
  DateTime? userSubscriptionEndDate;

  static String? _uppercaseFirstName(String? name) =>
      convertToCamelCase(name ?? '');

  SubscriptionPlan({
    required this.id,
    required this.orgId,
    required this.name,
    required this.description,
    required this.subscriptionType,
    required this.targetId,
    required this.price,
    required this.currency,
    required this.subscriptionFrequencyType,
    required this.validFrom,
    required this.validTo,
    required this.subscriptionPlanStatus,
    required this.isUserSubscribed,
    required this.userSubscriptionStatus,
    required this.userSubscriptionStartDate,
    required this.userSubscriptionEndDate,
  });

  factory SubscriptionPlan.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionPlanFromJson(json);

  Map<String, dynamic> toJson() => _$SubscriptionPlanToJson(this);
}

final planStatusValues = EnumValues({
  "pending": PlanStatusEnum.PENDING.toString().toLowerCase(),
  "approved": PlanStatusEnum.APPROVED.toString().toLowerCase(),
  "none": PlanStatusEnum.UNSUBSCRIBED.toString().toLowerCase(),
});

enum PlanStatusEnum {
  @JsonValue('none')
  UNSUBSCRIBED,
  @JsonValue("pending")
  PENDING,
  @JsonValue("approved")
  APPROVED
}

enum Currency {
  @JsonValue("inr")
  CURRENCY_INR,
  @JsonValue("INR")
  INR
}

final currencyValues =
    EnumValues({"inr": Currency.CURRENCY_INR, "INR": Currency.INR});

enum SubscriptionFrequencyType {
  @JsonValue("annual")
  ANNUAL,
  @JsonValue("custom")
  CUSTOM,
  @JsonValue("monthly")
  MONTHLY
}

final subscriptionFrequencyTypeValues = EnumValues({
  "annual": SubscriptionFrequencyType.ANNUAL,
  "custom": SubscriptionFrequencyType.CUSTOM,
  "monthly": SubscriptionFrequencyType.MONTHLY
});

enum SubscriptionPlanStatus {
  @JsonValue("active")
  ACTIVE,
  @JsonValue("inactive")
  INACTIVE
}

final subscriptionPlanStatusValues =
    EnumValues({"active": SubscriptionPlanStatus.ACTIVE});

enum SubscriptionType {
  @JsonValue("course_based")
  COURSE_BASED,
  @JsonValue("resource_based")
  RESOURCE_BASED,
  @JsonValue("time_based")
  TIME_BASED
}

final subscriptionTypeValues = EnumValues({
  "course_based": SubscriptionType.COURSE_BASED,
  "resource_based": SubscriptionType.RESOURCE_BASED,
  "time_based": SubscriptionType.TIME_BASED
});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
