// import 'dart:html';

import 'dart:io';

import 'package:SmartLearn/main.dart';
import 'package:SmartLearn/src/domain/services/localizations/resources/de.dart';
import 'package:flutter/material.dart';

import 'package:provider/provider.dart';

import '../../../config/app_config/sl_config.dart';
import '../provider/language_provider.dart';
import 'resources/en.dart';

///
/// This class is used to access a string
///
class SLStrings {
  // current language of app, defaults to english
  static String currentLanguage = "en";

  static Locale currentLocale = Locale(Platform.localeName);
  static ValueNotifier? localeStream;

  // returns the translated string based on current language of device
  // if no translation is found for the key, returns the key
  static String getTranslatedString(String key) {
    BuildContext? currentContext = scaffoldMessengerKey.currentContext;
    if (currentContext != null) {
      currentLocale = View.of(currentContext).platformDispatcher.locale;
    }
    // NavigationService.navigatorKey.currentContext;
    currentLanguage = currentContext != null
        ? Provider.of<LanguageProvider>(currentContext, listen: false)
            .currentLanguage
        : currentLanguage;
    String? result = key;
    localeStream?.value = currentLanguage;
    try {
      if (currentLanguage == SLConfig.LOCALE_DE) {
        result = SLGermanStrings.GERMAN_STRINGS[key];
      } else {
        result = SLEnglishStrings.ENGLISH_STRINGS[key];
      }
    } on Exception catch (e) {
      debugPrint('[SLStrings][getString]: $e');
      return key;
    }
    result ??= key;
    return result;
  }

  static String getCurrentLangauge(List<Locale>? locale) {
    if (locale != null && locale.isNotEmpty) {
      if (locale[0].languageCode.contains(SLConfig.LOCALE_DE)) {
        SLStrings.currentLanguage = SLConfig.LOCALE_DE;
        SLStrings.currentLocale = const Locale(SLConfig.LOCALE_DE);
      } else {
        SLStrings.currentLanguage = SLConfig.DEFAULT_LOCALE_EN;
        SLStrings.currentLocale = const Locale(SLConfig.DEFAULT_LOCALE_EN);
      }
    }
    return SLStrings.currentLanguage;
  }
}
