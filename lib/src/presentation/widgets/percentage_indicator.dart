import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../config/themes/theme_colors.dart';

class PercentageIndicator extends StatelessWidget {
  final double progress;
  final String progressvalue;
  final BoxConstraints? constraints;
  final double? radius;
  final double? lineWidth;

  final Color backgroundColor;

  const PercentageIndicator(
      {super.key,
      required this.progress,
      required this.progressvalue,
      this.constraints,
      this.lineWidth = 2.0,
      this.radius = 5.0,
      this.backgroundColor = AppTheme.ternaryBlue});

  @override
  Widget build(BuildContext context) {
    return CircularPercentIndicator(
        radius: constraints == null ? radius! : constraints!.maxWidth / 25,
        lineWidth:
            constraints == null ? lineWidth! : (constraints!.maxWidth / 25) / 5,
        animation: true,
        percent: progress ?? 0,
        center: AutoSizeText(
          progressvalue + "%",
          maxLines: 1,
          minFontSize: 5,
          style: LMSFonts.mediumFont(12, LmsColors.black, 1),
        ),
        backgroundColor: backgroundColor,
        circularStrokeCap: CircularStrokeCap.round,
        progressColor: AppTheme.primaryBlue,
        fillColor: LmsColors.white);
  }
}
