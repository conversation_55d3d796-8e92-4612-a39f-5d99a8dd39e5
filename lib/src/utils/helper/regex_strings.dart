String emailValidationRegex =
    r'^[^@\s]+@[a-zA-Z]+(\.{1}[a-zA-Z]+(\.{0,1}[a-zA-Z]+)?)$';
String phoneNumValidationRegex = r'^\+?[1-9]\d{9}$';

// Regular expression patterns for various YouTube URL formats
final youtubeURLPatterns = [
  RegExp(
      r"^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|embed)\/|watch\?(?:(?=.*v=([^#\&\?]+))|.*)(?:\S+)?))(?:(?=.*[^\w\s])\S)*$"),
  RegExp(
      r'^https://www\.youtube\.com/watch\?\app\=\desktop\v=([a-zA-Z0-9_-]+)'),
  RegExp(r'^https://www\.youtube\.com/watch\?v=([a-zA-Z0-9_-]+)'),
  RegExp(r'^https://youtu\.be/([a-zA-Z0-9_-]+)'),
  RegExp(r'^https://www\.youtube\.com/embed/([a-zA-Z0-9_-]+)'),
  RegExp(r'^https://www\.youtube\.com/v/([a-zA-Z0-9_-]+)'),
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtube\.com/watch\?v=)([\w-]+)$'), //Standard Video URL - format https://www.youtube.com/watch?v=VIDEO_ID
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtu\.be/)([\w-]+)$'), //Shortened Video URL - format https://youtu.be/VIDEO_ID
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtube\.com/channel/)([\w-]+)$'), //Channel URL (ID-based) - format https://www.youtube.com/channel/CHANNEL_ID
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtube\.com/c/)([\w-]+)$'), //Custom Channel URL - format https://www.youtube.com/c/ChannelName
  RegExp(
      r'^(?:https?://)?(?:www\.)?(?:youtube\.com/embed/)([\w-]+)$'), //Embed Video URL - format https://www.youtube.com/embed/VIDEO_ID
];

String inputNameRegex = '[a-z A-Z]';
