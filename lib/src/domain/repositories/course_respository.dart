import '../models/category/user_category.dart';
import '../models/course_details.dart';
import '../models/resource_comment.dart';

abstract class CourseRepository {
  Future<List<CourseDetails>> fetchCourseDetailsInfo(
      String courseId, String orgId, String userId);
  Future<List<CurrentAffairs>> fetchMoreCurrentAffairs();
  Future<List<ResourseComment>> fetchCommentsForId(
      String instanceId, String activityType);
  Future<dynamic> uploadCommentsForId(Map<String, dynamic> jsonBody);
  Future<dynamic> setCourseProgress(
      String courseId, String instance, Map<String, dynamic> duration);

  Future<dynamic> setSkippedResource(Map<String, dynamic> jsonReq);
  Future<List<ResourseComment>> fetchLikeCount(String instanceId);
  Future<dynamic> setLikeCount(Map<String, dynamic> jsonBody);
}
