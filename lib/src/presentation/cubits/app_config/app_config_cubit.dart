import '/src/config/app_config/app_branding.dart';
import '/src/config/themes/app_dynamic_theme.dart';
import '/src/domain/repositories/app_config_repository.dart';
import '/src/utils/resources/shared_preference_utils.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../../domain/models/branding_config/branding_config.dart';
import '../../../utils/constants/helper.dart';
import '../../../utils/constants/strings.dart';
import 'package:provider/provider.dart' as provider;

part 'app_config_state.dart';

class AppConfigCubit extends Cubit<AppConfigState> {
  AppConfigCubit(this._appConfigRepository) : super(AppConfigInitial());

  final AppConfigRepository _appConfigRepository;

  /// Fetches the app branding configuration.
  fetchAppBrandingConfig(BuildContext context) async {
    emit(AppConfigLoading());
    try {
      if (ORG_ID.isEmpty) {
        return;
      }

      Map<String, dynamic> reqJson = {'org_id': ORG_ID};

      // Fetch the branding config here
      var brandingConfig =
          await _appConfigRepository.fetchAppBrandingConfig(reqJson);

      if (brandingConfig == null) {
        const brandingJson = AppBranding.lightTheme;
        brandingConfig = BrandingConfig.fromJson(brandingJson);
      }
      // provider.Provider.of<AppDynamicTheme>(context, listen: false)
      //     .updateFromBrandingConfig(brandingConfig);
      emit(AppConfigLoadedWithBranding(brandingConfig));
    } catch (e) {
      emit(AppConfigError(e.toString()));
    }
  }

  Future setUserActivityLog(Map<String, dynamic> reqJson) async {
    final sharedPref = SharedPreferencesUtils.instance;

    final _deviceInfoObj = await fetchDeviceInfo();
    final userToken = await sharedPref.getUserAccessToken();

    final Map<String, dynamic> activityLog = {
      'user_id': USER_ID,
      'org_id': ORG_ID,
      'activity_type': reqJson['activity_type'] as String,
      'screen_name': reqJson['screen_name'] as String,
      'action_details': reqJson['action_details'] as String,
      'target_id': reqJson['target_id'] as String,
      'session_id': userToken,
      'action_comment': reqJson['action_comment'] as String,
      'user_agent': _deviceInfoObj?.userAgent ?? "",
      'log_source': 'Mobile',
      'log_result': reqJson['log_result'] as String,
    };
    try {
      final response =
          await _appConfigRepository.setUserActivityLog(activityLog);
      if (response == 'success') {
        emit(const UserLogSubmitted());
        return true;
      } else {
        emit(const AppConfigError('Failed to submit user activity log'));
        return false;
      }
    } catch (e) {
      emit(AppConfigError(e.toString()));
      return false;
    }
  }
}
