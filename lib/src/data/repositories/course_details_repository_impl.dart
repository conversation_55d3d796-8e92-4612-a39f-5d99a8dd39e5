import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../config/app_config/api_config.dart';
import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_dashboard/courseDetailUserStats.dart';
import '../../domain/models/course_dashboard/course_assignments.dart';
import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../domain/models/course_dashboard/course_page_resource.dart';
import '../../domain/models/course_dashboard/course_video_resource.dart';
import '../../domain/models/course_dashboard/examPerformanceCatwise.dart';
import '../../domain/models/course_dashboard/userCourseProgress.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/repositories/course_details_repository.dart';

class CourseDetailsRepositoryImpl extends CourseDetailsRepository {
  CourseDetailsRepositoryImpl();

  @override
  Future<List<CourseDetailUserStatiModel>> fetchCoursewiseStats(
      Map<String, dynamic> jsonReq) async {
    List<CourseDetailUserStatiModel> allCourseInfo = [];

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_COURSEWISE_STATS, params: jsonReq);

    List<dynamic> responseData = response ?? [];

    allCourseInfo = List<CourseDetailUserStatiModel>.from(
            responseData.map((x) => CourseDetailUserStatiModel.fromJson(x)))
        .toList();
    return allCourseInfo;
  }

  @override
  Future<UserCourseProgress> fetchGraphicalProgress(
      Map<String, dynamic> jsonReq) async {
    UserCourseProgress userCourseProgressInfo;

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_USER_COURSE_PROGRESS, params: jsonReq);

    dynamic responseData = response ?? {};

    userCourseProgressInfo = UserCourseProgress.fromJson(responseData);
    return userCourseProgressInfo;
  }

  @override
  Future<ExamPerformanceCatwise> fetchExamPerformancecatWise(
      Map<String, dynamic> jsonReq) async {
    ExamPerformanceCatwise examPerformanceCatwise;

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_EXAM_PERFORMANCE_CATWISE, params: jsonReq);

    Map<String, dynamic> responseData = response ?? {};

    examPerformanceCatwise = ExamPerformanceCatwise.fromJson(responseData);
    return examPerformanceCatwise;
  }

  @override
  Future<List<CourseAssignments>> fetchCourseAssignments(
      Map<String, dynamic> jsonReq) async {
    List<CourseAssignments> courseAssignments = [];
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_COURSE_ASSIGNMENTS, params: jsonReq);

    if (response == null) {
      return [];
    } else {
      Map<String, dynamic> responseJson = response;
      if (responseJson.containsKey("result")) {
        List<dynamic> responseData = responseJson['result'] ?? [];

        courseAssignments = List<CourseAssignments>.from(
            responseData.map((x) => CourseAssignments.fromJson(x))).toList();
        courseAssignments
            .sort(((a, b) => a.moduleOrder.compareTo(b.moduleOrder)));
      }
    }

    return courseAssignments;
  }

  @override
  Future<List<CourseVideoResource>> fetchCourseVideoResource(
      Map<String, dynamic> jsonReq) async {
    List<CourseVideoResource> courseVideoResInfo = [];

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_VIDEO_RESOURCE_INFO, params: jsonReq);

    courseVideoResInfo = List<CourseVideoResource>.from(
        response.map((x) => CourseVideoResource.fromJson(x)));

    return courseVideoResInfo;
  }

  @override
  Future<CheckPointData?> fetchVideoCheckPointInfo(
      Map<String, dynamic> jsonReq) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_RESOURCE_CHECKPOINT_DATA, params: jsonReq);
    if (response != null) {
      CheckPointData checkPointData = CheckPointData.fromJson(response);
      return checkPointData;
    }
    return response;
  }

  @override
  Future<List<CourseFileResource>> fetchCourseFileResource(
      Map<String, dynamic> jsonReq) async {
    List<CourseFileResource> courseFileResInfo = [];

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_FILE_RESOURCE_INFO, params: jsonReq);

    courseFileResInfo = List<CourseFileResource>.from(
        response.map((x) => CourseFileResource.fromJson(x)));

    return courseFileResInfo;
  }

  @override
  Future<CoursePageResource?> fetchCoursePageResource(
      Map<String, dynamic> jsonReq) async {
    CoursePageResource coursePageResInfo;

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_PAGE_RESOURCE_INFO, params: jsonReq);

    coursePageResInfo = CoursePageResource.fromJson(response);

    return coursePageResInfo;
  }

  @override
  Future<ResourceProgress?> getVideoResProgress(
      Map<String, dynamic> jsonReq) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_COURSE_PROGRESS_FN, params: jsonReq);

    if (response != null) {
      List<dynamic> json = response;
      ResourceProgress courseProgress = ResourceProgress.fromJson(json.first);
      return courseProgress;
    }
    return null;
  }

  @override
  Future<dynamic> setFileProgress(Map<String, dynamic> jsonReq) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.SET_FILE_RES_PROGRESS, params: jsonReq);

    if (response != null) {
      Map<String, dynamic> responseData = response;
      return responseData;
    }
    return null;
  }
}
