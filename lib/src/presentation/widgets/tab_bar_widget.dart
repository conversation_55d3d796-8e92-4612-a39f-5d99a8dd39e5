import '/src/presentation/cubits/exam/exam_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '/src/domain/models/tabbar_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';

class TabbarWidget extends HookWidget {
  final List<TabbarItem> tabBarItems;
  final TabController tabController;

  const TabbarWidget(
      {super.key, required this.tabBarItems, required this.tabController});

  @override
  Widget build(BuildContext context) {
    final ValueNotifier<int> _currentIndex = useState(tabController.index);
    final _examCubit = BlocProvider.of<ExamCubit>(context);
    useEffect(() {
      tabController.addListener(() {
        _currentIndex.value = tabController.index;
        _examCubit.updateSelectedTab(tabController.index);
      });
      return () {
        tabController.removeListener(() {});
      };
    }, []);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 25),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        child: TabBar(
          controller: tabController,
          labelPadding: const EdgeInsets.symmetric(horizontal: 5),
          tabs: tabBarItems.map((item) {
            return _tabItemWidget(item, _currentIndex.value);
          }).toList(),
          indicator: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(
            color: AppTheme.primaryBlue,
          ))),
          unselectedLabelColor: AppTheme.unselectedTabColor,
          indicatorSize: TabBarIndicatorSize.label,
        ),
      ),
    );
  }

  Widget _tabItemWidget(TabbarItem item, int index) {
    return Column(
      children: [
        Image.asset(
          item.icon!,
          height: 30,
          width: 30,
          color: index == item.id
              ? AppTheme.primaryBlue
              : AppTheme.unselectedTabColor,
        ),
        Text(item.label,
            textAlign: TextAlign.center,
            style: tabController.index == item.id
                ? LMSFonts.selectedTabItem()
                : LMSFonts.unSelectedTabItem()),
      ],
    );
  }
}
