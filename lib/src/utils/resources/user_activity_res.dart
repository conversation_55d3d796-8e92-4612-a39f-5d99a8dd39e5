import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/enums/resource_activity_status.dart';
import '../../presentation/cubits/app_config/app_config_cubit.dart';

class UserActivityLog {
  static final UserActivityLog instance = UserActivityLog._internal();

  UserActivityLog._internal();

  ///
  /// Sets user activity log for the chosen resource
  ///
  Future<void> setResourceActivityLog(
      {required BuildContext context,
      required String resourceType,
      required ResourceActivityStatus status,
      required double currentDuration,
      required String id,
      required String result}) async {
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);
    final isStarted = status == ResourceActivityStatus.started;
    final isInProgress = status == ResourceActivityStatus.inProgress;
    final now = DateTime.now();

    final reqJson = {
      'activity_type': 'Course_Resource',
      'screen_name': 'Course $resourceType view',
      'action_details': isStarted
          ? 'Course $resourceType started'
          : isInProgress
              ? 'Course $resourceType paused at duration: $currentDuration'
              : 'Course $resourceType ended',
      'target_id': id,
      'action_comment': isStarted
          ? 'Course $resourceType started at $now'
          : isInProgress
              ? 'Course $resourceType paused at $now'
              : 'Course $resourceType ended at $now',
      'log_result': result,
    };
    await _appConfigCubit.setUserActivityLog(reqJson);
  }

  ///
  /// sets activity log for resource
  /// when skip event occurs
  ///
  setResSkipActivityLog(
      {required BuildContext context,
      required String screen,
      required String resourceType,
      required String id,
      required String result,
      required String responseStatus}) async {
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    final reqJson = {
      'activity_type': 'Course_Resource',
      'screen_name': screen,
      'action_details': 'Course $resourceType skipped',
      'target_id': id,
      'action_comment':
          'Course $resourceType $responseStatus at ${DateTime.now()}',
      'log_result': result,
    };
    await _appConfigCubit.setUserActivityLog(reqJson);
  }

  ///
  /// sets activity log for resource
  /// progress update
  ///
  setResProgressActivityLog(
      {required BuildContext context,
      required String screen,
      required String resourceType,
      required String id,
      required String result,
      required String responseStatus}) async {
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    final reqJson = {
      'activity_type': 'Course_Resource',
      'screen_name': screen,
      'action_details': 'Course $resourceType progress update',
      'target_id': id,
      'action_comment':
          'Course $resourceType $responseStatus at ${DateTime.now()}',
      'log_result': result,
    };
    await _appConfigCubit.setUserActivityLog(reqJson);
  }
}
