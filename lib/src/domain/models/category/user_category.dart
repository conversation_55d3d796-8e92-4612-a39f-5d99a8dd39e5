// To parse this JSON data, do
//
//     final category = categoryFromJson(jsonString);

import 'dart:convert';

String categoryToJson(List<UserCategory> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

///
/// stores category by hierarchy and their respective courses
///

class UserCategory {
  String id;
  String name;
  String orgId;
  List<Course> courses;
  List<UserCategory> subCategory;
  int childIndex;
  bool isPremium;
  String description;
  PublishStatus publishStatus;
  bool isExpanded;

  UserCategory({
    required this.id,
    required this.name,
    required this.orgId,
    required this.courses,
    required this.subCategory,
    required this.childIndex,
    required this.isPremium,
    required this.description,
    required this.publishStatus,
    required this.isExpanded,
  });

  factory UserCategory.fromJson(Map<String, dynamic> json, int childIndex) =>
      UserCategory(
          id: json["id"],
          name: json["name"],
          orgId: json["org_id"],
          courses:
              List<Course>.from(json["courses"].map((x) => Course.fromJson(x))),
          subCategory: List<UserCategory>.from(json["children"]
              .map((x) => UserCategory.fromJson(x, childIndex + 1))),
          childIndex: childIndex,
          isPremium: json["is_premium"],
          description: json["description"],
          publishStatus: publishStatusValues.map[json["publish_status"]] ??
              PublishStatus.DRAFT,
          isExpanded: false);

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "org_id": orgId,
        "courses": List<dynamic>.from(courses.map((x) => x.toJson())),
        "children": List<dynamic>.from(subCategory.map((x) => x.toJson())),
        "is_premium": isPremium,
        "description": description,
        "publish_status": publishStatusValues.reverse[publishStatus],
      };
}

class Course {
  String courseId;
  String courseName;
  String courseFullName;
  String courseShortName;

  Course({
    required this.courseId,
    required this.courseName,
    required this.courseFullName,
    required this.courseShortName,
  });

  factory Course.fromJson(Map<String, dynamic> json) {
    String fullName =
        json["full_name"] ?? json["short_name"] ?? json["course_name"] ?? '';

    return Course(
      courseId: json["course_id"],
      courseName: json["course_name"],
      courseFullName: fullName,
      courseShortName: json["short_name"] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        "course_id": courseId,
        "course_name": courseName,
      };
}

enum PublishStatus { PUBLISHED, DRAFT }

final publishStatusValues = EnumValues(
    {"Published": PublishStatus.PUBLISHED, "Draft": PublishStatus.DRAFT});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
