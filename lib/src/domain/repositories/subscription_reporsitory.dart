import '../models/purchase_history/purchase_history.dart';
import '../models/subscription_plan/plan_course_result.dart';
import '/src/domain/models/subscription_plan/subscription_plan.dart';

abstract class SubscriptionRepository {
  Future<List<SubscriptionPlan>> getSubscriptionPlans(
      Map<String, dynamic> reqBody);
  Future<bool> submitSelectedPlan(Map<String, dynamic> reqBody);
  Future<bool> getSubscriptionPlanStatus(Map<String, dynamic> reqBody);
  Future<List<PlanCourse>> getCourseForSelectedPlan(
      Map<String, dynamic> reqBody);
  Future<List<PurchaseHistoryResult>> getUserPurchaseHistory(
      Map<String, dynamic> reqBody);
}
