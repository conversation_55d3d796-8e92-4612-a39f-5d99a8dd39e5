import 'dart:io';


import '../models/organization.dart';
import '../models/profile.dart';

abstract class UserRepository {
  Future<Profile> getUserInfo(String userId);

  Future<dynamic> updateUserInfo(String userId, String firstName,
      String phoneNum, String lastName, String avatarUrl, File? file);
  Future<List<Organization>> getUserOrganizations(String userId);
  Future<dynamic> userSignout();
  Future<dynamic> checkTokenAndRefresh();
}
