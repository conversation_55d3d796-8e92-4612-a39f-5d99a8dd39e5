import '/src/config/router/custom_transition.dart';

import '/src/config/app_config/sl_config.dart';
import 'package:flutter/material.dart';

abstract class AppTheme {
  static const primaryColor = Color.fromRGBO(0, 199, 214, 1);
  static const bgColor = primaryAppColor;
  static const secondaryColor = Color(0xFFDEB041);
  static const Color primaryBlue = primaryColor;
  static const Color primaryRed = Color.fromRGBO(255, 108, 118, 1);

  static const pitchBlack =
      Color.fromRGBO(28, 51, 88, 1.0); // Color.fromRGBO(7, 7, 7, 1.0);

  static const Color iconColor = Color(0xFF007791);

  static const Color primaryTextColorBlack = Color.fromRGBO(7, 7, 7, 1.0);

  static const Color systemUIOverlayColor = Colors.transparent;
  static const Color _lightPrimaryColor = Color(0xFF546E7A);
  static const Color _lightPrimaryVariantColor = Color(0xFF546E7A);
  static const Color _lightSecondaryColor = Colors.green;
  static const Color _lightOnPrimaryColor = Colors.black;

  static const Color _darkPrimaryColor = Colors.white24;
  static const Color _darkPrimaryVariantColor = Colors.black;
  static const Color _darkSecondaryColor = Colors.white;
  static const Color _darkOnPrimaryColor = Colors.white;

  static const Color sessionExpiredBgColor = primaryRed;
  static const Color sessionExpiredTextColor = Colors.white;

  ///Course Details
  static const Color secondaryBtnColor =
      Color(0xFFFC6A03); // same used for tab bar indicator color

  ///Exam View
  static const Color markedForLaterBg =
      Color.fromRGBO(238, 169, 144, 1); // Color(0xFFFCAE1E);
  // Color.fromRGBO(238, 169, 144, 1);
  static Color questionNumberBg = Colors.grey.withOpacity(0.1);
  static Color selectedAnswerQuestionNumber =
      const Color.fromRGBO(210, 241, 253, 1); // Colors.blue.shade200;

  ///Exam Results
  static const Color doughnutPurple = Color.fromRGBO(228, 231, 254, 1);
  static const Color doughnutGreen = Color.fromRGBO(231, 252, 195, 1);
  static const Color doughnutCorrect = Color.fromRGBO(166, 219, 188, 1);
  static const Color doughnutWrong = Color.fromARGB(255, 255, 136, 132);
  static const Color doughnutSkipped = Color(0xffffb933);

  static Color examProgressSD = Colors.green;
  static Color reportQstnSD = Colors.redAccent;
  static Color markForLaterSD = Colors.pink.shade200;
  static Color borderColor = Colors.grey.shade500;
  static Color warningYellow = Color(0xFFFFC107);

  static const Color primaryOrange = Color.fromRGBO(255, 169, 69, 1);
  static const Color secondaryBlue = Color.fromRGBO(237, 250, 255, 1);
  static const Color ternaryBlue = Color.fromRGBO(210, 241, 253, 1);
  static const Color border = Color.fromRGBO(159, 168, 177, 1);
  static const Color tabUnSelectedBlueBG = Color.fromRGBO(203, 237, 247, 1);
  static const Color onboardingBackground = Color.fromRGBO(212, 236, 250, 1);

  /// Exam Intro
  static const Color examDurationBlue = Color.fromRGBO(0, 116, 211, 1);
  static const Color examMarkPurple = Color.fromRGBO(84, 23, 96, 1);
  static const Color examDateGreen = Color.fromRGBO(15, 155, 75, 1);
  static const Color examIntroPink = Color.fromRGBO(255, 166, 215, 1);

  /// Exam
  static const Color correctANswerGreen = Color.fromRGBO(0, 133, 64, 1);
  static const Color wrongANswerRed =
      Colors.red; // Color.fromRGBO(255, 108, 118, 1);
  static const Color skippedANswerOrange = Color.fromRGBO(255, 169, 69, 1);
  //exam result graph popup
  static const Color infoBlue = Colors.blueAccent;

  static ThemeData get light {
    return ThemeData(
      useMaterial3: false,
      fontFamily: SLConfig.FONT_FAMILY,
      appBarTheme: const AppBarTheme(
        elevation: 0,
        backgroundColor: primaryAppColor,
        titleTextStyle: TextStyle(
            color: Colors.white, fontSize: 22.0, fontWeight: FontWeight.w600),
      ),
      scaffoldBackgroundColor: primaryAppColor,
      primaryColor: primaryColor,
      splashColor: Colors.transparent,
      colorScheme: ColorScheme.fromSwatch().copyWith(secondary: Colors.black),
      iconTheme: const IconThemeData(
        color: iconColor, // Define the desired icon color
        size: 24, // Define the desired icon size
      ),
      dividerTheme: const DividerThemeData(
        color: dividerColor,
      ),
      radioTheme: RadioThemeData(
        fillColor: MaterialStateColor.resolveWith((states) => Colors.blue),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonBgColour,
          shadowColor: Colors.white,
          disabledBackgroundColor: Colors.grey[300]?.withOpacity(0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(0),
          ),
          textStyle: const TextStyle(
            fontSize: 20,
            fontFamily: SLConfig.FONT_FAMILY,
          ), // Set the button's text style
        ),
      ),
      pageTransitionsTheme: PageTransitionsTheme(builders: {
        TargetPlatform.android: CustomPageTransitionsBuilder(),
        TargetPlatform.iOS: CustomPageTransitionsBuilder()
      }),
    );
  }

  static final ThemeData darkTheme = ThemeData(
      scaffoldBackgroundColor: _darkPrimaryVariantColor,
      appBarTheme: const AppBarTheme(
        color: _darkPrimaryVariantColor,
        iconTheme: IconThemeData(color: _darkOnPrimaryColor),
      ),
      colorScheme: const ColorScheme.dark(
        primary: _darkPrimaryColor,
        secondary: _darkSecondaryColor,
        onPrimary: _darkOnPrimaryColor,
        background: Colors.white12,
      ),
      iconTheme: const IconThemeData(
        color: iconColor, // Define the desired icon color
        size: 24, // Define the desired icon size
      ),
      dividerTheme: const DividerThemeData(color: Colors.blue));

  static final ThemeData tabBarTheme = ThemeData(
      scaffoldBackgroundColor: primaryColor,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: primaryBlue,
        background: Colors.white,
      ),
      iconTheme: IconThemeData(
        color: iconColor, // Define the desired icon color
        size: 24, // Define the desired icon size
      ),
      dividerTheme: const DividerThemeData(color: Colors.blue));

  ///
  /// New UI
  ///
  static const Color primaryAppColor = Color(0xFF00B9C7);
  static const Color secondaryAppColor = Color(0xFFFDB666);

  static const Color headerTextColor = Color(0xFF4C4E4D);

  static const Color secondaryTextColor = Color(0xFF4C4E4D);
  static const Color whiteTextColor = Color(0xFFFFFFFF);

  static const Color blackColor = Colors.black;
  static const Color whiteColor = Colors.white;
  static const Color transparentColor = Colors.transparent;

  ///
  /// course details
  ///
  static const Color examsItemTextColorInHome = Color(0xFF646464);

  static const Color carousalSelectedDotColor = Color(0xFF98A2B3);
  static const Color carousalUnSelectedDotColor = Color(0xFFD9D9D9);

  static const Color subjectsLinearGradientTop = Color(0xFF00C6D4);
  static const Color subjectsLinearGradientBottom = Color(0xFF0099A4);

  static const Color examsBorderColorInHome = Color(0xFFCDCDCD);
  static const Color examsTitleColor = Color(0xFF646464);

  ///
  /// login, signup
  ///
  static const Color placeHolderText = Color(0xFF8D8E99);
  static const Color textFieldErrorText = Color(0xFFFF8C8C);
  static const Color textFieldErrorBorder = Color(0xFFFF0202);

  static const Color enabledBtnColor = secondaryAppColor;
  static const Color cardColor = Color.fromRGBO(0, 0, 0, 0.30);
  static const Color passwordColour = Color(0xFF11DCE8);

  static const Color signupTextColor = Color(0xff11DCE8);
  static const Color disabledBtnColor = Color.fromRGBO(253, 182, 102, 0.50);

  ///
  /// section details
  ///

  static const Color sectionDetailsBgColor = Color.fromRGBO(21, 82, 100, 0.10);

  static const Color stepperColor = Color(0xFFFDB666);
  static const Color sectionCardBgColor = Color(0xFFFFF8F1);
  static const Color progressContainerColor = Color(0xFF155264);
  static const Color sectionDetailsTextColor = Color(0xFF33363F);

  static const Color iconEnabledColor = Color(0xFF33363F);
  static Color iconDisableddColor = Color(0xFF33363F).withOpacity(0.3);

  ///
  // Exam List
  ///

  static const Color unselectedTabColor = placeHolderText;
  static const Color examInfoTextColor = Color(0xFFFB8500);
  static const Color viewResultButtonColor = Color(0xFF00AFBB);
  static const Color buttonBgColour = Color(0xFF9FC089);

//
// Exam Introduction
//
  static const Color dividerColor = Color.fromRGBO(0, 0, 0, 0.2);
  static const Color containerBorderColor = Color.fromRGBO(253, 182, 102, 0.20);
  static const Color examIntroTextColor = Color(0xFF423338);

  //
  // Exam Result Analysis
  //
  static const Color failColor = Color(0xFFFF3D00);
  static const Color resultDescriptionColor = Color(0xFF33363F);
  static const Color correctColor = Color(0xFF00A642);
  static const Color wrongColor = Color(0xFFFF2B2B);
  static const Color skippColor = Color(0xFFFFC107);
  static const Color examBorderColor = Color(0xFF000000);
  static const Color titleColor = Color(0xFF423338);
  static const Color scoreDescriptionColor = Color(0xFF33363F);
  static const Color attendedColor = Color(0xFF00B9C7);
  static const Color correctAnswerColor = Color(0xFF00A642);
  static const Color wrongAnswerColor = Color(0xFFFF2A2A);
  static const Color resultContainerColor = Color.fromRGBO(255, 43, 43, 0.10);
  static const Color topicBgColor = Color(0xFF3D3D3D);
  static const Color passBgColor = Color.fromRGBO(0, 166, 66, 0.10);
  // popup
  static const Color popupQstnNum = Color(0xFF423338);
  static const Color popupQstnText = Color(0xFF33363F);
  static const Color popupYourAnswr = Color(0xFFFDB666);
  static const Color okBtnGreen = Color(0xFF9FC089);

  ///
  /// RankList
  ///
  static const Color topStudentBgColor = Color(0xFFF6E5D3);

  /// exam view
  ///
  static const Color questionAnsText = Color(0xFF33363F);

  static const Color examDateTimeText = Color(0xFF33363F);
  static const Color selectedAnswerColor = Color(0xFF48C10C);
  static const Color selectedCorrectAnswerColor = Color(0xFF48C10C);
  static const Color selectedWrongAnswerColor = Color(0xFFCA0204);
  static const Color unAnsweredColor = Color(0xFFD2D2D2);
  static const Color previousBtnColor = Color(0xFF423338);
  static const Color nextBtnColor = Color(0xFF155264);
  static const Color submitBtnColor = Color(0xFF9FC089);

  static const Color selectedQstnFillColor = primaryAppColor;
  static const Color skippedFillColor = Color(0xFFFFC107); //skippcolor
  static const Color attendedFillColor = selectedAnswerColor;
  static const Color flaggedForReviewFillColor = Color(0xFF4C4E4D);

  static const Color questionNumUnanswered = Color(0xFFB8B8B8);
  static const Color questionNumAnswered = whiteTextColor;
  static const Color penaltyText = Color(0xFFFF2A2A);

  ///
  /// Profile view
  ///
  static const Color textFieldTitle = Color(0xFF423338);
  static const Color textFieldBorder = Color(0xFF423338);
  static Color continueBtnDisabled = Color(0xFF9FC089).withOpacity(0.5);
  static const Color profileEditOptions = Color(0xFF33363F);

  ///
  ///sidemenu
  ///
  static const Color sidemenuBGColor = Color(0xFF00AFBB);

  ///
  /// Exam Review
  ///
  static const Color examTitleColorInReview = Color(0xFF423338);
  static const Color courseNameColor = Color(0xFF797979);
  static const Color durationBoxBgColor = Color(0xFF273B4A);
  static const Color answerOptedColor = Color(0xFFFB8500);
  static const Color dividerColorInReview = Color.fromRGBO(0, 0, 0, 0.1);

  ///
  ///Current Affairs
  ///
  static const Color currentAffairsBorder = Color(0xFDB66633);

  ///
  /// course video
  ///
  static const Color courseVideoPrimaryTextColor = Color(0xFF423338);
  static const Color courseCommentDate = Color(0xFFADADAD);
  static const Color videoPlayPauseBg = Color(0xFFE3E1E1);
  static const Color commentBoxContent = Color(0xFF33363F);
  static const Color commentTextFieldBg = Color(0xFF00B9C7);
  static const Color unSelectedChipBorder = Color(0xFFE7E7E7);
  static const Color unselectedChipBg = Color(0xFFFAFAFA);
  static const Color currentAffairsItemBg = Color(0xFFFFFFFF);

  ///
  ///onboarding
  ///
  static const Color onBoardingSelectedIndex = Color(0xFFFB921C);

  // subscription
  static const Color subscriptionC1 = Color(0xFFE0FFDE);
  static const Color subscriptionC1Border = Color(0xFFC1ECBF);
  static const Color subscriptionC2 = Color(0xFFFEFAEE);
  static const Color subscriptionC2Border = Color(0xFFF8DAA9);
  static const Color subscriptionC3 = Color(0xFFD4EEFD);
  static const Color subscriptionC3Border = Color(0xFFABDBFE);
  static const Color subscriptionC4 = Color(0xFFe4fee4);
  static const Color subscriptionC4Border = Color(0xFFc0ebc2);
  static const Color subscriptionC5 = Color(0xFFfffbf1);
  static const Color subscriptionC5Border = Color(0xFFfad9ad);
  static const Color subscriptionC6 = Color(0xFFd8f0fd);
  static const Color subscriptionC6Border = Color(0xFFa8dcfc);
  static const Color subscriptionC7 = Color(0xFFd0fdfe);
  static const Color subscriptionC7Border = Color(0xFFacddde);
  static const Color subscriptionC8 = Color(0xFFe1fff0);
  static const Color subscriptionC8Border = Color(0xFFcaf1de);
  static const Color subscriptionC9 = Color(0xFFfff6eb);
  static const Color subscriptionC9Border = Color(0xFFffe7c7);
  static const Color descrptionBg = Color(0xFFE8AE01);
  static const Color planHistoryHead = Color(0xFFFB8500);
  static const Color planHistoryListTileBg = Color(0xFF33363F);
  static const Color planHistoryPrice = Color(0xFFFDB666);
  static const Color planHistoryDivider = Color(0xFFBB6300);
  static const Color searchBarBg = Color(0xFFF3F3F3);
  static const Color searchBarPlaceholderText = Color(0xFF8D8E99);

  static const Color emptyContent = Color(0xFF33363F);

  ///
  /// topic, course list
  ///
  static const Color topicExpandedIconColor = Color(0xFF00AFBB);
  static const Color topicExpandedViewColor = Color(0xFFEEEEEE);

  static const Color primaryGreenColor = Color(0xFF9FC089);
  static const carouselColorList = [
    LinearGradient(
      colors: [
        Color(0xFF33BEA2), // rgb(51, 190, 162)
        Color(0xFF29AB91), // rgb(41, 171, 145)
        Color(0xFF169B73), // rgb(22, 139, 115)
      ],
    ),
    // bg-total-marks gradient
    LinearGradient(
      colors: [
        Color(0xFFE44A8A), // rgb(228, 74, 138)
        Color(0xFFD44D92), // rgb(212, 77, 146)
        Color(0xFFC6509E), // rgb(198, 80, 158)
      ],
    ),

    // bg-progress gradient
    LinearGradient(
      colors: [
        Color(0xFF864FE1), // rgb(134, 79, 225)
        Color(0xFF6D44C4), // rgb(109, 68, 196)
        Color(0xFF5438A6), // rgb(84, 56, 166)
      ],
    ),

    // bg-time-spent gradient
    LinearGradient(
      colors: [
        Color(0xFF4DB9EA), // rgb(77, 185, 234)
        Color(0xFF54ADE7), // rgb(84, 173, 231)
        Color(0xFF5CA2E0), // rgb(92, 162, 224)
      ],
    ),

    // bg-achievements gradient
    LinearGradient(
      colors: [
        Color(0xFFFFB62E), // rgb(255, 178, 46)
        Color(0xFFFBA23E), // rgb(251, 162, 62)
        Color(0xFFF78D4F), // rgb(247, 141, 79)
      ],
    ),

    // bg-total-course gradient
  ];

  static const List<Color> analyticsColors = [
    Colors.red,
    Colors.green,
    Colors.blue,
    Colors.orange,
    Colors.purple,
    Colors.yellow,
    Colors.teal,
  ];

  static const courseBgOrange = Color.fromRGBO(234, 88, 12, 1);
  static const courseCompletedBgGreen = Color.fromRGBO(34, 197, 94, 1);
  static const courseCompletedTxtGreen = Color.fromRGBO(220, 252, 231, 1);
  static const courseCompletedProgressGreen = courseCompletedBgGreen;
  static const coursePecentageTxtGrey = Color.fromRGBO(107, 114, 128, 1);
}
