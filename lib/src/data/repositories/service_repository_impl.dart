import 'package:supabase_flutter/supabase_flutter.dart';

import '../../config/app_config/api_config.dart';
import '/src/domain/repositories/service_repository.dart';

class ServiceRepositoryImpl implements ServiceRepository {

  /// upload fcm push notification token
  @override
  Future<dynamic> uploadFCMToken(
      Map<String, dynamic> reqJson, String userId, String orgId) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.INSERT_FCM_TOKEN, params: {
      'notification_data': reqJson,
      'user_id': userId,
      'org_id': orgId
    });

    return response;
  }

  // upload device info
  @override
  Future<dynamic> uploadDeviceInfo(
      Map<String, dynamic> reqJson, String userId, String orgId) async {
    final response = await Supabase.instance.client.rpc(
        APIConfig.UPLOAD_DEVICE_INFO,
        params: {'device_data': reqJson, 'user_id': userId, 'org_id': orgId});

    return response;
  }
}


