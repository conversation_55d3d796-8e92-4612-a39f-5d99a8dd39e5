import 'package:equatable/equatable.dart';

import '../../config/enums/rank_level.dart';

class Rank extends Equatable {
  final String userName;
  final String userId;
  final String avatar;
  final double mark;
  final String examAttended;
  final int correctCount;
  final int wrongCount;
  final int skippedCount;
  final int rankValue;
  final RankLevel rankLevel;

  Rank({
    required this.userName,
    required this.userId,
    required this.avatar,
    required this.mark,
    required this.examAttended,
    required this.rankValue,
    required this.correctCount,
    required this.wrongCount,
    required this.skippedCount,
    required this.rankLevel,
  });

  factory Rank.fromJson(Map<String, dynamic> json) {
    int rankVal = json['rank'] ?? 0;
    return Rank(
      userName: json['user_name'] ?? '',
      userId: json['user_id'] ?? '',
      avatar: json['avatar_url'] ?? '',
      mark: (json['total_sum_grades'] ?? 0) * 1.0,
      examAttended: (json['attempts'] ?? 0).toString(),
      correctCount: (json['correct_answers_count'] ?? 0),
      wrongCount: (json['wrong_answers_count'] ?? 0),
      skippedCount: (json['skipped_answers_count'] ?? 0),
      rankValue: rankVal,
      rankLevel: rankVal == 1
          ? RankLevel.gold
          : rankVal == 2
              ? RankLevel.silver
              : rankVal == 3
                  ? RankLevel.bronze
                  : RankLevel.none,
    );
  }

  factory Rank.returnDefaultConstructor() {
    return Rank(
      userName: '',
      userId: '',
      avatar: '',
      mark: 1.0,
      examAttended: '',
      correctCount: 0,
      wrongCount: 0,
      skippedCount: 0,
      rankValue: 0,
      rankLevel: RankLevel.none,
    );
  }
  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [userName, mark, examAttended, rankValue];
  }
}
