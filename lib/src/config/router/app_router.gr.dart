// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

abstract class _$AppRouter extends RootStackRouter {
  // ignore: unused_element
  _$AppRouter({super.navigatorKey});

  @override
  final Map<String, PageFactory> pagesMap = {
    AboutViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const AboutViewScreen(),
      );
    },
    CourseDashboardRoute.name: (routeData) {
      final args = routeData.argsAs<CourseDashboardRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseDashboardScreen(
          key: args.key,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    CourseDescriptionViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseDescriptionViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseDescriptionViewScreen(
          key: args.key,
          summary: args.summary,
        ),
      );
    },
    CourseDetailsDashboardRoute.name: (routeData) {
      final args = routeData.argsAs<CourseDetailsDashboardRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseDetailsDashboardScreen(
          courseInfo: args.courseInfo,
          key: args.key,
        ),
      );
    },
    CourseDetailsViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseDetailsViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseDetailsViewScreen(
          key: args.key,
          courseId: args.courseId,
          courseName: args.courseName,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    CourseListViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseListViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseListViewScreen(
          key: args.key,
          category: args.category,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    CourseModuleViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseModuleViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseModuleViewScreen(
          key: args.key,
          courseModule: args.courseModule,
        ),
      );
    },
    CourseNewsViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseNewsViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseNewsViewScreen(
          key: args.key,
          currentAffairs: args.currentAffairs,
        ),
      );
    },
    CourseVideoViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseVideoViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseVideoViewScreen(
          args.courseVideo,
          key: args.key,
          isFromSectionDetails: args.isFromSectionDetails,
          courseProgress: args.courseProgress,
          checkPoints: args.checkPoints,
          isFromExamScreen: args.isFromExamScreen,
          isExamPassed: args.isExamPassed,
          navigateBackTo: args.navigateBackTo,
        ),
      );
    },
    DailyCourseListViewRoute.name: (routeData) {
      final args = routeData.argsAs<DailyCourseListViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: DailyCourseListViewScreen(
          key: args.key,
          title: args.title,
        ),
      );
    },
    DrawingPadViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: DrawingPadViewScreen(),
      );
    },
    DrawingReviewViewRoute.name: (routeData) {
      final args = routeData.argsAs<DrawingReviewViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: DrawingReviewViewScreen(
          args.imageData,
          args.remarks,
          key: args.key,
        ),
      );
    },
    ExamIntroductionViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamIntroductionViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamIntroductionViewScreen(
          key: args.key,
          questionData: args.questionData,
          isFromSectionDetails: args.isFromSectionDetails,
          isFromVideoScreen: args.isFromVideoScreen,
          navigateBackTo: args.navigateBackTo,
        ),
      );
    },
    ExamListViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamListViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamListViewScreen(
          args.tabIndex,
          key: args.key,
        ),
      );
    },
    ExamResultViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamResultViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamResultViewScreen(
          key: args.key,
          examSummary: args.examSummary,
          tabIndex: args.tabIndex,
          navigateBackTo: args.navigateBackTo,
        ),
      );
    },
    ExamReviewViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamReviewViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamReviewViewScreen(
          key: args.key,
          quizId: args.quizId,
          quizAttemptId: args.quizAttemptId,
          isGradeCalculated: args.isGradeCalculated,
          navigateBackTo: args.navigateBackTo,
        ),
      );
    },
    ExamViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamViewScreen(
          key: args.key,
          examDuration: args.examDuration,
          shouldShowTimer: args.shouldShowTimer,
          isFromViewResult: args.isFromViewResult,
          quizAttemptId: args.quizAttemptId,
          questionData: args.questionData,
          isFromVideoView: args.isFromVideoView,
          isFromPptView: args.isFromPptView,
          checkPoints: args.checkPoints,
          navigateBackTo: args.navigateBackTo,
        ),
      );
    },
    ImageViewRoute.name: (routeData) {
      final args = routeData.argsAs<ImageViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ImageViewScreen(
          key: args.key,
          resourceFile: args.resourceFile,
          resourceProgress: args.resourceProgress,
          moduleLength: args.moduleLength,
        ),
      );
    },
    LiveClassViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const LiveClassViewScreen(),
      );
    },
    LoginEmailViewRoute.name: (routeData) {
      final args = routeData.argsAs<LoginEmailViewRouteArgs>(
          orElse: () => const LoginEmailViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: LoginEmailViewScreen(
          key: args.key,
          isTokenExpired: args.isTokenExpired,
        ),
      );
    },
    NewsViewMoreViewRoute.name: (routeData) {
      final args = routeData.argsAs<NewsViewMoreViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: NewsViewMoreViewScreen(
          args.affairsList,
          key: args.key,
        ),
      );
    },
    NoAccessViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const NoAccessViewScreen(),
      );
    },
    OnboardingViewRoute.name: (routeData) {
      final args = routeData.argsAs<OnboardingViewRouteArgs>(
          orElse: () => const OnboardingViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: OnboardingViewScreen(key: args.key),
      );
    },
    OrganizationViewRoute.name: (routeData) {
      final args = routeData.argsAs<OrganizationViewRouteArgs>(
          orElse: () => const OrganizationViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: OrganizationViewScreen(
          key: args.key,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    PDFViewRoute.name: (routeData) {
      final args = routeData.argsAs<PDFViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: PDFViewScreen(
          key: args.key,
          courseFileResource: args.courseFileResource,
          checkPoints: args.checkPoints,
          isFromExamScreen: args.isFromExamScreen,
          isExamPassed: args.isExamPassed,
          navigateBackTo: args.navigateBackTo,
        ),
      );
    },
    PPTViewerRoute.name: (routeData) {
      final args = routeData.argsAs<PPTViewerRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: PPTViewerScreen(
          key: args.key,
          courseFileResource: args.courseFileResource,
          checkPoints: args.checkPoints,
          isFromExamScreen: args.isFromExamScreen,
          isExamPassed: args.isExamPassed,
          navigateBackTo: args.navigateBackTo,
        ),
      );
    },
    ProfileReviewRoute.name: (routeData) {
      final args = routeData.argsAs<ProfileReviewRouteArgs>(
          orElse: () => const ProfileReviewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ProfileReviewScreen(key: args.key),
      );
    },
    ProfileViewRoute.name: (routeData) {
      final args = routeData.argsAs<ProfileViewRouteArgs>(
          orElse: () => const ProfileViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ProfileViewScreen(
          key: args.key,
          isFromLogin: args.isFromLogin,
          isFromSideMenu: args.isFromSideMenu,
        ),
      );
    },
    RankListViewRoute.name: (routeData) {
      final args = routeData.argsAs<RankListViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: RankListViewScreen(
          args.isCourseRankList,
          args.rankListForExam,
          key: args.key,
        ),
      );
    },
    ResetPasswordViewRoute.name: (routeData) {
      final args = routeData.argsAs<ResetPasswordViewRouteArgs>(
          orElse: () => const ResetPasswordViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ResetPasswordViewScreen(key: args.key),
      );
    },
    SectionDetailsViewRoute.name: (routeData) {
      final args = routeData.argsAs<SectionDetailsViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SectionDetailsViewScreen(
          key: args.key,
          sectionId: args.sectionId,
          sectionDetailsData: args.sectionDetailsData,
          showFolderResources: args.showFolderResources,
          folderIndex: args.folderIndex,
          title: args.title,
        ),
      );
    },
    SettingsViewRoute.name: (routeData) {
      final args = routeData.argsAs<SettingsViewRouteArgs>(
          orElse: () => const SettingsViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SettingsViewScreen(key: args.key),
      );
    },
    SignUpViewRoute.name: (routeData) {
      final args = routeData.argsAs<SignUpViewRouteArgs>(
          orElse: () => const SignUpViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SignUpViewScreen(key: args.key),
      );
    },
    SplashRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SplashScreen(),
      );
    },
    StudyMaterialViewRoute.name: (routeData) {
      final args = routeData.argsAs<StudyMaterialViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: StudyMaterialViewScreen(
          key: args.key,
          resourceProgress: args.resourceProgress,
          moduleLength: args.moduleLength,
          pageContent: args.pageContent,
        ),
      );
    },
    SubjectsDetailedTabViewRoute.name: (routeData) {
      final args = routeData.argsAs<SubjectsDetailedTabViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SubjectsDetailedTabViewScreen(
          args.sectionDetailsData,
          key: args.key,
        ),
      );
    },
    SubscriptionInfoViewRoute.name: (routeData) {
      final args = routeData.argsAs<SubscriptionInfoViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SubscriptionInfoViewScreen(
          planName: args.planName,
          planInfo: args.planInfo,
          key: args.key,
        ),
      );
    },
    SubscriptionViewRoute.name: (routeData) {
      final args = routeData.argsAs<SubscriptionViewRouteArgs>(
          orElse: () => const SubscriptionViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SubscriptionViewScreen(key: args.key),
      );
    },
    TopicListViewRoute.name: (routeData) {
      final args = routeData.argsAs<TopicListViewRouteArgs>(
          orElse: () => const TopicListViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: TopicListViewScreen(
          key: args.key,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    WelcomeViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const WelcomeViewScreen(),
      );
    },
  };
}

/// generated route for
/// [AboutViewScreen]
class AboutViewRoute extends PageRouteInfo<void> {
  const AboutViewRoute({List<PageRouteInfo>? children})
      : super(
          AboutViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'AboutViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [CourseDashboardScreen]
class CourseDashboardRoute extends PageRouteInfo<CourseDashboardRouteArgs> {
  CourseDashboardRoute({
    Key? key,
    required bool isFromLogin,
    List<PageRouteInfo>? children,
  }) : super(
          CourseDashboardRoute.name,
          args: CourseDashboardRouteArgs(
            key: key,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseDashboardRoute';

  static const PageInfo<CourseDashboardRouteArgs> page =
      PageInfo<CourseDashboardRouteArgs>(name);
}

class CourseDashboardRouteArgs {
  const CourseDashboardRouteArgs({
    this.key,
    required this.isFromLogin,
  });

  final Key? key;

  final bool isFromLogin;

  @override
  String toString() {
    return 'CourseDashboardRouteArgs{key: $key, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [CourseDescriptionViewScreen]
class CourseDescriptionViewRoute
    extends PageRouteInfo<CourseDescriptionViewRouteArgs> {
  CourseDescriptionViewRoute({
    Key? key,
    required String summary,
    List<PageRouteInfo>? children,
  }) : super(
          CourseDescriptionViewRoute.name,
          args: CourseDescriptionViewRouteArgs(
            key: key,
            summary: summary,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseDescriptionViewRoute';

  static const PageInfo<CourseDescriptionViewRouteArgs> page =
      PageInfo<CourseDescriptionViewRouteArgs>(name);
}

class CourseDescriptionViewRouteArgs {
  const CourseDescriptionViewRouteArgs({
    this.key,
    required this.summary,
  });

  final Key? key;

  final String summary;

  @override
  String toString() {
    return 'CourseDescriptionViewRouteArgs{key: $key, summary: $summary}';
  }
}

/// generated route for
/// [CourseDetailsDashboardScreen]
class CourseDetailsDashboardRoute
    extends PageRouteInfo<CourseDetailsDashboardRouteArgs> {
  CourseDetailsDashboardRoute({
    required CoursesInfo courseInfo,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          CourseDetailsDashboardRoute.name,
          args: CourseDetailsDashboardRouteArgs(
            courseInfo: courseInfo,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseDetailsDashboardRoute';

  static const PageInfo<CourseDetailsDashboardRouteArgs> page =
      PageInfo<CourseDetailsDashboardRouteArgs>(name);
}

class CourseDetailsDashboardRouteArgs {
  const CourseDetailsDashboardRouteArgs({
    required this.courseInfo,
    this.key,
  });

  final CoursesInfo courseInfo;

  final Key? key;

  @override
  String toString() {
    return 'CourseDetailsDashboardRouteArgs{courseInfo: $courseInfo, key: $key}';
  }
}

/// generated route for
/// [CourseDetailsViewScreen]
class CourseDetailsViewRoute extends PageRouteInfo<CourseDetailsViewRouteArgs> {
  CourseDetailsViewRoute({
    Key? key,
    required String courseId,
    required String courseName,
    bool isFromLogin = false,
    List<PageRouteInfo>? children,
  }) : super(
          CourseDetailsViewRoute.name,
          args: CourseDetailsViewRouteArgs(
            key: key,
            courseId: courseId,
            courseName: courseName,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseDetailsViewRoute';

  static const PageInfo<CourseDetailsViewRouteArgs> page =
      PageInfo<CourseDetailsViewRouteArgs>(name);
}

class CourseDetailsViewRouteArgs {
  const CourseDetailsViewRouteArgs({
    this.key,
    required this.courseId,
    required this.courseName,
    this.isFromLogin = false,
  });

  final Key? key;

  final String courseId;

  final String courseName;

  final bool isFromLogin;

  @override
  String toString() {
    return 'CourseDetailsViewRouteArgs{key: $key, courseId: $courseId, courseName: $courseName, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [CourseListViewScreen]
class CourseListViewRoute extends PageRouteInfo<CourseListViewRouteArgs> {
  CourseListViewRoute({
    Key? key,
    required UserCategory category,
    bool isFromLogin = false,
    List<PageRouteInfo>? children,
  }) : super(
          CourseListViewRoute.name,
          args: CourseListViewRouteArgs(
            key: key,
            category: category,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseListViewRoute';

  static const PageInfo<CourseListViewRouteArgs> page =
      PageInfo<CourseListViewRouteArgs>(name);
}

class CourseListViewRouteArgs {
  const CourseListViewRouteArgs({
    this.key,
    required this.category,
    this.isFromLogin = false,
  });

  final Key? key;

  final UserCategory category;

  final bool isFromLogin;

  @override
  String toString() {
    return 'CourseListViewRouteArgs{key: $key, category: $category, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [CourseModuleViewScreen]
class CourseModuleViewRoute extends PageRouteInfo<CourseModuleViewRouteArgs> {
  CourseModuleViewRoute({
    Key? key,
    required CourseModule courseModule,
    List<PageRouteInfo>? children,
  }) : super(
          CourseModuleViewRoute.name,
          args: CourseModuleViewRouteArgs(
            key: key,
            courseModule: courseModule,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseModuleViewRoute';

  static const PageInfo<CourseModuleViewRouteArgs> page =
      PageInfo<CourseModuleViewRouteArgs>(name);
}

class CourseModuleViewRouteArgs {
  const CourseModuleViewRouteArgs({
    this.key,
    required this.courseModule,
  });

  final Key? key;

  final CourseModule courseModule;

  @override
  String toString() {
    return 'CourseModuleViewRouteArgs{key: $key, courseModule: $courseModule}';
  }
}

/// generated route for
/// [CourseNewsViewScreen]
class CourseNewsViewRoute extends PageRouteInfo<CourseNewsViewRouteArgs> {
  CourseNewsViewRoute({
    Key? key,
    required CurrentAffairs currentAffairs,
    List<PageRouteInfo>? children,
  }) : super(
          CourseNewsViewRoute.name,
          args: CourseNewsViewRouteArgs(
            key: key,
            currentAffairs: currentAffairs,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseNewsViewRoute';

  static const PageInfo<CourseNewsViewRouteArgs> page =
      PageInfo<CourseNewsViewRouteArgs>(name);
}

class CourseNewsViewRouteArgs {
  const CourseNewsViewRouteArgs({
    this.key,
    required this.currentAffairs,
  });

  final Key? key;

  final CurrentAffairs currentAffairs;

  @override
  String toString() {
    return 'CourseNewsViewRouteArgs{key: $key, currentAffairs: $currentAffairs}';
  }
}

/// generated route for
/// [CourseVideoViewScreen]
class CourseVideoViewRoute extends PageRouteInfo<CourseVideoViewRouteArgs> {
  CourseVideoViewRoute({
    required CourseVideoResource courseVideo,
    Key? key,
    bool isFromSectionDetails = false,
    required ResourceProgress? courseProgress,
    List<CheckPoint> checkPoints = const [],
    bool isFromExamScreen = false,
    bool isExamPassed = false,
    required NavigationTo navigateBackTo,
    List<PageRouteInfo>? children,
  }) : super(
          CourseVideoViewRoute.name,
          args: CourseVideoViewRouteArgs(
            courseVideo: courseVideo,
            key: key,
            isFromSectionDetails: isFromSectionDetails,
            courseProgress: courseProgress,
            checkPoints: checkPoints,
            isFromExamScreen: isFromExamScreen,
            isExamPassed: isExamPassed,
            navigateBackTo: navigateBackTo,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseVideoViewRoute';

  static const PageInfo<CourseVideoViewRouteArgs> page =
      PageInfo<CourseVideoViewRouteArgs>(name);
}

class CourseVideoViewRouteArgs {
  const CourseVideoViewRouteArgs({
    required this.courseVideo,
    this.key,
    this.isFromSectionDetails = false,
    required this.courseProgress,
    this.checkPoints = const [],
    this.isFromExamScreen = false,
    this.isExamPassed = false,
    required this.navigateBackTo,
  });

  final CourseVideoResource courseVideo;

  final Key? key;

  final bool isFromSectionDetails;

  final ResourceProgress? courseProgress;

  final List<CheckPoint> checkPoints;

  final bool isFromExamScreen;

  final bool isExamPassed;

  final NavigationTo navigateBackTo;

  @override
  String toString() {
    return 'CourseVideoViewRouteArgs{courseVideo: $courseVideo, key: $key, isFromSectionDetails: $isFromSectionDetails, courseProgress: $courseProgress, checkPoints: $checkPoints, isFromExamScreen: $isFromExamScreen, isExamPassed: $isExamPassed, navigateBackTo: $navigateBackTo}';
  }
}

/// generated route for
/// [DailyCourseListViewScreen]
class DailyCourseListViewRoute
    extends PageRouteInfo<DailyCourseListViewRouteArgs> {
  DailyCourseListViewRoute({
    Key? key,
    required String title,
    List<PageRouteInfo>? children,
  }) : super(
          DailyCourseListViewRoute.name,
          args: DailyCourseListViewRouteArgs(
            key: key,
            title: title,
          ),
          initialChildren: children,
        );

  static const String name = 'DailyCourseListViewRoute';

  static const PageInfo<DailyCourseListViewRouteArgs> page =
      PageInfo<DailyCourseListViewRouteArgs>(name);
}

class DailyCourseListViewRouteArgs {
  const DailyCourseListViewRouteArgs({
    this.key,
    required this.title,
  });

  final Key? key;

  final String title;

  @override
  String toString() {
    return 'DailyCourseListViewRouteArgs{key: $key, title: $title}';
  }
}

/// generated route for
/// [DrawingPadViewScreen]
class DrawingPadViewRoute extends PageRouteInfo<void> {
  const DrawingPadViewRoute({List<PageRouteInfo>? children})
      : super(
          DrawingPadViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'DrawingPadViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [DrawingReviewViewScreen]
class DrawingReviewViewRoute extends PageRouteInfo<DrawingReviewViewRouteArgs> {
  DrawingReviewViewRoute({
    required Uint8List imageData,
    required String remarks,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          DrawingReviewViewRoute.name,
          args: DrawingReviewViewRouteArgs(
            imageData: imageData,
            remarks: remarks,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'DrawingReviewViewRoute';

  static const PageInfo<DrawingReviewViewRouteArgs> page =
      PageInfo<DrawingReviewViewRouteArgs>(name);
}

class DrawingReviewViewRouteArgs {
  const DrawingReviewViewRouteArgs({
    required this.imageData,
    required this.remarks,
    this.key,
  });

  final Uint8List imageData;

  final String remarks;

  final Key? key;

  @override
  String toString() {
    return 'DrawingReviewViewRouteArgs{imageData: $imageData, remarks: $remarks, key: $key}';
  }
}

/// generated route for
/// [ExamIntroductionViewScreen]
class ExamIntroductionViewRoute
    extends PageRouteInfo<ExamIntroductionViewRouteArgs> {
  ExamIntroductionViewRoute({
    Key? key,
    required QuestionData questionData,
    bool isFromSectionDetails = false,
    bool isFromVideoScreen = false,
    required NavigationTo navigateBackTo,
    List<PageRouteInfo>? children,
  }) : super(
          ExamIntroductionViewRoute.name,
          args: ExamIntroductionViewRouteArgs(
            key: key,
            questionData: questionData,
            isFromSectionDetails: isFromSectionDetails,
            isFromVideoScreen: isFromVideoScreen,
            navigateBackTo: navigateBackTo,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamIntroductionViewRoute';

  static const PageInfo<ExamIntroductionViewRouteArgs> page =
      PageInfo<ExamIntroductionViewRouteArgs>(name);
}

class ExamIntroductionViewRouteArgs {
  const ExamIntroductionViewRouteArgs({
    this.key,
    required this.questionData,
    this.isFromSectionDetails = false,
    this.isFromVideoScreen = false,
    required this.navigateBackTo,
  });

  final Key? key;

  final QuestionData questionData;

  final bool isFromSectionDetails;

  final bool isFromVideoScreen;

  final NavigationTo navigateBackTo;

  @override
  String toString() {
    return 'ExamIntroductionViewRouteArgs{key: $key, questionData: $questionData, isFromSectionDetails: $isFromSectionDetails, isFromVideoScreen: $isFromVideoScreen, navigateBackTo: $navigateBackTo}';
  }
}

/// generated route for
/// [ExamListViewScreen]
class ExamListViewRoute extends PageRouteInfo<ExamListViewRouteArgs> {
  ExamListViewRoute({
    required int tabIndex,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ExamListViewRoute.name,
          args: ExamListViewRouteArgs(
            tabIndex: tabIndex,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamListViewRoute';

  static const PageInfo<ExamListViewRouteArgs> page =
      PageInfo<ExamListViewRouteArgs>(name);
}

class ExamListViewRouteArgs {
  const ExamListViewRouteArgs({
    required this.tabIndex,
    this.key,
  });

  final int tabIndex;

  final Key? key;

  @override
  String toString() {
    return 'ExamListViewRouteArgs{tabIndex: $tabIndex, key: $key}';
  }
}

/// generated route for
/// [ExamResultViewScreen]
class ExamResultViewRoute extends PageRouteInfo<ExamResultViewRouteArgs> {
  ExamResultViewRoute({
    Key? key,
    required ExamSummary examSummary,
    required int tabIndex,
    required NavigationTo navigateBackTo,
    List<PageRouteInfo>? children,
  }) : super(
          ExamResultViewRoute.name,
          args: ExamResultViewRouteArgs(
            key: key,
            examSummary: examSummary,
            tabIndex: tabIndex,
            navigateBackTo: navigateBackTo,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamResultViewRoute';

  static const PageInfo<ExamResultViewRouteArgs> page =
      PageInfo<ExamResultViewRouteArgs>(name);
}

class ExamResultViewRouteArgs {
  const ExamResultViewRouteArgs({
    this.key,
    required this.examSummary,
    required this.tabIndex,
    required this.navigateBackTo,
  });

  final Key? key;

  final ExamSummary examSummary;

  final int tabIndex;

  final NavigationTo navigateBackTo;

  @override
  String toString() {
    return 'ExamResultViewRouteArgs{key: $key, examSummary: $examSummary, tabIndex: $tabIndex, navigateBackTo: $navigateBackTo}';
  }
}

/// generated route for
/// [ExamReviewViewScreen]
class ExamReviewViewRoute extends PageRouteInfo<ExamReviewViewRouteArgs> {
  ExamReviewViewRoute({
    Key? key,
    required String quizId,
    required String quizAttemptId,
    required bool isGradeCalculated,
    required NavigationTo navigateBackTo,
    List<PageRouteInfo>? children,
  }) : super(
          ExamReviewViewRoute.name,
          args: ExamReviewViewRouteArgs(
            key: key,
            quizId: quizId,
            quizAttemptId: quizAttemptId,
            isGradeCalculated: isGradeCalculated,
            navigateBackTo: navigateBackTo,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamReviewViewRoute';

  static const PageInfo<ExamReviewViewRouteArgs> page =
      PageInfo<ExamReviewViewRouteArgs>(name);
}

class ExamReviewViewRouteArgs {
  const ExamReviewViewRouteArgs({
    this.key,
    required this.quizId,
    required this.quizAttemptId,
    required this.isGradeCalculated,
    required this.navigateBackTo,
  });

  final Key? key;

  final String quizId;

  final String quizAttemptId;

  final bool isGradeCalculated;

  final NavigationTo navigateBackTo;

  @override
  String toString() {
    return 'ExamReviewViewRouteArgs{key: $key, quizId: $quizId, quizAttemptId: $quizAttemptId, isGradeCalculated: $isGradeCalculated, navigateBackTo: $navigateBackTo}';
  }
}

/// generated route for
/// [ExamViewScreen]
class ExamViewRoute extends PageRouteInfo<ExamViewRouteArgs> {
  ExamViewRoute({
    Key? key,
    required int examDuration,
    required bool shouldShowTimer,
    required bool isFromViewResult,
    required String quizAttemptId,
    required QuestionData questionData,
    bool isFromVideoView = false,
    bool isFromPptView = false,
    List<CheckPoint> checkPoints = const [],
    required NavigationTo navigateBackTo,
    List<PageRouteInfo>? children,
  }) : super(
          ExamViewRoute.name,
          args: ExamViewRouteArgs(
            key: key,
            examDuration: examDuration,
            shouldShowTimer: shouldShowTimer,
            isFromViewResult: isFromViewResult,
            quizAttemptId: quizAttemptId,
            questionData: questionData,
            isFromVideoView: isFromVideoView,
            isFromPptView: isFromPptView,
            checkPoints: checkPoints,
            navigateBackTo: navigateBackTo,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamViewRoute';

  static const PageInfo<ExamViewRouteArgs> page =
      PageInfo<ExamViewRouteArgs>(name);
}

class ExamViewRouteArgs {
  const ExamViewRouteArgs({
    this.key,
    required this.examDuration,
    required this.shouldShowTimer,
    required this.isFromViewResult,
    required this.quizAttemptId,
    required this.questionData,
    this.isFromVideoView = false,
    this.isFromPptView = false,
    this.checkPoints = const [],
    required this.navigateBackTo,
  });

  final Key? key;

  final int examDuration;

  final bool shouldShowTimer;

  final bool isFromViewResult;

  final String quizAttemptId;

  final QuestionData questionData;

  final bool isFromVideoView;

  final bool isFromPptView;

  final List<CheckPoint> checkPoints;

  final NavigationTo navigateBackTo;

  @override
  String toString() {
    return 'ExamViewRouteArgs{key: $key, examDuration: $examDuration, shouldShowTimer: $shouldShowTimer, isFromViewResult: $isFromViewResult, quizAttemptId: $quizAttemptId, questionData: $questionData, isFromVideoView: $isFromVideoView, isFromPptView: $isFromPptView, checkPoints: $checkPoints, navigateBackTo: $navigateBackTo}';
  }
}

/// generated route for
/// [ImageViewScreen]
class ImageViewRoute extends PageRouteInfo<ImageViewRouteArgs> {
  ImageViewRoute({
    Key? key,
    required CourseFileResource resourceFile,
    required ResourceProgress? resourceProgress,
    required int moduleLength,
    List<PageRouteInfo>? children,
  }) : super(
          ImageViewRoute.name,
          args: ImageViewRouteArgs(
            key: key,
            resourceFile: resourceFile,
            resourceProgress: resourceProgress,
            moduleLength: moduleLength,
          ),
          initialChildren: children,
        );

  static const String name = 'ImageViewRoute';

  static const PageInfo<ImageViewRouteArgs> page =
      PageInfo<ImageViewRouteArgs>(name);
}

class ImageViewRouteArgs {
  const ImageViewRouteArgs({
    this.key,
    required this.resourceFile,
    required this.resourceProgress,
    required this.moduleLength,
  });

  final Key? key;

  final CourseFileResource resourceFile;

  final ResourceProgress? resourceProgress;

  final int moduleLength;

  @override
  String toString() {
    return 'ImageViewRouteArgs{key: $key, resourceFile: $resourceFile, resourceProgress: $resourceProgress, moduleLength: $moduleLength}';
  }
}

/// generated route for
/// [LiveClassViewScreen]
class LiveClassViewRoute extends PageRouteInfo<void> {
  const LiveClassViewRoute({List<PageRouteInfo>? children})
      : super(
          LiveClassViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'LiveClassViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [LoginEmailViewScreen]
class LoginEmailViewRoute extends PageRouteInfo<LoginEmailViewRouteArgs> {
  LoginEmailViewRoute({
    Key? key,
    bool isTokenExpired = false,
    List<PageRouteInfo>? children,
  }) : super(
          LoginEmailViewRoute.name,
          args: LoginEmailViewRouteArgs(
            key: key,
            isTokenExpired: isTokenExpired,
          ),
          initialChildren: children,
        );

  static const String name = 'LoginEmailViewRoute';

  static const PageInfo<LoginEmailViewRouteArgs> page =
      PageInfo<LoginEmailViewRouteArgs>(name);
}

class LoginEmailViewRouteArgs {
  const LoginEmailViewRouteArgs({
    this.key,
    this.isTokenExpired = false,
  });

  final Key? key;

  final bool isTokenExpired;

  @override
  String toString() {
    return 'LoginEmailViewRouteArgs{key: $key, isTokenExpired: $isTokenExpired}';
  }
}

/// generated route for
/// [NewsViewMoreViewScreen]
class NewsViewMoreViewRoute extends PageRouteInfo<NewsViewMoreViewRouteArgs> {
  NewsViewMoreViewRoute({
    required List<CurrentAffairs> affairsList,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          NewsViewMoreViewRoute.name,
          args: NewsViewMoreViewRouteArgs(
            affairsList: affairsList,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'NewsViewMoreViewRoute';

  static const PageInfo<NewsViewMoreViewRouteArgs> page =
      PageInfo<NewsViewMoreViewRouteArgs>(name);
}

class NewsViewMoreViewRouteArgs {
  const NewsViewMoreViewRouteArgs({
    required this.affairsList,
    this.key,
  });

  final List<CurrentAffairs> affairsList;

  final Key? key;

  @override
  String toString() {
    return 'NewsViewMoreViewRouteArgs{affairsList: $affairsList, key: $key}';
  }
}

/// generated route for
/// [NoAccessViewScreen]
class NoAccessViewRoute extends PageRouteInfo<void> {
  const NoAccessViewRoute({List<PageRouteInfo>? children})
      : super(
          NoAccessViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'NoAccessViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [OnboardingViewScreen]
class OnboardingViewRoute extends PageRouteInfo<OnboardingViewRouteArgs> {
  OnboardingViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          OnboardingViewRoute.name,
          args: OnboardingViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'OnboardingViewRoute';

  static const PageInfo<OnboardingViewRouteArgs> page =
      PageInfo<OnboardingViewRouteArgs>(name);
}

class OnboardingViewRouteArgs {
  const OnboardingViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'OnboardingViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [OrganizationViewScreen]
class OrganizationViewRoute extends PageRouteInfo<OrganizationViewRouteArgs> {
  OrganizationViewRoute({
    Key? key,
    bool isFromLogin = false,
    List<PageRouteInfo>? children,
  }) : super(
          OrganizationViewRoute.name,
          args: OrganizationViewRouteArgs(
            key: key,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'OrganizationViewRoute';

  static const PageInfo<OrganizationViewRouteArgs> page =
      PageInfo<OrganizationViewRouteArgs>(name);
}

class OrganizationViewRouteArgs {
  const OrganizationViewRouteArgs({
    this.key,
    this.isFromLogin = false,
  });

  final Key? key;

  final bool isFromLogin;

  @override
  String toString() {
    return 'OrganizationViewRouteArgs{key: $key, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [PDFViewScreen]
class PDFViewRoute extends PageRouteInfo<PDFViewRouteArgs> {
  PDFViewRoute({
    Key? key,
    required CourseFileResource courseFileResource,
    required List<CheckPoint> checkPoints,
    required bool isFromExamScreen,
    required bool isExamPassed,
    required NavigationTo navigateBackTo,
    List<PageRouteInfo>? children,
  }) : super(
          PDFViewRoute.name,
          args: PDFViewRouteArgs(
            key: key,
            courseFileResource: courseFileResource,
            checkPoints: checkPoints,
            isFromExamScreen: isFromExamScreen,
            isExamPassed: isExamPassed,
            navigateBackTo: navigateBackTo,
          ),
          initialChildren: children,
        );

  static const String name = 'PDFViewRoute';

  static const PageInfo<PDFViewRouteArgs> page =
      PageInfo<PDFViewRouteArgs>(name);
}

class PDFViewRouteArgs {
  const PDFViewRouteArgs({
    this.key,
    required this.courseFileResource,
    required this.checkPoints,
    required this.isFromExamScreen,
    required this.isExamPassed,
    required this.navigateBackTo,
  });

  final Key? key;

  final CourseFileResource courseFileResource;

  final List<CheckPoint> checkPoints;

  final bool isFromExamScreen;

  final bool isExamPassed;

  final NavigationTo navigateBackTo;

  @override
  String toString() {
    return 'PDFViewRouteArgs{key: $key, courseFileResource: $courseFileResource, checkPoints: $checkPoints, isFromExamScreen: $isFromExamScreen, isExamPassed: $isExamPassed, navigateBackTo: $navigateBackTo}';
  }
}

/// generated route for
/// [PPTViewerScreen]
class PPTViewerRoute extends PageRouteInfo<PPTViewerRouteArgs> {
  PPTViewerRoute({
    Key? key,
    required CourseFileResource courseFileResource,
    List<CheckPoint> checkPoints = const [],
    bool isFromExamScreen = false,
    bool isExamPassed = false,
    required NavigationTo navigateBackTo,
    List<PageRouteInfo>? children,
  }) : super(
          PPTViewerRoute.name,
          args: PPTViewerRouteArgs(
            key: key,
            courseFileResource: courseFileResource,
            checkPoints: checkPoints,
            isFromExamScreen: isFromExamScreen,
            isExamPassed: isExamPassed,
            navigateBackTo: navigateBackTo,
          ),
          initialChildren: children,
        );

  static const String name = 'PPTViewerRoute';

  static const PageInfo<PPTViewerRouteArgs> page =
      PageInfo<PPTViewerRouteArgs>(name);
}

class PPTViewerRouteArgs {
  const PPTViewerRouteArgs({
    this.key,
    required this.courseFileResource,
    this.checkPoints = const [],
    this.isFromExamScreen = false,
    this.isExamPassed = false,
    required this.navigateBackTo,
  });

  final Key? key;

  final CourseFileResource courseFileResource;

  final List<CheckPoint> checkPoints;

  final bool isFromExamScreen;

  final bool isExamPassed;

  final NavigationTo navigateBackTo;

  @override
  String toString() {
    return 'PPTViewerRouteArgs{key: $key, courseFileResource: $courseFileResource, checkPoints: $checkPoints, isFromExamScreen: $isFromExamScreen, isExamPassed: $isExamPassed, navigateBackTo: $navigateBackTo}';
  }
}

/// generated route for
/// [ProfileReviewScreen]
class ProfileReviewRoute extends PageRouteInfo<ProfileReviewRouteArgs> {
  ProfileReviewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ProfileReviewRoute.name,
          args: ProfileReviewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'ProfileReviewRoute';

  static const PageInfo<ProfileReviewRouteArgs> page =
      PageInfo<ProfileReviewRouteArgs>(name);
}

class ProfileReviewRouteArgs {
  const ProfileReviewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'ProfileReviewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [ProfileViewScreen]
class ProfileViewRoute extends PageRouteInfo<ProfileViewRouteArgs> {
  ProfileViewRoute({
    Key? key,
    bool isFromLogin = false,
    bool isFromSideMenu = false,
    List<PageRouteInfo>? children,
  }) : super(
          ProfileViewRoute.name,
          args: ProfileViewRouteArgs(
            key: key,
            isFromLogin: isFromLogin,
            isFromSideMenu: isFromSideMenu,
          ),
          initialChildren: children,
        );

  static const String name = 'ProfileViewRoute';

  static const PageInfo<ProfileViewRouteArgs> page =
      PageInfo<ProfileViewRouteArgs>(name);
}

class ProfileViewRouteArgs {
  const ProfileViewRouteArgs({
    this.key,
    this.isFromLogin = false,
    this.isFromSideMenu = false,
  });

  final Key? key;

  final bool isFromLogin;

  final bool isFromSideMenu;

  @override
  String toString() {
    return 'ProfileViewRouteArgs{key: $key, isFromLogin: $isFromLogin, isFromSideMenu: $isFromSideMenu}';
  }
}

/// generated route for
/// [RankListViewScreen]
class RankListViewRoute extends PageRouteInfo<RankListViewRouteArgs> {
  RankListViewRoute({
    required bool isCourseRankList,
    required List<Rank> rankListForExam,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          RankListViewRoute.name,
          args: RankListViewRouteArgs(
            isCourseRankList: isCourseRankList,
            rankListForExam: rankListForExam,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'RankListViewRoute';

  static const PageInfo<RankListViewRouteArgs> page =
      PageInfo<RankListViewRouteArgs>(name);
}

class RankListViewRouteArgs {
  const RankListViewRouteArgs({
    required this.isCourseRankList,
    required this.rankListForExam,
    this.key,
  });

  final bool isCourseRankList;

  final List<Rank> rankListForExam;

  final Key? key;

  @override
  String toString() {
    return 'RankListViewRouteArgs{isCourseRankList: $isCourseRankList, rankListForExam: $rankListForExam, key: $key}';
  }
}

/// generated route for
/// [ResetPasswordViewScreen]
class ResetPasswordViewRoute extends PageRouteInfo<ResetPasswordViewRouteArgs> {
  ResetPasswordViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ResetPasswordViewRoute.name,
          args: ResetPasswordViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordViewRoute';

  static const PageInfo<ResetPasswordViewRouteArgs> page =
      PageInfo<ResetPasswordViewRouteArgs>(name);
}

class ResetPasswordViewRouteArgs {
  const ResetPasswordViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'ResetPasswordViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [SectionDetailsViewScreen]
class SectionDetailsViewRoute
    extends PageRouteInfo<SectionDetailsViewRouteArgs> {
  SectionDetailsViewRoute({
    Key? key,
    required String sectionId,
    required SectionDetails? sectionDetailsData,
    required bool showFolderResources,
    required int folderIndex,
    required String title,
    List<PageRouteInfo>? children,
  }) : super(
          SectionDetailsViewRoute.name,
          args: SectionDetailsViewRouteArgs(
            key: key,
            sectionId: sectionId,
            sectionDetailsData: sectionDetailsData,
            showFolderResources: showFolderResources,
            folderIndex: folderIndex,
            title: title,
          ),
          initialChildren: children,
        );

  static const String name = 'SectionDetailsViewRoute';

  static const PageInfo<SectionDetailsViewRouteArgs> page =
      PageInfo<SectionDetailsViewRouteArgs>(name);
}

class SectionDetailsViewRouteArgs {
  const SectionDetailsViewRouteArgs({
    this.key,
    required this.sectionId,
    required this.sectionDetailsData,
    required this.showFolderResources,
    required this.folderIndex,
    required this.title,
  });

  final Key? key;

  final String sectionId;

  final SectionDetails? sectionDetailsData;

  final bool showFolderResources;

  final int folderIndex;

  final String title;

  @override
  String toString() {
    return 'SectionDetailsViewRouteArgs{key: $key, sectionId: $sectionId, sectionDetailsData: $sectionDetailsData, showFolderResources: $showFolderResources, folderIndex: $folderIndex, title: $title}';
  }
}

/// generated route for
/// [SettingsViewScreen]
class SettingsViewRoute extends PageRouteInfo<SettingsViewRouteArgs> {
  SettingsViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          SettingsViewRoute.name,
          args: SettingsViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SettingsViewRoute';

  static const PageInfo<SettingsViewRouteArgs> page =
      PageInfo<SettingsViewRouteArgs>(name);
}

class SettingsViewRouteArgs {
  const SettingsViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'SettingsViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [SignUpViewScreen]
class SignUpViewRoute extends PageRouteInfo<SignUpViewRouteArgs> {
  SignUpViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          SignUpViewRoute.name,
          args: SignUpViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SignUpViewRoute';

  static const PageInfo<SignUpViewRouteArgs> page =
      PageInfo<SignUpViewRouteArgs>(name);
}

class SignUpViewRouteArgs {
  const SignUpViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'SignUpViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [SplashScreen]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [StudyMaterialViewScreen]
class StudyMaterialViewRoute extends PageRouteInfo<StudyMaterialViewRouteArgs> {
  StudyMaterialViewRoute({
    Key? key,
    required ResourceProgress? resourceProgress,
    required int moduleLength,
    required CoursePageResource pageContent,
    List<PageRouteInfo>? children,
  }) : super(
          StudyMaterialViewRoute.name,
          args: StudyMaterialViewRouteArgs(
            key: key,
            resourceProgress: resourceProgress,
            moduleLength: moduleLength,
            pageContent: pageContent,
          ),
          initialChildren: children,
        );

  static const String name = 'StudyMaterialViewRoute';

  static const PageInfo<StudyMaterialViewRouteArgs> page =
      PageInfo<StudyMaterialViewRouteArgs>(name);
}

class StudyMaterialViewRouteArgs {
  const StudyMaterialViewRouteArgs({
    this.key,
    required this.resourceProgress,
    required this.moduleLength,
    required this.pageContent,
  });

  final Key? key;

  final ResourceProgress? resourceProgress;

  final int moduleLength;

  final CoursePageResource pageContent;

  @override
  String toString() {
    return 'StudyMaterialViewRouteArgs{key: $key, resourceProgress: $resourceProgress, moduleLength: $moduleLength, pageContent: $pageContent}';
  }
}

/// generated route for
/// [SubjectsDetailedTabViewScreen]
class SubjectsDetailedTabViewRoute
    extends PageRouteInfo<SubjectsDetailedTabViewRouteArgs> {
  SubjectsDetailedTabViewRoute({
    required SectionDetails? sectionDetailsData,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          SubjectsDetailedTabViewRoute.name,
          args: SubjectsDetailedTabViewRouteArgs(
            sectionDetailsData: sectionDetailsData,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SubjectsDetailedTabViewRoute';

  static const PageInfo<SubjectsDetailedTabViewRouteArgs> page =
      PageInfo<SubjectsDetailedTabViewRouteArgs>(name);
}

class SubjectsDetailedTabViewRouteArgs {
  const SubjectsDetailedTabViewRouteArgs({
    required this.sectionDetailsData,
    this.key,
  });

  final SectionDetails? sectionDetailsData;

  final Key? key;

  @override
  String toString() {
    return 'SubjectsDetailedTabViewRouteArgs{sectionDetailsData: $sectionDetailsData, key: $key}';
  }
}

/// generated route for
/// [SubscriptionInfoViewScreen]
class SubscriptionInfoViewRoute
    extends PageRouteInfo<SubscriptionInfoViewRouteArgs> {
  SubscriptionInfoViewRoute({
    required String planName,
    required List<PlanCourse> planInfo,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          SubscriptionInfoViewRoute.name,
          args: SubscriptionInfoViewRouteArgs(
            planName: planName,
            planInfo: planInfo,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'SubscriptionInfoViewRoute';

  static const PageInfo<SubscriptionInfoViewRouteArgs> page =
      PageInfo<SubscriptionInfoViewRouteArgs>(name);
}

class SubscriptionInfoViewRouteArgs {
  const SubscriptionInfoViewRouteArgs({
    required this.planName,
    required this.planInfo,
    this.key,
  });

  final String planName;

  final List<PlanCourse> planInfo;

  final Key? key;

  @override
  String toString() {
    return 'SubscriptionInfoViewRouteArgs{planName: $planName, planInfo: $planInfo, key: $key}';
  }
}

/// generated route for
/// [SubscriptionViewScreen]
class SubscriptionViewRoute extends PageRouteInfo<SubscriptionViewRouteArgs> {
  SubscriptionViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          SubscriptionViewRoute.name,
          args: SubscriptionViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SubscriptionViewRoute';

  static const PageInfo<SubscriptionViewRouteArgs> page =
      PageInfo<SubscriptionViewRouteArgs>(name);
}

class SubscriptionViewRouteArgs {
  const SubscriptionViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'SubscriptionViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [TopicListViewScreen]
class TopicListViewRoute extends PageRouteInfo<TopicListViewRouteArgs> {
  TopicListViewRoute({
    Key? key,
    bool isFromLogin = false,
    List<PageRouteInfo>? children,
  }) : super(
          TopicListViewRoute.name,
          args: TopicListViewRouteArgs(
            key: key,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'TopicListViewRoute';

  static const PageInfo<TopicListViewRouteArgs> page =
      PageInfo<TopicListViewRouteArgs>(name);
}

class TopicListViewRouteArgs {
  const TopicListViewRouteArgs({
    this.key,
    this.isFromLogin = false,
  });

  final Key? key;

  final bool isFromLogin;

  @override
  String toString() {
    return 'TopicListViewRouteArgs{key: $key, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [WelcomeViewScreen]
class WelcomeViewRoute extends PageRouteInfo<void> {
  const WelcomeViewRoute({List<PageRouteInfo>? children})
      : super(
          WelcomeViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'WelcomeViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}
