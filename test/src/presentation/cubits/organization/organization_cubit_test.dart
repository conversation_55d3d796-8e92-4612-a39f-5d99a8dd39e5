import 'package:SmartLearn/src/utils/constants/strings.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:SmartLearn/src/domain/models/organization.dart';
import 'package:SmartLearn/src/domain/repositories/organization_repository.dart';
import 'package:SmartLearn/src/presentation/cubits/organization/organization_cubit.dart';
import 'package:SmartLearn/src/utils/constants/api_response.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MockOrganizationRepository implements OrganizationRepository {
  @override
  Future<List<Organization>> getUserOrganizations(String userId) {
    return Future(() => MOCK_ORGANIZATION_LIST);
  }
}

Future<void> main() async {
  testWidgets('organization cubit ...', (tester) async {});

  late MockOrganizationRepository _mockOrganizationRepo;
  late OrganizationCubit _organizationCubit;

  setUp(() {
    SharedPreferences.setMockInitialValues({});
    USER_ID = MOCK_USER_ID;
    _mockOrganizationRepo = MockOrganizationRepository();
    _organizationCubit = OrganizationCubit(_mockOrganizationRepo);
  });

  tearDown(() {
    _organizationCubit.close();
  });

  group('OrganizationCubit', () {
    blocTest<OrganizationCubit, OrganizationState>(
      'emits OrganizationSuccess when organizations are fetched',
      build: () => _organizationCubit,
      act: (bloc) => bloc.getUserOrganizations(),
      expect: () => [
        const OrganizationSuccess(
            organizations: MOCK_ORGANIZATION_LIST, buttonColor: false)
      ],
    );
  });
}
