class AppBranding {
  static const Map<String, String> lightTheme = {
    "active": "Active",
    "org_id": "87e018ab-149b-43e4-ab75-3633951015d1",
    "favicon": "https://hff.com/favicon.ico",
    "app_logo": "https://example.com/app_logo.png",
    "valid_to": "2026-05-01T00:00:00+00:00",
    "main_logo": "https://hltp.com/logo.png",
    "created_at": "2025-05-19T10:48:26.357328+00:00",
    "font_color": "#4C4E4D",
    "theme_name": "Modern Blue",
    "updated_at": "2025-05-19T10:50:44.141251+00:00",
    "valid_from": "2025-05-01T00:00:00+00:00",
    "font_family": "Roboto, Arial, sans-serif",
    "footer_text": "© 2025 Example Corp. All rights reserved.",
    "banner_image": "https://example.com/banner.jpg",
    "welcome_text": "Welcome to Example Corp!",
    "font_base_size": "16px",
    "toast_info_color": "#1e88e5",
    "footer_text_color": "#ffffff",
    "navbar_text_color": "#ffffff",
    "toast_error_color": "#e5393",
    "top_bar_text_color": "#ffffff",
    "top_bar_active_color": "#3949ab",
    "top_bar_background_color": "#1a237e",
    "toast_success_color": "#43a047",
    "toast_warning_color": "#fbc02d",
    "app_background_color": "#00B9C7",
    "button_primary_color": "#1976d2",
    "sidebar_active_color": "#1976d2",
    "button_info_text_color": "#0d47a1",
    "button_secondary_color": "#e3f2fd",
    "button_dismiss_bg_color": "#eeeeee",
    "footer_background_color": "#1a237e",
    "navbar_background_color": "#283593",
    "navbar_text_color_hover": "#ffeb3b",
    "sidebar_background_color": "#00B9C7",
    "sidebar_active_background_color": "#e3f2fd",
    "sidebar_text_color": "#ffffff",
    "button_dismiss_text_color": "#757575",
    "button_primary_text_color": "#ffffff",
    "button_secondary_text_color": "#1976d2",
    "button_info_background_color": "#bbdefb",
  };
  static const Map<String, String> darkTheme = {
    "active": "Active",
    "org_id": "87e018ab-149b-43e4-ab75-3633951015d1",
    "favicon": "https://hff.com/favicon.ico",
    "app_logo": "https://example.com/app_logo.png",
    "valid_to": "2026-05-01T00:00:00+00:00",
    "main_logo": "https://hltp.com/logo.png",
    "created_at": "2025-05-19T10:48:26.357328+00:00",
    "font_color": "#E0E0E0", // Light grey for readability
    "theme_name": "Modern Blue Dark",
    "updated_at": "2025-05-19T10:50:44.141251+00:00",
    "valid_from": "2025-05-01T00:00:00+00:00",
    "font_family": "Roboto, Arial, sans-serif",
    "footer_text": "© 2025 Example Corp. All rights reserved.",
    "banner_image": "https://example.com/banner.jpg",
    "welcome_text": "Welcome to Example Corp!",
    "font_base_size": "16px",
    "toast_info_color": "#64b5f6", // Lighter info blue
    "footer_text_color": "#E0E0E0",
    "navbar_text_color": "#E0E0E0",
    "toast_error_color": "#ef5350", // Bright red
    "top_bar_text_color": "#E0E0E0",
    "top_bar_active_color": "#7986cb", // Lightened version
    "top_bar_background_color": "#121858", // Darker version
    "toast_success_color": "#66bb6a", // Light green
    "toast_warning_color": "#fdd835", // Brighter warning
    "app_background_color": "#121212", // Standard dark background
    "button_primary_color": "#2196f3", // Bright blue
    "sidebar_active_color": "#2196f3",
    "button_info_text_color": "#90caf9", // Softer text blue
    "button_secondary_color": "#1e1e1e", // Dark grey
    "button_dismiss_bg_color": "#2c2c2c", // Dark grey dismiss
    "footer_background_color": "#0d133d", // Very dark blue
    "navbar_background_color": "#1c1c3a", // Dark navy
    "navbar_text_color_hover": "#ffff8d", // Soft yellow hover
    "sidebar_background_color": "#1c1c1c",
    "sidebar_active_background_color": "#2e2e2e",
    "sidebar_text_color": "#E0E0E0",
    "button_dismiss_text_color": "#bdbdbd", // Light grey
    "button_primary_text_color": "#ffffff",
    "button_secondary_text_color": "#90caf9",
    "button_info_background_color": "#0d47a1", // Dark info background

  };
  
  static const Map<String, String> customTheme = {
    'top_bar_color': '#004953',
    'app_background_color': '#00CED1',
    'navigation_text_color': '#ffffff',
    'footer_background_color': '#ffffff',
    'footer_text': '#eeeeee',
    'button_primary_color': '#9fc089',
    'button_primary_text_color': '#ffffff',
    'button_secondary_color': '#000000',
    'button_secondary_text_color': '#ffffff',
    'button_dismiss_bg_color': '#000000',
    'button_dismiss_text_color': '#ffffff',
    'button_info_background_color': '#88e0ef',
    'button_info_text_color': '#003f5c',
    'toast_success_color': '#6fcf97',
    'toast_error_color': '#eb5757',
    'toast_warning_color': '#f2c94c',
    'toast_info_color': '#2f80ed',
    'font_family': 'Segoe UI, sans-serif',
  };
}
