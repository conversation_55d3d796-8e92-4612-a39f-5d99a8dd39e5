import 'courses_info.dart';

import 'course_analytics.dart';
import 'user_course.dart';

class CourseDashboardInfo {
  final List<UserCourse> userCourses;
  final int courseCount;
  final double totalProgress;
  final double totalPercentage;
  final double totalMarks;
  final String totalTimeSpent;
  final int totalAchievements;
  final int completedCourseCount;
  List<CoursesInfo> coursesInfo = [];
  CourseAnalyticsModel analyticsInfo;

  CourseDashboardInfo(
      {required this.userCourses,
      required this.courseCount,
      required this.totalProgress,
      required this.totalPercentage,
      required this.totalMarks,
      required this.totalTimeSpent,
      required this.totalAchievements,
      required this.completedCourseCount,
      this.coursesInfo = const [],
      required this.analyticsInfo});
}
