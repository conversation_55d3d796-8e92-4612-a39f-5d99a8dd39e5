// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_examtable.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveExamTableAdapter extends TypeAdapter<HiveExamTable> {
  @override
  final int typeId = 0;

  @override
  HiveExamTable read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveExamTable(
      fields[0] as int,
      fields[1] as String,
      fields[2] as String,
      fields[3] as String,
      fields[4] as String,
      fields[5] as String,
      fields[6] as String,
      fields[7] as String,
      fields[8] as String,
      fields[9] as String,
      fields[10] as String,
      fields[11] as String,
      fields[12] as dynamic,
      fields[13] as String,
      fields[14] as int,
      fields[15] as int,
    );
  }

  @override
  void write(BinaryWriter writer, HiveExamTable obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.examId)
      ..writeByte(2)
      ..write(obj.quizAttemptId)
      ..writeByte(3)
      ..write(obj.examName)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.duration)
      ..writeByte(6)
      ..write(obj.endTime)
      ..writeByte(7)
      ..write(obj.startTime)
      ..writeByte(8)
      ..write(obj.passMark)
      ..writeByte(9)
      ..write(obj.mainTopic)
      ..writeByte(10)
      ..write(obj.totalMark)
      ..writeByte(11)
      ..write(obj.numOfQuestions)
      ..writeByte(12)
      ..write(obj.questAnswers)
      ..writeByte(13)
      ..write(obj.networkStatus)
      ..writeByte(14)
      ..write(obj.examUploadStatus)
      ..writeByte(15)
      ..write(obj.examCompletedStatus);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveExamTableAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
