class VideoPlayerData {
  int? id;
  late String videoId;
  late int startTime;
  late int endTime;
  late int milestonesCompleted;
  late int lastWatchTime;
  late String milestoneDurations;

  VideoPlayerData({
    this.id,
    required this.videoId,
    required this.startTime,
    required this.endTime,
    required this.milestonesCompleted,
    required this.lastWatchTime,
    required this.milestoneDurations,
  });

  VideoPlayerData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    milestonesCompleted = json['milestones_completed'];
    lastWatchTime = json['last_watch_time'];
    milestoneDurations = json['milestone_durations'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = id;
    data['video_id'] = videoId;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['milestones_completed'] = milestonesCompleted;
    data['last_watch_time'] = lastWatchTime;
    data['milestone_durations'] = milestoneDurations;
    return data;
  }
}
