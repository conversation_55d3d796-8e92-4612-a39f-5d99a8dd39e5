import 'dart:async';

import '../../../config/app_config/api_constants.dart';
import '/src/domain/models/purchase_history/purchase_history.dart';

import '/src/domain/models/subscription_plan/plan_course_result.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../domain/repositories/user_repository.dart';
import '../../../utils/constants/helper.dart';
import '/src/domain/repositories/subscription_reporsitory.dart';
import '/src/utils/constants/strings.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../domain/models/subscription_plan/subscription_plan.dart';

part 'subscription_state.dart';

class SubscriptionCubit extends Cubit<SubscriptionState> {
  final SubscriptionRepository _subscriptionRepository;
  final UserRepository _userRepository;

  SubscriptionCubit(
    this._subscriptionRepository,
    this._userRepository,
  ) : super(SubscriptionInitial());

  List<SubscriptionPlan> subscriptionPlans = [];
  Timer? _timer;

  getSubscriptionPlans() async {
    try {
      if (ORG_ID.isNotEmpty && USER_ID.isNotEmpty) {
        Map<String, dynamic> reqBody = {
          "user_id": USER_ID,
          "org_id": ORG_ID,
        };
        List<SubscriptionPlan> _plans =
            await _subscriptionRepository.getSubscriptionPlans(reqBody);
        _plans.retainWhere((element) =>
            element.subscriptionPlanStatus == SubscriptionPlanStatus.ACTIVE);
        _plans.removeWhere((element) =>
            element.price == null || ((element.price ?? 0.0) <= 0.0));

        subscriptionPlans = _plans;
        subscriptionPlans
            .sort((a, b) => (a.name ?? '').compareTo((b.name ?? '')));
        emit(SubscriptionsFetched(plansList: _plans));
      } else {
        emit(const SubscriptionsFailure(error: 'Invalid organization'));
      }
    } on PostgrestException catch (e) {
      if (e.code == TOKEN_EXPIRED_STATUS_CODE) {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          await getSubscriptionPlans();
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      }
      debugPrint(
          '[SubscriptionCubit][Exception][getSubscriptionPlans]: ${e.details}');
      emit(SubscriptionsFailure(error: e.details));
    } on Exception catch (e) {
      debugPrint(
          '[SubscriptionCubit][PostgrestException][getSubscriptionPlans]: ${e.toString()}');
      emit(SubscriptionsFailure(error: e.toString()));
    }
  }

  submitSelectedPlan(String planId) async {
    try {
      if (ORG_ID.isNotEmpty && USER_ID.isNotEmpty && planId.isNotEmpty) {
        Map<String, dynamic> reqBody = {
          "user_id": USER_ID,
          "org_id": ORG_ID,
          "plan_id": planId,
          "subscription_plan_for_user_data": {
            "payment_method": "cash", //not mandatory for future use
            // "start_date": "2024-01-18 11:00:00", //not mandatory for future use
            // "end_date": "2025-01-19 11:00:00", //not mandatory for future use
            // "purchase_date":
            //     "2024-01-22 11:00:00" //not mandatory for future use, this is for temperory data.
          }
        };

        bool submissionStatus =
            await _subscriptionRepository.submitSelectedPlan(reqBody);

        if (submissionStatus) {
          bool isPlanApproved = await getPlanStatus(planId);

          /// admin approves the selected plan
          /// listen in interval whether the plan is approved
          ///
          if (isPlanApproved) {
            emit(SubscriptionApproved(
                isApproved: isPlanApproved, selectedPlanId: planId));
          } else {
            emit(SubscriptionApproved(
                isApproved: false, selectedPlanId: planId));
            await fetchPlanStatus(planId);
          }
        } else {
          emit(const SubscriptionsFailure(error: 'Invalid Plan'));
        }
        // emit(SubscriptionSubmitted(submissionStatus: submissionStatus));
      } else {
        emit(const SubscriptionsFailure(error: 'Invalid Plan'));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          '[SubscriptionCubit][Exception][submitSelectedPlan]: ${e.details}');
      emit(SubscriptionsFailure(error: e.details));
    } on Exception catch (e) {
      debugPrint(
          '[SubscriptionCubit][PostgrestException][submitSelectedPlan]: ${e.toString()}');
      emit(SubscriptionsFailure(error: e.toString()));
    }
  }

  Future<bool> getPlanStatus(String planId) async {
    try {
      if (ORG_ID.isNotEmpty && USER_ID.isNotEmpty && planId.isNotEmpty) {
        Map<String, dynamic> reqBody = {
          "user_id": USER_ID,
          "org_id": ORG_ID,
          "plan_id": planId,
        };

        bool isApproved =
            await _subscriptionRepository.getSubscriptionPlanStatus(reqBody);
        return isApproved;
        // emit(SubscriptionApproved(isApproved: isApproved));
        // } else {
        //  // emit(const SubscriptionsFailure(error: 'Invalid Plan'));
      }
      return false;
    } on PostgrestException catch (e) {
      if (e.code == 'P0001') {
        return false;
      } else {
        debugPrint(
            '[SubscriptionCubit][Exception][submitSelectedPlan]: ${e.details}');
        emit(SubscriptionsFailure(error: e.details));
      }
    } on Exception catch (e) {
      debugPrint(
          '[SubscriptionCubit][PostgrestException][submitSelectedPlan]: ${e.toString()}');
      emit(SubscriptionsFailure(error: e.toString()));
    }
    return false;
  }

  onIndexChange(int index) {
    emit(OnSubscriptionIndexChange(selectedIndex: index));
  }

  setLoading(bool status) {
    emit(IsLoading(isLoading: status));
  }

  /// listen to the status
  /// whether the plan is approved
  /// by the admin
  fetchPlanStatus(String planId) async {
    // _timer = Timer.periodic(const Duration(seconds: 5), (_) async {
    try {
      print('---------168');
      bool isApproved = await getPlanStatus(planId);
      print('---------170');
      if (isApproved) {
        print('---------172');
        emit(SubscriptionApproved(isApproved: true, selectedPlanId: planId));
      }
      print('---------175');
    } on PostgrestException catch (e) {
      debugPrint(
          '[SubscriptionCubit][Exception][submitSelectedPlan]: ${e.details}');
      emit(SubscriptionsFailure(error: e.details));
    } on Exception catch (e) {
      debugPrint(
          '[SubscriptionCubit][PostgrestException][submitSelectedPlan]: ${e.toString()}');
      emit(SubscriptionsFailure(error: e.toString()));
    }
    // });
  }

  ///change the plans status to approved
  ///and update the list
  ///
  updatePlansList(String planId, bool isApproved) async {
    int index = subscriptionPlans.indexWhere((element) => element.id == planId);
    if (index != -1) {
      subscriptionPlans[index].isUserSubscribed = true;
      subscriptionPlans[index].userSubscriptionStatus =
          isApproved ? PlanStatusEnum.APPROVED : PlanStatusEnum.PENDING;
      emit(SubscriptionsFetched(plansList: subscriptionPlans));
    }
  }

  ///
  /// fetch list of course/resource linked to a plan
  ///

  Future<List<PlanCourse>> getCourseLinkedtoPlan(String planId) async {
    try {
      if (planId.isNotEmpty && ORG_ID.isNotEmpty) {
        Map<String, dynamic> reqBody = {
          "org_id": ORG_ID,
          "plan_id": planId,
        };
        List<PlanCourse> _planCourse =
            await _subscriptionRepository.getCourseForSelectedPlan(reqBody);
        return _planCourse;
      }
    } on Exception catch (e) {
      debugPrint('[SubscriptionCubit][getCourseLinkedtoPlan][Exception]: $e');
    }
    return [];
  }

  ///
  /// fetch user purchase history
  ///
  getUserPurchaseHistory() async {
    try {
      if (ORG_ID.isNotEmpty) {
        Map<String, dynamic> reqBody = {"org_id": ORG_ID};

        List<PurchaseHistoryResult> _resultList =
            await _subscriptionRepository.getUserPurchaseHistory(reqBody);

        List<PurchasedPlanListsByUser> purchasedPlans = [];
        List<PurchasedPlanListsByUser> activePlans = [];
        List<PendingPlanListsByUser> pendingPlans = [];

        if (_resultList.isNotEmpty) {
          PurchaseHistoryResult _result = _resultList.first;
          purchasedPlans = _result.purchasedPlanListsByUser ?? [];

          activePlans =
              purchasedPlans.where((element) => !element.isExpired).toList();

          pendingPlans = _result.pendingPlanListsByUser ?? [];

          purchasedPlans.removeWhere((element) =>
              (element.price == null || ((element.price ?? 0.0) <= 0.0)) ||
              !element.isExpired);

          purchasedPlans
              .sort((a, b) => (a.planName ?? '').compareTo((b.planName ?? '')));
          activePlans
              .sort((a, b) => (a.planName ?? '').compareTo((b.planName ?? '')));
          pendingPlans
              .sort((a, b) => (a.planName ?? '').compareTo((b.planName ?? '')));
        }

        emit(PurchaseHistoryFetched(
            purchasedPlans: purchasedPlans,
            activePlans: activePlans,
            pendingPlans: pendingPlans));
      } else {
        emit(const PurchaseHistoryFailure(error: 'Invalid organization'));
      }
    } on PostgrestException catch (e) {
      if (e.code == TOKEN_EXPIRED_STATUS_CODE) {
        /// token refresh failed
        ///
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          await getUserPurchaseHistory();
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      }
      debugPrint(
          '[SubscriptionCubit][Exception][getUserPurchaseHistory]: ${e.details}');
      emit(PurchaseHistoryFailure(error: e.details));
    } on Exception catch (e) {
      debugPrint(
          '[SubscriptionCubit][PostgrestException][getUserPurchaseHistory]: ${e.toString()}');
      emit(PurchaseHistoryFailure(error: e.toString()));
    }
  }
}
