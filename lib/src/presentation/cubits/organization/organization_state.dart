part of 'organization_cubit.dart';

abstract class OrganizationState extends Equatable {
  final List<Organization> organizations;
  final List<TopicList> topicList;

  //final buttonColor;

  const OrganizationState({
    this.organizations = const [],
    this.topicList = const [],
  });

  @override
  List<Object> get props => [
        organizations,
      ];
}

class OrganizationInitial extends OrganizationState {
  final buttonColor;
  const OrganizationInitial({this.buttonColor});
}

class OrganizationLoading extends OrganizationState {
  final bool isDataLoading;
  const OrganizationLoading({required this.isDataLoading});
}

class OrganizationSuccess extends OrganizationState {
  final List<Organization> organizations;
  final buttonColor;

  const OrganizationSuccess(
      {required this.organizations, required this.buttonColor
      // required this.topicList
      });
  @override
  List<Object> get props => [organizations, buttonColor];
}

class OrganizationError extends OrganizationState {
  final String error;
  final bool buttonColor;

  const OrganizationError({required this.error, required this.buttonColor});
  @override
  List<Object> get props => [error, buttonColor];
}

class UpdateSelectedOrgColor extends OrganizationState {
  final bool shouldUpdateColor;

  const UpdateSelectedOrgColor({required this.shouldUpdateColor});

  @override
  List<Object> get props => [shouldUpdateColor];
}

class PrivilegeAccessFetched extends OrganizationState {
  final PrivilegeAccess privilegeAccess;

  const PrivilegeAccessFetched({required this.privilegeAccess});

  @override
  List<Object> get props => [privilegeAccess];
}

class PrivilegeAccessError extends OrganizationState {
  final String error;
  const PrivilegeAccessError({required this.error});

  @override
  List<Object> get props => [error];
}
