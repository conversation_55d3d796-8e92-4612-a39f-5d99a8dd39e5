import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:SmartLearn/src/domain/models/hive_db_models/hive_examtable.dart';
import '/src/domain/repositories/user_repository.dart';
import 'package:SmartLearn/src/domain/services/hive_db_service.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../config/app_config/preference_config.dart';
import '../../../domain/models/check_point_quiz.dart';
import '../../../domain/services/localizations/sl_strings.dart';
import '../../../domain/services/localizations/string_keys.dart';
import '/src/domain/models/exam_summary.dart';
import '/src/domain/services/db_helper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase/supabase.dart';

import '../../../domain/models/exam.dart';
import '../../../domain/models/exam_review.dart';
import '/src/config/router/app_router.dart';
import '/src/domain/repositories/exam_repository.dart';
import '/src/utils/constants/helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signature/signature.dart';

import '../../../utils/constants/strings.dart';
import '../../../domain/models/question_data.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'exam_state.dart';

class ExamCubit extends Cubit<ExamState> {
  final ExamRepository _examRepository;
  final UserRepository _userRepository;
  ExamCubit(this._examRepository, this._userRepository)
      : super(ExamListInitial());

  final DbHelper _dbHelper = DbHelper.instance;

  List<ExamSummary> examSummaryResult = [];

// Exam Review Screen
  List<ExamReview> _examReviewData = [];

  CheckPointQuiz? checkPointData;

  dynamic checkPointResponseData;
  bool isNetworkAvailable = true;

  // for routing purpose in web
  bool navigatedToExamIntro = false;
  bool disableNavigationBackToIntro = false;
  bool disableNavigationBackToExam = false;
  bool disableNavigationBackToReview = false;
  bool disableNavigationBackToResult = false;
  bool disableNavigationBackToList = false;

  int selectedTab = 0;

  /// fetch new list of exams for a course
  fetchExamList() async {
    try {
      String courseId = COURSE_ID;
      String orgId = ORG_ID;
      String userId = USER_ID;
      if (userId.isNotEmpty && courseId.isNotEmpty && orgId.isNotEmpty) {
        List<Exam> exams =
            await _examRepository.fetchExamList(orgId, courseId, userId);
        List<Exam> attemptedExams = [];
        // Removed becoz of the 2 diff api calls
        //await _examRepository.fetchAttemptedExamList(orgId, courseId, userId);
        emit(ExamsFetched(exams: exams, attemptedExams: attemptedExams));
      }
      // ignore: avoid_catches_without_on_clauses
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][ExamCubit][fetchExamList] :${e.toString()}");
      if (e.code == 'PGRST301') {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          fetchExamList();
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      } else {
        emit(ExamsFailureState(error: e.details.toString()));
      }
    } on Exception catch (e) {
      debugPrint("[Exception][ExamCubit][fetchExamList] :${e.toString()}");
      emit(ExamsFailureState(error: e.toString()));
    }
  }

  /// fetch attempted/passedlist of exams for a course
  fetchAttemptedExamList() async {
    try {
      String courseId = COURSE_ID;
      String orgId = ORG_ID;
      String userId = USER_ID;
      if (userId.isNotEmpty && courseId.isNotEmpty && orgId.isNotEmpty) {
        List<Exam> attemptedExams = await _examRepository
            .fetchAttemptedExamList(orgId, courseId, userId);
        emit(AttemptedExamsFetched(attemptedExams: attemptedExams));
      }
      // ignore: avoid_catches_without_on_clauses
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][ExamCubit][fetchAttemptedExamList] :${e.toString()}");
      if (e.code == 'PGRST301') {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          fetchExamList();
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      } else {
        emit(ExamsFailureState(error: e.details.toString()));
      }
    } on Exception catch (e) {
      debugPrint("[Exception][ExamCubit][fetchExamList] :${e.toString()}");
      emit(ExamsFailureState(error: e.toString()));
    }
  }

  /// Fetch Exam Questions
  fetchQuestions(String examId) async {
    List<QuestionData> _questionData = [];
    List<Question> _questions = [];
    try {
      if (examId.isNotEmpty) {
        _questionData = await _examRepository.fetchQuestionsOfQuiz(examId);
        _questions =
            _questionData.isNotEmpty ? _questionData.first.questions : [];
      }
      // _questionData =
      //     await _fetchQstnsFromDB(_questionData[0].examId) ?? _questionData;
      emit(ExamQuestionsFetched(examData: _questionData));
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint("[Exception][ExamListCubit][fetchQuestions] :${e.toString()}");
      emit(ExamsQuestionsFailureState(error: e.toString()));
    }
  }

  /// fetch checkpoint Exam
  startCheckpointQuiz(String checkPointId) async {
    List<Question> _questions = [];
    String orgId = ORG_ID;
    String userId = USER_ID;
    SharedPreferences _pref = await SharedPreferences.getInstance();
    try {
      if (checkPointId.isNotEmpty && orgId.isNotEmpty && userId.isNotEmpty) {
        Map<String, dynamic> jsonBody = {
          "checkpoint_id": checkPointId,
          "org_id": orgId,
          "user_id": userId,
          "user_start_time": DateTime.now().toString()
        };
        checkPointData = await _examRepository.fetchCheckpointQuiz(jsonBody);
        if (checkPointData != null && checkPointData?.questions?.length != 0) {
          _questions = checkPointData?.questions?.first.questions ?? [];
          await _pref.setString(
              PREF_KEY_QUIZ_ATTEMPT_ID, checkPointData?.quizAttemptId ?? '');
          emit(ExamQuestionsFetched(examData: checkPointData?.questions ?? []));
        }
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][ExamCubit][startCheckpointQuiz] :${e.toString()}");
      emit(ExamPGException(error: e.details));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][ExamListCubit][startCheckpointQuiz] :${e.toString()}");
      emit(ExamsQuestionsFailureState(error: e.toString()));
    }
  }

  ///
  /// save the questions to db
  ///
  saveQstnsToDB(QuestionData questionData) async {
    await checkNetworkAvailability();
    questionData.networkStatus = isNetworkAvailable.toString();

    try {
      String _questions = jsonEncode(questionData.questions);
      Map<String, dynamic> _questionJson = questionData.toJson();
      _questionJson['quest_answers'] = _questions;
      if (!kIsWeb) {
        int status =
            await _dbHelper.insertOrUpdate(_questionJson, questionData.examId);
      } else {
        final value = HiveExamTable(
          questionData.id,
          questionData.examId,
          questionData.quizAttemptId,
          questionData.examName,
          questionData.intro,
          questionData.duration.toString(),
          questionData.endTime.toString(),
          questionData.startTime.toString(),
          questionData.passMark.toString(),
          questionData.mainTopic,
          questionData.totalMark.toString(),
          questionData.numOfQuestions.toString(),
          _questions,
          questionData.networkStatus,
          questionData.examUploadStatus,
          questionData.examCompletionStatus,
        );
        await HiveDbHelper().hiveInsertOrUpdate(value);
      }

      // _dbHelper.dropTable();
    } on Exception catch (e) {
      debugPrint("[Exception][ExamListCubit][_saveQstnsToDB] :${e.toString()}");
    }
  }

  ///
  /// fetch the questions from db
  ///

  Future<Map<String, dynamic>> fetchQstnsFromDB(String examId) async {
    Map<String, dynamic> updatedQstnsMap = {};
    try {
      List<Map<String, dynamic>> queryData =
          await _dbHelper.queryForExam(examId);
      List<dynamic> _questions = [];
      if (queryData.isNotEmpty) {
        Map<String, dynamic> _questionJson = List.from(queryData).first;
        updatedQstnsMap = {};
        if (_questionJson.containsKey('quest_answers')) {
          _questions = jsonDecode(_questionJson['quest_answers']);

          _questions.map((x) {
            x['answers'] = jsonDecode(x['answers']);
          });
          // _questions['answers'] = answers;
          updatedQstnsMap = {
            ..._questionJson, // Copy the original map
            'quest_answers':
                _questions, // Add/Update the 'data' field with the new value
          };
        }
      }
      List<QuestionData> _questionFromJson = List<QuestionData>.from(
          [updatedQstnsMap].map((x) => QuestionData.fromDBJson(x, examId)));
      return updatedQstnsMap;
    } on Exception catch (e) {
      debugPrint(
          "[Exception][ExamListCubit][_fetchQstnsFromDB] :${e.toString()}");
    }
    return {};
  }

  ///
  /// start examination
  /// informs the server the exam has started
  ///
  startExam(List<Question> questions, String examId) async {
    try {
      SharedPreferences _pref = await SharedPreferences.getInstance();
      String userId = USER_ID;
      String orgId = ORG_ID;

      // clear exisiting quiz attempt ids; if any
      _pref.remove(PREF_KEY_QUIZ_ATTEMPT_ID);

      if (userId.isNotEmpty && orgId.isNotEmpty && examId.isNotEmpty) {
        Map<String, dynamic> jsonBody = {
          "org_id": orgId,
          "quiz_id": examId,
          "user_id": userId,
          "user_start_time": DateTime.now().toString()
        };

        Map<String, dynamic> responseData =
            await _examRepository.startExam(jsonBody);
        if (responseData.containsKey('quiz_attempt_id')) {
          debugPrint('quiz attempt id: ${responseData['quiz_attempt_id']}');
          // save this id and pass when submit the exam
          // QUIZ_ATTEMPT_ID = responseData['quiz_attempt_id'];
          _pref.setString(
              PREF_KEY_QUIZ_ATTEMPT_ID, responseData['quiz_attempt_id']);
          emit(ExamsStarted(questions: questions));
        } else {
          emit(ExamQuizIdFailure(
              error: 'Your request has been rejected by admin'));
        }
      }
    } on PostgrestException catch (e) {
      debugPrint("[PostgrestException][ExamCubit][startExam] :${e.toString()}");
      emit(ExamPGException(error: e.details));
    } on Exception catch (e) {
      debugPrint("[Exception][ExamCubit][startExam] :${e.toString()}");
      emit(ExamQuizIdFailure(error: e.toString()));
    }
  }

  setExamListLoading(bool status) {
    emit(ExamsLoading(loadingStatus: status));
  }

  setLoading(bool status) {
    if (status) {
      emit(ExamsLoading(loadingStatus: status));
    } else {
      emit(ExamListInitial());
    }
  }

  setQuestionsLoading(bool status) {
    emit(ExamQuestionsLoading(loadingStatus: status));
  }

  setLoadingWithoutIndicator(bool status) {
    if (status) {
      emit(ExamsLoadingWithoutIndicator(loadingStatus: status));
    } else {
      emit(ExamListInitial());
    }
  }

  enableSubmitBtn(bool status) {
    emit(ExamSubmitBtnState(enableSubmitBtn: status));
  }

  Future<void> updateColor(String text, List<int> indexes) async {
    if (text.isNotEmpty) {
      emit(ExamListButtonColor(buttonColor: true, indexes: indexes));
    } else {
      emit(ExamListButtonColor(buttonColor: false, indexes: indexes));
    }
  }

  Future<void> showIcon(bool show) async {
    if (show == true) {
      emit(ExamListShowIcon(button: true));
    } else {
      emit(ExamListShowIcon(button: false));
    }
  }

  changeIndex(int index) async {
    emit(ExamListChangeIndex(changedIndex: index));
  }

  scrollToIndex(int index) async {
    emit(ExamQuestionScrollToIndex(scrollToIndex: index));
  }

  void selectOption(int value) {
    emit(ExamListChangeRadioButton(RadioValue: value));
  }

  /// Save selected answer
  /// calls every time when an answer is selected
  ///
  saveAnswer(
      QuestionData _questionData,
      Question answeredQuestion,
      int selectedQuestionIndex,
      List<Map<String, dynamic>> answeredQuestionList,
      Map<String, dynamic> examJson) async {
    SharedPreferences _pref = await SharedPreferences.getInstance();
    String quizAttemptId = _pref.getString(PREF_KEY_QUIZ_ATTEMPT_ID) ?? '';
    _questionData.quizAttemptId = quizAttemptId;
    List<Answer> answers = answeredQuestion.options
        .where((element) =>
            answeredQuestion.selectedAnswerIds.contains(element.answerId))
        .toList();
    List<String> answerTitles = answers.map((e) => e.answerVal).toList();

    ///
    /// order id field is added for sorting purpose from ui side
    /// which is the question_slot in db
    /// not actually required in server side
    Map<String, dynamic> newQuestion = {
      "order_id": answeredQuestion.id,
      "question_id": answeredQuestion.questionId,
      "question_with_options": answeredQuestion.questionText,
      "response_summary": answerTitles.join(', '),
      "selected_answer_ids": answeredQuestion.selectedAnswerIds,
      // "question_type": answeredQuestion.questionType,
    };

    int answeredQuestionIndex = answeredQuestionList.indexWhere(
        (element) => element.values.contains(answeredQuestion.questionId));

    if (answeredQuestionIndex != -1) {
      /// question already answered
      /// exists in the list
      /// replace the answer details
      ///
      answeredQuestionList[answeredQuestionIndex] = newQuestion;
    } else {
      /// question not yet answered
      /// does not exist in the list
      /// add the answer details to list
      ///
      answeredQuestionList.add(newQuestion);
    }

    answeredQuestionList.toSet().toList();
    examJson = {
      "org_id": ORG_ID,
      "quiz_id": answeredQuestion.quizId,
      "user_id": USER_ID,
      "submit_datas": answeredQuestionList,
    };

    _questionData.questions[selectedQuestionIndex] = answeredQuestion;

    // TO DO: To be submitted to sqflite db
    // String examJsonData = jsonEncode(_questionData.toJson());
    // saveToDB(_questionData);
    _questionData.examCompletionStatus = 0;
    await saveQstnsToDB(_questionData);
    emit(ExamSaveAnswer(questionData: _questionData, examJsonBody: examJson));
  }

  /// Submit exam data
  submitExam(Map<String, dynamic> jsonBody, QuestionData _questionData) async {
    // setLoading(true);
    final responseData;
    SharedPreferences _pref = await SharedPreferences.getInstance();
    String quizAttemptId = _pref.getString(PREF_KEY_QUIZ_ATTEMPT_ID) ?? '';
    _questionData.quizAttemptId = quizAttemptId;
    _questionData.examCompletionStatus = 1;
    await saveQstnsToDB(_questionData);

    try {
      jsonBody['quiz_attempt_id'] = quizAttemptId;
      responseData = await _examRepository.submitExam(jsonBody);
      if (responseData.toString() == 'success') {
        _pref.remove(PREF_KEY_QUIZ_ATTEMPT_ID);
        _questionData.examUploadStatus = 1;
        emit(ExamsSubmitted());
      } else {
        _questionData.examUploadStatus = 0;

        emit(ExamsQuestionsFailureState(error: responseData.toString()));
      }
      await saveQstnsToDB(_questionData);
      // ignore: avoid_catches_without_on_clauses
    } on PostgrestException catch (error) {
      debugPrint(
          '[PostgrestException][ExamCubit][Submit Exam] => ${error.details.toString()}');
      if (error.code == 'PGRST301') {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          submitExam(jsonBody, _questionData);
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      } else {
        emit(ExamsQuestionsFailureState(
          error: error.details.toString(),
        ));
      }
    } on Exception catch (e) {
      debugPrint("[Exception][ExamCubit][Submit Exam] :${e.toString()}");
      emit(ExamsQuestionsFailureState(error: e.toString()));
    }
  }

  /// submit checkPoint exam
  submitCheckPointExam(
      Map<String, dynamic> jsonBody, QuestionData _questionData) async {
    setLoading(true);

    SharedPreferences _pref = await SharedPreferences.getInstance();
    String quizAttemptId = _pref.getString(PREF_KEY_QUIZ_ATTEMPT_ID) ?? '';
    _questionData.quizAttemptId = quizAttemptId;
    _questionData.examCompletionStatus = 1;
    await saveQstnsToDB(_questionData);

    try {
      jsonBody['quiz_attempt_id'] = quizAttemptId;
      checkPointResponseData =
          await _examRepository.submitCheckPointExam(jsonBody);
      if (checkPointResponseData != null &&
          checkPointResponseData['status'] == "success") {
        _pref.remove(PREF_KEY_QUIZ_ATTEMPT_ID);
        _questionData.examUploadStatus = 1;
        emit(CheckPointQuizSubmitted(
            sessionStatus: checkPointResponseData['result']));
      } else {
        _questionData.examUploadStatus = 0;

        emit(ExamsQuestionsFailureState(
            error: checkPointResponseData.toString()));
      }
      await saveQstnsToDB(_questionData);
      // ignore: avoid_catches_without_on_clauses
    } on PostgrestException catch (error) {
      debugPrint(
          '[PostgrestException][ExamCubit][Submit Exam] => ${error.details.toString()}');
      if (error.code == 'PGRST301') {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          submitCheckPointExam(jsonBody, _questionData);
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      } else {
        emit(ExamsQuestionsFailureState(
          error: error.details.toString(),
        ));
      }
    } on Exception catch (e) {
      debugPrint("[Exception][ExamCubit][Submit Exam] :${e.toString()}");
      emit(ExamsQuestionsFailureState(error: e.toString()));
    }
  }
/*
  /// Save exam info to db
  saveToDB(QuestionData questionData) async {
    try {
      Map<String, dynamic> questionDataJson = questionData.toJson();
      _dbHelper.insert(questionDataJson);
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint("[Exception][ExamCubit][saveToDB] :${e.toString()}");
    }
  }

  fetchFromDB() async {
    try {
      List<Map<String, dynamic>> xx = await _dbHelper.queryAllRows();
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint("[Exception][ExamCubit][fetchFromDB] :${e.toString()}");
    }
  }*/

  ///
  ///Drawing Pad
  ///

  // export drawn image
  Future<void> exportImage(BuildContext context, SignatureController controller,
      String remarks) async {
    if (controller.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          key: Key('snackbarPNG'),
          content: Text(EMPTY_CONTENT),
        ),
      );
      return;
    }

    final Uint8List? data =
        await controller.toPngBytes(height: 1000, width: 1000);
    if (data == null) {
      return;
    }
    emit(DrawingPadStateChange(
        imageData: data, controllerStatus: controller.disabled));

    appRouter.push(DrawingReviewViewRoute(imageData: data, remarks: remarks));
  }

  ///undo last edits in the drawing view
  undoLastEdit(SignatureController _signatureController) {
    _signatureController.undo();
    emit(
        DrawingPadStateChange(controllerStatus: _signatureController.disabled));
  }

  ///redo last edits in the drawing view
  redoLastEdit(SignatureController _signatureController) {
    _signatureController.redo();
    emit(
        DrawingPadStateChange(controllerStatus: _signatureController.disabled));
  }

  ///clear the drawing view
  clearAll(SignatureController _signatureController) {
    _signatureController.clear();
    emit(
        DrawingPadStateChange(controllerStatus: _signatureController.disabled));
  }

  toggleController(SignatureController _signatureController) {
    _signatureController.disabled = !_signatureController.disabled;

    emit(
        DrawingPadStateChange(controllerStatus: _signatureController.disabled));
  }

  /// convert to file image and save
  Future<void> saveImage(Uint8List data, BuildContext context) async {
    File fileImage = await convertUint8ListToPath(data);
    emit(DrawingComplete(fileImage: fileImage));
  }

  /// Fetch submitted exam review list
  fetchReviewList(String quizId, String quizAttemptId) async {
    try {
      if (quizId.isNotEmpty) {
        List<dynamic> responseJson =
            await _examRepository.fetchExamReviewList(quizId, quizAttemptId);

        // if (_examReviewData.length > 100) {
        //  _examReviewData = _examReviewData.sublist(0, 100);
        // }

        /// fetch qstn details from db and show the data
        /// from db, instead of api
        ///
        /// fix for issue: exam review takes time
        /// to load qstn & answers of html content
        ///

        if (!kIsWeb) {
          Map<String, dynamic> attendedExamInfo =
              await fetchQstnsFromDB(quizId);
          List<dynamic> qstns = attendedExamInfo.containsKey('quest_answers')
              ? attendedExamInfo['quest_answers']
                  .map((item) => item as Map<String, dynamic>)
                  .toList()
              : [];

          List<ExamReview> questionFromJson = List<ExamReview>.from(qstns.map(
              (dbJson) => ExamReview.fromDBJson(dbJson, responseJson.first)));
          _examReviewData = questionFromJson;
          emit(ExamReviewFetched(examReviewData: _examReviewData));
        } else {
          final quizData = await HiveDbHelper().fetchQuestions(quizId);
          final List<dynamic> _questions = jsonDecode(quizData?.questAnswers);
          List<ExamReview> questionFromJson = List<ExamReview>.from(
              _questions.map((dbJson) =>
                  ExamReview.fromDBJson(dbJson, responseJson.first)));
          _examReviewData = questionFromJson;
          emit(ExamReviewFetched(examReviewData: _examReviewData));
        }
      } else {
        emit(ExamReviewFailureState(
            error: SLStrings.getTranslatedString(
                KEY_DIALOGUE_CONTENT_REVIEW_FAILURE)));
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint("[Exception][ExamCubit][fetchReviewList] :${e.toString()}");
      emit(ExamReviewFailureState(error: e.toString()));
    }
  }

  examReviewLoading(bool status) {
    if (status) {
      emit(ExamReviewLoading(loadingStatus: true));
    } else {
      emit(ExamReviewFetched(examReviewData: _examReviewData));
    }
  }

  checkNetworkAvailability() async {
    isNetworkAvailable = await checkNetworkStatus();
    //  emit(NetworkStatus(isNetworkAvailable: isNetworkAvailable));
  }

  /// Fetch submitted exam review list

  Future<List<ExamSummary>> fetchExamResults(
      String quizAttemptId, String quizId) async {
    List<ExamSummary> examSummary = [];

    try {
      examSummary = await _examRepository.fetchExamResultStatisticalData(
          quizAttemptId, quizId);
    } on Exception catch (e) {
      debugPrint("[Exception][ExamCubit][_fetchExamResults] :${e.toString()}");
    }
    return examSummary;
  }

  fetchExamResultStatisticalData(String quizAttemptId, String quizId) async {
    try {
      if (quizAttemptId.isNotEmpty) {
        List<ExamSummary> examSummary =
            await fetchExamResults(quizAttemptId, quizId);
        emit(ExamResultSummaryFetched(examSummaryResult: examSummary));
      } else {
        emit(ExamsQuestionsFailureState(error: VIEW_RESULTS_FAILURE));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][ExamCubit][fetchExamResultStatisticalData] :${e.toString()}");
      emit(ExamsQuestionsFailureState(error: e.details));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][ExamCubit][fetchExamResultStatisticalData] :${e.toString()}");
      emit(ExamsQuestionsFailureState(error: e.toString()));
    }
  }

  /// Fetch Exam result
  // fetchExamResult(String attemptId, String quizId) async {
  //   List<QuestionData> _questionData = [];
  //   List<Question> _questions = [];
  //   try {
  //     _questionData = await _examRepository.viewExamResult(attemptId, quizId);
  //     _questions =
  //         _questionData.isNotEmpty ? _questionData.first.questions : [];

  //     emit(ExamResultFetched(examData: _questionData));
  //   } on Exception catch (e) {
  //     emit(ExamsQuestionsFailureState(error: e.toString()));
  //   }
  // }

  //Calculate quiz grades
  calculateQuizGrades(String quizId, String quizAttemptId) async {
    final responseData =
        await _examRepository.calculateExamResult(quizAttemptId);

    try {
      if (responseData["status"] == 'success') {
        if (quizId.isNotEmpty) {
          if (quizId.isNotEmpty && quizAttemptId.isNotEmpty) {
            List<ExamSummary> examSummary =
                await fetchExamResults(quizAttemptId, quizId);

            if (examSummary.isNotEmpty) {
              examSummaryResult = examSummary;
            }
          }
        }
        emit(ExamsQuizGradeState(status: true));
      } else {
        emit(ExamsQuizFailureState(error: responseData.toString()));
      }
    } on Exception catch (e) {
      debugPrint(
          "[Exception][ExamCubit][calculateQuizGrades] :${e.toString()}");
    }
  }

  onHtmlRenderFinished() {
    emit(HtmlRenderFinished());
  }

  setMoreOptionsVisibility(bool status) {
    emit(ExamSpeedDialState(enableSpeedDial: status));
  }

  updateExamSubmissionStatus(bool isSubmitingExam) {
    emit(ExamSubmissionStatus(status: isSubmitingExam));
  }

  updateCheckBox(bool status, List<int> selectedAnswerIndexes) {
    emit(ExamMilestoneCheckBoxStatus(
        status: status, selectedAnswerIndexes: selectedAnswerIndexes));
  }

  updateNavigationStatus(bool value) {
    navigatedToExamIntro = value;
  }

  /// if true, navigation redirection back to
  /// exam intro from exam view will be blocked
  updateNavigationStatustoIntro({required bool disableBackNavigation}) {
    disableNavigationBackToIntro = disableBackNavigation;
  }

  /// if true, navigation redirection back to
  /// exam view from exam review will be blocked
  updateNavigationStatustoExamView({required bool disableBackNavigation}) {
    disableNavigationBackToExam = disableBackNavigation;
  }

  /// if true, navigation redirection back to
  /// exam review from exam result will be blocked
  updateNavigationStatustoReview({required bool disableBackNavigation}) {
    disableNavigationBackToReview = disableBackNavigation;
  }

  /// if true, navigation redirection back to
  /// exam result from exam view(view solution) will be blocked
  updateNavigationStatustoResult({required bool disableBackNavigation}) {
    disableNavigationBackToResult = disableBackNavigation;
  }

  /// if true, navigation redirection back to
  /// exam list tab view from exam result will be blocked
  updateNavigationStatustoList({required bool disableBackNavigation}) {
    disableNavigationBackToList = disableBackNavigation;
  }

  enableAllRoutes() {
    disableNavigationBackToList = false;
    disableNavigationBackToResult = false;
    disableNavigationBackToReview = false;
    disableNavigationBackToExam = false;
    disableNavigationBackToIntro = false;
  }

  updateSelectedTab(int value) {
    selectedTab = value;
  }
}
