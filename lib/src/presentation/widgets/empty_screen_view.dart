import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/foundation.dart';

import '/src/config/themes/app_theme.dart';

import 'package:flutter/material.dart';

import '../../config/themes/lms_fonts.dart';

class EmptyScreenView extends StatelessWidget {
  final String title;
  final double? fontSize;
  final String buttonText;
  final VoidCallback? retryAction;
  final bool showRetryButton;

  const EmptyScreenView({
    super.key,
    required this.title,
    this.fontSize = 17,
    this.buttonText = '',
    this.retryAction,
    this.showRetryButton = false,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return LayoutBuilder(builder: (context, constraints) {
      return Align(
        child: Container(
        
          width: constraints.maxWidth < 720 ? double.infinity : 800,
          height: constraints.maxWidth < 720 ?500 : 800,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                Flexible(
                  child: Image(
                    image: const AssetImage('assets/enhancement/no-data.png'),
                    height: constraints.maxHeight < 720 ? screenWidth / 2 : 400,
                    width: constraints.maxHeight < 720 ? screenWidth / 2 : 400,
                  ),
                ),
                const SizedBox(height: 20),
                Flexible(
                  child: AutoSizeText(
                    title,
                    textAlign: TextAlign.center,
                    minFontSize: 10,
                    style: LMSFonts.emptyTextStyle(fontSize ?? 17),
                  ),
                ),
                const SizedBox(height: 50),
                Visibility(
                  visible: showRetryButton,
                  child: TextButton(
                    onPressed: retryAction,
                    child: Text(
                      buttonText,
                      style: LMSFonts.semiBoldFont(
                          16, AppTheme.viewResultButtonColor),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}
