// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'privilege_access.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PrivilegeAccess _$PrivilegeAccessFromJson(Map<String, dynamic> json) =>
    PrivilegeAccess(
      orgId: json['org_id'] as String?,
      status: json['status'] as String?,
      userId: json['user_id'] as String?,
      userRoles: (json['user_roles'] as List<dynamic>)
          .map((e) => e as String?)
          .toList(),
      rolePrivileges: (json['role_privileges'] as List<dynamic>)
          .map((e) => e == null
              ? null
              : RolePrivilege.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PrivilegeAccessToJson(PrivilegeAccess instance) =>
    <String, dynamic>{
      'org_id': instance.orgId,
      'status': instance.status,
      'user_id': instance.userId,
      'user_roles': instance.userRoles,
      'role_privileges': instance.rolePrivileges,
    };

RolePrivilege _$RolePrivilegeFromJson(Map<String, dynamic> json) =>
    RolePrivilege(
      screen: json['screen'] as String?,
      actions: Map<String, bool>.from(json['actions'] as Map),
    );

Map<String, dynamic> _$RolePrivilegeToJson(RolePrivilege instance) =>
    <String, dynamic>{
      'screen': instance.screen,
      'actions': instance.actions,
    };
