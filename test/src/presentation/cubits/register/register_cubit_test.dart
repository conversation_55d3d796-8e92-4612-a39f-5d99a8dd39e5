import 'package:SmartLearn/src/utils/constants/api_response.dart';
import 'package:SmartLearn/src/domain/repositories/register_repository.dart';
import 'package:SmartLearn/src/presentation/cubits/register/register_cubit.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gotrue/src/types/user.dart';

class MockRegisterRepository implements RegisterRepository {
  @override
  Future<User?> userRegistration(
      String email, String password, Map<String, dynamic> data) {
    return Future(() => MOCK_USER_RESPONSE);
  }
}

void main() {
  late MockRegisterRepository _mockRegisterRepositoryImpl;
  late RegisterCubit _registerCubit;

  setUp(() {
    _mockRegisterRepositoryImpl = MockRegisterRepository();
    _registerCubit = RegisterCubit(_mockRegisterRepositoryImpl);
  });

  tearDown(() {
    _registerCubit.close();
  });

  group('RegisterCubit', () {
    Map<String, dynamic> data = {
      'first_name': 'Misha',
      'last_name': '<PERSON><PERSON>y',
      'phonenumber1': '7559825473'
    };

    blocTest<RegisterCubit, RegisterState>(
      'emits instance of User when user is registered',
      build: () => _registerCubit,
      act: (cubit) =>
          {cubit.register('<EMAIL>', '123456', data)},
      expect: () => [RegisterSuccess(user: MOCK_USER_RESPONSE)],
    );
  });
}
