import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'check_point_data.g.dart';

@JsonSerializable()
class CheckPointData extends Equatable {
  final String? status;
  @Json<PERSON>ey(name: 'check_points')
  final List<CheckPoint>? checkPoints;

  const CheckPointData({this.status, this.checkPoints});

  factory CheckPointData.fromJson(Map<String, dynamic> json) {
    return _$CheckPointDataFromJson(json);
  }

  Map<String, dynamic> toJson() => _$CheckPointDataToJson(this);

  CheckPointData copyWith({
    String? status,
    List<CheckPoint>? checkPoints,
  }) {
    return CheckPointData(
      status: status ?? this.status,
      checkPoints: checkPoints ?? this.checkPoints,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props => [status, checkPoints];
}

@JsonSerializable()
class CheckPoint extends Equatable {
  @JsonKey(name: 'org_id')
  final String? orgId;
  final int? sequence;
  @Json<PERSON>ey(name: 'created_at')
  final DateTime? createdAt;
  @Json<PERSON>ey(name: 'created_by')
  final String? createdBy;
  @Json<PERSON>ey(name: 'start_page')
  final int? startPage; // New field
  @JsonKey(name: 'start_time')
  final String? startTime;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  @JsonKey(name: 'updated_by')
  final String? updatedBy;
  @JsonKey(name: 'instance_id')
  final String? instanceId;
  @JsonKey(name: 'module_name')
  final String? moduleName;
  @JsonKey(name: 'is_mandatory')
  final bool? isMandatory;
  @JsonKey(name: 'checkpoint_id')
  final String? checkpointId;
  @JsonKey(name: 'module_type_id')
  final String? moduleTypeId;
  @JsonKey(name: 'checkpoint_name')
  final String? checkpointName;
  @JsonKey(name: 'checkpoint_type')
  final String? checkpointType;
  @JsonKey(name: 'course_module_id')
  final String? courseModuleId;
  @JsonKey(name: 'instance_end_time') // New field
  final DateTime? instanceEndTime;

  const CheckPoint({
    this.orgId,
    this.sequence,
    this.createdAt,
    this.createdBy,
    this.startPage,
    this.startTime,
    this.updatedAt,
    this.updatedBy,
    this.instanceId,
    this.moduleName,
    this.isMandatory,
    this.checkpointId,
    this.moduleTypeId,
    this.checkpointName,
    this.checkpointType,
    this.courseModuleId,
    this.instanceEndTime, // New field
  });

  factory CheckPoint.fromJson(Map<String, dynamic> json) {
    return _$CheckPointFromJson(json);
  }

  Map<String, dynamic> toJson() => _$CheckPointToJson(this);

  CheckPoint copyWith({
    String? orgId,
    int? sequence,
    DateTime? createdAt,
    String? createdBy,
    int? startPage,
    String? startTime,
    DateTime? updatedAt,
    String? updatedBy,
    String? instanceId,
    String? moduleName,
    bool? isMandatory,
    String? checkpointId,
    String? moduleTypeId,
    String? checkpointName,
    String? checkpointType,
    String? courseModuleId,
    DateTime? instanceEndTime, // New field
  }) {
    return CheckPoint(
      orgId: orgId ?? this.orgId,
      sequence: sequence ?? this.sequence,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      startPage: startPage ?? this.startPage,
      startTime: startTime ?? this.startTime,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      instanceId: instanceId ?? this.instanceId,
      moduleName: moduleName ?? this.moduleName,
      isMandatory: isMandatory ?? this.isMandatory,
      checkpointId: checkpointId ?? this.checkpointId,
      moduleTypeId: moduleTypeId ?? this.moduleTypeId,
      checkpointName: checkpointName ?? this.checkpointName,
      checkpointType: checkpointType ?? this.checkpointType,
      courseModuleId: courseModuleId ?? this.courseModuleId,
      instanceEndTime: instanceEndTime ?? this.instanceEndTime, // New field
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      orgId,
      sequence,
      createdAt,
      createdBy,
      startPage,
      startTime,
      updatedAt,
      updatedBy,
      instanceId,
      moduleName,
      isMandatory,
      checkpointId,
      moduleTypeId,
      checkpointName,
      checkpointType,
      courseModuleId,
      instanceEndTime, // New field
    ];
  }
}
