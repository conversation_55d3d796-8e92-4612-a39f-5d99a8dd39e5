import 'dart:io';

import 'package:equatable/equatable.dart';

import '../../../domain/models/organization.dart';
import '../../../domain/models/profile.dart';

abstract class UserState extends Equatable {
  final Profile? profile;
  final bool isLoading;
  final String? error;
  final File? imageFile;
  final List<Organization>? orgList;

  final File? imageChosen;

  const UserState(
      {this.profile,
      this.error,
      this.imageFile,
      this.orgList,
      // this.userFirstName,
      // this.userLastName,
      // this.userProfilePic,
      this.imageChosen,
      this.isLoading = false});

  @override
  List<Object?> get props => [
        profile,
        error,
        imageFile,
        orgList,
        isLoading,
        // userFirstName,
        // userLastName,
        // userProfilePic,
      ];
}

class UserInitial extends UserState {}

class UserLoadingState extends UserState {
  const UserLoadingState({
    super.profile,
    super.isLoading,
    super.imageChosen,
    super.orgList,
    // super.userFirstName,
    // super.userLastName,
    // super.userProfilePic,
  });

  @override
  List<Object?> get props => [
        profile,
        error,
        imageFile,
        orgList,
        isLoading,
        // userFirstName,
        // userLastName,
        // userProfilePic
      ];
}

class UserLoadedState extends UserState {
  // final String? userFirstName;
  // final String? userLastName;
  // final String? userProfile;

  const UserLoadedState(
      {super.profile,
      super.orgList,
      // this.userFirstName,
      // this.userLastName,
      // this.userProfile,
      super.imageChosen});

  @override
  List<Object?> get props => [
        profile,
        error,
        imageFile,
        orgList,
        isLoading,
        // userFirstName,
        // userLastName,
        // userProfilePic
      ];
}

class UserSavingState extends UserState {
  const UserSavingState();
}

class UserUpdatedState extends UserState {
  const UserUpdatedState({super.profile, super.orgList});

  @override
  List<Object?> get props => [profile, orgList];
}

class UserErrorState extends UserState {
  const UserErrorState({
    super.error,
    super.profile,
    super.orgList,
  });
}

class UserProfileChange extends UserState {
  final bool didChangeProfile;
  const UserProfileChange(
      {super.profile, super.orgList, required this.didChangeProfile});
}

//from gallery
class ProfilePicUpdatedFromGallery extends UserState {
  const ProfilePicUpdatedFromGallery(
      {super.profile, super.imageFile, super.orgList, super.imageChosen});
}

class ProfilePicUpdatedFromCamera extends UserState {
  const ProfilePicUpdatedFromCamera(
      {super.profile, super.imageFile, super.orgList, super.imageChosen});
}

class ProfilePicRemoved extends UserState {
  const ProfilePicRemoved({super.profile, super.orgList});
}

// user signout
class UserSignoutError extends UserState {
  final String error;
  UserSignoutError({required this.error});

  @override
  List<Object> get props => [error];
}

class UserSignoutSuccess extends UserState {
  UserSignoutSuccess();

  @override
  List<Object> get props => [];
}
