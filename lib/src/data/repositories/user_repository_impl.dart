import '/src/utils/constants/strings.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../config/app_config/api_config.dart';
import '../../domain/models/organization.dart';
import '../../domain/models/profile.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/services/network_services.dart';
import '../../utils/constants/helper.dart';

class UserRepositoryImpl implements UserRepository {
  UserRepositoryImpl();

  @override
  Future<Profile> getUserInfo(String userId) async {
    final response = await Supabase.instance.client
        .from(APIConfig.V_PERSON)
        .select()
        .eq('id', userId);

    List<dynamic> responseData = response;
    List<Map<String, dynamic>> mapList =
        responseData.map((item) => item as Map<String, dynamic>).toList();

    List<Map<String, dynamic>> loggedInUserInfo =
        mapList.where((element) => element['id'] == userId).toList();
    Profile profile =
        mapList.isNotEmpty ? Profile.fromJson(loggedInUserInfo[0]) : Profile();
    return profile;
  }

  ///
  /// check whether the current user has an active token
  /// or user is authenticated or not
  ///
  @override
  Future<dynamic> checkTokenAndRefresh() async {
    try {
      Supabase.instance;
      final currentUser = Supabase.instance.client.auth.currentUser;
      return currentUser;

      // ignore: unused_catch_clause
      // ignore: avoid_catching_errors
    } on AssertionError catch (_) {
      /// if the network wasn't available during
      /// supabase init, then this api call will throw assertion error
      /// in such case, re-init supabase again when the network is available
      ///
      bool isNetworkAvailable = await checkNetworkStatus();
      if (isNetworkAvailable) {
        await NetworkServices().initSupabase();
      } else {
        return isNetworkAvailable;
      }
    } on AuthException catch (e) {
      debugPrint(
          '[UserRepositoryImpl][checkTokenAndRefresh][AuthException]: $e');
    } on Exception catch (e) {
      debugPrint('[UserRepositoryImpl][checkTokenAndRefresh][Exception]: $e');
    }
    return null;
  }

  @override
  Future<dynamic> updateUserInfo(String userId, String firstName,
      String lastName, String avatarUrl, dynamic file) async {
    String path = "";
    String fileName = '';
    String urlResponse;
    bool isValid = await isValidUrl(avatarUrl);

    Map<String, dynamic> jsonBody = {
      "id": userId,
      "first_name": firstName,
      "last_name": lastName,
      "avatar_url": avatarUrl
    };

    if (file != null && file.path != NULL_PATH) {
      fileName = DateTime.now().millisecondsSinceEpoch.toString();

      final avatarFile = file;

      /// upload the image to specified storage in supabase
      path = await Supabase.instance.client.storage
          .from(APIConfig.PROFILE_STORAGE)
          .upload(
            fileName,
            avatarFile,
          );
    }

    if (isValid || file == null || file.path == NULL_PATH) {
      urlResponse = avatarUrl;
    } else {
      /// create signed network url for the uploaded file image
      urlResponse = await Supabase.instance.client.storage
          .from(APIConfig.PROFILE_STORAGE)
          .createSignedUrl(fileName, 52560000);
    }

    ///
    /// rpc
    ///
    jsonBody = {
      "id": userId,
      "first_name": firstName,
      "last_name": lastName,
      "avatar_url": urlResponse
    };

    /// update profile info- first,last names and profile picture to db
    final response = await Supabase.instance.client
        .rpc(APIConfig.UPDATE_PROFILE_FN, params: {
      'profile_datas': [jsonBody],
    });

    if (response != null) {
      return response;
    }
    return false;
  }

  @override
  Future<List<Organization>> getUserOrganizations(String userId) async {
    final organizationResponse = await Supabase.instance.client
        .from(APIConfig.V_USER_ORGS)
        .select()
        .eq('user_id', userId);

    List<dynamic> userList = organizationResponse;
    List<Map<String, dynamic>> mapList =
        userList.map((item) => item as Map<String, dynamic>).toList();

    List<Organization> organizationList =
        mapList.map((json) => Organization.fromJson(json)).toList();

    return organizationList;
  }

//Logout User

  @override
  Future userSignout() async {
    try {
      await Supabase.instance.client.auth.signOut();
      return true;
    } on AuthException catch (e) {
      debugPrint('[UserRepositoryImpl][userSignout][AuthException]: $e');
    } on Exception catch (e) {
      debugPrint('[UserRepositoryImpl][userSignout][Exception]: $e');
    }
  }

// check network availability of device
}
