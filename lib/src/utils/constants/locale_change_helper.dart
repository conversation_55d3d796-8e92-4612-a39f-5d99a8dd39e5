import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/app_config/preference_config.dart';
import '../../config/app_config/sl_config.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../presentation/cubits/profile/profile_cubit.dart';
import '../../presentation/widgets/lang_change_alert.dart';
import 'helper.dart';
import 'lists.dart';
import '../../domain/services/provider/language_provider.dart';
import 'package:provider/provider.dart' as provider;

didChangeAppLocale(List<Locale>? locales, BuildContext? currentContext) async {
  if (locales != null && locales.isNotEmpty) {
    String newLang = locales[0].languageCode;
    String changedLang = newLang;
    if (SLStrings.currentLanguage.contains(newLang)) {
      // language not changed
    } else {
      /// changed language from system settings
      ///

      if (currentContext != null) {
        if (localizationLangauges.contains(newLang)) {
          /// language in the list of locales supported
          /// set to changed new lang
          ///

          showLanguageChangedAlert(currentContext, false, changedLang);
        } else {
          /// language not in the list of locales supported
          /// set to default lang- en
          ///
          /// if the existing language was en,
          /// then just inform the user language change detected,
          /// and since the language was 'en' previously, no need to reset to 'en'

          bool wasDefaultLangEN =
              SLStrings.currentLanguage.contains(SLConfig.DEFAULT_LOCALE_EN);

          if (wasDefaultLangEN) {
            // if the app language was already 'en',
            // then no need to show popup and change to 'en'
            changedLang = SLConfig.DEFAULT_LOCALE_EN;
          } else {
            // if the app language was not 'en',
            // then show popup and change language to 'en'

            changedLang = SLConfig.DEFAULT_LOCALE_EN;
            showLanguageChangedAlert(
                currentContext, wasDefaultLangEN, changedLang);
          }
        }
        // bool isThereCurrentDialogShowing = ModalRoute.of(currentContext)?.isCurrent != true;

        // if (isThereCurrentDialogShowing) {
        // } else {
        Future.delayed(const Duration(seconds: 2), () {
          /// if locale not updated in background,
          /// if we change lang multiple times, then
          /// the text inside popup will not change acco. to current language
          updateCurrentLanguageStatus(currentContext, changedLang);
        });
        // }
      }
    }
  }
}

Future<String> getUpdatedLang(String _selectedLanguage) async {
  SharedPreferences _prefs = await SharedPreferences.getInstance();
  String savedLang =
      _prefs.getString(PREF_KEY_CURRENT_LANG) ?? SLConfig.DEFAULT_LOCALE_EN;
  _selectedLanguage = savedLang;
  if (Platform.localeName.contains(SLConfig.DEFAULT_LOCALE_EN)) {
    _selectedLanguage = SLConfig.DEFAULT_LOCALE_EN;
  } else if (Platform.localeName.contains(SLConfig.LOCALE_DE)) {
    _selectedLanguage = SLConfig.LOCALE_DE;
  } else {
    _selectedLanguage = SLConfig.DEFAULT_LOCALE_EN;
  }
  return _selectedLanguage;
}

updateCurrentLanguageStatus(BuildContext context, String changedLang) async {
  String languageCode = changedLang;
  SharedPreferences _prefs = await SharedPreferences.getInstance();
  _prefs.setString(PREF_KEY_CURRENT_LANG, languageCode);
  updateAppLocale(context, [Locale(languageCode)]);
}

updateAppLocale(BuildContext context, List<Locale>? locale) {
  provider.Provider.of<LanguageProvider>(context, listen: false)
      .changeCurrentLanguage(SLStrings.getCurrentLangauge(locale));
}

logoutUserOnLanguageChangeDetection(BuildContext context, String lang) async {
  try {
    final _userCubit = BlocProvider.of<UserCubit>(context);
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    // Future.delayed(const Duration(seconds: 2), () {
    _prefs.setBool(PREF_KEY_SIGNOUT, true);
    _prefs.setString(PREF_KEY_CURRENT_LANG, lang);
    // await clearCurrentUserInfo();
    // await _userCubit.userSignout();
    SLStrings.currentLanguage = lang;
    SLStrings.currentLocale = Locale(lang);
  } on Exception catch (_) {
    // TODO
  }
}

String changeLocaleWithoutAlert(
    BuildContext? currentContext, String newLang, String _selectedLanguage) {
  if (currentContext != null) {
    if (localizationLangauges.contains(newLang)) {
      // new language is supported by app
      // i.e. exists in the list of locales supported by app

      //_showLanguageChangedAlert(currentContext!, _selectedLanguage);
    } else {
      // changed language from system settings
      // language not in the list of locales supported
      // by app
      // set to default lang- 'en'
      //
      bool wasDefaultLangEN =
          SLStrings.currentLanguage.contains(SLConfig.DEFAULT_LOCALE_EN) &&
              _selectedLanguage.contains(SLConfig.DEFAULT_LOCALE_EN);

      if (wasDefaultLangEN) {
        // if the app language was already 'en',
        // then no need to show popup and change to 'en'
      } else {
        // if the app language was not 'en',
        // then show popup and change language to 'en'
        _selectedLanguage = SLConfig.DEFAULT_LOCALE_EN;
        /*if (currentContext != null) {
                _showLanguageChangedAlert(currentContext!, _selectedLanguage);
              }*/
      }
    }
  }
  return _selectedLanguage;
}
