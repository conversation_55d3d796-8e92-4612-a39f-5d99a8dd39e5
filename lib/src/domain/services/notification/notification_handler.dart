import 'dart:io';

import '/src/config/app_config/preference_config.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PushNotificationService {
  PushNotificationService();

  late final FirebaseMessaging _fcm;
  late final FlutterLocalNotificationsPlugin _localNotification;

  Future initNotifications() async {
    try {
      _fcm = FirebaseMessaging.instance;
      _localNotification = FlutterLocalNotificationsPlugin();

      SharedPreferences _prefs = await SharedPreferences.getInstance();

      if (Platform.isIOS) {
        _fcm.requestPermission();
      }

      String? token = await _fcm.getToken();

      debugPrint("FirebaseMessaging token: $token");

      if (token != null) {
        _prefs.setString(PREF_KEY_FCM_TOKEN, token);
      }

      final generalNotificationDetails = await _initLocalNotifications();

      await _handleMessages(generalNotificationDetails);
    } on Exception catch (e) {
      debugPrint(
          '[PushNotificationService][initNotifications]: ${e.toString()}');
    }
  }

  /// show local notification when
  /// push notification recieved in foreground state
  ///
  Future<NotificationDetails> _initLocalNotifications() async {
    const AndroidNotificationChannel _androidNotificationChannel =
        AndroidNotificationChannel('id', 'local_notification');
    var androiInit =
        const AndroidInitializationSettings("@mipmap/ic_launcher"); //for logo

    var initSetting = InitializationSettings(android: androiInit);

    _localNotification.initialize(initSetting);

    var androidDetails = AndroidNotificationDetails(
        _androidNotificationChannel.id, _androidNotificationChannel.name,
        importance: Importance.high, priority: Priority.high);

    var generalNotificationDetails =
        NotificationDetails(android: androidDetails);
    return generalNotificationDetails;
  }

  _handleMessages(NotificationDetails generalNotificationDetails) async {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      RemoteNotification? notification = message.notification;
      AndroidNotification? android = message.notification?.android;
      if (notification != null && android != null) {
        _localNotification.show(notification.hashCode, notification.title,
            notification.body, generalNotificationDetails);
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint("message:$message");
      debugPrint("message-:${message.data}");
      debugPrint("message--:${message.notification}");
    });

    _fcm.setForegroundNotificationPresentationOptions(
        alert: true, badge: true, sound: true);

    // FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {}
}
