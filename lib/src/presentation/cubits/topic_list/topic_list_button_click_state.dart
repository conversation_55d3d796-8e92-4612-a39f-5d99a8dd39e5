// part of 'topic_list_button_click_cubit.dart';



// abstract class TopicListButtonClickState extends Equatable {
//   final  buttonColor;
 
//   const TopicListButtonClickState( {this.buttonColor=false });

//   @override
//   List<Object> get props => [buttonColor];
// }

// class TopicListButtonClickInitial extends TopicListButtonClickState {

  
//  const TopicListButtonClickInitial({super.buttonColor});
// }
