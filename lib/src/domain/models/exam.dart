// To parse this JSON data, do
//
//     final exam = examFromJson(jsonString);

import 'package:equatable/equatable.dart';
import 'dart:convert';

import '../../config/enums/exam_status.dart';

List<Exam> examFromJson(String str) =>
    List<Exam>.from(json.decode(str).map((x) => Exam.fromJson(x)));

String examToJson(List<Exam> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Exam extends Equatable {
  final String id;
  final String name;
  final String intro;
  final String orgId;
  final ExamType examType;
  final int duration;
  final DateTime endTime;
  final String courseId;
  final double passMark;
  final DateTime createdAt;
  final String mainTopic;
  final DateTime startTime;
  final double totalMark;
  final DateTime updatedAt;
  final int numOfQuestions;

  final int pendingAttempts;
  final String quizId;
  final String quizAttemptId;
  final ExamResult result;

  Exam({
    required this.id,
    required this.name,
    required this.intro,
    required this.orgId,
    required this.examType,
    required this.duration,
    required this.endTime,
    required this.courseId,
    required this.passMark,
    required this.createdAt,
    required this.mainTopic,
    required this.startTime,
    required this.totalMark,
    required this.updatedAt,
    required this.numOfQuestions,
    required this.pendingAttempts,
    required this.quizId,
    required this.quizAttemptId,
    required this.result,
  });

  factory Exam.fromJson(Map<String, dynamic> json) => Exam(
        id: json["id"] ?? json["quiz_id"] ?? '',
        name: json["name"] ?? '',
        intro: json["intro"] ?? '',
        orgId: json["org_id"] ?? '',
        duration: json["duration"] ?? 0,
        endTime: DateTime.parse(json["end_time"] ?? DateTime.now().toString()),
        courseId: json["course_id"] ?? '',
        passMark: json["pass_mark"] * 1.0 ?? 0,
        createdAt:
            DateTime.parse(json["created_at"] ?? DateTime.now().toString()),
        mainTopic: json["main_topic"] ?? '',
        startTime:
            DateTime.parse(json["start_time"] ?? DateTime.now().toString()),
        totalMark: json["total_mark"] * 1.0 ?? 0,
        updatedAt:
            DateTime.parse(json["updated_at"] ?? DateTime.now().toString()),
        numOfQuestions: json["num_of_questions"] ?? 0,
        pendingAttempts: json['attempts_remaining'] ?? 0,
        quizAttemptId: json['quiz_attempt_id'] ?? '',
        quizId: json['quiz_id'] ?? '',
        result: json['result'] != null
            ? json['result'].toString().toLowerCase() == 'failed'
                ? ExamResult.failed
                : ExamResult.passed
            : ExamResult.notStarted,
        examType:
            (json['quiz_type'] ?? '').toString().toLowerCase() == 'practice'
                ? ExamType.practice
                : ExamType.main,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "intro": intro,
        "org_id": orgId,
        "duration": duration,
        "end_time": endTime.toIso8601String(),
        "course_id": courseId,
        "pass_mark": passMark,
        "created_at": createdAt.toIso8601String(),
        "main_topic": mainTopic,
        "start_time": startTime.toIso8601String(),
        "total_mark": totalMark,
        "updated_at": updatedAt.toIso8601String(),
        "num_of_questions": numOfQuestions,
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      id,
      name,
      intro,
      orgId,
      duration,
      endTime,
      courseId,
      passMark,
      createdAt,
      mainTopic,
      startTime,
      totalMark,
      updatedAt,
      numOfQuestions,
      examType,
    ];
  }
}
