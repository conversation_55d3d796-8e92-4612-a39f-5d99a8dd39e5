import 'package:flutter/foundation.dart';

import '/src/config/themes/app_theme.dart';

import 'package:flutter/material.dart';

import '../../config/themes/lms_fonts.dart';

class EmptyScreenView extends StatelessWidget {
  final String title;
  final double? fontSize;
  final String buttonText;
  final VoidCallback? retryAction;
  final bool showRetryButton;

  const EmptyScreenView({
    super.key,
    required this.title,
    this.fontSize = 22,
    this.buttonText = '',
    this.retryAction,
    this.showRetryButton = false,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return LayoutBuilder(builder: (context, constraints) {
      return Align(
        child: Container(
          width: constraints.maxWidth < 720 ? double.infinity : 800,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image(
                  image: const AssetImage('assets/images/empty-box.png'),
                  height: constraints.maxHeight < 720 ? screenWidth / 2 : 400,
                  width: constraints.maxHeight < 720 ? screenWidth / 2 : 400,
                ),
                const SizedBox(height: 20),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: LMSFonts.emptyTextStyle(fontSize ?? 22),
                ),
                const SizedBox(height: 50),
                Visibility(
                  visible: showRetryButton,
                  child: TextButton(
                    onPressed: retryAction,
                    child: Text(
                      buttonText,
                      style: LMSFonts.semiBoldFont(
                          16, AppTheme.viewResultButtonColor),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}
