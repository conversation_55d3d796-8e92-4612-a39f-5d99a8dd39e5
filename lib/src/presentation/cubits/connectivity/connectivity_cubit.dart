import 'dart:async';
import 'dart:convert';

import '/src/config/app_config/preference_config.dart';

import 'package:flutter/foundation.dart';

import '/src/domain/repositories/user_repository.dart';
import '/src/domain/services/network_services.dart';
import 'package:bloc/bloc.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../domain/models/question_data.dart';
import '../../../domain/repositories/exam_repository.dart';
import '../../../domain/services/db_helper.dart';
import '../../../utils/constants/helper.dart';
import '../../../utils/constants/strings.dart';
import 'connectivity_state.dart';

class ConnectivityCubit extends Cubit<ConnectivityState> {
  final Connectivity connectivity;
  final ExamRepository _examRepository;
  final UserRepository _userRepository;

  late StreamSubscription connectivityStreamSubscription;
  final DbHelper _dbHelper = DbHelper.instance;
  Timer? _timer;

  ConnectivityCubit(
      this.connectivity, this._examRepository, this._userRepository)
      : assert(connectivity != null),
        super(InternetLoading()) {
    checkConnectivity();
    connectivityStreamSubscription =
        connectivity.onConnectivityChanged.listen((connectivityResult) async {
      if (connectivityResult == ConnectivityResult.wifi) {
        checkConnectivity();
        // emitInternetConnected(ConnectionType.wifi);
      } else if (connectivityResult == ConnectivityResult.mobile) {
        // emitInternetConnected(ConnectionType.mobile);
        checkConnectivity();
      } else if (connectivityResult == ConnectivityResult.none) {
        emitInternetDisconnected();
      }
    });
  }

  void startListening(ConnectivityResult result) {
    _timer = Timer.periodic(const Duration(seconds: 3), (_) async {
      bool hasInternet = await checkNetworkStatus();
      if (hasInternet) {
        _timer?.cancel();
        if (result == ConnectivityResult.wifi) {
          emitInternetConnected(ConnectionType.wifi);
        } else {
          emitInternetConnected(ConnectionType.mobile);
        }
      } else {
        emitInternetDisconnected();
      }
    });
  }

  Future<void> checkConnectivity() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    final result = await connectivity.checkConnectivity();
    bool _isNetworkReachable = await checkNetworkStatus();
    if (result == ConnectivityResult.none) {
      emitInternetDisconnected();
    } else if (_isNetworkReachable) {
      if (NetworkServices.client?.client.auth.currentSession == null &&
          _prefs.getString(PREF_KEY_USER_ID) != null) {
        await NetworkServices().initializeSupabase();
        await _userRepository.checkTokenAndRefresh();
      }
      if (result == ConnectivityResult.wifi) {
        emitInternetConnected(ConnectionType.wifi);
      } else {
        emitInternetConnected(ConnectionType.mobile);
      }
    } else if (!_isNetworkReachable) {
      startListening(result);
    }
  }

  void emitInternetConnected(ConnectionType _connectionType) async {
    _saveNetworkStatus(false);
    if (!kIsWeb) {
      List<QuestionData> pendingQuestionsToUpload =
          await _fetchPendingQstns() ?? [];
      if (pendingQuestionsToUpload.isNotEmpty) {
        await _uploadPendingData(pendingQuestionsToUpload);
      }
    }

    emit(InternetConnected(connectionType: _connectionType));
  }

  Future<void> emitInternetDisconnected() async {
    _saveNetworkStatus(true);
    emit(InternetDisconnected());
  }

  Future<void> _saveNetworkStatus(bool isConnected) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    await _prefs.setBool(PREF_KEY_NETWORK_STATUS, isConnected);
  }

  ///
  /// when network is connected, upload the unuploaded questions
  /// that were saved in local db.
  ///
  Future<List<QuestionData>?> _fetchPendingQstns() async {
    Map<String, dynamic> updatedMap = {};
    try {
      List<Map<String, dynamic>> queryData =
          await _dbHelper.queryForUnuploadedExams();
      List<dynamic> _questions = [];
      if (queryData.isNotEmpty) {
        Map<String, dynamic> _questionJson = List.from(queryData).first;
        updatedMap = {};
        if (_questionJson.containsKey('quest_answers') &&
            _questionJson.containsKey('exam_upload_status') &&
            _questionJson['exam_upload_status'] == 0) {
          _questions = jsonDecode(_questionJson['quest_answers']);

          _questions.map((x) {
            x['answers'] = jsonDecode(x['answers']);
          });
          // _questions['answers'] = answers;
          updatedMap = {
            ..._questionJson, // Copy the original map
            'quest_answers':
                _questions, // Add/Update the 'data' field with the new value
          };
        }
      }
      List<QuestionData> _questionFromJson = updatedMap.isNotEmpty
          ? List<QuestionData>.from(
              [updatedMap].map((x) => QuestionData.fromDBJson(x, '')))
          : [];
      return _questionFromJson;
    } on Exception catch (e) {
      debugPrint(
          "[Exception][ConnectivityCubit][_fetchAndUploadPendingQstns] :${e.toString()}");
    }
    return null;
  }

  /// json to submit to api
  Map<String, dynamic> _examJson = {};

  _uploadPendingData(List<QuestionData> _questionFromJson) async {
    /// list of all the answers student attended
    List<Map<String, dynamic>> _answeredQuestionList = [];

    for (var questionObj in _questionFromJson) {
      for (var i = 0; i < questionObj.questions.length; i++) {
        Question answeredQuestion = questionObj.questions[i];
        await saveAnswer(answeredQuestion, i, _answeredQuestionList);
      }
    }

    await submitExam(_examJson, _questionFromJson[0]);
  }

  saveAnswer(Question answeredQuestion, int selectedQuestionIndex,
      List<Map<String, dynamic>> answeredQuestionList) async {
    Map<String, dynamic> newQuestion = {};
    List<Answer> answers = answeredQuestion.options
        .where((element) =>
            answeredQuestion.selectedAnswerIds.contains(element.answerId))
        .toList();
    List<String> answerTitles = answers.map((e) => e.answerVal).toList();

    ///
    /// order id field is added for sorting purpose from ui side
    /// which is the question_slot in db
    /// not actually required in server side

    newQuestion = {
      "order_id": answeredQuestion.id,
      "question_id": answeredQuestion.questionId,
      "question_with_options": answeredQuestion.questionText,
      "response_summary": answerTitles.join(', '),
      "selected_answer_ids": answeredQuestion.selectedAnswerIds,
    };
    answeredQuestionList.add(newQuestion);

    int answeredQuestionIndex = answeredQuestionList.indexWhere(
        (element) => element.values.contains(answeredQuestion.questionId));

    if (answeredQuestionIndex != -1) {
      /// question already answered
      /// exists in the list
      /// replace the answer details
      ///
      answeredQuestionList[answeredQuestionIndex] = newQuestion;
    } else {
      /// question not yet answered
      /// does not exist in the list
      /// add the answer details to list
      ///
      answeredQuestionList.add(newQuestion);
    }

    answeredQuestionList.toSet().toList();

    answeredQuestionList.sort(
      (a, b) {
        int orderID1 = a["order_id"];
        int orderID2 = b["order_id"];
        return orderID1.compareTo(orderID2);
      },
    );
    _examJson = {
      "org_id": ORG_ID,
      "quiz_id": answeredQuestion.quizId,
      "user_id": USER_ID,
      "submit_datas": answeredQuestionList,
    };
  }

  /// Submit exam data
  submitExam(Map<String, dynamic> jsonBody, QuestionData _questionData) async {
    final String responseData;
    SharedPreferences _pref = await SharedPreferences.getInstance();
    try {
      jsonBody['quiz_attempt_id'] = _questionData.quizAttemptId;
      responseData = await _examRepository.submitExam(jsonBody);
      if (responseData.toString() == 'success') {
        _questionData.examUploadStatus = 1;
      } else {
        _questionData.examUploadStatus = 0;
      }
      await saveQstnsToDB(_questionData);
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint(
          "[Exception][ConnectivityCubit][Submit Exam] :${e.toString()}");
    }
  }

  ///
  /// save the questions to db
  ///
  saveQstnsToDB(QuestionData questionData) async {
    try {
      String _questions = jsonEncode(questionData.questions);
      Map<String, dynamic> _questionJson = questionData.toJson();
      _questionJson['quest_answers'] = _questions;
      int status =
          await _dbHelper.insertOrUpdate(_questionJson, questionData.examId);

      // _dbHelper.dropTable();
    } on Exception catch (e) {
      debugPrint("[Exception][ExamListCubit][_saveQstnsToDB] :${e.toString()}");
    }
  }

  Future<ConnectivityResult> checkConnectivityforScreen() async {
    final result = await connectivity.checkConnectivity();
    return result;
  }

  @override
  Future<void> close() {
    connectivityStreamSubscription.cancel();
    return super.close();
  }
}
