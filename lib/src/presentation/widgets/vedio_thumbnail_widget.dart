import '/src/presentation/widgets/loading_indicator.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '/src/presentation/widgets/percentage_indicator.dart';
import 'package:flutter/material.dart';
import '../../config/themes/app_theme.dart';
import '../../utils/constants/strings.dart';

class VideoThumbnailWidget extends StatelessWidget {
  final String videoUrl;
  final String videoTitle;
  final String thumbnail;
  final bool isYoutubeVideo;
  final bool removeShadow;
  final double progress;
  final BoxConstraints constraints;
  final bool shouldShowVideoProgress;
  final VoidCallback onTapCallback;
  // final Future? futureInst;

  const VideoThumbnailWidget({
    super.key,
    required this.videoUrl,
    required this.videoTitle,
    required this.thumbnail,
    required this.isYoutubeVideo,
    this.removeShadow = false,
    required this.progress,
    required this.constraints,
    required this.shouldShowVideoProgress,
    required this.onTapCallback,
    // required this.futureInst,
  });

  @override
  Widget build(BuildContext context) {
    bool isInValidPath = thumbnail.isEmpty;
    return GestureDetector(
        onTap: () => onTapCallback(),
        child: Container(
          height: 130,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            color: AppTheme.blackColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: isInValidPath
              ? _thumbnailPlaceHolder()
              : CachedNetworkImage(
                  height: 130,
                  fit: BoxFit.contain,
                  imageUrl: thumbnail,
                  placeholder: (context, url) => Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: LoadingIndicatorClass(
                      size: const Size(35, 35),
                    ),
                  ),
                  errorWidget: (context, url, error) => _thumbnailPlaceHolder(),
                ),
        ) /* Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.red,
            boxShadow: removeShadow
                ? []
                : [
                    BoxShadow(
                        color: LmsColors.black.withOpacity(0.1),
                        blurRadius: 10.0,
                        offset: const Offset(0, 1),
                        spreadRadius: 1),
                  ]),
        child: Stack(
          children: [
            Container(
              clipBehavior: Clip.antiAlias,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: isInValidPath
                  ? thumbnail.isEmpty
                      ? _thumbnailPlaceHolder()
                      : LoadingIndicatorClass(
                          size: const Size(30, 30), strokeWidth: 3)
                  : _thumbnailPlaceHolder() /* CachedNetworkImage(
                      imageUrl: thumbnail,
                      placeholder: (context, url) =>
                          const CircularProgressIndicator(),
                      errorWidget: (context, url, error) =>
                          _thumbnailPlaceHolder(),
                    )*/
              ,
            )

            // shouldShowVideoProgress
            //     ? progressBar(progress, constraints)
            //     : Container(),
            //  playButton(constraints),
          ],
        ),
      ),*/
        //}),
        );
  }

  Widget _thumbnailPlaceHolder() {
    return Container(
      color: AppTheme.resultDescriptionColor,
      width: double.infinity,
      child: Image.asset(
        '$ASSETS_PATH/video_thumbnail_default.png',
        fit: BoxFit.cover,
      ),
    );
  }

  Widget progressBar(double? progress, BoxConstraints constraints) {
    String progressvalue = progress != null
        ? progress == 0.0
            ? '0'
            : (progress * 100).toStringAsFixed(0)
        : '0';
    progressvalue = progressvalue == 'NaN' ? '0' : progressvalue;
    return Positioned(
      bottom: 4,
      left: 4,
      child: ClipOval(
          child: PercentageIndicator(
              progress: progress ?? 0.0,
              progressvalue: progressvalue,
              constraints: constraints)),
    );
  }

  Widget playButton(BoxConstraints constraints) {
    return Positioned(
        bottom: 5,
        right: 5,
        child: CircleAvatar(
          radius: constraints.maxWidth / 25,
          backgroundColor: AppTheme.primaryBlue,
          child: Icon(
            Icons.play_arrow,
            size: constraints.maxWidth / 15,
            color: AppTheme.whiteColor,
          ),
        ));
  }
}
