import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/services/network_services.dart';
import '/src/domain/repositories/register_repository.dart';
import '/src/config/app_config/api_config.dart';

class RegisterRepositoryImpl implements RegisterRepository {
  RegisterRepositoryImpl();

  @override
  Future<dynamic> userRegistration(
      String email, String password, Map<String, dynamic> data) async {
    try {
      Supabase.instance;
      // ignore: unused_catch_clauses
      // ignore: avoid_catching_errors
    } on AssertionError catch (_) {
      /// if the network wasn't available during
      /// supabase init, then this api call will throw assertion error
      /// in such case, re-init supabase again when the network is available
      ///
      await NetworkServices().initSupabase();
      return '';
    }

    final client = Supabase.instance.client;
    // TO DO: Check later
    /*   final userExist =
        await Supabase.instance.client.from('v_person').select().eq(
              "email",
              email,
            );
    if (userExist.length > 0) {
      return true;
    } else {*/
    final response = await client.auth.signUp(
        email: email,
        password: password,
        data: data,
        emailRedirectTo: APIConfig.EMAIL_REDIRECT_URL);

    final User? user = response.user;
    return user;
  }
}
