import '/src/config/themes/app_theme.dart';
import '/src/config/themes/lms_fonts.dart';

import '/src/utils/constants/strings.dart';
import 'package:flutter/material.dart';

class SessionExpiredWidget extends StatelessWidget {
  const SessionExpiredWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 80,
      padding: const EdgeInsets.all(10.00),
      margin: const EdgeInsets.only(bottom: 10.00),
      color: AppTheme.sessionExpiredBgColor,
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.only(right: 6.00),
            child:
                const Icon(Icons.info, color: AppTheme.sessionExpiredTextColor),
          ),
          Text(TOKEN_EXPIRED, style: LMSFonts.sessionExpiredText()),
        ],
      ),
    );
  }
}

class ExamUploadingStatusWidget extends StatelessWidget {
  const ExamUploadingStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 80,
      padding: const EdgeInsets.all(10.00),
      margin: const EdgeInsets.only(bottom: 10.00),
      color: AppTheme.infoBlue,
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.only(right: 6.00),
            child:
                const Icon(Icons.info, color: AppTheme.sessionExpiredTextColor),
          ),
          Text(EXAM_SUBMISSION_STATUS, style: LMSFonts.sessionExpiredText()),
        ],
      ),
    );
  }
}
