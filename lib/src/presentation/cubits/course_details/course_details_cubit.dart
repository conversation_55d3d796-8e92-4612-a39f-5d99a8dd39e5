// import '/src/domain/repositories/course_details_repository.dart';
// import 'package:bloc/bloc.dart';
// import 'package:equatable/equatable.dart';

// import '../../../domain/models/course_details.dart';

// part 'course_details_state.dart';

// class CourseDetailsCubit extends Cubit<CourseDetailsState> {
//   final CourseDetailsRepository _courseDetailsRepository;
//   CourseDetailsCubit(this._courseDetailsRepository)
//       : super(CourseDetailsInitial());

//   fetchCourseDetails(String courseId) async {
//     try {
//       final courseDetails =
//           await _courseDetailsRepository.fetchCourseDetailsInfo(courseId);
//       emit(CourseDetailsFetched(courseDetailsData: courseDetails));
//     } catch (e) {
//       List<dynamic> responseData = [{}];

//       CourseDetails _courseDetails = CourseDetails.fromJson(responseData[0]);
//       print(e);
//       emit(CourseDetailsError(error: e.toString()));
//     }
//   }

//   setLoading(bool loadStatus) {
//     if (loadStatus) emit(CourseDetailsLoading(isDataLoading: loadStatus));
//   }
// }
