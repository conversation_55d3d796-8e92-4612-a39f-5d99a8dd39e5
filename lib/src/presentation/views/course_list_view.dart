import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/app_config/preference_config.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/models/category/user_category.dart';
import '../../utils/constants/nums.dart';
import '../widgets/empty_screen_view.dart';
import '/src/presentation/widgets/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/router/app_router.dart';
import '../../config/themes/theme_colors.dart';
import '../../utils/constants/strings.dart';
import '../cubits/course_list/course_list_cubit.dart';
import '../widgets/app_bar.dart';
import '../widgets/button_widget.dart';

@RoutePage()
// ignore: must_be_immutable
class CourseListViewScreen extends HookWidget {
  final UserCategory category;
  final bool isFromLogin;

  CourseListViewScreen({
    Key? key,
    required this.category,
    this.isFromLogin = false,
  }) : super(key: key);

  bool isEmptyData = false;
  List<Course> courseList = [];

  _resetCourseSelection() async {
    COURSE_ID = '';
    COURSE_NAME = '';
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    _prefs.remove(PREF_KEY_COURSE_ID);
    _prefs.remove(PREF_KEY_COURSE_NAME);
  }

  @override
  Widget build(BuildContext context) {
    ValueNotifier<Course?> selectedTopic = useState<Course?>(null);

    final courseCubit = BlocProvider.of<CourseListCubit>(context);
    final screenWidth = MediaQuery.of(context).size.width;

    useEffect(() {
      courseList = category.courses;
      courseList.sort((a, b) => a.courseFullName.compareTo(b.courseFullName));

      return null;
    }, const []);

    return PopScope(
        canPop: isFromLogin,
        onPopInvoked: (bool didPop) async {
          if (didPop && !isFromLogin) {
            return;
          }
          if (isFromLogin) {
            await _resetCourseSelection();
          }
        },
        child: Scaffold(
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(70),
              child: AppBarWidget(
                title: SELECT_COURSE,
                leadingIconName: BACK_ARROW,
                isFromLogin: !isFromLogin,
                trailingWidget: Container(),
                leadingBtnAction: () async {
                  await _resetCourseSelection();
                  kIsWeb
                      ? Beamer.of(context).beamBack()
                      : appRouter.popForced();
                },
              )),
          body: BlocBuilder<CourseListCubit, CourseListState>(
              builder: (context, state) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              child: (state is CourseDetailsLoading && state.isDataLoading)
                  ? LoadingIndicatorClass()
                  : (courseList.isNotEmpty)
                      ? LayoutBuilder(builder: (context, constraints) {
                          return _mainContainer(context, constraints,
                              courseCubit, selectedTopic, screenWidth);
                        })
                      : const EmptyScreenView(
                          title: COURSE_EMPTY,
                        ),
            );
          }),
        ));
  }

  Widget _mainContainer(
      BuildContext context,
      BoxConstraints constraints,
      CourseListCubit courseCubit,
      ValueNotifier<Course?> selectedTopic,
      double screenWidth) {
    return Align(
      child: SizedBox(
        width: constraints.maxWidth < 720 ? constraints.maxWidth : 800,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
                padding: const EdgeInsets.only(left: 16, top: 20),
                child: Text(category.name,
                    style: LMSFonts.semiBoldFont(
                        18, AppTheme.primaryTextColorBlack))),
            const Divider(
              indent: 16,
              endIndent: 16,
            ),
            Expanded(
              child: ListView.builder(
                itemCount: courseList.length,
                itemBuilder: (BuildContext context, int index) {
                  return _courseListTile(
                      context, selectedTopic, courseList[index]);
                },
              ),
            ),
            _buttonClick(
                screenWidth,
                courseCubit.state,
                selectedTopic.value,
                selectedTopic.value?.courseId ?? '',
                selectedTopic.value?.courseFullName ?? '',
                context)
          ],
        ),
      ),
    );
  }

  Widget _courseListTile(BuildContext context,
      ValueNotifier<Course?> selectedTopic, Course courseItem) {
    bool isSelected = selectedTopic.value?.courseId == courseItem.courseId;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Material(
        color: Colors.white,
        child: InkWell(
          splashColor: AppTheme.okBtnGreen.withOpacity(0.1),
          highlightColor: AppTheme.okBtnGreen.withOpacity(0.1),
          onTap: () {
            selectedTopic.value = courseItem;
            context
                .read<CourseListCubit>()
                .handleCourseSelection(courseItem.courseFullName);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            margin: const EdgeInsets.only(
                left: horizontalMargin, right: horizontalMargin),
            decoration: const BoxDecoration(),
            clipBehavior: Clip.antiAlias,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Image.asset('$ASSETS_PATH/sub_topic_open.png',
                      height: isSelected ? 26 : 24,
                      width: isSelected ? 26 : 24,
                      color: AppTheme.okBtnGreen),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(courseItem.courseFullName,
                            style: LMSFonts.regularFontWithHeight(
                                isSelected ? 16 : 14.0,
                                AppTheme.secondaryTextColor,
                                1.2)),
                      ),
                    ),
                  ),
                  isSelected
                      ? Image.asset(
                          '$ASSETS_PATH/done_button.png',
                          height: 24,
                          width: 24,
                          color: AppTheme.okBtnGreen,
                        )
                      : const SizedBox(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buttonClick(double screenWidth, CourseListState state, Course? course,
      String courseId, String courseName, BuildContext context) {
    return Align(
        alignment: Alignment.bottomCenter,
        child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: ButtonWidget(
              child: SizedBox(
                width: double.infinity,
                child: Text(
                  COMMON_BUTTON_TEXT,
                  style: LMSFonts.buttonStyle(16.0),
                  textAlign: TextAlign.center,
                ),
              ),
              textColor: LmsColors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              onPressed: course != null
                  ? () async {
                      COURSE_ID = courseId;
                      COURSE_NAME = courseName;
                      SharedPreferences _prefs =
                          await SharedPreferences.getInstance();

                      _prefs.setString(PREF_KEY_COURSE_ID, COURSE_ID);
                      _prefs.setString(PREF_KEY_COURSE_NAME, COURSE_NAME);
                      kIsWeb
                          ? Beamer.of(context)
                              .beamToReplacementNamed('/course-details', data: {
                              'courseId': course.courseId,
                              'courseName': course.courseFullName,
                              'isFromLogin': true,
                            })
                          : await appRouter
                              .push(CourseDashboardRoute(isFromLogin: true));
                    }
                  : null,
            )));
  }
}
