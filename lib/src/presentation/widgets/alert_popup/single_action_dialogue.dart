import 'package:flutter/foundation.dart';

import '../../../domain/services/localizations/sl_strings.dart';
import '../../../domain/services/localizations/string_keys.dart';
import '/src/config/themes/app_theme.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/config/themes/theme_colors.dart';
import '/src/presentation/widgets/button_widget.dart';
import 'package:flutter/material.dart';

typedef BooleanCallback = Function();

/// with ok button only
class SingleActionDialogue extends StatelessWidget {
  final String title;
  final String message;
  final String buttonText;
  final bool isDefaultAction;
  final Widget? iconWidget;
  final VoidCallback handleOkCallback;

  const SingleActionDialogue({
    super.key,
    required this.title,
    required this.message,
    required this.buttonText,
    required this.isDefaultAction,
    this.iconWidget,
    required this.handleOkCallback,
  });

  @override
  Widget build(BuildContext context) {
    Size popupSize = MediaQuery.of(context).size;

    return Material(
      type: MaterialType.transparency,
      child: LayoutBuilder(builder: (context, constraints) {
        double webWidth = 379.6;
        // constraints.maxWidth / 5;
        double mobileWidth = constraints.maxWidth;
        double allowedWidth = constraints.maxWidth > 1200
            ? webWidth
            : constraints.maxWidth >= 500
                ? webWidth
                : mobileWidth;
        return Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: allowedWidth,
              margin: const EdgeInsets.symmetric(horizontal: 22),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.0),
                  color: Colors.white),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  title.isNotEmpty
                      ? Text(
                          title,
                          textAlign: TextAlign.center,
                          style: LMSFonts.semiBoldFont(
                              18, AppTheme.commentBoxContent),
                        )
                      : Container(),
                  const SizedBox(height: 20),
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: LMSFonts.regularFontWithHeight(
                        16, AppTheme.commentBoxContent, 1.2),
                  ),
                  const SizedBox(height: 30),
                  _okBtn(context, popupSize),
                ],
              ),
            ),
          ],
        );
      }),
    );
/*
    return PopScope(
      canPop: false,
      child: LayoutBuilder(builder: (context, constraints) {
        double webWidth = 379.6;
        // constraints.maxWidth / 5;
        double mobileWidth = constraints.maxWidth;
        double allowedWidth = constraints.maxWidth > 1200
            ? webWidth
            : constraints.maxWidth >= 500
                ? webWidth
                : mobileWidth;
        print('-------------$mobileWidth');
        print('allowedWidth-------------$allowedWidth');
        return AlertDialog(
          title: title.isNotEmpty
              ? Text(
                  title,
                  textAlign: TextAlign.center,
                  style: LMSFonts.semiBoldFont(18, AppTheme.commentBoxContent),
                )
              : Container(),
          content: Builder(builder: (context) {
            return Container(
              width: allowedWidth,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  iconWidget ?? Container(),
                  SizedBox(height: iconWidget != null ? 15 : 0),
                  Text(
                    message,
                    textAlign: TextAlign.center,
                    style: LMSFonts.regularFontWithHeight(
                        16, AppTheme.commentBoxContent, 1.2),
                  ),
                ],
              ),
            );
          }),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
          actionsPadding: const EdgeInsets.only(bottom: 16),
          actions: <Widget>[
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 30),
              child: Align(
                child: ButtonWidget(
                    minimumSize: Size(popupSize.width / 3, 35.0),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50.0)),
                    child: Text(
                      buttonText,
                      textAlign: TextAlign.center,
                      style: LMSFonts.mediumFont(14, AppTheme.whiteColor, 1.0),
                    ),
                    onPressed: () => isDefaultAction
                        ? Navigator.pop(context)
                        : handleOkCallback()),
              ),
            ),
          ],
        );
      }),
    );*/
  }

  Widget _okBtn(BuildContext context, Size popupSize) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 30),
      child: Align(
        child: ButtonWidget(
            minimumSize: Size(kIsWeb ? 150 : popupSize.width / 3, 35.0),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50.0)),
            child: Text(
              buttonText,
              textAlign: TextAlign.center,
              style: LMSFonts.mediumFont(14, AppTheme.whiteColor, 1.0),
            ),
            onPressed: () =>
                isDefaultAction ? Navigator.pop(context) : handleOkCallback()),
      ),
    );
  }
}

class LangChangeAlertPopup extends StatelessWidget {
  final String title;
  final String message;
  final Widget? iconWidget;
  final VoidCallback onTapped;
  final BooleanCallback onBackTapped;

  const LangChangeAlertPopup({
    super.key,
    required this.title,
    required this.message,
    this.iconWidget,
    required this.onTapped,
    required this.onBackTapped,
  });

  @override
  Widget build(BuildContext context) {
    Size popupSize = MediaQuery.of(context).size;
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
        onBackTapped();
      },
      child: AlertDialog(
        title: title.isNotEmpty
            ? Text(
                title,
                textAlign: TextAlign.center,
                style:
                    LMSFonts.semiBoldFont(22, AppTheme.primaryTextColorBlack),
              )
            : Container(),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            iconWidget ?? Container(),
            SizedBox(height: iconWidget != null ? 15 : 0),
            Text(
              message,
              textAlign: TextAlign.center,
              style: LMSFonts.regularFontWithHeight(
                  18, AppTheme.primaryTextColorBlack, 1.2),
            ),
          ],
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
        actionsPadding: const EdgeInsets.only(bottom: 16),
        actions: <Widget>[
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 30),
            child: ButtonWidget(
                minimumSize: Size(popupSize.width, 40.0),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0)),
                child: Text(
                  SLStrings.getTranslatedString(KEY_OK),
                  textAlign: TextAlign.center,
                  style: LMSFonts.mediumFont(16, LmsColors.white, 1.0),
                ),
                onPressed: () => onTapped()),
          ),
        ],
      ),
    );
  }
}
