import 'package:SmartLearn/src/domain/models/course.dart';
import 'package:SmartLearn/src/domain/models/course_details.dart';
import 'package:SmartLearn/src/domain/models/resource_comment.dart';
import 'package:SmartLearn/src/domain/repositories/course_respository.dart';
import 'package:SmartLearn/src/presentation/cubits/course_list/course_list_cubit.dart';
import 'package:SmartLearn/src/utils/constants/api_response.dart';
import 'package:SmartLearn/src/utils/constants/strings.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MockCourseRepository implements CourseRepository {
  @override
  Future<List<CourseDetails>> fetchCourseDetailsInfo(String courseId) {
    return Future.value(MOCK_COURSE_DETAILS_DATA_LIST);
  }

  @override
  Future<List<Course>> getUserCourse(String topicId, String orgId) {
    return Future.value(MOCK_COURSE_LIST_RESPONSE);
  }

  @override
  Future<List<ResourseComment>> fetchCommentsForId(String instanceId) {
    return Future.value(MOCK_COMMENTS);
  }

  @override
  Future<List<CurrentAffairs>> fetchMoreCurrentAffairs() {
    return Future.value(MOCK_CURRENT_AFFAIRS);
  }

  @override
  Future setCourseProgress(
      String courseId, String instance, Map<String, dynamic> duration) {
    return Future(() => {'status': 'success'});
  }

  @override
  Future uploadCommentsForId(Map<String, dynamic> jsonBody) {
    return Future(() => {'status': 'success'});
  }
}

void main() {
  testWidgets('course list cubit ...', (tester) async {
    // TODO: Implement test
  });

  late MockCourseRepository _mockCourseRepo;
  late CourseListCubit _courseCubit;

  setUp(() {
    SharedPreferences.setMockInitialValues({});
    TOPIC_ID = MOCK_TOPIC_ID;
    ORG_ID = MOCK_ORG_ID;
    _mockCourseRepo = MockCourseRepository();
    _courseCubit = CourseListCubit(_mockCourseRepo);
  });

  tearDown(() {
    _courseCubit.close();
  });

  group('CourseCubit', () {
    TestWidgetsFlutterBinding.ensureInitialized();

    blocTest<CourseListCubit, CourseListState>(
      'emits CourseListSuccess when courses are fetched',
      build: () => _courseCubit,
      act: (cubit) => cubit.getUserCourse(),
      expect: () => [
        CourseListSuccess(course: MOCK_COURSE_LIST_RESPONSE, buttonColor: false)
      ],
    );

    blocTest<CourseListCubit, CourseListState>(
      'emits CourseDetailsFetched when course details are fetched',
      build: () => _courseCubit,
      act: (cubit) => cubit.fetchCourseDetails(MOCK_COURSE_ID),
      expect: () => [
        CourseDetailsFetched(
            courseDetailsData: MOCK_COURSE_DETAILS_DATA,
            course: MOCK_COURSE_LIST_RESPONSE)
      ],
    );

    blocTest<CourseListCubit, CourseListState>(
      'emits CourseCommentsFetched  when comments are fetched',
      build: () => _courseCubit,
      act: (cubit) => cubit.fetchCommentsForId(MOCK_INSTANCE),
      wait: const Duration(seconds: 1),
      expect: () => [
        CourseDetailsLoading(isDataLoading: false),
        CourseCommentsFetched(comments: MOCK_COMMENTS)
      ],
    );

    blocTest<CourseListCubit, CourseListState>(
      'emits CurrentAffairsFetched  when current affairs are fetched',
      build: () => _courseCubit,
      act: (cubit) => cubit.fetchMoreCurrentAffairs(),
      expect: () => [
        CurrentAffairsFetched(
            currentAffairs: MOCK_CURRENT_AFFAIRS,
            course: MOCK_COURSE_LIST_RESPONSE)
      ],
    );

    blocTest<CourseListCubit, CourseListState>(
      'uploadComment',
      build: () => _courseCubit,
      act: (cubit) => cubit.uploadComment(MOCK_UPLOAD_COMMENT_JSON),
      expect: () => [
        CourseDetailsLoading(isDataLoading: false),
        CourseCommentsUploaded(comments: [])
      ],
    );

    blocTest<CourseListCubit, CourseListState>(
      'emits setResourceProgressState  when setCourseProgress',
      build: () => _courseCubit,
      act: (cubit) =>
          cubit.setCourseProgress(MOCK_COURSE_ID, MOCK_INSTANCE, MOCK_DURATION),
      expect: () => [SetResourceProgressState()],
    );
  });
}
