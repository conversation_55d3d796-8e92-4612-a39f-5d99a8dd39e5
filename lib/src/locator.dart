import '../../src/domain/repositories/course_dashboard_repository.dart';
import '../../src/domain/services/hive_db_service.dart';
import 'package:flutter/foundation.dart';

import '/src/config/app_config/api_config.dart';
import '/src/data/repositories/rank_list_repository_impl.dart';
import '/src/domain/repositories/rank_list_repository.dart';

import '/src/data/repositories/exam_repository_impl.dart';
import '/src/domain/repositories/exam_repository.dart';

import '/src/data/repositories/course_details_repository_impl.dart';
import '/src/data/repositories/login_email_repository_impl.dart';
import '/src/data/repositories/register_repository_impl.dart';
import '/src/domain/repositories/course_details_repository.dart';
import '/src/domain/repositories/login_email_repository.dart';
import '/src/domain/repositories/register_repository.dart';
import 'package:awesome_dio_interceptor/awesome_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:supabase/supabase.dart';
import '../../../src/data/repositories/topic_repository_impl.dart';

import 'data/datasources/local/app_database.dart';
import 'data/datasources/remote/news_api_service.dart';
import 'data/repositories/app_config_repository_impl.dart';
import 'data/repositories/course_dashboard_repository_impl.dart';
import 'data/repositories/course_repository_impl.dart';
import 'data/repositories/database_repository_impl.dart';
import 'data/repositories/section_repositorty_impl.dart';
import 'data/repositories/service_repository_impl.dart';
import 'data/repositories/subscription_repository_impl.dart';
import 'data/repositories/user_repository_impl.dart';
import 'data/repositories/organization_repository_impl.dart';
import 'domain/repositories/app_config_repository.dart';
import 'domain/repositories/course_respository.dart';
import 'domain/repositories/database_repository.dart';
import 'domain/repositories/section_repository.dart';
import 'domain/repositories/service_repository.dart';
import 'domain/repositories/subscription_reporsitory.dart';
import 'domain/repositories/user_repository.dart';
import 'domain/repositories/organization_repository.dart';
import 'domain/repositories/topic_repository.dart';
import 'utils/constants/strings.dart';

final locator = GetIt.instance;

Future<void> initializeDependencies() async {
  if (!kIsWeb) {
    final db = await $FloorAppDatabase.databaseBuilder(databaseName).build();
    locator.registerSingleton<AppDatabase>(db);
  }

  final dio = Dio();
  dio.interceptors.add(AwesomeDioInterceptor());

  final client =
      SupabaseClient(APIConfig().SUPABASE_URL, APIConfig().SUPABASE_ANNON_KEY);
  locator.registerSingleton<SupabaseClient>(client);

  locator.registerSingleton<Dio>(dio);

  locator.registerSingleton<NewsApiService>(
    NewsApiService(locator<Dio>()),
  );

  locator.registerSingleton<HiveDbHelper>(HiveDbHelper());

  if (!kIsWeb) {
    locator.registerSingleton<DatabaseRepository>(
      DatabaseRepositoryImpl(locator<AppDatabase>()),
    );
  }

  locator.registerSingleton<UserRepository>(
    UserRepositoryImpl(),
  );

  locator.registerSingleton<OrganizationRepository>(
    OrganizationRepositoryImpl(),
  );

  locator.registerSingleton<TopicRepository>(
    TopicRepositoryImpl(),
  );

  locator.registerSingleton<CourseRepository>(
    CourseRepositoryImpl(),
  );

  locator.registerSingleton<RegisterRepository>(
    RegisterRepositoryImpl(),
  );

  locator.registerSingleton<LoginEmailRepository>(
    LoginEmailRepositoryImpl(),
  );

  locator.registerSingleton<CourseDetailsRepository>(
    CourseDetailsRepositoryImpl(),
  );

  locator.registerSingleton<SectionRepository>(
    SectionRepositoryImpl(),
  );

  locator.registerSingleton<ExamRepository>(
    ExamRepositoryImpl(),
  );

  locator.registerSingleton<RankListRepository>(
    RankListRepositoryImpl(),
  );

  locator.registerSingleton<ServiceRepository>(
    ServiceRepositoryImpl(),
  );

  locator.registerSingleton<SubscriptionRepository>(
    SubscriptionRepositoryImpl(),
  );
  locator.registerSingleton<CourseDashboardRepository>(
    CourseDashboardRepositoryImpl(),
  );
  locator.registerSingleton<AppConfigRepository>(
    AppConfigRepositoryImpl(),
  );
}
