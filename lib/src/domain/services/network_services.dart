import 'dart:io';

import '../../../main.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../config/app_config/api_config.dart';
import '../../config/app_config/preference_config.dart';
import '../../config/router/app_router.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/strings.dart';

class NetworkServices {
  static Supabase? client;
  initSupabase() async {
    client = await Supabase.initialize(
      url: APIConfig.SUPABASE_URL,
      anonKey: APIConfig.SUPABASE_ANONN_KEY,
    ).onError((error, stackTrace) {
      debugPrint(
          '[main][_initSupabase][AuthException]: $error\n stackTrace:$stackTrace');
      throw error.toString();
    });
  }

  /// initialize supabase
  initializeSupabase() async {
    try {
      bool isNetworkAvailable = await checkNetworkStatus();

      if (isNetworkAvailable) {
        await initSupabase();
        await _listenAuthStatusChanges();
      } else {
        debugPrint('[main][_initSupabase]: ');
      }
    } on AuthException catch (e) {
      debugPrint('[main][_initSupabase][AuthException]: $e');
    } on SocketException {
      debugPrint('[main][_initSupabase]: network not available');
    } on Exception catch (e) {
      debugPrint('[main][_initSupabase]: $e');
    }
  }

  /// listen supabase auth state changes to handle session timeout
  _listenAuthStatusChanges() async {
    try {
      final SupabaseClient _supabaseClient = Supabase.instance.client;
      SharedPreferences _prefs = await SharedPreferences.getInstance();

      _supabaseClient.auth.onAuthStateChange.listen((data) async {
        final AuthChangeEvent event = data.event;
        final Session? session = data.session;
        bool _manualSignoutStatus = _prefs.getBool(PREF_KEY_SIGNOUT) ?? false;
        bool _didAppLaunchForFirstTime =
            _prefs.getBool(PREF_KEY_FIRST_LAUNCH) ?? true;
        if (event == AuthChangeEvent.signedIn) {
          if (session != null && session.refreshToken != null) {
            await _saveRefreshToken(data.session!.refreshToken!);
          }
          if (session != null) {
            await _saveUserId(session.user.id, _manualSignoutStatus);
          }
          // handle signIn event
          debugPrint('User signedin with id: ${session?.user.id ?? ''}');
        } else if (event == AuthChangeEvent.signedOut) {
          await clearCurrentUserInfo();

          if (_didAppLaunchForFirstTime) {
            // installed for the first time
            debugPrint('APP INITIAL LAUNCH!!!');
            kIsWeb
                ? Beamer.of(beamerGlobalKey.currentContext!)
                    .beamToNamed('/login-view', data: {
                    'isTokenExpired': false,
                  })
                : appRouter.push(LoginEmailViewRoute(isTokenExpired: false));
          } else if (_manualSignoutStatus) {
            // stay in login screen
            debugPrint('USER LOGGED OUT!!!');
            kIsWeb
                ? Beamer.of(beamerGlobalKey.currentContext!)
                    .beamToNamed('/login-view', data: {
                    'isTokenExpired': false,
                  })
                : appRouter.push(LoginEmailViewRoute(isTokenExpired: false));
          } else {
            ///session time out
            kIsWeb
                ? Beamer.of(beamerGlobalKey.currentContext!)
                    .beamToNamed('/login-view', data: {
                    'isTokenExpired': true,
                  })
                : appRouter.push(LoginEmailViewRoute(isTokenExpired: true));
            debugPrint('SESSION TIME OUT!!!');
          }
        } else if (event == AuthChangeEvent.tokenRefreshed) {
          if (data.session != null && data.session!.refreshToken != null) {
            await _saveRefreshToken(data.session!.refreshToken!);
          }
          if (data.session != null) {
            await _saveUserId(data.session!.user.id, _manualSignoutStatus);
          }
          debugPrint(
              'Token refresh successfull! ${data.session?.refreshToken}');
        } else {
          debugPrint('AuthChangeEvent else');
        }
      }).onError((error) {
        _onError(_supabaseClient, error);
        return;
      });
      // ..onDone(() {
      //   debugPrint('onDone');
      // });
    } on AuthException catch (e) {
      debugPrint('[main][listenAuthStatusChanges][AuthException]: $e');
    } on SocketException {
      debugPrint(
          '[main][listenAuthStatusChanges][SocketException]: network not available');
    } on Exception catch (e) {
      debugPrint('[main][listenAuthStatusChanges][Exception]: $e');
    }
  }

  _onError(SupabaseClient _supabaseClient, dynamic error) async {
    try {
      await clearCurrentUserInfo();
      kIsWeb
          ? Beamer.of(beamerGlobalKey.currentContext!)
              .beamToNamed('/login-view', data: {
              'isTokenExpired': true,
            })
          : appRouter.push(LoginEmailViewRoute(isTokenExpired: true));
      return;
    } on AuthException catch (e) {
      debugPrint('[main][_onError][AuthException]: $e');
      return;
    } on SocketException {
      debugPrint('[main][_onError][SocketException]: network not available');
      return;
    } on Exception catch (e) {
      debugPrint('[main][_onError][Exception]: $e');
      return;
    }
  }

  _saveUserId(String userId, bool _manualSignoutStatus) async {
    if (_manualSignoutStatus) {
      /// if user signed out manually
      /// either by signout or through lang change,
      /// do not - fetch the user id and save it.
    } else {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      _prefs.setString(PREF_KEY_USER_ID, userId);
      USER_ID = userId;
    }
  }

  _saveRefreshToken(String refreshToken) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.setString(PREF_KEY_REFRESH_TOKEN, refreshToken);
  }

  Future<String> _fetchRefreshToken() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    return _prefs.getString(PREF_KEY_REFRESH_TOKEN) ?? '';
  }

  Future<void> _refreshJWT() async {
    final currentSession = Supabase.instance.client.auth.currentSession;
    if (currentSession != null && currentSession.expiresAt != null) {
      int timestampInMilliseconds = currentSession.expiresAt! * 1000;
      DateTime expDateTime =
          DateTime.fromMillisecondsSinceEpoch(timestampInMilliseconds);
      debugPrint('expires at--------?$expDateTime');
      DateTime currentTime = DateTime.now();
      if (currentTime.isAfter(expDateTime)) {
        //   await Supabase.instance.client.auth.refreshSession();
      }
    }
  }
}
