import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart' as provider;
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/app_config/api_config.dart';
import '../../config/app_config/preference_config.dart';
import '../../config/app_config/sl_config.dart';
import '../../config/enums/alert_types.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../domain/services/provider/language_provider.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/lists.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../../utils/constants/nums.dart';
import '../cubits/login_email/login_cubit.dart';
import '../cubits/organization/organization_cubit.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/config/themes/lms_fonts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../utils/constants/strings.dart';
import '../cubits/register/register_cubit.dart';
import '../cubits/validation/validation_cubit.dart';
import '../widgets/button_widget.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/textfield_widget.dart';

@RoutePage()

// ignore: must_be_immutable
class SignUpViewScreen extends HookWidget with WidgetsBindingObserver {
  SignUpViewScreen({Key? key}) : super(key: key);

  late TextEditingController _emailController;
  late TextEditingController _passwordController;
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _confirmPasswordController;
  late TextEditingController _phoneNumberController;

  String? emailError;
  String? passwordError;
  String? confirmPasswordError;
  String? phoneNumError;
  bool enableSubmitButton = false;
  bool isValidSubmission = false;

  BuildContext? currentContext;

  @override
  Future<void> didChangeLocales(List<Locale>? locales) async {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    if (locales != null && locales.isNotEmpty) {
      String newLang = locales[0].languageCode;
      if (SLStrings.currentLanguage.contains(newLang)) {
        // language not changed
      } else {
        // language changed
        String _selectedLanguage = await getUpdatedLang(newLang);

        _selectedLanguage = changeLocaleWithoutAlert(
            currentContext, newLang, _selectedLanguage);
        if (currentContext != null && currentContext!.routeData.isActive) {
          // Future.delayed(const Duration(milliseconds: 500), () {
          _updateAppLocale(currentContext!, _selectedLanguage);
          // });
        }
      }
    }
  }

  _updateAppLocale(BuildContext context, String _selectedLanguage) async {
    // await _getUpdatedLang();
    provider.Provider.of<LanguageProvider>(context, listen: false)
        .changeCurrentLanguage(_selectedLanguage);
    _onLangaugeChangedinSignup(context, _selectedLanguage);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;

    final validationCubit = context.read<ValidationCubit>();
    final registerCubit = context.read<RegisterCubit>();

    final screenWidth = MediaQuery.of(context).size.width;
    FocusNode _firstNameFocusNode = useFocusNode();
    FocusNode _lastNameFocusNode = useFocusNode();
    FocusNode _emailFocusNode = useFocusNode();
    FocusNode _passwordFocusNode = useFocusNode();
    FocusNode _confirmPasswordFocusNode = useFocusNode();
    FocusNode _phoneNumFocusNode = useFocusNode();

    _emailController = useTextEditingController();
    _passwordController = useTextEditingController();
    _firstNameController = useTextEditingController();
    _lastNameController = useTextEditingController();
    _confirmPasswordController = useTextEditingController();
    _phoneNumberController = useTextEditingController();

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      _clearTextFieldValidation(validationCubit);
      _firstNameController.addListener(() {
        validationCubit.updateFirstName(_firstNameController.text);
      });

      _lastNameController.addListener(() {
        validationCubit.updateLastName(_lastNameController.text);
      });

      _emailController.addListener(() {
        validationCubit.changeEmail(_emailController.text);
      });

      _passwordController.addListener(() {
        validationCubit.changePassword(_passwordController.text);
      });

      _confirmPasswordController.addListener(() {
        validationCubit.changeConfirmPassword(_confirmPasswordController.text);
      });

      _phoneNumberController.addListener(() {
        validationCubit.changeNumber(_phoneNumberController.text);
      });
      return () {
        WidgetsBinding.instance.removeObserver(this);
        // validationCubit.password.sink
      };
    }, const []);
    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
          // validationCubit.validateEmail(_emailController.text);
        }
      },
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('$ASSETS_PATH/background.png'),
              fit: BoxFit.cover,
            ),
          ),
          child: BlocListener<RegisterCubit, RegisterState>(
            listener: (_registerListenerCubit, _registerListenerState) {},
            child: BlocBuilder<RegisterCubit, RegisterState>(
              builder: (_registerContext, _registerState) {
                if (_registerState is ChangeLangauge) {}
                return SafeArea(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      SingleChildScrollView(
                        clipBehavior: Clip.none,
                        child: BlocBuilder<ValidationCubit, ValidationState>(
                          builder: (_validationContext, _validationState) {
                            if (_validationState is ValidatedAllFields) {
                              isValidSubmission = checkAllFieldsOccupied;

                              validationCubit.checkSubmitButtonStatus(
                                  isValidSubmission,
                                  _phoneNumberController.text,
                                  _firstNameController.text,
                                  _lastNameController.text,
                                  emailError,
                                  passwordError,
                                  confirmPasswordError,
                                  phoneNumError);
                            }
                            if (_validationState is SubmitButtonStatus) {
                              enableSubmitButton =
                                  _validationState.enableButton;
                            }

                            return Container(
                              // height: MediaQuery.of(context).size.height -
                              //     MediaQuery.of(context).padding.top,
                              decoration: const BoxDecoration(
                                  // image: DecorationImage(
                                  //   image: AssetImage(
                                  //       '$ASSETS_PATH/background.png'),
                                  //   fit: BoxFit.cover,
                                  // ),
                                  ),
                              child: Card(
                                margin: const EdgeInsets.all(20.0),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                color: const Color.fromARGB(75, 34, 12, 12),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 22),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const SizedBox(height: 19),
                                      _signupTitle(),
                                      const SizedBox(height: 16),
                                      _firstNameWidget(
                                          context,
                                          screenWidth,
                                          _validationContext,
                                          _firstNameController,
                                          _firstNameFocusNode,
                                          validationCubit),
                                      const SizedBox(height: 15.0),
                                      _lastNameWidget(
                                          context,
                                          screenWidth,
                                          _validationContext,
                                          _lastNameController,
                                          _lastNameFocusNode,
                                          validationCubit),
                                      const SizedBox(height: 15.0),
                                      _emailWidget(
                                          screenWidth,
                                          _emailController,
                                          _emailFocusNode,
                                          _validationState.emailError ??
                                              emailError,
                                          _validationContext,
                                          validationCubit),
                                      const SizedBox(height: 15.0),
                                      _passwordWidget(
                                        screenWidth,
                                        _passwordController,
                                        _passwordFocusNode,
                                        validationCubit,
                                        _validationContext,
                                      ),
                                      const SizedBox(height: 15.0),
                                      _confirmpasswordWidget(
                                          screenWidth,
                                          _passwordController,
                                          _confirmPasswordController,
                                          _confirmPasswordFocusNode,
                                          _validationContext,
                                          validationCubit),
                                      const SizedBox(height: 20.0),
                                      _phoneNumberWidget(
                                          screenWidth,
                                          _phoneNumberController,
                                          _phoneNumFocusNode,
                                          phoneNumError,
                                          _validationContext,
                                          validationCubit),
                                      const SizedBox(height: 60),
                                      // Spacer(),
                                      ButtonWidget(
                                        color: enableSubmitButton
                                            ? AppTheme.enabledBtnColor
                                            : AppTheme.disabledBtnColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                              buttonRadius),
                                        ),
                                        child: Text(
                                            SLStrings.getTranslatedString(
                                                KEY_SUBMIT),
                                            style: LMSFonts.buttonStyle(16)),
                                        onPressed: enableSubmitButton
                                            ? () async {
                                                await _handleUserRegistration(
                                                    context,
                                                    _registerContext,
                                                    _validationContext,
                                                    registerCubit);
                                              }
                                            : () {},
                                      ),
                                      const SizedBox(height: 27),
                                      _signupOptionsWidget(context),
                                      const SizedBox(height: 40),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      _registerState.isLoading
                          ? LoadingIndicatorClass()
                          : Container(),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  get checkAllFieldsOccupied =>
      _firstNameController.text.trim().isNotEmpty &&
      _lastNameController.text.trim().isNotEmpty &&
      _emailController.text.isNotEmpty &&
      _passwordController.text.isNotEmpty &&
      _confirmPasswordController.text.isNotEmpty &&
      _phoneNumberController.text.isNotEmpty;

  void _clearTextFieldValidation(ValidationCubit validationCubit) {
    validationCubit.clearFirstNameError();
    validationCubit.clearLastNameError();
    validationCubit.clearEmailError();
    validationCubit.clearPasswordError();
    validationCubit.clearConfirmPasswordError();
    validationCubit.clearPhoneError();
  }

  Widget _signupTitle() {
    return Text(SLStrings.getTranslatedString(KEY_SIGNUP_TITLE),
        textAlign: TextAlign.center, style: LMSFonts.loginSignupTitle());
  }

  Widget _firstNameWidget(
      BuildContext context,
      double screenWidth,
      BuildContext _validationContext,
      TextEditingController _firstNameController,
      FocusNode _firstNameFocusNode,
      ValidationCubit validationCubit) {
    return StreamBuilder<String>(
        stream: validationCubit.firstName,
        builder: (context, snapshot) {
          String? errorMessage = snapshot.error != null && snapshot.error != ''
              ? snapshot.error.toString()
              : null;

          if (_firstNameFocusNode.hasFocus) {
            validationCubit.validateAllFields();
          }
          return SLTextFieldWidget(
            obscureText: false,
            focusNode: _firstNameFocusNode,
            controller: _firstNameController,
            errorMsg: _firstNameFocusNode.hasFocus ? null : errorMessage,
            showSuffixIcon: true,
            hint: SLStrings.getTranslatedString(KEY_FIRST_NAME),
            isNameField: true,
            onTap: () async {
              await validateInputData(
                  _validationContext,
                  _emailController.text,
                  _passwordController.text,
                  _confirmPasswordController.text,
                  _phoneNumberController.text);
            },
            onChange: (val) {
              validationCubit.validateFirstNameInput;
              validationCubit.validateAllFields();
            },
            onSufixIconTapped: () {
              _firstNameController.clear();
            },
            onSubmitted: (val) {},
          );
        });
  }

  Widget _lastNameWidget(
      BuildContext context,
      double screenWidth,
      BuildContext _validationContext,
      TextEditingController _lastNameController,
      FocusNode _lastNameFocusNode,
      ValidationCubit validationCubit) {
    return StreamBuilder<String>(
        stream: validationCubit.lastName,
        builder: (context, snapshot) {
          String? errorMessage = snapshot.error != null && snapshot.error != ''
              ? snapshot.error.toString()
              : null;
          if (_lastNameFocusNode.hasFocus) {
            validationCubit.validateAllFields();
          }

          return SLTextFieldWidget(
            focusNode: _lastNameFocusNode,
            obscureText: false,
            controller: _lastNameController,
            hint: SLStrings.getTranslatedString(KEY_LAST_NAME),
            errorMsg: _lastNameFocusNode.hasFocus ? null : errorMessage,
            isNameField: true,
            showSuffixIcon: true,
            onTap: () async {
              await validateInputData(
                  _validationContext,
                  _emailController.text,
                  _passwordController.text,
                  _confirmPasswordController.text,
                  _phoneNumberController.text);
            },
            onChange: (val) {
              validationCubit.validateAllFields();
            },
            onSufixIconTapped: () {
              _lastNameController.clear();
            },
            onSubmitted: (val) {},
          );
        });
  }

  Widget _emailWidget(
      double screenWidth,
      TextEditingController _emailController,
      FocusNode _emailFocusNode,
      String? error,
      BuildContext _validationContext,
      ValidationCubit validationCubit) {
    return StreamBuilder<String>(
        stream: validationCubit.email,
        builder: (context, snapshot) {
          error = snapshot.error != null && snapshot.error != ''
              ? snapshot.error.toString()
              : null;
          emailError = error;

          if (_emailFocusNode.hasFocus) {
            validationCubit.validateAllFields();
          }

          return SLTextFieldWidget(
            focusNode: _emailFocusNode,
            obscureText: false,
            showSuffixIcon: true,
            controller: _emailController,
            textInputType: TextInputType.emailAddress,
            errorMsg: _emailFocusNode.hasFocus ? null : error,
            hint: SLStrings.getTranslatedString(KEY_EMAIL),
            onTap: () async {
              await validateInputData(
                  _validationContext,
                  _emailController.text,
                  _passwordController.text,
                  _confirmPasswordController.text,
                  _phoneNumberController.text);
            },
            onChange: (val) async {
              validationCubit.validateEmailInput;
              validationCubit.validateAllFields();
            },
            onSufixIconTapped: () {
              _emailController.clear();
              if (_emailController.text.isNotEmpty) {
                validationCubit.email.drain();
              }
            },
            onSubmitted: (val) async {
              validationCubit.validateEmailInput;
            },
          );
        });
  }

  Widget _passwordWidget(
      double screenWidth,
      TextEditingController _passwordController,
      FocusNode _passwordFocusNode,
      ValidationCubit validationCubit,
      BuildContext _validationContext) {
    return StreamBuilder<String>(
        stream: validationCubit.password,
        builder: (context, snapshot) {
          String? error = snapshot.error != null && snapshot.error != ''
              ? snapshot.error.toString()
              : null;
          passwordError = error;

          if (_passwordFocusNode.hasFocus) {
            validationCubit.validateAllFields();
          }

          return SLTextFieldWidget(
            focusNode: _passwordFocusNode,
            obscureText: true,
            errorMsg: _passwordFocusNode.hasFocus ? null : passwordError,
            controller: _passwordController,
            hint: SLStrings.getTranslatedString(KEY_PASSWORD),
            textInputType: TextInputType.visiblePassword,
            showSuffixIcon: true,
            onTap: () async {
              await validateInputData(
                  _validationContext,
                  _emailController.text,
                  _passwordController.text,
                  _confirmPasswordController.text,
                  _phoneNumberController.text);
            },
            onChange: (val) async {
              validationCubit.validatePasswordInput;
              validationCubit.validatePasswords;

              validationCubit.validateAllFields();
            },
            onSufixIconTapped: () {
              _passwordController.clear();

              if (_passwordController.text.isNotEmpty) {
                validationCubit.password.drain();
              }
            },
            onSubmitted: (val) async {},
          );
        });
  }

  Widget _confirmpasswordWidget(
      double screenWidth,
      TextEditingController _passwordController,
      TextEditingController _confirmPasswordController,
      FocusNode _confirmPasswordFocusNode,
      BuildContext _validationContext,
      ValidationCubit validationCubit) {
    return StreamBuilder<String>(
        stream: validationCubit.confirmPassword,
        builder: (context, snapshot) {
          String? error = snapshot.error != null &&
                  snapshot.error !=
                      '' //&&_confirmPasswordController.text.isNotEmpty
              ? snapshot.error.toString()
              : null;
          confirmPasswordError = error;

          if (_confirmPasswordFocusNode.hasFocus) {
            validationCubit.validateAllFields();
          }
          return SLTextFieldWidget(
            focusNode: _confirmPasswordFocusNode,
            obscureText: true,
            controller: _confirmPasswordController,
            errorMsg: _confirmPasswordFocusNode.hasFocus
                ? null
                : confirmPasswordError != null &&
                        confirmPasswordError?.trim() != ''
                    ? confirmPasswordError
                    : null,
            hint: SLStrings.getTranslatedString(KEY_CONFIRM_PASSWORD),
            textInputType: TextInputType.visiblePassword,
            showSuffixIcon: true,
            onTap: () async {
              await validateInputData(
                  _validationContext,
                  _emailController.text,
                  _passwordController.text,
                  _confirmPasswordController.text,
                  _phoneNumberController.text);
            },
            onChange: (val) async {
              // if()
              // validationCubit.validateConfirmPasswordInput;
              validationCubit.validatePasswords;
              validationCubit.validateAllFields();
            },
            onSufixIconTapped: () {
              _confirmPasswordController.clear();
              if (_confirmPasswordController.text.isNotEmpty) {
                validationCubit.confirmPassword.drain();
              }
            },
            onSubmitted: (val) async {
              validationCubit.validatePasswords;
            },
          );
        });
  }

  Widget _phoneNumberWidget(
      double screenWidth,
      TextEditingController _phoneNumberController,
      FocusNode _phoneNumFocusNode,
      String? error,
      BuildContext _validationContext,
      ValidationCubit validationCubit) {
    return StreamBuilder<String>(
        stream: validationCubit.phoneNum,
        builder: (context, snapshot) {
          error = snapshot.error != null &&
                  snapshot.error !=
                      '' //&& _phoneNumberController.text.isNotEmpty
              ? snapshot.error.toString()
              : null;
          phoneNumError = error;
          if (_phoneNumFocusNode.hasFocus) {
            validationCubit.validateAllFields();
          }

          return SLTextFieldWidget(
            focusNode: _phoneNumFocusNode,
            obscureText: false,
            controller: _phoneNumberController,
            textInputType: TextInputType.phone,
            errorMsg: _phoneNumFocusNode.hasFocus ? null : phoneNumError,
            hint: SLStrings.getTranslatedString(KEY_PHONE_NUMBER),
            showSuffixIcon: true,
            onTap: () async {
              await validateInputData(
                  _validationContext,
                  _emailController.text,
                  _passwordController.text,
                  _confirmPasswordController.text,
                  _phoneNumberController.text);
            },
            onChange: (val) async {
              validationCubit.validatePhoneInput;

              validationCubit.validateAllFields();
            },
            onSufixIconTapped: () {
              _phoneNumberController.clear();
            },
            onSubmitted: (val) async {
              await _validationContext
                  .read<ValidationCubit>()
                  .validatePhoneNumber(
                    _phoneNumberController.text,
                  );
            },
          );
        });
  }

  Widget _signupOptionsWidget(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        noAccountText(),
        const SizedBox(width: 8),
        loginWidget(context)
      ],
    );
  }

  Widget noAccountText() {
    return Flexible(
        child: Text(
      SLStrings.getTranslatedString(KEY_ACCOUNT),
      style: LMSFonts.regularFontWithHeight(14, AppTheme.whiteColor, 1.0),
    ));
  }

  Widget loginWidget(BuildContext context) {
    return GestureDetector(
        onTap: () {
          kIsWeb ? Beamer.of(context).beamBack() : appRouter.popForced();
        },
        child: Container(
            alignment: Alignment.centerRight,
            child: Text(
              SLStrings.getTranslatedString(KEY_LOGIN),
              style:
                  LMSFonts.regularFontWithHeight(14, AppTheme.primaryBlue, 1.0),
            )));
  }

  _handleUserRegistration(BuildContext context, BuildContext _registerContext,
      BuildContext _validationContext, RegisterCubit registerCubit) async {
    await _registerContext.read<RegisterCubit>().setLoading(true);
    await validateInputData(
        _validationContext,
        _emailController.text,
        _passwordController.text,
        _confirmPasswordController.text,
        _phoneNumberController.text);
    await _registerContext.read<RegisterCubit>().setLoading(true);
    final userData = {
      'first_name': _firstNameController.text,
      'last_name': _lastNameController.text,
      'phonenumber1': _phoneNumberController.text,
    };

    await _registerContext
        .read<RegisterCubit>()
        .register(_emailController.text, _passwordController.text, userData);

    RegisterState registerState = registerCubit.state;

    if (registerState is RegisterSuccess) {
      await _setDefaultOrg(context);
      _showRegistrationSuccessPopup(context);
    } else if (registerState is RegisterError) {
      _showRegistrationFailurePopup(context, registerState.message);
    } else if (registerState is ExitUserError) {
      _showExistingMailIdPopup(context);
    }

    await _registerContext.read<RegisterCubit>().setLoading(false);
  }

  ///
  /// if no org is fetched,
  /// set to the static competitor org
  /// do not navigate to welcome screen and let admin the org
  ///
  _setDefaultOrg(BuildContext context) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    final _orgCubit = BlocProvider.of<OrganizationCubit>(context);
    final _loginCubit = BlocProvider.of<LoginEmailCubit>(context);

    /// first call login before calling any other apis
    /// If login is not performed,
    /// then supabase won't authenticate other apis
    await _loginCubit.loginEmail(
      _emailController.text,
      _passwordController.text,
    );

    ORG_ID = APIConfig.defaultOrgId;
    _prefs.setString(PREF_KEY_ORG_ID, ORG_ID);
    await _orgCubit.setDefaultOrganization(ORG_ID, APIConfig.defaultRollId);
  }

  _showRegistrationSuccessPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return MultiActionDialogue(
            alertType: AlertType.success,
            title: SLStrings.getTranslatedString(KEY_SUCCESS),
            content: SLStrings.getTranslatedString(KEY_REGISTER_SUCCESS_MSG),
            cancelBtnText: SLStrings.getTranslatedString(KEY_CLOSE),
            confirmBtnText: '',
            onCancelTap: () async {
              await clearCurrentUserInfo();

              if (kIsWeb) {
                Navigator.of(context).pop();
                Beamer.of(context).beamBack();
              } else {
                appRouter.popForced();
                appRouter.replace(LoginEmailViewRoute(isTokenExpired: false));
              }
            },
            onContinueTap: () {});
      },
    );
  }

  _showRegistrationFailurePopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_WARNING),
            alertType: AlertType.warning,
            content: msg,
            cancelBtnText: '',
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
            onCancelTap: () {},
            onContinueTap: () {
              kIsWeb ? Navigator.of(context).pop() : appRouter.popForced();
            });
      },
    );
  }

  _showExistingMailIdPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return MultiActionDialogue(
            alertType: AlertType.error,
            title: SLStrings.getTranslatedString(KEY_SUCCESS),
            content: SLStrings.getTranslatedString(KEY_EMAIL_EXIST),
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
            cancelBtnText: '',
            onContinueTap: () async {
              await clearCurrentUserInfo();
              if (kIsWeb) {
                Navigator.of(context).pop();
                // Beamer.of(context).beamBack();
              } else {
                appRouter.popForced();
                // appRouter.replace(LoginEmailViewRoute(isTokenExpired: false));
              }
            },
            onCancelTap: () {});
      },
    );
  }

  /// Validate all input fields
  validateInputData(BuildContext _validationContext, String email,
      String password, String confirmPassworrd, String phNo) async {
    await _validationContext.read<ValidationCubit>().validateEmail(
          email,
        );
    await _validationContext
        .read<ValidationCubit>()
        .validatePassword(password, confirmPassworrd);

    await _validationContext.read<ValidationCubit>().validatePhoneNumber(phNo);
  }

  void _showLanguageChangedAlert(BuildContext context, String newLang) {
    bool isThereCurrentDialogShowing =
        ModalRoute.of(context)?.isCurrent != true;

    if (context != null &&
        context.routeData.isActive &&
        !isThereCurrentDialogShowing) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return SingleActionDialogue(
            title: '',
            message:
                SLStrings.getTranslatedString(KEY_LANGUAGE_CHANGE_ALERT_EN),
            iconWidget: const Icon(Icons.error, size: 60),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: true,
            handleOkCallback: () {},
          );
        },
      ).then((value) {
        _onLangaugeChangedinSignup(context, SLStrings.currentLanguage);
      });
    }
  }

  _onLangaugeChangedinSignup(BuildContext context, String newLang) {
    final registerCubit = BlocProvider.of<RegisterCubit>(context);
    final loginCubit = BlocProvider.of<LoginEmailCubit>(context);
    final validationCubit = context.read<ValidationCubit>();

    registerCubit.handleLanguageSelection(context, newLang);
    loginCubit.handleLanguageSelection(context, newLang);
    validationCubit.validatePassword(
        _passwordController.text, _confirmPasswordController.text);
  }
}
