import 'dart:async';

import 'package:intl/intl.dart';

import '../cubits/app_config/app_config_cubit.dart';
import '/src/config/themes/lms_fonts.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '/src/config/themes/app_theme.dart';
import '/src/utils/constants/strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../config/router/app_router.dart';
import '../widgets/app_bar.dart';
import '../cubits/livelist/livelist_cubit.dart';
import '../../data/repositories/live_class_list_repository_impl.dart';
import '../../domain/models/live_class_model.dart';

@RoutePage()
class LiveClassViewScreen extends HookWidget {
  const LiveClassViewScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          LivelistCubit(LiveClassListRepositoryImpl())..fetchLiveClasses(),
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
            title: LIVE_CLASS_APPBAR_TITLE,
            trailingWidget: Container(),
            leadingIconName: BACK_ARROW,
            leadingBtnAction: () {
              kIsWeb ? Beamer.of(context).beamBack() : appRouter.popForced();
            },
          ),
        ),
        body: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: BlocBuilder<LivelistCubit, LivelistState>(
                builder: (context, state) {
                  if (state is LivelistLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (state is LivelistError) {
                    return Center(
                      child: Text(
                        'Error: ${state.message}',
                        style: const TextStyle(
                          color: Colors.red,
                          fontSize: 16,
                        ),
                      ),
                    );
                  }

                  if (state is LivelistLoaded) {
                    if (state.liveClasses.isEmpty) {
                      return noLiveClassesWidget();
                    }
                    return ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: state.liveClasses.length,
                      itemBuilder: (context, index) {
                        final liveClass = state.liveClasses[index];
                        return LiveClassCard(
                            liveClass: liveClass, context: context);
                      },
                    );
                  }

                  return noLiveClassesWidget();
                },
              ),
            );
          },
        ),
      ),
    );
  }

  Widget noLiveClassesWidget() {
    return const Center(
      child: Text(
        'No live classes available',
        style: TextStyle(
          fontSize: 16,
        ),
      ),
    );
  }
}

class LiveClassCard extends StatelessWidget {
  final BuildContext context;
  final LiveClassModel liveClass;

  const LiveClassCard(
      {Key? key, required this.liveClass, required this.context})
      : super(key: key);

  String _formatDateTime(DateTime dateTime) {
    String formattedTime = DateFormat('dd-MM-yy hh:mm a').format(dateTime);

    // return "${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}";
    return formattedTime;
  }

  Future<String> _launchMeeting(String? url, String? passcode) async {
    String result = '';
    if (url == null || url.isEmpty) {
      result = 'Invalid url';
      return result;
    }

    final Uri meetingUri = Uri.parse(url);

    try {
      if (await canLaunchUrl(meetingUri)) {
        await launchUrl(
          meetingUri,
          mode: LaunchMode.externalApplication,
        );

        // If there's a passcode, show it to the user
        if (passcode != null && passcode.isNotEmpty) {
          result = 'Successfully launched meeting with url: $url';
          // Show passcode dialog
          // ignore: use_build_context_synchronously
          // showDialog(
          //   context: context,
          //   builder: (context) => AlertDialog(
          //     title: const Text('Meeting Passcode'),
          //     content: SelectableText(
          //       passcode,
          //       style:
          //           const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          //     ),
          //     actions: [
          //       TextButton(
          //         onPressed: () => Navigator.pop(context),
          //         child: const Text('Close'),
          //       ),
          //     ],
          //   ),
          // );
        }
      } else {
        result = 'Could not launch meeting URL';
        throw result;
      }
    } on Exception catch (e) {
      result = 'Error launching meeting: $e';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result),
          backgroundColor: Colors.red,
        ),
      );
    }
    return result;
  }

  Future<void> _setActivityLog(
      BuildContext context, String url, String result) async {
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    final logResult = result.contains('Successfully') ? 'success' : 'error';
    final actionDetails = result;
    final actionComment = '$result at ${DateTime.now()}';

    final logPayload = {
      'activity_type': 'Subscription',
      'screen_name': 'SubscriptionView',
      'action_details': actionDetails,
      'target_id': liveClass.meetingId,
      'action_comment': actionComment,
      'log_result': logResult,
    };

    await _appConfigCubit.setUserActivityLog(logPayload);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  (liveClass.meetingId ?? '').trim(),
                  style: LMSFonts.semiBoldFont(
                    16,
                    AppTheme.primaryTextColorBlack,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: liveClass.currentMeetingStatus ==
                            CurrentMeetingStatus.LIVE
                        ? Colors.green[100]
                        : liveClass.currentMeetingStatus ==
                                CurrentMeetingStatus.UPCOMING
                            ? Colors.orange[100]
                            : liveClass.currentMeetingStatus ==
                                    CurrentMeetingStatus.ENDED
                                ? Colors.red[100]
                                : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    liveClass.currentMeetingStatus.name.toLowerCase(),
                    style: LMSFonts.regularFont(
                      12,
                      liveClass.currentMeetingStatus ==
                              CurrentMeetingStatus.LIVE
                          ? Colors.green.shade800
                          : liveClass.currentMeetingStatus ==
                                  CurrentMeetingStatus.UPCOMING
                              ? Colors.orange.shade800
                              : liveClass.currentMeetingStatus ==
                                      CurrentMeetingStatus.ENDED
                                  ? Colors.red.shade800
                                  : Colors.grey.shade800,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  liveClass.meetingType == MeetingType.ZOOM
                      ? Icons.videocam
                      : Icons.video_call,
                  size: 20,
                  color: liveClass.meetingType == MeetingType.ZOOM
                      ? Colors.blue
                      : Colors.green,
                ),
                const SizedBox(width: 4),
                Text(
                  liveClass.meetingType == MeetingType.ZOOM
                      ? 'Zoom'
                      : 'Google Meet',
                  style: LMSFonts.mediumFont(
                      12,
                      liveClass.meetingType == MeetingType.ZOOM
                          ? Colors.blue
                          : Colors.green,
                      1.0),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.calendar_month,
                  size: 18,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  _formatDateTime(liveClass.startDate) +
                      " to " +
                      _formatDateTime(liveClass.endDate),
                  maxLines: 2,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: liveClass.currentMeetingStatus ==
                            CurrentMeetingStatus.LIVE
                        ? () async {
                            final String result = await _launchMeeting(
                              liveClass.meetingUrl,
                              liveClass.passcode,
                            );
                            await _setActivityLog(
                                context, liveClass.meetingUrl ?? '', result);
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.courseCompletedProgressGreen,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Join Meeting',
                      style: LMSFonts.buttonStyle(14),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
