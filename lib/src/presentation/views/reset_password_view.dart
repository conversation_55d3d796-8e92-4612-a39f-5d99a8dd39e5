import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/enums/alert_types.dart';
import '../../config/themes/lms_fonts.dart';
import '../../utils/constants/nums.dart';

import '../cubits/reset_password/reset_password_cubit.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/button_widget.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/textfield_widget.dart';
import '/src/config/themes/app_theme.dart';

import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';

import '../widgets/app_bar.dart';

@RoutePage()

// ignore: must_be_immutable
class ResetPasswordViewScreen extends HookWidget {
  ResetPasswordViewScreen({
    Key? key,
  }) : super(key: key);
  final TextEditingController _resetEmailController = TextEditingController();
  bool enableSubmitButton = false;
  bool _isValidSubmission = false;
  @override
  Widget build(BuildContext context) {
    final loginEmailCubit = BlocProvider.of<ResetPasswordCubit>(context);
    FocusNode _emailResetFocusNode = useFocusNode();
    useEffect(() {
      return () {
        _resetEmailController.dispose();
        _emailResetFocusNode.unfocus();
      };
    }, []);

    _resetEmailController.addListener(() {
      loginEmailCubit.resetChangeEmail(_resetEmailController.text);
    });

    return GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: PreferredSize(
                preferredSize: const Size.fromHeight(70),
                child: AppBarWidget(
                  title: FORGOT_PASSWORD_APPBAR_TITLE,
                  trailingWidget: Container(),
                  leadingIconName: BACK_ARROW,
                  leadingBtnAction: () {
                    kIsWeb ? Beamer.of(context).beamBack() : appRouter.popForced();
                  },
                )),
            body: BlocBuilder<ResetPasswordCubit, ResetPasswordState>(
                builder: (context, state) {
              return LayoutBuilder(builder: (context, constraints) {
                return Stack(children: [
                  Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16)),
                    ),
                    child: Align(
                      child: Container(
                          width: constraints.maxWidth < 720
                              ? double.infinity
                              : 800,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 30, vertical: 50),
                          child: Column(
                            children: [
                              const Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(FORGOT_PASSWORD_TITLE,
                                      style: TextStyle(
                                        fontSize: 25,
                                      ))),
                              const SizedBox(
                                height: 10,
                              ),
                              const Text(FORGOT_PASSWORD_INTRO_TEXT),
                              const SizedBox(
                                height: 20,
                              ),
                              _textfieldForEmail(context, _emailResetFocusNode,
                                  loginEmailCubit),
                              const SizedBox(height: 20),
                              Spacer(),
                              _submitButton(context, state, loginEmailCubit)
                            ],
                          )),
                    ),
                  ),
                  state is ResetPasswordLoading
                      ? Positioned.fill(child: LoadingIndicatorClass())
                      : Container(),
                ]);
              });
            })));
  }

  Widget _textfieldForEmail(BuildContext context,
      FocusNode _emailResetFocusNode, ResetPasswordCubit loginCubit) {
    return StreamBuilder<String>(
      stream: loginCubit.resetEmail,
      builder: (context, snapshot) {
        String? errorMessage = snapshot.error != null &&
                snapshot.error != '' &&
                _resetEmailController.text.isNotEmpty
            ? snapshot.error.toString()
            : null;

        if (errorMessage != null && errorMessage.isNotEmpty) {
          _isValidSubmission = false;
        } else {
          if (_resetEmailController.text.isNotEmpty) {
            _isValidSubmission = true;
          } else {
            _isValidSubmission = false;
          }
        }

        return Container(
            margin: const EdgeInsets.only(top: 20, bottom: 20),
            child: TextFieldWidget(
                obscureText: false,
                focusNode: _emailResetFocusNode,
                controller: _resetEmailController,
                hint: EMAIL,
                textInputType: TextInputType.emailAddress,
                errorMsg: _emailResetFocusNode.hasFocus ? null : errorMessage,
                onTap: () {},
                onChange: (val) => {loginCubit.validateResetEmailInput},
                onSubmitted: (val) => {loginCubit.validateResetEmailInput}));
      },
    );
  }

  Widget _submitButton(BuildContext context, ResetPasswordState state,
      ResetPasswordCubit _loginCubit) {
    return StreamBuilder<String>(
        stream: _loginCubit.resetEmail,
        builder: (context, snapshot) {
          String? errorMessage = snapshot.error != null &&
                  snapshot.error != '' &&
                  _resetEmailController.text.isNotEmpty
              ? snapshot.error.toString()
              : null;

          if (errorMessage != null && errorMessage.isNotEmpty) {
            _isValidSubmission = false;
          } else {
            if (_resetEmailController.text.isNotEmpty) {
              _isValidSubmission = true;
            } else {
              _isValidSubmission = false;
            }
          }
          return Align(
            alignment: Alignment.bottomCenter,
            child: GestureDetector(
              onDoubleTap: () {},
              child: ButtonWidget(
                  child: Text(SUBMIT, style: LMSFonts.buttonStyle(16)),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(buttonRadius),
                  ),
                  onPressed: _isValidSubmission
                      ? () async {
                          _loginCubit.restPasswordLoading(true);
                          if (_resetEmailController.text.isNotEmpty) {
                            await _loginCubit.resetPassword(
                              _resetEmailController.text,
                            );
                          }

                          state = _loginCubit.state;
                          if (state is ResetPasswordSuccess) {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return MultiActionDialogue(
                                    alertType: AlertType.success,
                                    title: SUCCESS,
                                    content: FORGOT_PASSWORD_ALERT_MSG,
                                    cancelBtnText: CLOSE,
                                    confirmBtnText: '',
                                    onCancelTap: () {
                                      kIsWeb
                                          ? Beamer.of(context)
                                              .beamToReplacementNamed(
                                                  '/login-view')
                                          : appRouter.popUntil((route) =>
                                              route.settings.name ==
                                              LoginEmailViewRoute.name);
                                    },
                                    onContinueTap: () {});
                              },
                            );
                          } else {
                            if (state is ResetPasswordError) {
                              _showPGExceptionPopup(context, state.error);
                            }
                          }
                        }
                      : null),
            ),
          );
        });
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
            title: SORRY,
            content: msg,
            cancelBtnText: OK,
            confirmBtnText: '',
            alertType: AlertType.error,
            onContinueTap: () =>
                kIsWeb ? Navigator.of(context).pop() : appRouter.popForced(),
            onCancelTap: () {
              kIsWeb
                  ? Beamer.of(context).beamToReplacementNamed('/login-view')
                  : appRouter.popUntil((route) =>
                      route.settings.name == LoginEmailViewRoute.name);
            });
      },
    );
  }
}
