      import 'dart:io';
import 'package:SmartLearn/src/presentation/cubits/app_config/app_config_cubit.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../src/domain/repositories/course_dashboard_repository.dart';
import '../../../src/presentation/cubits/course_dashboard/course_dashboard_cubit.dart';

import '/src/presentation/views/exam_review_view.dart';

import '/src/presentation/cubits/subscription/subscription_cubit.dart';
import '../../../src/config/router/beamer_router.dart';
import '../../../src/domain/services/hive_db_service.dart';
import '../../../src/utils/resources/shared_preference_utils.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';

import '/src/domain/services/network_services.dart';
import 'package:provider/provider.dart';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import '/src/config/app_config/sl_config.dart';
import '/src/presentation/cubits/reset_password/reset_password_cubit.dart';

import '/src/domain/repositories/exam_repository.dart';
import '/src/presentation/cubits/exam_clock/exam_clock_cubit.dart';

import '/src/domain/repositories/section_repository.dart';
import '/src/presentation/cubits/connectivity/connectivity_cubit.dart';
import '/src/presentation/cubits/course_feedback/course_feedback_cubit.dart';
import '/src/presentation/cubits/exam/exam_cubit.dart';
import '/src/presentation/cubits/rank/rank_list_cubit.dart';
import '/src/presentation/cubits/section_details/section_details_cubit.dart';
import '/src/presentation/cubits/settings/settings_cubit.dart';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/services.dart';

import '../../../src/domain/repositories/course_respository.dart';
import '../../../src/domain/repositories/login_email_repository.dart';
import '../../../src/domain/repositories/register_repository.dart';
import '../../../src/presentation/cubits/course_list/course_list_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'firebase_options.dart';
import 'src/config/router/app_router.dart';
import 'src/config/themes/app_dynamic_theme.dart';
import 'src/config/themes/app_theme.dart';
import 'src/domain/repositories/app_config_repository.dart';
import 'src/domain/repositories/course_details_repository.dart';
import 'src/domain/repositories/organization_repository.dart';
import 'src/domain/repositories/rank_list_repository.dart';
import 'src/domain/repositories/service_repository.dart';
import 'src/domain/repositories/subscription_reporsitory.dart';
import 'src/domain/repositories/topic_repository.dart';

import 'src/domain/services/db_helper.dart';
import 'src/domain/services/localizations/sl_strings_delegate.dart';
import 'src/domain/services/provider/language_provider.dart';
import 'src/presentation/cubits/course_details/course_details_cubit.dart';
import 'src/presentation/cubits/organization/organization_cubit.dart';
import 'src/presentation/cubits/service_cubit/service_cubit.dart';
import 'src/presentation/cubits/splash_screen/splash_screen_cubit.dart';
import 'src/presentation/cubits/topic_list/topic_list_cubit.dart';
import 'src/domain/repositories/user_repository.dart';
import 'src/presentation/cubits/profile/profile_cubit.dart';
import 'src/presentation/cubits/register/register_cubit.dart';
import 'src/presentation/cubits/login_email/login_cubit.dart';
import 'src/locator.dart';
import 'src/presentation/cubits/validation/validation_cubit.dart';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:universal_html/html.dart' as html;
import 'package:provider/provider.dart' as provider;

final scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();
final beamerGlobalKey = GlobalKey<NavigatorState>();

Future<void> main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  if (kIsWeb) {
    html.window.onBeforeUnload.listen((event) async {
      event.preventDefault();
      (event as html.BeforeUnloadEvent).returnValue = '';
    });
  }
  // if (kIsWeb) {
  //   usePathUrlStrategy();
  // }
  FlutterError.onError = (details) {
    FlutterError.presentError(details);
    if (kReleaseMode) exit(1);
  };

  try {
    await dotenv.load(); // Load environment variables
  } on Exception catch (e) {
    throw Exception('Error loading .env file: $e');
  }

  await NetworkServices().initializeSupabase();
  // TO DO: enable for production
  // disable screenshot
  // if (Platform.isAndroid) {
  //   await FlutterWindowManager.addFlags(FlutterWindowManager.FLAG_SECURE);
  // }

  await initializeDependencies();

  // TO DO: Implement Later
  DbHelper _dbHelper = DbHelper.instance;
  if (!kIsWeb) {
    await _dbHelper.initDatabase();
  } else {
    HiveDbHelper _hiveHelper = HiveDbHelper();
    await _hiveHelper.hiveInit();
    await _hiveHelper.registerHiveAdapters();
  }
  await SharedPreferencesUtils.instance.initPreference();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  //Firebase Initialization
  await Firebase.initializeApp(
    name: 'competitor-lms',
    options: DefaultFirebaseOptions.currentPlatform,
  );

    var status = await Permission.notification.request();
  if (status.isGranted) {
    print('Notification permission granted');
  } else {
    print('Notification permission denied');
  }

  if (!kIsWeb) {
    // Log Flutter errors to FB
    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };

    // Log other errors to FB
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  }

  // Sample log for signup
  await FirebaseAnalytics.instance.logSignUp(
    signUpMethod: 'email',
  );

  // Sample log for login
  await FirebaseAnalytics.instance.logLogin();

  FlutterNativeSplash.remove();
  runApp(
    ChangeNotifierProvider(
      create: (_) => AppDynamicTheme(
      ),
      child: MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  final BeamerRouter beamerRouter = BeamerRouter();

  MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => LanguageProvider(),
        ),
        BlocProvider(
          create: (context) => TopicListCubit(
            locator<TopicRepository>(),
            locator<UserRepository>(),
          ),
        ),
        BlocProvider<LoginEmailCubit>(
          create: (context) => LoginEmailCubit(
            locator<LoginEmailRepository>(),
            locator<OrganizationRepository>(),
          ),
        ),
        BlocProvider<RegisterCubit>(
          create: (context) => RegisterCubit(
            locator<RegisterRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => OrganizationCubit(
            locator<OrganizationRepository>(),
            //  locator<UserRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => CourseListCubit(
            locator<CourseRepository>(),
            locator<CourseDetailsRepository>(),
            locator<HiveDbHelper>(),
            locator<UserRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => UserCubit(
            locator<UserRepository>(),
          )..getVersionNumber(),
        ),
        BlocProvider<ValidationCubit>(
          create: (context) => ValidationCubit(),
        ),
        BlocProvider(
          create: (context) => SplashScreenCubit(locator<CourseRepository>(),
              locator<UserRepository>(), locator<OrganizationRepository>())
            ..initUserInfo(),
        ),
        BlocProvider<ExamCubit>(
          create: (context) => ExamCubit(
            locator<ExamRepository>(),
            locator<UserRepository>(),
          ),
        ),
        BlocProvider<RankListCubit>(
          create: (context) => RankListCubit(
            locator<RankListRepository>(),
          ),
        ),
        BlocProvider<SettingsCubit>(
          create: (context) => SettingsCubit(),
        ),
        BlocProvider<ConnectivityCubit>(
          create: (context) => ConnectivityCubit(
            Connectivity(),
            locator<ExamRepository>(),
            locator<UserRepository>(),
          ),
        ),
        BlocProvider<CourseFeedbackCubit>(
          create: (context) => CourseFeedbackCubit(),
        ),
        BlocProvider(
          create: (context) => SectionDetailsCubit(
            locator<SectionRepository>(),
            locator<CourseRepository>(),
            locator<UserRepository>(),
          ),
        ),
        BlocProvider<ExamClockCubit>(
          create: (context) => ExamClockCubit()..updateCurrentTime(),
        ),
        BlocProvider<ResetPasswordCubit>(
          create: (context) => ResetPasswordCubit(
            locator<LoginEmailRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => ServiceCubit(
            locator<ServiceRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => SubscriptionCubit(
            locator<SubscriptionRepository>(),
            locator<UserRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => CourseDashboardCubit(
            locator<CourseDashboardRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => CourseDetailsCubit(
            locator<CourseDetailsRepository>(),
          ),
        ),
        BlocProvider(
          create: (context) => AppConfigCubit(
            locator<AppConfigRepository>(),
          ),
        )
      ],
      child: kIsWeb
          ? BeamerProvider(
              key: beamerGlobalKey,
              routerDelegate: beamerRouter.routerDelegate,
              child: MaterialApp.router(
                scrollBehavior:
                    const MaterialScrollBehavior().copyWith(scrollbars: false),
                routeInformationParser: BeamerParser(),
                routerDelegate: beamerRouter.routerDelegate,
                builder: (context, child) {
                  final data = MediaQuery.of(context);
                  return MediaQuery(
                      data: data.copyWith(textScaler: TextScaler.noScaling),
                      child: child ?? const SizedBox());
                },
                debugShowCheckedModeBanner: false,
                theme: AppTheme.light,
                backButtonDispatcher: BeamerBackButtonDispatcher(
                    delegate: beamerRouter.routerDelegate),
              ),
            )
          : MaterialApp.router(
              builder: (context, child) {
                final data = MediaQuery.of(context);
                return ScrollConfiguration(
                  behavior: CustomScrollBehaviour(),
                  child: MediaQuery(
                      data: data.copyWith(textScaler: TextScaler.noScaling),
                      child: child ?? const SizedBox()),
                );
              },
              scaffoldMessengerKey: scaffoldMessengerKey,
              debugShowCheckedModeBanner: false,
              routerDelegate: appRouter.delegate(),
              routeInformationParser: appRouter.defaultRouteParser(),
              title: SLConfig.APP_TITLE,
              theme: AppTheme.light,
              // locale: SLStrings.currentLocale, // Locale(state.selectedLang),
              supportedLocales: const [
                Locale(SLConfig.DEFAULT_LOCALE_EN, ''),
                Locale(SLConfig.LOCALE_DE, ''), // german
              ],
              localizationsDelegates: const [
                SLStringsDelegate(),
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              localeResolutionCallback: (locale, supportedLocales) {
                return null;
              },
            ),
    );
  }
}
