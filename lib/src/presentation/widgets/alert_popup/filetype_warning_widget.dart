import '../../../config/enums/alert_types.dart';
import '../../../domain/services/localizations/sl_strings.dart';
import '../../../domain/services/localizations/string_keys.dart';
import '/src/utils/constants/strings.dart';

import '/src/config/router/app_router.dart';
import 'multi_action_dialogue.dart';
import 'package:flutter/material.dart';

showFileWarning(BuildContext context, String content) {
  showDialog(
      context: context,
      builder: (context1) {
        return MultiActionDialogue(
          title: WARNING,
          alertType: AlertType.warning,
          content: content,
          cancelBtnText: "",
          confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
          onContinueTap: () {
            appRouter.popForced();
          },
          onCancelTap: () {
            appRouter.popForced();
          },
        );
      });
}
