import 'dart:async';
import 'dart:io';

import '../../config/themes/app_dynamic_theme.dart';
import '/src/presentation/widgets/loading_indicator.dart';
import 'package:flutter/gestures.dart';

import '../../domain/models/purchase_history/purchase_history.dart';
import '../../domain/models/subscription_plan/subscription_plan.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../cubits/subscription/subscription_cubit.dart';
import '/src/presentation/widgets/alert_popup/confirmation_popup.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '../widgets/profile_placeholder_widget.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/utils/constants/helper.dart';

import '../../config/themes/app_theme.dart';
import '/src/config/themes/theme_colors.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../../utils/constants/strings.dart';
import '../cubits/profile/profile_cubit.dart';
import '../cubits/profile/profile_state.dart';
import '../widgets/app_bar.dart';
import 'package:provider/provider.dart' as provider;

enum PlanHistoryType { active, purchased, pending }

@RoutePage()
// ignore: must_be_immutable
class ProfileReviewScreen extends HookWidget with WidgetsBindingObserver {
  ProfileReviewScreen({Key? key}) : super(key: key);

  final double _horizontalMargin = 16.0;

  BuildContext? currentContext;

  final ValueNotifier<List<PurchasedPlanListsByUser>> _activePlans =
      ValueNotifier([]);
  final ValueNotifier<List<PurchasedPlanListsByUser>> _purchasedPlans =
      ValueNotifier([]);
  final ValueNotifier<List<PendingPlanListsByUser>> _plansWaitingApproval =
      ValueNotifier([]);

  final ValueNotifier<bool> _expandPurchasedList = ValueNotifier(false);
  final ValueNotifier<bool> _expandActiveList = ValueNotifier(false);
  final ValueNotifier<bool> _expandPendingList = ValueNotifier(false);

  final ValueNotifier<bool> _isDataLoading = ValueNotifier(false);

  Future<bool> _fetchProfileEditAccess() async {
    // decides whether to show profile details
    String screen = PrivilegeAccessConsts.SCREEN_USER;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_UPDATE_USER_PROFILE;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    didChangeAppLocale(locales, currentContext);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final _userCubit = BlocProvider.of<UserCubit>(context);
    final _subscriptionCubit = BlocProvider.of<SubscriptionCubit>(context);
    final Size screenSize = MediaQuery.of(context).size;
    final double maxWidth = screenSize.width;

    ///1.5;

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);

      Future<void>.microtask(() async {
        await _getUserPlansHistory(_subscriptionCubit);
      });

      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, const []);

    return Scaffold(
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
            title: SLStrings.getTranslatedString(KEY_PROFILE_APPBAR_TITLE),
            leadingIconName: BACK_ARROW,
            trailingWidget: IconButton(
              icon: const Icon(Icons.edit, color: LmsColors.white, size: 20),
              onPressed: () async {
                bool _hasEditAccess = await _fetchProfileEditAccess();
                if (_hasEditAccess) {
                  kIsWeb
                      ? Beamer.of(context).beamToNamed('/profile-view',
                          data: {'isFromSideMenu': true})
                      : appRouter.push(ProfileViewRoute(isFromSideMenu: true));
                } else {
                  showNoAccessPopup(context, '');
                }
              },
            ),
            leadingBtnAction: () {
              kIsWeb ? Beamer.of(context).beamBack() : appRouter.popForced();
            },
          )),
      body: BlocBuilder<UserCubit, UserState>(
          bloc: _userCubit,
          builder: (context, state) {
            final avatarUrl = state.profile?.avatarUrl ?? '';
            final firstName = state.profile?.firstName ?? '';
            final lastName = state.profile?.lastName ?? '';
            final email = state.profile?.email ?? '';
            final phone = state.profile?.phoneNum ?? '';
            return LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
              return ValueListenableBuilder(
                  valueListenable: _isDataLoading,
                  builder: (context, _, __) {
                    return Stack(
                      children: [
                        Container(
                          height: constraints.maxHeight,
                          decoration: const BoxDecoration(
                            color: AppTheme.whiteColor,
                            borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(16),
                                topRight: Radius.circular(16)),
                          ),
                          child: ScrollConfiguration(
                            behavior: ScrollConfiguration.of(context).copyWith(
                              dragDevices: {
                                PointerDeviceKind.touch,
                                PointerDeviceKind.mouse,
                              },
                            ),
                            child: SingleChildScrollView(
                              child: Align(
                                child: Container(
                                  margin: EdgeInsets.only(
                                      left: _horizontalMargin,
                                      right: _horizontalMargin,
                                      top: 25),
                                  width: constraints.maxWidth < 720
                                      ? constraints.maxWidth
                                      : 800,
                                  child: Column(
                                    children: [
                                      _profileInfo(maxWidth, avatarUrl,
                                          firstName, lastName, email, phone),
                                      const SizedBox(height: 25.0),
                                      _purchasePlanHistory(_subscriptionCubit),
                                      _purchaseHistoryListView(),
                                      // _progressDetails(maxWidth),
                                      // const SizedBox(height: 25.0),
                                      // _subjects(maxHeight, maxWidth),
                                      // const SizedBox(height: 25.0),
                                      // _deleteAccountButton(context, state, _userCubit),
                                      const SizedBox(height: 25.0),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        _isDataLoading.value
                            ? LoadingIndicatorClass()
                            : Container(),
                      ],
                    );
                  });
            });
          }),
    );
  }

  Widget _profileInfo(
      maxWidth, avatarUrl, firstName, lastName, email, String phone) {
    return SizedBox(
      width: maxWidth,
      child: Column(
        children: [
          _profileImage(maxWidth, avatarUrl),
          const SizedBox(height: 16),
          _profileName(firstName, lastName),
          const SizedBox(height: 8),
          _emailDetails(email),
          const SizedBox(height: 8),
          phone.isNotEmpty ? _emailDetails('+91 ' + phone) : Container(),
        ],
      ),
    );
  }

  Widget _profileImage(double maxWidth, String avatarUrl) {
    return ProfileImageWidget(
      avatarUrl: avatarUrl,
      showCamera: false,
      radius: 150,
      onEditTapped: () {},
    );
  }

  Widget _profileName(String firstname, String lastName) {
    firstname = convertToCamelCase(firstname);
    lastName = convertToCamelCase(lastName);
    return AutoSizeText(
      (firstname) + (lastName),
      maxLines: 2,
      style: LMSFonts.mediumFont(20, AppTheme.headerTextColor, 1.0),
      textAlign: TextAlign.center,
    );
  }

  Widget _emailDetails(String email) {
    return AutoSizeText(
      email,
      style: LMSFonts.mediumFont(14, AppTheme.headerTextColor, 1.0),
      textAlign: TextAlign.center,
    );
  }

/*
// TODO: remove later
  Widget _progressDetails(double maxWidth) {
    return Container(
      padding: const EdgeInsets.all(13),
      decoration: BoxDecoration(
        color: LmsColors.white,
        borderRadius: BorderRadius.circular(maxWidth * 0.02),
        boxShadow: [
          BoxShadow(
            color: LmsColors.greyForButton.withOpacity(0.5),
            blurRadius: maxWidth * 0.02,
            offset: const Offset(0, 1),
            spreadRadius: maxWidth * 0.01,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: FittedBox(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    LEARNING_TIME,
                    style: LMSFonts.mediumFont(18, AppTheme.pitchBlack, 1.0),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 15),
                  Text(
                    HOURS,
                    style: LMSFonts.boldFont(25, AppTheme.primaryOrange),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 15),
                  Text(
                    KEEP_GOING,
                    style: LMSFonts.mediumFont(15, AppTheme.pitchBlack, 1.0),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 15),
                  ButtonWidget(
                      elevation: 1.0,
                      minimumSize: const Size(20, 35),
                      shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(13))),
                      color: AppTheme.ternaryBlue,
                      child: Text(
                        VIEW_SCHEDULE,
                        style: LMSFonts.buttonStyle(16),
                        textAlign: TextAlign.center,
                      ),
                      onPressed: () => {})
                ],
              ),
            ),
          ),
          const SizedBox(width: 10),
          CircularPercentIndicator(
            radius: maxWidth * 0.19,
            lineWidth: maxWidth * 0.03,
            animation: true,
            percent: 0.7,
            center: Text(
              '70.0%',
              style: LMSFonts.mediumFont(20, AppTheme.pitchBlack, 1.0),
            ),
            circularStrokeCap: CircularStrokeCap.round,
            backgroundColor: AppTheme.ternaryBlue,
            progressColor: AppTheme.primaryBlue,
          ),
        ],
      ),
    );
  }

  Widget _subjects(double maxHeight, double maxWidth) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                COURSES,
                style: LMSFonts.boldFont(22, AppTheme.pitchBlack),
              ),
              Padding(
                padding: const EdgeInsets.only(right: 19),
                child: Text(
                  VIEW_ALL,
                  style: LMSFonts.mediumFont(16, AppTheme.pitchBlack, 1.0),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.all(13),
          decoration: BoxDecoration(
            color: LmsColors.white,
            borderRadius: BorderRadius.circular(maxWidth * 0.02),
            boxShadow: [
              BoxShadow(
                color: LmsColors.greyForButton.withOpacity(0.5),
                blurRadius: maxWidth * 0.02,
                offset: const Offset(0, 1),
                spreadRadius: maxWidth * 0.01,
              ),
            ],
          ),
          child: Container(
            decoration: BoxDecoration(boxShadow: [
              BoxShadow(
                  color: AppTheme.pitchBlack.withOpacity(0.2),
                  blurRadius: 23.0,
                  offset: const Offset(0, 1),
                  spreadRadius: 1),
            ]),
            child: GridView.count(
              padding: EdgeInsets.zero,
              childAspectRatio: (maxWidth / 0.35) / maxHeight / 0.5,
              crossAxisCount: 1,
              mainAxisSpacing: 16,
              shrinkWrap: true,
              primary: false,
              children: [
                _courseDetails(maxHeight),
                _courseDetails(maxHeight),
                _courseDetails(maxHeight),
              ],
            ),
          ),
        ),
        const SizedBox(height: 15)
      ],
    );
  }

  Widget _courseDetails(double maxHeight) {
    return Container(
        decoration: BoxDecoration(
          color: AppTheme.secondaryBlue,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Container(
                height: maxHeight / 5.5,
                margin: const EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: AppTheme.primaryOrange,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Center(
                  child: AutoSizeText(
                    COURSE_DATA,
                    textAlign: TextAlign.center,
                    style: LMSFonts.mediumFont(20, AppTheme.pitchBlack, 1.0),
                  ),
                ),
              ),
            ),
            //const SizedBox(height: 8),
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SizedBox(height: 1),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  child: AutoSizeText(
                    LEARNING_STATUS,
                    style: LMSFonts.mediumFont(15, AppTheme.pitchBlack, 1.0),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(
                  width: 160,
                  height: 12,
                  child: ClipRRect(
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      child: LinearProgressIndicator(
                        value: 0.45,
                        minHeight: 8.0,
                        backgroundColor: AppTheme.ternaryBlue,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppTheme.primaryBlue),
                      )),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      LESSON,
                      style: LMSFonts.mediumFont(13, AppTheme.pitchBlack, 1.0),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(width: 90),
                    Text(
                      '30m',
                      style: LMSFonts.mediumFont(13, AppTheme.pitchBlack, 1.0),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const SizedBox(height: 2),
              ],
            ),
          ],
        ));
  }

  Widget _deleteAccountButton(
      BuildContext context, UserState state, UserCubit _userCubit) {
    return Align(
      alignment: Alignment.centerRight,
      child: ButtonWidget(
        child: Text(DELETE_ACCOUNT, style: LMSFonts.buttonStyle(16)),
        // minimumSize: Size(screenWidth * 0.2, 40),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(buttonRadius),
        ),
        onPressed: () => _showAccountDeleteConfirmationPopup(context),
      ),
    );
  }

  _showAccountDeleteConfirmationPopup(BuildContext parentContext) {
    showDialog(
      context: parentContext,
      builder: (context) {
        return MultiActionDialogue(
          title: CONFIRM,
          alertType: AlertType.confirm,
          content: ACCOUNT_DELETE_CONFIRM,
          cancelBtnText: NO,
          confirmBtnText: YES,
          onCancelTap: () {
            appRouter.popForced();
          },
          onContinueTap: () {
            appRouter.popForced();
            // TO DO: need to implement api
          },
        );
      },
    );
  }
  */

  /// subscription info

  Widget _purchasePlanHistory(SubscriptionCubit _subscriptionCubit) {
    return Visibility(
      visible: _purchasedPlans.value.isNotEmpty ||
          _plansWaitingApproval.value.isNotEmpty ||
          _activePlans.value.isNotEmpty,
      child: Container(
        alignment: Alignment.centerLeft,
        // margin: const EdgeInsets.only(bottom: 8),
        // color: Colors.amber,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              SLStrings.getTranslatedString(KEY_PURCHASE_HISTORY),
              style: LMSFonts.semiBoldFont(
                  16, AppTheme.courseVideoPrimaryTextColor),
            ),
            IconButton(
              padding: EdgeInsets.zero,
              icon: const Icon(Icons.refresh_outlined,
                  color: AppTheme.courseVideoPrimaryTextColor),
              onPressed: () async =>
                  await _getUserPlansHistory(_subscriptionCubit),
            ),
          ],
        ),
      ),
    );
  }

  Widget _purchaseHistoryListView() {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: AppTheme.planHistoryHead),
      child: Column(
        children: [
          //purchased/active
          _purchasedPlanHead(PlanHistoryType.active),
          _plansListView(
              PlanHistoryType.active,
              _plansWaitingApproval.value.isEmpty &&
                  _purchasedPlans.value.isEmpty),
          _divider(
              AppTheme.planHistoryDivider,
              _plansWaitingApproval.value.isNotEmpty &&
                  _purchasedPlans.value.isNotEmpty),
          //pending
          _purchasedPlanHead(PlanHistoryType.pending),
          _plansListView(
              PlanHistoryType.pending, _purchasedPlans.value.isEmpty),
          _divider(
              AppTheme.planHistoryDivider,
              _plansWaitingApproval.value.isNotEmpty &&
                  _purchasedPlans.value.isNotEmpty),
          //purchased/expired
          _purchasedPlanHead(PlanHistoryType.purchased),
          _plansListView(PlanHistoryType.purchased, true),
        ],
      ),
    );
  }

  Widget _divider(Color dividerColor, bool visibility) {
    return Visibility(
      visible: visibility,
      child: Divider(
        color: dividerColor,
        thickness: 0.5,
        height: 1,
      ),
    );
  }

  Widget _purchasedPlanHead(PlanHistoryType type) {
    bool isPending = false;
    bool isActive = false;
    bool isExpired = false;
    bool visibility = false;
    ValueListenable<bool> valueListenable = ValueNotifier(false);
    String title = SLStrings.getTranslatedString(KEY_PURCHASED_PLANS);
    String asset = '$ASSETS_PATH/purchased_plans.png';

    switch (type) {
      case PlanHistoryType.active:
        visibility = _activePlans.value.isNotEmpty;
        isActive = true;
        valueListenable = _expandActiveList;
        title = SLStrings.getTranslatedString(KEY_ACTIVE_PLANS);
        asset = '$ASSETS_PATH/purchased_plans.png';
        break;
      case PlanHistoryType.purchased:
        visibility = _purchasedPlans.value.isNotEmpty;
        isExpired = true;
        valueListenable = _expandPurchasedList;
        title = SLStrings.getTranslatedString(KEY_PURCHASED_PLANS);
        asset = '$ASSETS_PATH/purchased_plans.png';
        break;
      case PlanHistoryType.pending:
        visibility = _plansWaitingApproval.value.isNotEmpty;
        isPending = true;
        valueListenable = _expandPendingList;
        title = SLStrings.getTranslatedString(KEY_PLANS_WAITING_FOR_APPROVAL);
        asset = '$ASSETS_PATH/pending_plans.png';
        break;
      default:
    }

    return ValueListenableBuilder(
        valueListenable: valueListenable,
        builder: (context, listen, _) {
          bool isExpanded = valueListenable.value;
          return Visibility(
            visible: visibility,
            child: GestureDetector(
              onTap: () {
                if (isExpired) {
                  // approved/expired
                  _expandPurchasedList.value = !_expandPurchasedList.value;
                } else if (isActive) {
                  // approved/active
                  _expandActiveList.value = !_expandActiveList.value;
                } else {
                  // pending
                  _expandPendingList.value = !_expandPendingList.value;
                }
              },
              child: Container(
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.all(11),
                color: Colors.transparent,
                child: Row(
                  children: [
                    const SizedBox(width: 5),
                    Image.asset(
                      asset,
                      height: !isPending ? 28 : 26,
                      width: !isPending ? 28 : 26,
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        title,
                        style:
                            LMSFonts.regularFont(15, AppTheme.whiteTextColor),
                      ),
                    ),
                    Icon(isExpanded ? Icons.arrow_drop_down : Icons.arrow_right,
                        color: AppTheme.whiteColor),
                  ],
                ),
              ),
            ),
          );
        });
  }

  Widget _plansListView(PlanHistoryType type, bool addBorderRadius) {
    bool _isPendingPlansList = false;
    bool _isActivePlans = false;
    List<PurchasedPlanListsByUser> _purchasedPlansUI = [];
    List<PurchasedPlanListsByUser> _activePlansUI = [];
    List<PendingPlanListsByUser> _pendingPlans = [];

    switch (type) {
      case PlanHistoryType.active:
        _activePlansUI = _activePlans.value;
        _isActivePlans = true;
        break;
      case PlanHistoryType.purchased:
        _purchasedPlansUI = _purchasedPlans.value;
        break;
      case PlanHistoryType.pending:
        _pendingPlans = _plansWaitingApproval.value;
        _isPendingPlansList = true;
        break;
      default:
    }

    return ValueListenableBuilder(
        valueListenable: _isPendingPlansList
            ? _expandPendingList
            : _isActivePlans
                ? _expandActiveList
                : _expandPurchasedList,
        builder: (context, listen, _) {
          bool visibility = _isPendingPlansList
              ? _plansWaitingApproval.value.isNotEmpty &&
                  _expandPendingList.value
              : _isActivePlans
                  ? _activePlans.value.isNotEmpty && _expandActiveList.value
                  : _purchasedPlans.value.isNotEmpty &&
                      _expandPurchasedList.value;
          return Visibility(
            visible: visibility,
            maintainAnimation: true,
            maintainState: true,
            child: ClipRRect(
              borderRadius: addBorderRadius
                  ? const BorderRadius.only(
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10))
                  : BorderRadius.zero,
              child: Container(
                color: Colors.yellow,
                child: Stack(
                  children: [
                    _listItem(
                        _isActivePlans ? _activePlansUI : _purchasedPlansUI,
                        _pendingPlans,
                        _isPendingPlansList),
                    const Positioned(
                      top: -10,
                      left: 20,
                      child: Icon(
                        Icons.arrow_drop_down,
                        color: AppTheme.planHistoryHead,
                      ),
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  Widget _listItem(List<PurchasedPlanListsByUser> _purchasedPlans,
      List<PendingPlanListsByUser> _pendingPlans, bool _isPendingPlans) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: _isPendingPlans
          ? _pendingPlans.asMap().entries.map((entry) {
              bool isInr = entry.value.currency != null &&
                  (entry.value.currency == Currency.CURRENCY_INR ||
                      entry.value.currency == Currency.INR);

              DateTime startDate =
                  entry.value.subscriptionValidFrom ?? DateTime.now();

              DateTime endDate =
                  entry.value.subscriptionValidTo ?? DateTime.now();

              String planStartDate = AppDateFormatter()
                  .formatForExamAvailabilityPopup((startDate));
              String planEndDate =
                  AppDateFormatter().formatForExamAvailabilityPopup((endDate));

              return _pendingListTile(entry, isInr, planStartDate, planEndDate,
                  _pendingPlans.length);
            }).toList()
          : _purchasedPlans.asMap().entries.map((entry) {
              bool isInr = entry.value.currency != null &&
                  (entry.value.currency == Currency.CURRENCY_INR ||
                      entry.value.currency == Currency.INR);

              DateTime startDate = entry.value.userSubscriptionStartDate != null
                  ? entry.value.userSubscriptionStartDate ?? DateTime.now()
                  : entry.value.subscriptionValidFrom ?? DateTime.now();

              DateTime endDate = entry.value.userSubscriptionEndDate != null
                  ? entry.value.userSubscriptionEndDate ?? DateTime.now()
                  : entry.value.subscriptionValidTo ?? DateTime.now();

              String planStartDate = AppDateFormatter()
                  .formatForExamAvailabilityPopup((startDate));
              String planEndDate =
                  AppDateFormatter().formatForExamAvailabilityPopup((endDate));

              return _purchasedListTile(entry, isInr, planStartDate,
                  planEndDate, _purchasedPlans.length);
            }).toList(),
    );
  }

  Widget _purchasedListTile(
      MapEntry<int, PurchasedPlanListsByUser> purchasedEntry,
      bool isInr,
      String planStartDate,
      String planEndDate,
      int length) {
    String name = (purchasedEntry.value.planName ?? '');

    String currency = isInr
        ? '₹ ${(purchasedEntry.value.price ?? '').toString()}'
        : '\$ ${(purchasedEntry.value.price ?? '').toString()}';
    return _listTile(name, currency, false, planStartDate, planEndDate, length,
        (purchasedEntry.key != length - 1));
  }

  Widget _pendingListTile(MapEntry<int, PendingPlanListsByUser> pendingEntry,
      bool isInr, String planStartDate, String planEndDate, int length) {
    String name = (pendingEntry.value.planName ?? '');

    String currency = isInr
        ? '₹ ${(pendingEntry.value.price ?? '').toString()}'
        : '\$ ${(pendingEntry.value.price ?? '').toString()}';
    return _listTile(name, currency, true, planStartDate, planEndDate, length,
        (pendingEntry.key != length - 1));
  }

  Widget _listTile(String name, String currency, bool isPending,
      String planStartDate, String planEndDate, int length, bool showDivider) {
    return GestureDetector(
      onTap: () => debugPrint('plan selected from profile view!'),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: const BoxDecoration(
          color: AppTheme.planHistoryListTileBg,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    name,
                    style: LMSFonts.semiBoldFont(14, AppTheme.whiteColor),
                  ),
                ),
                const SizedBox(width: 10),
                Text(
                  currency,
                  style:
                      LMSFonts.mediumFont(14, AppTheme.planHistoryPrice, 1.0),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _durationDescription(!isPending, planStartDate, planEndDate),
            const SizedBox(height: 12),
            _divider(AppTheme.whiteColor.withOpacity(0.5), showDivider),
          ],
        ),
      ),
    );
  }

  RichText _durationDescription(
      bool isApproved, String planStartDate, String planEndDate) {
    return RichText(
      text: TextSpan(
        style: LMSFonts.regularFont(13, AppTheme.whiteColor),
        children: <TextSpan>[
          TextSpan(
            text: isApproved
                ? SLStrings.getTranslatedString(KEY_PLAN_VALIDITY)
                : SLStrings.getTranslatedString(KEY_PLAN_AVAILABILITY_FROM),
            style: LMSFonts.mediumFont(13, AppTheme.whiteColor, 1.0),
          ),
          TextSpan(
            text: planStartDate,
          ),
          TextSpan(
            text: SLStrings.getTranslatedString(KEY_PLAN_AVAILABILITY_TO),
          ),
          TextSpan(
            text: planEndDate,
          ),
        ],
      ),
    );
  }

  /// fetch plan history
  ///
  _getUserPlansHistory(SubscriptionCubit _subscriptionCubit) async {
    _isDataLoading.value = true;
    await _subscriptionCubit.getUserPurchaseHistory();
    SubscriptionState state = _subscriptionCubit.state;
    if (state is PurchaseHistoryFetched) {
      _purchasedPlans.value = state.purchasedPlans;
      _activePlans.value = state.activePlans;
      _plansWaitingApproval.value = state.pendingPlans;
    }
    _isDataLoading.value = false;
  }

  /// init all plans
  /// divide into purchase/approvde plans and
  /// pending/waiting for approval plans
  _initPlans(SubscriptionCubit _subscriptionCubit) async {
    // List<SubscriptionPlan> _plans =
    //     List.from(_subscriptionCubit.subscriptionPlans);
    // _plans.removeWhere((element) =>
    //     element.userSubscriptionStatus == PlanStatusEnum.UNSUBSCRIBED ||
    //     element.userSubscriptionStatus == null);
    // _plans.sort((a, b) => (a.name ?? '')
    //     .trim()
    //     .toLowerCase()
    //     .compareTo((b.name ?? '').trim().toLowerCase()));

    // _purchasedPlans.value = List.from(_plans);
    // _purchasedPlans.value.retainWhere(
    //     (element) => element.userSubscriptionStatus == PlanStatusEnum.APPROVED);
    // _plansWaitingApproval.value = List.from(_plans);

    // _plansWaitingApproval.value.retainWhere(
    //     (element) => element.userSubscriptionStatus == PlanStatusEnum.PENDING);

    // // timer.value?.cancel();
    // // timer.value = Timer.periodic(
    // //   const Duration(seconds: 5),
    // //   (Timer t) {
    // //     for (var pendingPlan in _plansWaitingApproval.value) {
    // //       _updatePlansList(_subscriptionCubit, pendingPlan);
    // //     }
    // //   },
    // // );
    // for (var pendingPlan in _plansWaitingApproval.value) {
    //   await _updatePlansList(_subscriptionCubit, pendingPlan);
    // }
  }

  /// update plan list in subscription cubit
  /// when the plan is approved
  // Future _updatePlansList(
  //     SubscriptionCubit _subscriptionCubit, SubscriptionPlan plan) async {
  //   bool isApproved =
  //       await _checkForApprovalStatusForPlan(_subscriptionCubit, plan);
  //   String? planId = plan.id;
  //   if (isApproved && planId != null) {
  //     // _statusStreamController.sink
  //     //     .add(_subscriptionCubit.updatePlansList(planId, isApproved));

  //     _subscriptionCubit.updatePlansList(planId, isApproved);
  //     // _initPlans(_subscriptionCubit);
  //   }
  // }

//   /// periodically check whether the plan is approved by admin
//   Future<bool> _checkForApprovalStatusForPlan(
//       SubscriptionCubit _subscriptionCubit, SubscriptionPlan plan) async {
//     String? planId = plan.id;
//     bool isApproved = false;
//     if (planId != null &&
//         plan.userSubscriptionStatus == PlanStatusEnum.PENDING) {
//       await _subscriptionCubit.fetchPlanStatus(planId);
//       SubscriptionState state = _subscriptionCubit.state;
//       if (state is SubscriptionApproved) {
//         if (state.isApproved && state.selectedPlanId == planId) {

//           isApproved = true;
//         }
//       }
//     }
//     return isApproved;
//   }
}

class ProfileImageWidget extends StatelessWidget {
  final String? avatarUrl;
  final bool showCamera;
  final double radius;
  final VoidCallback onEditTapped;

  const ProfileImageWidget({
    super.key,
    required this.avatarUrl,
    required this.showCamera,
    required this.radius,
    required this.onEditTapped,
  });

  @override
  Widget build(BuildContext context) {
    final Size screenSize = MediaQuery.sizeOf(context);
    final double maxWidth = screenSize.width;

    return _profileImageWidget(maxWidth, context);
  }

  Widget _profileImageWidget(double maxWidth, BuildContext context) {
    final double outerCircleRadius = radius;
    final double innerCircleRadius = (outerCircleRadius - 5 / 2);

    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);

    return Stack(
      children: [
        Container(
          width: outerCircleRadius,
          height: outerCircleRadius,
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            color: AppTheme.whiteColor,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppTheme.blackColor.withOpacity(0.25),
                offset: const Offset(0, 2),
                blurRadius: 2,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(2.5),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(outerCircleRadius),
              child: avatarUrl != null && avatarUrl!.isNotEmpty
                  ? (avatarUrl!.startsWith(HTTP) ||
                          avatarUrl!.startsWith(HTTPS))
                      ? CachedNetworkImage(
                          fit: BoxFit.cover,
                          imageUrl: avatarUrl!,
                          imageBuilder: (context, imageProvider) =>
                              CircleAvatar(
                            backgroundImage: imageProvider,
                            radius: innerCircleRadius,
                          ),
                          placeholder: (context, url) =>
                              ProfilePlaceholderWidget(maxWidth: radius),
                          errorWidget: (context, url, error) =>
                              ProfilePlaceholderWidget(maxWidth: radius),
                        )
                      : Image.file(
                          File(avatarUrl!),
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              ProfilePlaceholderWidget(maxWidth: radius),
                        )
                  : ProfilePlaceholderWidget(maxWidth: radius),
            ),
          ),
        ),
        _cameraIcon(),
      ],
    );
  }

  Widget _cameraIcon() {
    const double outerCircleRadius = 45;
    const double innerCircleRadius = (outerCircleRadius - 5 / 2);

    return Visibility(
      visible: showCamera,
      child: Positioned(
        bottom: 0,
        right: 0,
        child: GestureDetector(
          onTap: onEditTapped,
          child: Container(
            height: outerCircleRadius,
            width: outerCircleRadius,
            decoration: const BoxDecoration(
                shape: BoxShape.circle, color: AppTheme.whiteColor),
            child: Padding(
              padding: const EdgeInsets.all(2.5),
              child: CircleAvatar(
                radius: innerCircleRadius,
                backgroundColor: AppTheme.secondaryAppColor,
                child: Image.asset(
                  '$ASSETS_PATH/camera.png',
                  height: 24,
                  width: 24,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
