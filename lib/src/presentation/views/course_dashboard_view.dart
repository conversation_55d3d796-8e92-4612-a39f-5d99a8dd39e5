import 'dart:math';

import 'package:beamer/beamer.dart';
import 'package:carousel_slider/carousel_slider.dart';

import '../../../src/domain/models/sl_user.dart';
import '../../../src/presentation/cubits/course_dashboard/course_dashboard_cubit.dart';
import '../../../src/presentation/views/feedback_rating_view.dart';
import 'package:awesome_circular_chart/awesome_circular_chart.dart';
import 'package:d_chart/d_chart.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/app_config/api_constants.dart';
import '../../config/app_config/preference_config.dart';
import '../../config/themes/app_dynamic_theme.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/models/course_dashboard/courseDetailUserStats.dart';
import '../../domain/models/course_dashboard/course_analytics.dart';
import '../../domain/models/course_dashboard/courses_info.dart';
import '../../domain/models/course_dashboard/course_dashboard_info.dart';
import '../../domain/models/course_details.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../presentation/widgets/app_bar.dart';
import '../../utils/constants/strings.dart';
import 'package:auto_route/annotations.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../../utils/constants/nums.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../../utils/helper/course_helper.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '../widgets/drawer_widget.dart';
import '../widgets/exit_on_double_tap.dart';
import '../widgets/linear_progress_indicator_widget.dart';
import '../widgets/loading_indicator.dart';
import 'package:provider/provider.dart' as provider;

@RoutePage()
class CourseDashboardScreen extends HookWidget with WidgetsBindingObserver {
  final bool isFromLogin;
  CourseDashboardScreen({super.key, required this.isFromLogin});

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ValueNotifier<bool> _isDataLoading = ValueNotifier<bool>(true);
  final CarouselController _controller = CarouselController();
  final _coursesInfo = ValueNotifier<List<CoursesInfo>>([]);

  List<CourseDashboardInfo> _courseDashboardInfo = [];
  List<Map<String, dynamic>> courseInfo = [];
  List<CurrentAffairs> _currentAffairs = [];

  final SLUser _currentUser = SLUser.shared;

  int _currentAffairsLimit = 0;
  static const double _affairsHeight = 150;
  final ValueNotifier<int> _currentCarouselItemIndex = ValueNotifier<int>(0);

  setCourseInfo(CourseDashboardInfo courseDashboardData) {
    courseInfo = [
      {
        "title": 'Total Course',
        "iconpath": "total-course.png",
        "content": courseDashboardData.courseCount.toString(),
      },
      {
        "title": "Completed Course",
        "iconpath": "total-marks.png",
        "content": courseDashboardData.completedCourseCount.toString(),
      },
      {
        "title": "Progress",
        "iconpath": "progress.png",
        "content": courseDashboardData.totalProgress.toStringAsFixed(2),
      },
      {
        "title": "Time Spent",
        "iconpath": "time-spent.png",
        "content": courseDashboardData.totalTimeSpent
      },
      {
        "title": "Achievements",
        "iconpath": "acheivment.png",
        "content": courseDashboardData.totalAchievements.toString(),
      },
    ];
  }

  _fetchCourseInfo(CourseDashboardCubit _courseDashboardCubit) async {
    await _courseDashboardCubit.fetchAllCourseInfo();
    CourseDashboardState state = _courseDashboardCubit.state;
    if (state is CourseDashboardDetailsFetched) {
      _courseDashboardInfo = state.courseDashboardInfo;
    }
    if (_courseDashboardInfo.isNotEmpty) {
      _coursesInfo.value = _courseDashboardInfo.first.coursesInfo;
      setCourseInfo(_courseDashboardInfo.first);
    }
  }

  _handleCourseSelection(CourseDashboardCubit _courseDashboardCubit,
      CoursesInfo courseInfo) async {
    await saveSelectedCourse(courseInfo);
    if (showCourseDetailsDashboard) {
      appRouter
          .push(CourseDetailsDashboardRoute(courseInfo: courseInfo))
          .then((value) async {
        if (value != null && value == true) {
          _isDataLoading.value = true;
          await _fetchCourseInfo(_courseDashboardCubit);
          _isDataLoading.value = false;
        }
      });
    } else {
      appRouter
          .push(CourseDetailsViewRoute(
              courseId: courseInfo.courseId, courseName: courseInfo.courseName))
          .then((value) async {
        if (value != null && value == true) {
          _isDataLoading.value = true;
          await _fetchCourseInfo(_courseDashboardCubit);
          _isDataLoading.value = false;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final _courseDashboardCubit =
        BlocProvider.of<CourseDashboardCubit>(context);

    _currentAffairsLimit = _courseDashboardCubit.currentAffairsCount;

    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      Future<void>.microtask(() async {
        _isDataLoading.value = true;
        await _courseDashboardCubit.fetchDashboardConfig();
        await _courseDashboardCubit.fetchCurrentAffairs();
        await _fetchCourseInfo(_courseDashboardCubit);
        _isDataLoading.value = false;
      });
      WidgetsBinding.instance.addObserver(this);

      return null;
    }, [_courseDashboardInfo]);

    return ExitOnDoubleTap(
        scaffoldKey: _scaffoldKey,
        child: Scaffold(
            drawerEnableOpenDragGesture: false,
            key: _scaffoldKey,
            appBar: PreferredSize(
                preferredSize: const Size.fromHeight(70),
                child: AppBarWidget(
                  title: 'Dashboard',
                  leadingIconName: APPBAR_ICON,
                  isHomeScreen: true,
                  trailingWidget: Container(),
                  leadingBtnAction: () async {
                    // final result = await InternetConnectionCheckerPlus().hasConnection;
                    final scaffoldState = _scaffoldKey.currentState;
                    if (scaffoldState != null) {
                      scaffoldState.openDrawer();
                    }
                  },
                )),
            drawer: Drawer(
                width: kIsWeb ? 350 : MediaQuery.of(context).size.width * 0.8,
                child: DrawerWidget(
                  name: _currentUser.first_name + " " + _currentUser.last_name,
                  email: _currentUser.email ?? '',
                  avatarUrl: _currentUser.avatar_url,
                  courseName: 'courseName',
                  hasCourseAccess: true,
                  hasProfileAccess: true,
                  hasSubscriptionAccess: true,
                )),
            body: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
              return BlocBuilder<CourseDashboardCubit, CourseDashboardState>(
                  builder: (context, state) {
                if (state is DashboardCurrentAffairsFetched) {
                  _currentAffairs = state.currentAffairs;
                }
                return Container(
                  decoration: BoxDecoration(
                    color: appDynamicTheme.appBackgroundColor,
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16)),
                  ),
                  child: _isDataLoading.value
                      ? LoadingIndicatorClass()
                      : Align(
                          alignment: Alignment.topCenter,
                          child: Container(
                            width: constraints.maxWidth < 720
                                ? double.infinity
                                : 800,
                            margin: const EdgeInsets.symmetric(
                                horizontal: horizontalMargin),
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 10),
                                  _currentAffairsView(context),
                                  SizedBox(
                                      height:
                                          _currentAffairs.isNotEmpty ? 8 : 0),
                                  _courseDashboardInfo.isNotEmpty
                                      ? _titleWidget('Course Overview')
                                      : const SizedBox.shrink(),
                                  const SizedBox(height: 8),
                                  _courseDashboardInfo.isNotEmpty
                                      ? CourseOverviewWidget(
                                          courseListData: _courseDashboardInfo
                                              .first.coursesInfo,
                                          resourceListData: [],
                                          courseInfo: courseInfo,
                                          onCourseSelected:
                                              (CoursesInfo courseInfo) async =>
                                                  {
                                            await _handleCourseSelection(
                                                _courseDashboardCubit,
                                                courseInfo),
                                          },
                                        )
                                      : Container(),
                                  const SizedBox(height: 8),
                                  _titleWidget("Analytics"),
                                  const SizedBox(height: 5),
                                  AnalyticsWidget(state: state),
                                  const SizedBox(height: 10),
                                  _coursesInfo.value.isNotEmpty
                                      ? _titleWidget("Courses")
                                      : const SizedBox.shrink(),
                                  const SizedBox(height: 5),
                                  ValueListenableBuilder(
                                      valueListenable: _coursesInfo,
                                      builder:
                                          (context, updatedCourseData, __) {
                                        final sortedCourseData = [
                                          ...updatedCourseData
                                        ]..sort((a, b) => a.courseName
                                            .toLowerCase()
                                            .compareTo(
                                                b.courseName.toLowerCase()));
                                        return CoursesWidget(
                                          coursesInfo: sortedCourseData,
                                          toggleInfo: (int index) {
                                            List<CoursesInfo> tempList =
                                                _coursesInfo.value;

                                            tempList[index].showCourseDetails =
                                                !tempList[index]
                                                    .showCourseDetails;

                                            _coursesInfo.value = [...tempList];
                                          },
                                          onCourseSelected:
                                              (CoursesInfo courseInfo) async =>
                                                  {
                                            await _handleCourseSelection(
                                                _courseDashboardCubit,
                                                courseInfo),
                                          },
                                        );
                                      }),
                                  const SizedBox(height: 16),
                                ],
                              ),
                            ),
                          ),
                        ),
                );
              });
            })));
  }

  Widget _titleWidget(String title) {
    return Text(
      title,
      style: LMSFonts.semiBoldFont(18, AppTheme.pitchBlack),
    );
  }

  _currentAffairsView(BuildContext context) {
    bool showMore = _currentAffairs.length > _currentAffairsLimit;
    int listLength = showMore ? _currentAffairsLimit : _currentAffairs.length;
    return Visibility(
      visible: listLength > 0,
      child: ValueListenableBuilder(
          valueListenable: _currentCarouselItemIndex,
          builder: (context, index, _) {
            return Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: _titleWidget(
                            SLStrings.getTranslatedString(KEY_CURRENT_AFFAIRS)),
                      ),
                      _seeAllWidget(context),
                    ],
                  ),
                  const SizedBox(height: 8),
                  _affairsCarouselWidget(context),
                ],
              ),
            );
          }),
    );
  }

  Widget _seeAllWidget(BuildContext context) {
    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    return Visibility(
      visible: true, // shouldFetchCurrentAffairs.value,
      child: InkWell(
        onTap: () async {
          kIsWeb
              ? Beamer.of(context).beamToNamed('/news-more',
                  data: {'affairsList': _currentAffairs})
              : appRouter
                  .push(NewsViewMoreViewRoute(affairsList: _currentAffairs));
        },
        child: Container(
          color: Colors.white,
          child: Row(
            children: [
              Text(
                SLStrings.getTranslatedString(KEY_SEE_ALL),
                style:
                    LMSFonts.semiBoldFont(14, appDynamicTheme.navbarTextColor),
              ),
              Image.asset(
                '$ASSETS_PATH/see_all_btn.png',
                width: 20,
                height: 10,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _affairsCarouselWidget(BuildContext context) {
    return Container(
      child: Column(children: [
        _carouselSliderWidget(context),
        const SizedBox(height: 12),
        _carouselDotsWidget(),
      ]),
    );
  }

  _carouselSliderWidget(BuildContext context) {
    double _affairsWidth = MediaQuery.sizeOf(context).width;
    final List<Widget> imageSliders = [];
    final limitedAffairs = _currentAffairs.take(_currentAffairsLimit).toList();

    for (var i = 0; i < limitedAffairs.length; i++) {
      CurrentAffairs? item = limitedAffairs[i];

      Widget newsWidget = item != null
          ? InkWell(
              onTap: () {
                if (item.newsContent.isNotEmpty) {
                  kIsWeb
                      ? Beamer.of(context).beamToNamed('/course-news',
                          data: {'currentAffairs': item})
                      : appRouter.push(CourseNewsViewRoute(
                          currentAffairs: item,
                        ));
                } else {
                  _showAlertDialog(
                      context,
                      SLStrings.getTranslatedString(
                          KEY_AFFAIRS_DETAILS_NOT_FOUND));
                }
              },
              child: Container(
                height: 150,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    image: DecorationImage(
                        fit: BoxFit.cover,
                        image: AssetImage(item.backgroundImage))),
                child: Stack(
                  children: <Widget>[
                    _carouselContentWidget(item, i),
                  ],
                ),
              ),
            )
          : Container();

      imageSliders.add(newsWidget);
    }

    return Container(
      color: Colors.transparent,
      child: CarouselSlider(
        items: imageSliders,
        carouselController: _controller,
        options: CarouselOptions(
            initialPage: _currentCarouselItemIndex.value,
            enlargeCenterPage: kIsWeb ? true : false,
            aspectRatio: _affairsWidth / _affairsHeight,
            viewportFraction: 1,
            enableInfiniteScroll: false,
            onPageChanged: (index, reason) =>
                _currentCarouselItemIndex.value = index),
      ),
    );
  }

  _carouselContentWidget(CurrentAffairs item, int index) {
    return Positioned(
      bottom: 0.0,
      left: 0.0,
      right: 0.0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: const LinearGradient(
            colors: [Color.fromRGBO(0, 0, 0, 1), Color.fromRGBO(0, 0, 0, 0)],
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _affairsTitleWidget(item.newsTitle),
            _dateItemWidget(index),
          ],
        ),
      ),
    );
  }

  _affairsTitleWidget(String newsTitle) {
    return Text(
      newsTitle,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: LMSFonts.mediumFont(16, AppTheme.whiteTextColor, 1.0),
    );
  }

  _dateItemWidget(int index) {
    String date = _currentAffairs[index] != null
        ? AppDateFormatter()
            .formatDatedMy(_currentAffairs[index]!.publishedDate)
        : AppDateFormatter().formatDatedMy(DateTime.now());
    return Text(
      date,
      textAlign: TextAlign.center,
      style: LMSFonts.mediumFont(12, AppTheme.whiteTextColor, 1),
    );
  }

  _carouselDotsWidget() {
    final limitedAffairs = _currentAffairs.take(_currentAffairsLimit).toList();
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: limitedAffairs.asMap().entries.map((entry) {
        return GestureDetector(
          onTap: () => _updateCarousalIndex(entry),
          child: Container(
            width: 15.0,
            height: 5.0,
            margin: const EdgeInsets.symmetric(horizontal: 4.0),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: _currentCarouselItemIndex.value == entry.key
                    ? AppTheme.carousalSelectedDotColor
                    : AppTheme.carousalUnSelectedDotColor),
          ),
        );
      }).toList(),
    );
  }

  _updateCarousalIndex(MapEntry<int, CurrentAffairs?> entry) {
    _controller.jumpToPage(entry.key);
    _currentCarouselItemIndex.value = entry.key;
  }

  void _showAlertDialog(BuildContext context, String msg) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: msg,
          iconWidget: const Icon(Icons.error, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }
}

class CourseOverviewWidget extends StatelessWidget {
  final List<CoursesInfo> courseListData;
  final List<CourseDetailUserStatiModel> resourceListData;
  final List<Map<String, dynamic>> courseInfo;
  final CourseInfoCallback onCourseSelected;

  CourseOverviewWidget(
      {super.key,
      required this.courseListData,
      required this.courseInfo,
      required this.resourceListData,
      required this.onCourseSelected});

  @override
  Widget build(BuildContext context) {
    const List<LinearGradient> gradients = AppTheme.carouselColorList;
    return LayoutBuilder(
      builder: (context, constraints) {
        final totalWidth = constraints.maxWidth;
        const crossAxisCount = 3;
        const spacing = 10.0;
        final tileWidth =
            (totalWidth - (((crossAxisCount - 1) * spacing) + 0.1)) /
                crossAxisCount;
        final itemCount = courseInfo.length;

        return Wrap(
          spacing: spacing,
          runSpacing: spacing,
          children: List.generate(itemCount, (index) {
            final isLastRow =
                index >= (itemCount ~/ crossAxisCount) * crossAxisCount;
            final remainingItems = itemCount % crossAxisCount;
            final width = isLastRow && remainingItems != 0
                ? (totalWidth - (remainingItems - 1) * spacing) / remainingItems
                : tileWidth;

            final topItem = courseInfo[index];
            final isCourseListNotEmpty = courseListData.isNotEmpty;
            final isResourceListNotEmpty = resourceListData.isNotEmpty;

            return Material(
              child: InkWell(
                highlightColor: Colors.black,
                borderRadius: BorderRadius.circular(12),
                onTap: () {
                  final isCompletedCourse =
                      topItem['title'] == "Completed Course";
                  final isTotalOrCompletedCourse =
                      topItem['title'] == 'Total Course' || isCompletedCourse;

                  showDialog(
                    context: context,
                    builder: (context) {
                      if (isCourseListNotEmpty) {
                        final filteredList = isCompletedCourse
                            ? courseListData
                                .where((element) => element.progress == 100.0)
                                .toList()
                            : courseListData;

                        return CustomPopup<CoursesInfo>(
                          mainTitle: topItem['title'],
                          title1: 'Course Name',
                          title2: isTotalOrCompletedCourse
                              ? 'View Courses'
                              : topItem['title'],
                          modelObjList: filteredList,
                          itemBuilder: (CoursesInfo coursesInfo) =>
                              _popupContent(coursesInfo, topItem['title']),
                          onCancel: () => Navigator.of(context).pop(),
                        );
                      } else if (isResourceListNotEmpty) {
                        return CustomPopup<CourseDetailUserStatiModel>(
                          mainTitle: topItem['title'],
                          title1: 'Resource Name',
                          title2: topItem['title'],
                          modelObjList: resourceListData,
                          itemBuilder: (CourseDetailUserStatiModel
                                  resourceInfo) =>
                              _popupResContent(resourceInfo, topItem['title']),
                          onCancel: () => Navigator.of(context).pop(),
                        );
                      }
                      return _emptyChartData();
                    },
                  );
                },
                child: Container(
                  height: 112,
                  width: width,
                  decoration: BoxDecoration(
                    gradient: gradients[index],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Text(
                            topItem['title'],
                            style: LMSFonts.semiBoldFont(
                                12, AppTheme.whiteTextColor),
                          ),
                        ),
                        const SizedBox(height: 3),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            topItem['content'],
                            style: LMSFonts.semiBoldFont(
                                13, AppTheme.whiteTextColor),
                          ),
                        ),
                        const SizedBox(height: 3),
                        _imageContainer(topItem['iconpath']),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _emptyChartData() {
    return AlertDialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      content: const SizedBox(
          height: 110,
          // width: 110,
          child: Center(child: Text('No data available!'))),
    );
  }

  Widget _popupCourseName(String col1, TextAlign textAlign) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Text(col1,
            textAlign: textAlign,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style:
                LMSFonts.mediumFont(14, AppTheme.primaryTextColorBlack, 1.0)),
      ),
    );
  }

  Widget _popupContent(CoursesInfo coursesInfo, String col2) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _popupCourseName(coursesInfo.courseName, TextAlign.left),
          const SizedBox(width: 5),
          _col2Widget(col2, coursesInfo),
        ],
      ),
    );
  }

  Widget _col2Widget(String col2, CoursesInfo coursesInfo) {
    final isViewable = col2 == 'Total Course' || col2 == 'Completed Course';

    if (isViewable) {
      return Expanded(
        child: GestureDetector(
          onTap: () async => await onCourseSelected(coursesInfo),
          child: Container(
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.all(8),
            child: Image.asset(
              '$ASSETS_PATH/arrow_right.png',
              height: 25,
              color: AppTheme.secondaryAppColor,
            ),
          ),
        ),
      );
    }

    switch (col2) {
      case 'Progress':
        return _popupCourseName(
            coursesInfo.progress.toStringAsFixed(2), TextAlign.right);
      case 'Time Spent':
        final duration = AppDateFormatter.instance
            .formatStringToDuration(coursesInfo.timeSpent);
        final totalTimeSpent =
            AppDateFormatter.instance.formatDurationToString(duration);
        return _popupCourseName(totalTimeSpent, TextAlign.right);
      case 'Achievements':
        return _popupCourseName(
            coursesInfo.achievements.toString(), TextAlign.right);
      default:
        return Image.asset('$ASSETS_PATH/view.png', height: 25);
    }
  }

  Widget _popupResContent(CourseDetailUserStatiModel resInfo, String col2) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _popupCourseName(resInfo.resourceName, TextAlign.left),
          const SizedBox(width: 5),
          _col2ResWidget(resInfo, col2),
        ],
      ),
    );
  }

  Widget _col2ResWidget(CourseDetailUserStatiModel resInfo, String col2) {
    switch (col2) {
      case "Total Marks":
        return _popupCourseName(resInfo.totalMarks.toString(), TextAlign.right);
      case "Progress":
        return _popupCourseName(
            resInfo.progress.toStringAsFixed(2), TextAlign.right);
      case "Time Spent":
        final AppDateFormatter _appDateFormatter = AppDateFormatter.instance;
        Duration duration =
            _appDateFormatter.formatStringToDuration(resInfo.timeSpent);
        final _totalTimeSpent =
            _appDateFormatter.formatDurationToString(duration);
        return _popupCourseName(_totalTimeSpent, TextAlign.right);
      case "Achievements":
        return _popupCourseName(
            resInfo.achievements.toString(), TextAlign.right);
      default:
        return Image.asset('$ASSETS_PATH/view.png', height: 25);
    }
  }

  Widget _imageContainer(String path) {
    return Container(
      height: 30,
      width: 30,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        image: DecorationImage(
          image: AssetImage(
            '$ASSETS_PATH/$path',
          ),
        ),
      ),
    );
  }
}

class CustomPopup<T> extends StatelessWidget {
  final String mainTitle;
  final String title1;
  final String title2;
  final List<T> modelObjList;
  final Widget Function(T model) itemBuilder;
  final VoidCallback onCancel;

  const CustomPopup({
    Key? key,
    required this.mainTitle,
    required this.title1,
    required this.title2,
    required this.modelObjList,
    required this.itemBuilder,
    required this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      title: Text(
        mainTitle,
        style: LMSFonts.semiBoldFont(18, AppTheme.primaryTextColorBlack),
      ),
      content: Container(
        width: MediaQuery.sizeOf(context).width,
        child: modelObjList.isEmpty
            ? Container(
                alignment: Alignment.center,
                height: 80,
                child: const Text('No data available',
                    style: TextStyle(fontSize: 16)),
              )
            : Column(
                mainAxisSize:
                    MainAxisSize.min, // Let the height adjust to content
                children: [
                  _buildTitleWidget(),
                  _buildContentWidget(),
                ],
              ),
      ),
      actionsPadding: const EdgeInsets.only(right: 20, bottom: 20),
      actions: [
        ElevatedButton(
          onPressed: onCancel,
          style: ElevatedButton.styleFrom(
            minimumSize: const Size(80, 35),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'Close',
            style: LMSFonts.regularFont(14, AppTheme.whiteTextColor),
          ),
        ),
      ],
    );
  }

  Widget _buildTitleWidget() {
    return Container(
      color: Colors.grey.withOpacity(0.1),
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title1,
              textAlign: TextAlign.left,
              style: LMSFonts.semiBoldFont(15, AppTheme.primaryTextColorBlack),
            ),
          ),
          Expanded(
            child: Text(
              title2,
              textAlign: TextAlign.right,
              style: LMSFonts.semiBoldFont(15, AppTheme.primaryTextColorBlack),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentWidget() {
    return Flexible(
      child: ListView.builder(
        shrinkWrap: true, // Allow the ListView to shrink-wrap its content
        physics: const ClampingScrollPhysics(), // Disable overscroll effect
        itemCount: modelObjList.length,
        itemBuilder: (context, index) => itemBuilder(modelObjList[index]),
      ),
    );
  }
}

class AnalyticsWidget<T> extends StatelessWidget {
  AnalyticsWidget({
    super.key,
    required this.state,
  });

  final CourseDashboardState state;
  List<T> courseList = [];

  List<Map<String, dynamic>> analyticsItems = [
    {
      "topColor": Colors.green,
      "bandColor": Colors.green.withOpacity(0.3),
      "heading": "Completion",
    },
    {
      "topColor": Colors.purple,
      "bandColor": Colors.purple.withOpacity(0.3),
      "heading": "Skills",
    },
    {
      "topColor": Colors.teal,
      "bandColor": Colors.teal.withOpacity(0.3),
      "heading": "Marks",
    },
    {
      "topColor": Colors.cyan,
      "bandColor": Colors.cyan.withOpacity(0.3),
      "heading": "Progress",
    },
  ];

  final GlobalKey<AnimatedCircularChartState> _chartKey =
      GlobalKey<AnimatedCircularChartState>();
  final GlobalKey<AnimatedCircularChartState> _chartKey1 =
      GlobalKey<AnimatedCircularChartState>();

  final _completionChartSize = const Size(140.0, 140.0);
  final _skillsChartSize = const Size(100.0, 100.0);
  double value = 0.0;
  Color? labelColor = Colors.blue[200];

  List<CircularStackEntry> _setProgressRadial() {
    if (state.courseDashboardInfo.isEmpty) {
      return [];
    }
    double progress = state.courseDashboardInfo.isNotEmpty
        ? state.courseDashboardInfo.first.totalProgress
        : 0.0;

    value = double.parse(progress.toStringAsFixed(2));

    List<CircularStackEntry> data = <CircularStackEntry>[
      CircularStackEntry(
        <CircularSegmentEntry>[
          CircularSegmentEntry(
            progress,
            Colors.blue,
            rankKey: 'Math',
          ),
          CircularSegmentEntry(
            100,
            Colors.grey[200],
            rankKey: 'Background',
          ),
        ],
        rankKey: 'Math',
      ),
      // Science
    ];

    return data;
  }

  Color getColor(int index) {
    List<Color> colors = [
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
    ];

    return colors[index % colors.length];
  }

  List<CircularStackEntry> _getSkillsRadial() {
    if (state.courseDashboardInfo.isEmpty) {
      return [];
    }

    List<CircularStackEntry> data = state.courseDashboardInfo.first
        .analyticsInfo.performanceSummary.questionCategory
        .asMap()
        .entries
        .skip(max(
            0,
            state.courseDashboardInfo.first.analyticsInfo.performanceSummary
                    .questionCategory.length -
                3))
        .map<CircularStackEntry>((entry) {
      int index = entry.key;
      var category = entry.value;

      return CircularStackEntry(
        [
          CircularSegmentEntry(
            category.marksObtained.toDouble(),
            getColor(index),
            rankKey: category.categoryId,
          ),
          CircularSegmentEntry(
            100,
            Colors.grey[200],
            rankKey: 'Background',
          ),
        ],
        rankKey: category.categoryId,
      );
    }).toList();
    return data;
  }

  List<OrdinalData> _getMarksData() {
    if (state.courseDashboardInfo.isEmpty) {
      return [];
    }

    List<OrdinalData> data = state.courseDashboardInfo.first.coursesInfo
        .skip(max(0, state.courseDashboardInfo.first.coursesInfo.length - 3))
        .map<OrdinalData>((course) {
      return OrdinalData(
          domain: (course.courseName.length >= 6
                  ? course.courseName.substring(0, 6)
                  : course.courseName) +
              "..",
          measure: course.totalMarks);
    }).toList();

    return data;
  }

  List<OrdinalData> _getprogressData() {
    if (state.courseDashboardInfo.isEmpty) {
      return [];
    }

    List<OrdinalData> data = state.courseDashboardInfo.first.coursesInfo
        .skip(max(0, state.courseDashboardInfo.first.coursesInfo.length - 3))
        .map<OrdinalData>((course) {
      return OrdinalData(
          domain: (course.courseName.length >= 6
                  ? course.courseName.substring(0, 6)
                  : course.courseName) +
              "..",
          measure: course.progress);
    }).toList();

    return data;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CourseDashboardCubit, CourseDashboardState>(
      builder: (context, state) {
        return LayoutBuilder(builder: (context, constraints) {
          final totalWidth = constraints.maxWidth;
          const crossAxisCount = 2;
          const spacing = 8.0;

          final tileWidth =
              (totalWidth - (crossAxisCount - 1) * spacing) / crossAxisCount;
          return Wrap(
            spacing: spacing,
            runSpacing: spacing,
            children: List.generate(
              analyticsItems.length,
              (index) {
                // Adjust width for the last row
                final width = (analyticsItems[index]["heading"] == "Marks" ||
                        analyticsItems[index]["heading"] == "Progress")
                    ? totalWidth
                    : tileWidth;

                final item = analyticsItems[index];

                return SizedBox(
                  height: 220,
                  width: width, // index > 1 ? totalWidth : (totalWidth) / 2,
                  child: Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    elevation: 4,
                    child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15),
                          child: Column(
                            children: [
                              Container(
                                height: 12,
                                color: item['topColor'],
                              ),
                              Container(
                                height: 30,
                                width: double.infinity,
                                color: item['bandColor'],
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  item['heading'],
                                  textAlign: TextAlign.left,
                                  style: LMSFonts.semiBoldFont(
                                      15, item['topColor']),
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  child: (() {
                                    switch (item["heading"]) {
                                      case "Completion":
                                        return _setProgressRadial().isEmpty
                                            ? _emptyChartData()
                                            : _completionGraph(context, item);
                                      case "Skills":
                                        return _getSkillsRadial().isEmpty
                                            ? _emptyChartData()
                                            : _skillsGraph(context, item);

                                      case "Marks":
                                        return _getMarksData().isEmpty
                                            ? _emptyChartData()
                                            : _marksGraph(context, item);

                                      case "Progress":
                                        return _getprogressData().isEmpty
                                            ? _emptyChartData()
                                            : _progressGraph(context, item);
                                    }
                                  }()),
                                ),
                              )
                            ],
                          ),
                        )),
                  ),
                );
              },
            ).toList(),
          );
        });
      },
    );
  }

  Widget _completionGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          if (state.courseDashboardInfo.isNotEmpty) {
            courseList = state.courseDashboardInfo.first.coursesInfo.cast<T>();
          }
          if (courseList.isNotEmpty) {
            showDialog(
              context: context,
              builder: (context) {
                return CustomPopup<CoursesInfo>(
                  mainTitle: item["heading"],
                  title1: 'Course Name',
                  title2: 'Percentage',
                  modelObjList: courseList.cast<CoursesInfo>(),
                  itemBuilder: (CoursesInfo coursesInfo) => _popupContent(
                      coursesInfo.courseName, coursesInfo.progress.toString()),
                  onCancel: () => Navigator.of(context).pop(),
                );
              },
            );
          }
        },
        child: AnimatedCircularChart(
          key: _chartKey1,
          size: _completionChartSize,
          initialChartData: _setProgressRadial(),
          edgeStyle: SegmentEdgeStyle.round,
          percentageValues: true,
          holeLabel: '$value%',
          duration: Durations.extralong1,
          holeRadius: 35,
        ),
      ),
    );
  }

  Widget _skillsGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          if (state.courseDashboardInfo.isNotEmpty) {
            courseList = state.courseDashboardInfo.first.analyticsInfo
                .performanceSummary.questionCategory
                .cast<T>();
          }
          showDialog(
            context: context,
            builder: (context) {
              return CustomPopup<QuestionCategory>(
                mainTitle: item["heading"],
                title1: 'Course Name',
                title2: 'Value',
                modelObjList: courseList.cast<QuestionCategory>(),
                itemBuilder: (QuestionCategory coursesInfo) => _popupContent(
                    coursesInfo.categoryName,
                    coursesInfo.marksObtained.toString()),
                onCancel: () => Navigator.of(context).pop(),
              );
            },
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Expanded(
                child: AnimatedCircularChart(
                  key: _chartKey,
                  size: _skillsChartSize,
                  initialChartData: _getSkillsRadial(),
                  edgeStyle: SegmentEdgeStyle.round,
                  percentageValues: true,
                  // holeLabel: '$value%',
                  duration: Durations.extralong1,
                  holeRadius: 20,
                ),
              ),
              SizedBox(
                width: 130,
                height: 55,
                child: ListView(
                  children: state.courseDashboardInfo.first.analyticsInfo
                      .performanceSummary.questionCategory
                      .sublist(max(
                          0,
                          state.courseDashboardInfo.first.analyticsInfo
                                  .performanceSummary.questionCategory.length -
                              3)) // Only the last 3 items
                      .map<Widget>((category) {
                    int index = state.courseDashboardInfo.first.analyticsInfo
                        .performanceSummary.questionCategory
                        .indexOf(category);
                    return Row(
                      children: [
                        Container(
                          height: 10,
                          width: 10,
                          color: getColor(index),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Text(
                          category.categoryName.length > 6
                              ? category.categoryName.substring(
                                      0, min(6, category.categoryName.length)) +
                                  '...' +
                                  " (${category.marksObtained.toStringAsFixed(1)})"
                              : category.categoryName +
                                  " (${category.marksObtained})",
                          style: LMSFonts.mediumFont(
                              12, AppTheme.primaryTextColorBlack, 1.0),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _marksGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        onTap: () {
          if (state.courseDashboardInfo.isNotEmpty) {
            courseList = state.courseDashboardInfo.first.coursesInfo.cast<T>();
          }
          showDialog(
            context: context,
            builder: (context) {
              return CustomPopup<CoursesInfo>(
                mainTitle: item["heading"],
                title1: 'Course Name',
                title2: 'Marks',
                modelObjList: courseList.cast<CoursesInfo>(),
                itemBuilder: (CoursesInfo coursesInfo) => _popupContent(
                    coursesInfo.courseName, coursesInfo.totalMarks.toString()),
                onCancel: () => Navigator.of(context).pop(),
              );
            },
          );
        },
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: DChartBarO(
                  groupList: [
                    OrdinalGroup(
                      id: '1',
                      data: _getMarksData(),
                    ),
                  ],
                ),
              ),
            ),
            Positioned.fill(
                child: Container(
              color: Colors.transparent,
            )),
          ],
        ),
      ),
    );
  }

  Widget _progressGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        onTap: () {
          if (state.courseDashboardInfo.isNotEmpty) {
            courseList = state.courseDashboardInfo.first.coursesInfo.cast<T>();
          }
          showDialog(
            context: context,
            builder: (context) {
              return CustomPopup<CoursesInfo>(
                mainTitle: item["heading"],
                title1: 'Course Name',
                title2: 'Progress',
                modelObjList: courseList.cast<CoursesInfo>(),
                itemBuilder: (CoursesInfo coursesInfo) => _popupContent(
                    coursesInfo.courseName, coursesInfo.progress.toString()),
                onCancel: () => Navigator.of(context).pop(),
              );
            },
          );
        },
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(15.0),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: DChartBarO(
                  vertical: false,
                  groupList: [
                    OrdinalGroup(
                      id: '1',
                      data: _getprogressData(),
                    ),
                  ],
                ),
              ),
            ),
            Positioned.fill(
                child: Container(
              color: Colors.transparent,
            )),
          ],
        ),
      ),
    );
  }

  Widget _emptyChartData() {
    return const Center(child: Text('No data available!'));
  }

  Widget _popupCourseName(String col1, TextAlign textAlign) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Text(col1,
            textAlign: textAlign,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style:
                LMSFonts.mediumFont(14, AppTheme.primaryTextColorBlack, 1.0)),
      ),
    );
  }

  Widget _popupContent(String name, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _popupCourseName(name, TextAlign.left),
        const SizedBox(width: 5),
        _col2Widget(value),
      ],
    );
  }

  Widget _col2Widget(String value) {
    return _popupCourseName(value, TextAlign.right);
  }
}

class CoursesWidget extends StatelessWidget {
  final List<CoursesInfo> coursesInfo;
  final IntCallback toggleInfo;
  final CourseInfoCallback onCourseSelected;

  const CoursesWidget({
    super.key,
    required this.toggleInfo,
    required this.coursesInfo,
    required this.onCourseSelected,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final totalWidth = constraints.maxWidth;
        const crossAxisCount = 2;
        const spacing = 8.0;
        final tileWidth =
            (totalWidth - (crossAxisCount) * spacing) / crossAxisCount;

        return Wrap(
          spacing: spacing,
          runSpacing: spacing,
          children: List.generate(
            coursesInfo.length,
            (index) {
              return _animatedView(context, index, tileWidth);
            },
          ),
        );
      },
    );
  }

  Widget _animatedView(BuildContext context, int index, double tileWidth) {
    return GestureDetector(
      onTap: () async => onCourseSelected(coursesInfo[index]),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        transitionBuilder: (child, animation) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0), // Slide in from the right
              end: Offset.zero, // Final position
            ).animate(animation),
            child: child,
          );
        },
        child: _courseCardWidget(context, index, tileWidth), // Default content
      ),
    );
  }

  Widget _courseCardWidget(BuildContext context, int index, double tileWidth) {
    final courseItem = coursesInfo[index];
    bool isCompleted = courseItem.progress >= 100.0;
    bool notStarted = courseItem.progress <= 0;
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // Rounded corners
      ),
      elevation: 4,
      child: Container(
        height: 200,
        width: tileWidth - 4, // default horizontal margin of card widget,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: courseItem.showCourseDetails
              ? _courseDetailsView(courseItem, index) // List of text data
              : Column(
                  children: [
                    Container(
                      height: 12,
                      decoration: const BoxDecoration(
                        color: AppTheme.courseBgOrange,
                      ),
                    ),
                    _courseInfoWidget(courseItem, index),
                    const SizedBox(height: 10),
                    _progressInfoWidget(courseItem),
                    const SizedBox(height: 10),
                    // Progress Bar
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: LinearProgressIndicatorWidget(
                        progress: courseItem.progress / 100,
                        progressColor: isCompleted
                            ? AppTheme.primaryBlue
                            : AppTheme.courseBgOrange,
                        backgroundColor: notStarted
                            ? AppTheme.coursePecentageTxtGrey.withOpacity(0.1)
                            : AppTheme.sectionCardBgColor,
                      ),
                    ),
                    const SizedBox(height: 5),
                    // Percentage Text
                    Align(
                      child: Text(
                        "${isCompleted ? '100' : notStarted ? '0' : courseItem.progress}% Complete",
                        style: LMSFonts.mediumFont(
                            12, AppTheme.primaryTextColorBlack, 1.0),
                      ),
                    ),
                    const SizedBox(height: 10)
                  ],
                ),
        ),
      ),
    );
  }

  Widget _courseInfoWidget(CoursesInfo courseItem, int index) {
    return Expanded(
      child: Container(
        color: AppTheme.sectionCardBgColor,
        child: Column(
          children: [
            Align(
              alignment: Alignment.topRight,
              child: GestureDetector(
                onTap: () => toggleInfo(index),
                child: Container(
                  margin: const EdgeInsets.only(top: 5, right: 5),
                  color: Colors.transparent,
                  width: 40,
                  alignment: Alignment.topRight,
                  child: const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 20,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(5),
                alignment: Alignment.center,
                child: Text(
                  courseItem.courseName,
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  style:
                      LMSFonts.semiBoldFont(14, AppTheme.primaryTextColorBlack),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            const SizedBox(height: 5),
          ],
        ),
      ),
    );
  }

  Widget _progressInfoWidget(CoursesInfo courseItem) {
    bool isCompleted =
        courseItem.progress == 100.00 || courseItem.progress == 100.0;
    bool notStarted = courseItem.progress <= 0;
    String completionStatus = isCompleted
        ? 'Completed'
        : notStarted
            ? 'Not Started'
            : "In Progress";
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Status Label
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: isCompleted
                  ? AppTheme.ternaryBlue
                  : notStarted
                      ? AppTheme.coursePecentageTxtGrey.withOpacity(0.1)
                      : Colors.yellow[100],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              completionStatus,
              style: LMSFonts.mediumFont(
                  12,
                  isCompleted
                      ? AppTheme.primaryBlue
                      : notStarted
                          ? AppTheme.primaryTextColorBlack
                          : Colors.brown,
                  1.0),
            ),
          ),
          Text(
            courseItem.timeSpent,
            style: LMSFonts.mediumFont(12, AppTheme.primaryTextColorBlack, 1.0),
          ),
        ],
      ),
    );
  }

  Widget _courseDetailsView(CoursesInfo courseItem, int index) {
    return GestureDetector(
      onTap: () => toggleInfo(index),
      child: Container(
        width: double.infinity,
        color: AppTheme.courseBgOrange,
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            courseInfoValue('Course Details'),
            const SizedBox(height: 8),
            courseInfoTitle('Time Spent'),
            const SizedBox(height: 2),
            courseInfoValue(courseItem.timeSpent),
            const SizedBox(height: 8),
            courseInfoTitle('Achievements'),
            const SizedBox(height: 2),
            courseItem.achievements == 0
                ? courseInfoValue('No achievements yet')
                : _achievementsStar(courseItem.achievements),
            const SizedBox(height: 8),
            courseInfoTitle('Mark Scored'),
            const SizedBox(height: 2),
            courseInfoValue(courseItem.totalMarks.toString()),
          ],
        ),
      ),
    );
  }

  Text courseInfoValue(String value) {
    return Text(
      value,
      textAlign: TextAlign.left,
      style: LMSFonts.semiBoldFont(13, AppTheme.whiteTextColor),
    );
  }

  Text courseInfoTitle(String title) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: LMSFonts.mediumFont(12, AppTheme.whiteTextColor, 1.0),
    );
  }

  Widget _achievementsStar(int count) {
    bool showEllipsis = count > 15;
    int displayedCount = showEllipsis ? 15 : count;

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      children: [
        ...List.generate(
          displayedCount,
          (index) => Container(
            padding: const EdgeInsets.only(right: 2), // Optional spacing
            child: const Icon(
              Icons.star,
              color: Colors.yellow,
              size: 15,
            ),
          ),
        ),
        if (showEllipsis)
          Padding(
            padding: const EdgeInsets.only(left: 2),
            child:
                Text(". . .", style: LMSFonts.semiBoldFont(14, Colors.yellow)),
          ),
      ],
    );
  }
}
