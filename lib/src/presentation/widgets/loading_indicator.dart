import 'package:flutter/material.dart';

class LoadingIndicatorClass extends StatelessWidget {
  Size size = const Size(50, 50);
  double strokeWidth = 4.0;

  LoadingIndicatorClass(
      {super.key, this.size = const Size(50, 50), this.strokeWidth = 4.0});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black.withOpacity(0.1),
      child: Center(
        child: SizedBox(
          height: size.height,
          width: size.width,
          child: CircularProgressIndicator(
            strokeWidth: strokeWidth,
          ),
        ),
      ),
    );
  }
}
