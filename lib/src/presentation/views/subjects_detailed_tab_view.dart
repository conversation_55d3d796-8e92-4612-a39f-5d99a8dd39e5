import 'package:SmartLearn/src/domain/models/tabbar_item.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_dashboard/course_assignments.dart';
import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../domain/models/course_dashboard/course_page_resource.dart';
import '../../domain/models/course_dashboard/course_video_resource.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/question_data.dart';
import '../../domain/models/responses/section_details.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/helper.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '../cubits/course_details/course_details_cubit.dart';
import '../cubits/exam/exam_cubit.dart';
import '../cubits/section_details/section_details_cubit.dart';
import '../widgets/alert_popup/confirmation_popup.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '../widgets/loading_indicator.dart';
import '/src/presentation/widgets/tab_bar_widget.dart';
import '/src/config/themes/lms_fonts.dart';
import '../../config/router/app_router.dart';
import '../widgets/app_bar.dart';
import '/src/config/themes/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../utils/constants/strings.dart';
import 'ppt_viewer.dart';

@RoutePage()

// ignore: must_be_immutable
class SubjectsDetailedTabViewScreen extends HookWidget
    with WidgetsBindingObserver {
  final SectionDetails? sectionDetailsData;

  SubjectsDetailedTabViewScreen(this.sectionDetailsData, {super.key});

  late TabController _tabController;
  final List<TabbarItem> _tabBarItems = [
    TabbarItem(id: 0, label: VIDEO, icon: '$ASSETS_PATH/video_light.png'),
    TabbarItem(
        id: 1, label: STUDY_MATERIAL, icon: '$ASSETS_PATH/file_dock.png'),
    TabbarItem(id: 2, label: PRACTICE_SET, icon: '$ASSETS_PATH/quiz_add.png')
  ];
  final ValueNotifier<bool> _isDataLoading = ValueNotifier<bool>(false);

  List<Modules> _videos = [];
  List<Modules> _studyMaterials = [];
  List<Modules> _practiceSets = [];
  List<Question> _questions = [];


  QuestionData? _questionData;
  SectionDetailsCubit? _sectionDetailsCubit;
  ExamCubit? _examCubit;
  BuildContext? currentContext;


  @override
  Widget build(BuildContext context) {
    currentContext = context;
    _sectionDetailsCubit = BlocProvider.of<SectionDetailsCubit>(context);
    _examCubit = BlocProvider.of<ExamCubit>(context);
    final _tabController =
        useTabController(initialLength: _tabBarItems.length, initialIndex: 0);
    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      _getResourceList();
      return () {
        currentContext = null;
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return Scaffold(
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: sectionDetailsData?.name ?? '',
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () => appRouter.popForced(),
              trailingWidget: Container(),
              
            )),
        body: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: ValueListenableBuilder(
                valueListenable: _isDataLoading,
                builder: (context, _, __) {
                  return Stack(
                    children: [
                      Column(
                        children: [
                          const SizedBox(height: 20),
                          TabbarWidget(
                              tabBarItems: _tabBarItems,
                              tabController: _tabController),
                          Expanded(
                            child: TabBarView(
                              controller: _tabController,
                              children: [
                                _buildVideoTabView(context),
                                _buildStudyMaterialTabView(context),
                                _buildPracticeSetTabView(context),
                              ],
                            ),
                          ),
                        ],
                      ),
                      _isDataLoading.value == true
                          ? Positioned.fill(child: LoadingIndicatorClass())
                          : Container()
                    ],
                  );
                }),
          );
        }));
  }

  void _getResourceList() {
    if (sectionDetailsData == null) return;

    final modules = [
      ...sectionDetailsData!.modules,
      if (sectionDetailsData!.folders.isNotEmpty)
        ...sectionDetailsData!.folders.first.folderModules,
    ];

    _videos = modules
        .where((module) =>
            module.moduleType == ResourceType.VIDEO ||
            module.moduleType == ResourceType.URL)
        .toList();

    _studyMaterials = modules
        .where((module) =>
            module.moduleType == ResourceType.PAGE ||
            module.moduleType == ResourceType.FILE ||
            module.moduleType == ResourceType.IMAGE ||
            module.fileExtension.toLowerCase() == PDF_EXTENSION)
        .toList();

    _practiceSets = modules
        .where((module) => module.moduleType == ResourceType.QUIZ)
        .toList();
  }

  // Reusable grid view component
  Widget _buildTabGridView({
    required String headerTitle,
    required List<Modules> items,
    required String iconPath,
    required int crossAxisCount,
    required ModuleCallback onItemTap,
  }) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(10.0),
          color: AppTheme.secondaryAppColor,
          child: Text(
            headerTitle,
            style: LMSFonts.semiBoldFont(16, AppTheme.whiteTextColor),
          ),
        ),
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 0.85,
            ),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              return _buildGridCard(
                item,
                iconPath,
                onItemTap: onItemTap,
              );
            },
          ),
        ),
      ],
    );
  }

  // Reusable grid card component
  Widget _buildGridCard(
    Modules item,
    String iconPath, {
    required ModuleCallback onItemTap,
  }) {
    return Card(
      // color: AppTheme.sectionCardBgColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        splashColor: AppTheme.primaryBlue.withOpacity(0.2),
        highlightColor: AppTheme.primaryBlue.withOpacity(0.1),
        onTap: () => onItemTap(item),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Image.asset(
                  iconPath,
                  width: 32,
                  height: 32,
                  color: AppTheme.primaryAppColor,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                item.moduleName,
                style: LMSFonts.mediumFont(
                    12, AppTheme.primaryTextColorBlack, 1.0),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoTabView(BuildContext context) {
    if (_videos.isEmpty) {
      return const EmptyTabView(tabItem: EMPTY_VIDEO_TAB);
    }

    return _buildTabGridView(
      headerTitle: 'Video Resources',
      items: _videos,
      iconPath: '$ASSETS_PATH/video_light.png',
      crossAxisCount: 3,
      onItemTap: (item) async => await _onResourceTapped(context, item),
    );
  }

  Widget _buildStudyMaterialTabView(BuildContext context) {
    final studyMaterialItems = _studyMaterials;
    if (studyMaterialItems.isEmpty) {
      return const EmptyTabView(tabItem: EMPTY_STUDY_MATERIALS_TAB);
    }

    return _buildTabGridView(
      headerTitle: 'Study Materials',
      items: studyMaterialItems,
      iconPath: '$ASSETS_PATH/file_dock.png',
      crossAxisCount: 3,
      onItemTap: (item) async => await _onResourceTapped(context, item),
    );
  }

  Widget _buildPracticeSetTabView(BuildContext context) {
    final practiceSetItems = _practiceSets;
    if (practiceSetItems.isEmpty) {
      return const EmptyTabView(tabItem: EMPTY_PRACTICE_TAB);
    }

    return _buildTabGridView(
      headerTitle: 'Practice Sets',
      items: _practiceSets,
      iconPath: '$ASSETS_PATH/quiz_add.png',
      crossAxisCount: 3,
      onItemTap: (item) async => await _onResourceTapped(context, item),
    );
  }

  _onResourceTapped(
    BuildContext context,
    Modules moduleObj,
  ) async {
    _isDataLoading.value = true;
    SectionDetailsState state =
        _sectionDetailsCubit?.state ?? const SectionDetailsInitial();

    bool _isTypeQuiz = moduleObj.moduleType == ResourceType.QUIZ;

    if (_isTypeQuiz) {
      if (moduleObj.remainingAttempts > 0) {
        _isDataLoading.value = false;
        await _fetchQuestionsForExam(context, moduleObj.instanceId);
      } else {
        _isDataLoading.value = false;
        _showInvalidExamPopup(context, 'Attempts Exceeded');
      }
    } else if (state is PageDetailsError) {
      _isDataLoading.value = false;
      showPGExceptionPopup(context, state.error);
    } else {
      bool _isTypeVideo = moduleObj.moduleType == ResourceType.VIDEO ||
          moduleObj.moduleType == ResourceType.URL;
      bool _isTypePage = moduleObj.moduleType == ResourceType.PAGE;
      bool _isTypeFileImage = moduleObj.moduleType == ResourceType.IMAGE ||
          moduleObj.moduleType == ResourceType.FILE;

      String instanceId = moduleObj.instanceId;

      ResourceProgress? _courseProgress =
          await _fetchCourseProgress(context, instanceId);

      ///
      /// _courseProgress == null --> if the _fetchCourseProgress() method returned null,
      /// happens if the privilege access is denied.
      ///
      /// _courseProgress != null -> check is added to
      /// prevent multiple privilege restricted popup
      ///

      if (_courseProgress != null) {
        if (_isTypeVideo) {
          int index = _videos.indexOf(moduleObj);
          await _handleVideoTypeNavigation(context, index, _courseProgress);
        } else if (_isTypePage) {
          int index = _studyMaterials.indexOf(moduleObj);
          await _handlePageTypeNavigation(context, index, _courseProgress);
        } else if (_isTypeFileImage) {
          int index = _studyMaterials.indexOf(moduleObj);
          await _handleFileImageTypeNavigation(context, index, _courseProgress);
        }
      }
    }
    _isDataLoading.value = false;
  }

  _handleVideoTypeNavigation(
      BuildContext context, int index, ResourceProgress courseProgress) async {
    CourseVideoResource? _courseVideoObj;
    List<CheckPoint> _checkPoints = [];
    String instanceId = _videos[index].instanceId;
    String courseModuleId = _videos[index].courseModuleId;

    _isDataLoading.value = true;

    await _sectionDetailsCubit?.fetchVideoContent(instanceId, courseModuleId);
    SectionDetailsState state =
        _sectionDetailsCubit?.state ?? const SectionDetailsInitial();

    if (state is VideoResourceError) {
      _isDataLoading.value = false;

      showPGExceptionPopup(context, state.error);
    } else if (state is VideoResourceFetched) {
      _courseVideoObj = state.courseVideo;
      _courseVideoObj.instanceId = instanceId;
      _checkPoints.addAll(state.checkPoints);
      state = _sectionDetailsCubit?.state ?? const SectionDetailsInitial();
      ;

      _isDataLoading.value = false;
      await _sectionDetailsCubit?.getCourseprogress(_courseVideoObj.instanceId);
      state = _sectionDetailsCubit?.state ?? const SectionDetailsInitial();
      ;
      if (state is GetCourseProgressState) {
        kIsWeb
            ? Beamer.of(context).beamToNamed(
                "/video-view",
                data: {
                  "courseVideo": _courseVideoObj,
                  "isFromSectionDetails": true,
                  "checkPoints": _checkPoints
                },
              )
            : appRouter
                .push(CourseVideoViewRoute(
                    courseVideo: _courseVideoObj,
                    courseProgress: courseProgress,
                    isFromSectionDetails: true,
                    navigateBackTo: NavigationTo.subjects_tab_view,
                    checkPoints: _checkPoints))
                .then((value) async {
                // progress not implemented in tab view
                if (value != null) {
                  Map<String, dynamic> args = value as Map<String, dynamic>;

                  double progress = double.parse(args['progress'].toString());

                  _videos[index].progress = progress;
                  _videos[index].didMarkedAsDone = (progress == 100);
                }
              });
      }
    }
    _isDataLoading.value = false;
  }

  Future<void> _handlePageTypeNavigation(
      BuildContext context, int index, ResourceProgress? courseProgress) async {
    String instanceId = _studyMaterials[index].instanceId;
    String courseModuleId = _studyMaterials[index].courseModuleId;
    const int folderResourceLength = 0;

    _isDataLoading.value = true;

    await _sectionDetailsCubit?.fetchPageContent(instanceId, courseModuleId);

    final currentState =
        _sectionDetailsCubit?.state ?? const SectionDetailsInitial();

    if (currentState is PageContentsFetched) {
      final CoursePageResource pageContent = currentState.pageContentData;

      _isDataLoading.value = false;
      await _sectionDetailsCubit?.setLoading(false);

      if (kIsWeb) {
        Beamer.of(context).beamToNamed(
          '/study-materials',
          data: {
            'pageContent': pageContent,
            'instance': instanceId,
            'courseId': COURSE_ID,
            'progess': courseProgress,
            'moduleLength': folderResourceLength,
          },
        );
      } else {
        final result = await appRouter.push(
          StudyMaterialViewRoute(
            pageContent: pageContent,
            moduleLength: folderResourceLength,
            resourceProgress: courseProgress,
          ),
        );

        if (result == true) {
          // Handle progress update if needed
          _studyMaterials[index].progress = 100.0;
          _studyMaterials[index].didMarkedAsDone = true;
        }
      }
    } else if (currentState is PageDetailsError) {
      _isDataLoading.value = false;
      showPGExceptionPopup(context, currentState.error);
    } else {
      _isDataLoading.value = false;
    }
  }

  _handleFileImageTypeNavigation(
      BuildContext context, int index, ResourceProgress? courseProgress) async {
    try {
      CourseFileResource? _resourceFile;
      String instance = _studyMaterials[index].instanceId;
      String courseModuleId = _studyMaterials[index].courseModuleId;
      _isDataLoading.value = true;

      await _sectionDetailsCubit?.fetchFileContent(instance, courseModuleId);
      // For Image View

      SectionDetailsState state =
          _sectionDetailsCubit?.state ?? const SectionDetailsInitial();

      if (state is FileResourceFetched) {
        _resourceFile = state.resourseFile;
        _resourceFile.id = instance;

        bool isNetworkUrl = await validateImage(_resourceFile.url ?? '');

        if (_studyMaterials[index].fileExtension == PPT_EXTENSION ||
            _studyMaterials[index].fileExtension == PPTX_EXTENSION) {
          ///
          /// PPT
          ///

          _resourceFile.progress =
              double.tryParse(_getProgressval(_studyMaterials[index])) ?? 0.0;
          // courseProgress?.progress ?? 0.0;

          await _handleNavigationToPPTReader(context, _resourceFile, index);
        } else if (_studyMaterials[index].fileExtension == PDF_EXTENSION) {
          final file = await createFileOfPdfUrl(_resourceFile.url);
          _resourceFile.url = file.path;
          await _handleNavigationToPDFView(context, _resourceFile, index);
          _isDataLoading.value = false;
        } else if (_resourceFile != null && !_resourceFile.url.endsWith(HTML)) {
          if (_resourceFile.url.endsWith(IMAGE_EXTENSIONJPG) ||
              _resourceFile.url.endsWith(IMAGE_EXTENSIONJPEG) ||
              _resourceFile.url.endsWith(IMAGE_EXTENSIONPNG) ||
              (isNetworkUrl)) {
            await _handleNavigationToImageView(
              context,
              index,
              courseProgress,
              _resourceFile,
            );
          } else {
            _isDataLoading.value = false;
            //   await _sectionDetailsCubit.setLoading(false);
            _showAlertDialog(context,
                SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
          }
        } else {
          _isDataLoading.value = false;
          _showAlertDialog(
              context, SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
        }
      } else if (state is FileResourceError) {
        _isDataLoading.value = false;
        showPGExceptionPopup(context, state.error);
      } else {
        _isDataLoading.value = false;
        _showAlertDialog(
            context, SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
      }
    } on Exception catch (_) {
      // TODO
      _isDataLoading.value = false;
    }
    _isDataLoading.value = false;
  }

  _handleNavigationToPDFView(
      BuildContext context, CourseFileResource _resourceFile, int index) async {
    try {
      _isDataLoading.value = true;
      String path =
          await _sectionDetailsCubit?.getFileFromUrl(SAMPLE_PDF_PATH) ?? "";

      if (path != '') {
        kIsWeb
            ? Beamer.of(context)
                .beamToNamed('/pdf-view', data: {'urlPDFPath': path})
            : await appRouter.push(PDFViewRoute(
                courseFileResource: _resourceFile,
                checkPoints: [],
                isFromExamScreen: false,
                isExamPassed: false,
                navigateBackTo: NavigationTo.subjects_tab_view,
              ));
      }

      // _sectionDetailsCubit.setLoading(false);
    } on Exception catch (e) {
      debugPrint('[section_details_view.dart][pdfScreenNavigation]: $e');
    }
  }

  _handleNavigationToPPTReader(
      BuildContext context, CourseFileResource _resourceFile, int index) async {
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

    _isDataLoading.value = true;

    await _courseDetailsCubit.fetchFileResource(
        _resourceFile.id, _resourceFile.courseModuleId);
    // For Image View

    CourseDetailsState state = _courseDetailsCubit.state;

    if (state is CourseDetailsFileResFetched) {
      if (state.courseFileResource.isNotEmpty) {
        final CourseFileResource _fileRes = state.courseFileResource.first;
        _sectionDetailsCubit?.courseFileobj = _fileRes;
        _fileRes.progress = _resourceFile.progress;

        final CheckPointData checkPointData = state.checkPointData;

        Future.delayed(const Duration(milliseconds: 500), () {
          _isDataLoading.value = false;
        });
        appRouter
            .push(PPTViewerRoute(
          courseFileResource: _fileRes,
          checkPoints: checkPointData.checkPoints ?? [],
          isFromExamScreen: false,
          navigateBackTo: NavigationTo.subjects_tab_view,
        ))
            .then((value) async {
          if (value != null && _fileRes.progress < 100) {
            final progress = (value as double).toInt();
            _studyMaterials[index].progress = progress.toDouble();
            _studyMaterials[index].didMarkedAsDone = (progress == 100);
          }
          _isDataLoading.value = false;
        });
      }
    }
  }

  _handleNavigationToImageView(
    BuildContext context,
    int index,
    ResourceProgress? courseProgress,
    CourseFileResource _resourceFile,
  ) async {
    String instanceId = _studyMaterials[index].instanceId;
    String courseModuleId = _studyMaterials[index].courseModuleId;
    int folderResourceLength = 0;
    String courseId = _studyMaterials[index].courseId ?? COURSE_ID;

    kIsWeb
        ? Beamer.of(context).beamToNamed('/image-view', data: {
            'imagePath': _resourceFile.url,
            'resourceFile': _resourceFile,
            'instance': instanceId,
            'courseId': courseId,
            'progess': courseProgress,
            'moduleLength': folderResourceLength,
          })
        : appRouter
            .push(ImageViewRoute(
                resourceFile: _resourceFile,
                moduleLength: folderResourceLength,
                resourceProgress: courseProgress))
            .then((value) async {
            if (value != null) {
              _isDataLoading.value = true;
              Map<String, dynamic> args = value as Map<String, dynamic>;

              double imageProgress = double.parse(args['progress'].toString());
              _isDataLoading.value = true;

              _studyMaterials[index].progress = imageProgress;
              _studyMaterials[index].didMarkedAsDone = (imageProgress == 100);
              _isDataLoading.value = false;
            }
          });
  }

  String _getProgressval(Modules module) {
    double progressPercentage = module.progress.toDouble() ?? 0.0;
    progressPercentage /= 100;
    String progressvalue = progressPercentage != null
        ? progressPercentage == 0.0
            ? '0'
            : (progressPercentage * 100).toStringAsFixed(0)
        : '0';
    progressvalue = progressvalue == 'NaN' ? '0' : progressvalue;
    return progressvalue;
  }

  _fetchQuestionsForExam(BuildContext context, String examId) async {
    try {
      ExamCubit _examCubit = BlocProvider.of<ExamCubit>(context);

      bool _hasAccess = await _checkAccessToFetchQuestions();

      if (_hasAccess) {
        //    _sectionDetailsCubit.setLoading(true);
        await _examCubit.fetchQuestions(examId);
        _isDataLoading.value = false;
        // _sectionDetailsCubit.setLoading(false);

        ExamState state = _examCubit.state;

        if (state is ExamsQuestionsFailureState) {
          bool _shouldShowPopUp = checkForActiveRoute(context,
              webCurrentRouteName: '/subject-tab-view',
              mobileCurrentRouteName: SubjectsDetailedTabViewRoute.name);
          if (_shouldShowPopUp) {
            showPGExceptionPopup(context, state.error);
          }
        } else {
          if (_examCubit.state.examData.isNotEmpty) {
            _questionData = _examCubit.state.examData.first;
          }
          _questions = _examCubit.state.examData.isEmpty
              ? []
              : _examCubit.state.examData.first.questions;

          DateTime currentTime = DateTime.now();
          DateTime startTime = _questionData?.startTime ?? currentTime;
          DateTime endTime = _questionData?.endTime ?? currentTime;

          if ((currentTime.isAfter(startTime) &&
                  currentTime.isBefore(endTime)) ||
              (currentTime.isAtSameMomentAs(startTime))) {
            // check if the exam is valid
            if (_questions.isNotEmpty) {
              _navigationInfo(context);
            } else {
              // no questions available for the exam
              _showQuestionsEmptyPopup(context);
            }
          } else {
            // not valid
            // exam expired
            // TO DO: remove after- modify list tile UI and show as invalid
            if (currentTime.isBefore(startTime)) {
              String startTimeFormatted =
                  AppDateFormatter().formatForExamAvailabilityPopup(startTime);
              _showInvalidExamPopup(context,
                  '${SLStrings.getTranslatedString(KEY_EXAM_AVAILABLE_POPUP)} $startTimeFormatted');
            } else {
              _showInvalidExamPopup(
                  context, SLStrings.getTranslatedString(KEY_EXAM_EXPIRED));
            }
          }
        }
      } else {
        bool _shouldShowPopUp = checkForActiveRoute(context,
            webCurrentRouteName: '/section-details',
            mobileCurrentRouteName: SectionDetailsViewRoute.name);
        if (_shouldShowPopUp) {
          showNoAccessPopup(context, '');
        }
      }
    } on Exception catch (e) {
      debugPrint('[section_details_view][_fetchQuestionsForExam]: $e');
    }
  }

  Future<ResourceProgress?> _fetchCourseProgress(
      BuildContext context, String instanceId) async {
    await _sectionDetailsCubit?.getCourseprogress(instanceId);
    SectionDetailsState state =
        _sectionDetailsCubit?.state ?? const SectionDetailsInitial();

    if (state is GetCourseProgressState) {
      ResourceProgress courseProgress = state.progress;
      return courseProgress;
    } else if (state is GetCourseProgressFailureState) {
      ResourceProgress courseProgress = ResourceProgress(
          progress: 0.0,
          timeSpent: '',
          instanceId: instanceId,
          markedAsDone: false);
      bool isActiveRouteInWeb = false;
      if (kIsWeb) {
        var beamerRouter = Beamer.of(context);
        isActiveRouteInWeb = beamerRouter.active;
      }

      bool isThereCurrentDialogShowing = kIsWeb
          ? isActiveRouteInWeb
          : ModalRoute.of(context)?.isCurrent != true;
      if (!isThereCurrentDialogShowing) {
        showPGExceptionPopup(context, state.error);
      }
      return courseProgress;
    }
    // } else {
    //   bool _shouldShowPopUp = checkForActiveRoute(context,
    //       webCurrentRouteName: '/section-details',
    //       mobileCurrentRouteName: SectionDetailsViewRoute.name);
    //   if (_shouldShowPopUp) {
    //     showNoAccessPopup(context, '');
    //   }
    // }
    return null;
  }

  Future<bool> _checkAccessToFetchQuestions() async {
    // decides whether to disable/enable attend  button
    String screen = PrivilegeAccessConsts.SCREEN_EXAM;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_GET_QUIZ_QUESTIONS;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _navigationInfo(BuildContext context) async {
    try {
      kIsWeb
          ? Beamer.of(context).beamToNamed('/exam-intro', data: {
              'questionData': _questionData!,
              'isFromSectionDetails': true
            })
          : appRouter.push(ExamIntroductionViewRoute(
              questionData: _questionData!,
              isFromSectionDetails: true,
              navigateBackTo: NavigationTo.subjects_tab_view,
            ));
    } on Exception catch (e) {
      debugPrint("exception => _navigationInfo => $e");
    }
  }

  _showInvalidExamPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: msg,
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showQuestionsEmptyPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        // ignore: lines_longer_than_80_chars
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: SLStrings.getTranslatedString(KEY_QUESTION_NOT_AVAILABLE),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              return;
            }
          },
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            alertType: AlertType.error,
            onContinueTap: () =>
                kIsWeb ? Navigator.of(context).pop() : appRouter.popForced(),
            onCancelTap: () {
              kIsWeb
                  ? Beamer.of(context).beamToNamed('/subject-tab-view')
                  : appRouter.popUntil((route) =>
                      route.settings.name == SubjectsDetailedTabViewRoute.name);
            },
            cancelBtnText: SLStrings.getTranslatedString(KEY_OK),
            confirmBtnText: '',
          ),
        );
      },
    );
  }

  void _showAlertDialog(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: msg,
          iconWidget: const Icon(Icons.error, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }
}

class CardView extends StatelessWidget {
  final String title;
  final Widget leadingWidget;
  final Widget trailingWidget;
  final int index;
  final VoidCallback onTapCallback;

  const CardView(
      {super.key,
      required this.title,
      required this.leadingWidget,
      required this.trailingWidget,
      required this.index,
      required this.onTapCallback});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTapCallback(),
      child: Card(
        color: AppTheme.secondaryBlue,
        elevation: 2.0,
        margin: const EdgeInsets.only(bottom: 15),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 18.0, horizontal: 18),
          alignment: Alignment.center,
          child: Row(
            children: [
              leadingWidget,
              const SizedBox(width: 10),
              Text(
                title,
                style: LMSFonts.mediumFont(18, Colors.black, 1.0),
              ),
              Expanded(child: Container()),
              trailingWidget
            ],
          ),
        ),
      ),
    );
  }
}

class EmptyTabView extends StatelessWidget {
  final String tabItem;

  const EmptyTabView({super.key, required this.tabItem});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      // height: MediaQuery.of(context).size.height,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            //image widget
            Image.asset(
              '$ASSETS_PATH/no-data.png',
            ),
            const SizedBox(height: 20),
            Text(
              tabItem,
              textAlign: TextAlign.center,
              style: LMSFonts.regularFontWithHeight(
                  17, AppTheme.primaryTextColorBlack, 1.2),
            ),
            SizedBox(height: MediaQuery.of(context).size.height * 0.1),
          ],
        ),
      ),
    );
  }
}
