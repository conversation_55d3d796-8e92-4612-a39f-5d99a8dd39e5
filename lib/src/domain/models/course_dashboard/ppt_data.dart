class PPTData {
  int? id;
  String pptindex; // Changed from 'index' to 'pptindex'
  String url;
  String checkpointId;

  PPTData({
    this.id,
    required this.pptindex, // Changed from 'index' to 'pptindex'
    required this.url,
    required this.checkpointId,
  });

  PPTData.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        pptindex = json['pptindex'], // Changed from 'index' to 'pptindex'
        url = json['url'],
        checkpointId = json['checkpoint_id'];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pptindex': pptindex, // Changed from 'index' to 'pptindex'
      'url': url,
      'checkpoint_id': checkpointId,
    };
  }
}
