import '/src/config/enums/question_answer_type.dart';

import '/src/domain/models/exam_summary.dart';
import 'package:equatable/equatable.dart';

import '../../config/enums/question_status.dart';
import 'package:html/parser.dart';

class QuestionData {
  final int id;
  final String examId;
  String quizAttemptId;
  final String examName;
  final String intro;
  final int duration;
  final DateTime endTime;
  final double passMark;
  final String mainTopic;
  final DateTime startTime;
  final double totalMark;
  final List<Question> questions;
  final int numOfQuestions;
  String networkStatus;
  int examUploadStatus;
  int examCompletionStatus;

  QuestionData({
    required this.id,
    required this.quizAttemptId,
    required this.examId,
    required this.examName,
    required this.intro,
    required this.duration,
    required this.endTime,
    required this.passMark,
    required this.mainTopic,
    required this.startTime,
    required this.totalMark,
    required this.questions,
    required this.numOfQuestions,
    required this.networkStatus,
    required this.examUploadStatus,
    required this.examCompletionStatus,
  });

  /// parse data from server
  factory QuestionData.fromJson(Map<String, dynamic> json, String examId) {
    List<Question> questionList = json["quest_answers"] != null
        ? List<Question>.from(
            json["quest_answers"].map((x) => Question.fromJson(x)))
        : [];
    questionList.sort((a, b) => a.id.compareTo(b.id));

    return QuestionData(
        id: int.parse((json['id'] ?? 1).toString()),
        quizAttemptId: json['quiz_attempt_id'] ?? '',
        examId: json["exam_id"] ?? examId,
        examName: json["name"] ?? '',
        intro: json["description"] ?? '',
        duration: int.parse((json["duration"] ?? '0').toString()),
        startTime: json["start_time"] != null
            ? DateTime.parse(json["start_time"]).toLocal()
            : DateTime.now(),
        endTime: json["end_time"] != null
            ? DateTime.parse(json["end_time"]).toLocal()
            : DateTime.now(),
        passMark: double.parse((json["pass_mark"] ?? '0.0').toString()) * 1.0,
        mainTopic: json["main_topic"] ?? '',
        totalMark: double.parse((json["total_mark"] ?? '0.0').toString()) * 1.0,
        questions: json["quest_answers"] != null ? questionList : [],
        numOfQuestions: int.parse((json["num_of_questions"] ?? '0').toString()),
        networkStatus: json['network_status'] ?? 'true',
        examUploadStatus: json['exam_upload_status'] ?? 0,
        examCompletionStatus: json['exam_completed_status'] ?? 0);
  }

  /// parse data from local db
  factory QuestionData.fromDBJson(Map<String, dynamic> json, String examId) {
    List<Question> questionList = json["quest_answers"] != null
        ? List<Question>.from(
            json["quest_answers"].map((x) => Question.fromDBJson(x)))
        : [];
    questionList.sort((a, b) => a.id.compareTo(b.id));

    return QuestionData(
        id: int.parse((json['id'] ?? 1).toString()),
        quizAttemptId: json['quiz_attempt_id'] ?? '',
        examId: json["exam_id"] ?? examId,
        examName: json["exam_name"] ?? '',
        intro: json["description"] ?? '',
        duration: int.parse((json["duration"] ?? '0').toString()),
        startTime: json["start_time"] != null
            ? DateTime.parse(json["start_time"])
            : DateTime.now(),
        endTime: json["end_time"] != null
            ? DateTime.parse(json["end_time"])
            : DateTime.now(),
        passMark: double.parse((json["pass_mark"] ?? '0.0').toString()) * 1.0,
        mainTopic: json["main_topic"] ?? '',
        totalMark: double.parse((json["total_mark"] ?? '0.0').toString()) * 1.0,
        questions: json["quest_answers"] != null ? questionList : [],
        numOfQuestions: int.parse((json["num_of_questions"] ?? '0').toString()),
        networkStatus: json['network_status'] ?? 'true',
        examUploadStatus: json['exam_upload_status'] ?? 0,
        examCompletionStatus: json['exam_completed_status'] ?? 0);
  }

  /// parse data from server
  factory QuestionData.fromExamSummary(ExamSummary examSummary) {
    List<Question> questionList = examSummary.questions;
    questionList.sort((a, b) => a.id.compareTo(b.id));

    return QuestionData(
        id: 1,
        quizAttemptId: '',
        examId: examSummary.questions.first.quizId ?? '',
        examName: examSummary.examName,
        intro: examSummary.description,
        duration: examSummary.duration,
        startTime: examSummary.startTime,
        endTime: examSummary.endTime,
        passMark: examSummary.passMark,
        mainTopic: examSummary.mainTopic,
        totalMark: examSummary.totalMark,
        questions: questionList,
        numOfQuestions: examSummary.numOfQuestions,
        networkStatus: 'true',
        examUploadStatus: 1,
        examCompletionStatus: 1);
  }
  Map<String, dynamic> toJson() => {
        "id": id,
        "quiz_attempt_id": quizAttemptId,
        "exam_id": examId,
        "exam_name": examName,
        "description": intro,
        "duration": duration,
        "end_time": endTime.toIso8601String(),
        "pass_mark": passMark,
        "main_topic": mainTopic,
        "start_time": startTime.toIso8601String(),
        "total_mark": totalMark,
        "quest_answers": List<dynamic>.from(questions.map((x) => x.toJson())),
        "num_of_questions": numOfQuestions,
        'network_status': networkStatus,
        'exam_upload_status': examUploadStatus,
        'exam_completed_status': examCompletionStatus,
      };
}

class Question extends Equatable {
  final int id;
  final String? questionId;
  final String? questionName;
  final String? questionText;
  final String? quizId;
  final double? penalty;
  final double? defaultMark;
  final double? markScored;
  QuestionStatus? questionsStatus;
  final String chosenAnswer;
  final String correctAnswer;
  final bool didAnswerCorrectly;
  final List<Answer> options;

  List<int> selectedAnswerIndexes = [];
  List<String> selectedAnswerIds = [];
  final String questionType;

  Question({
    this.id = 0,
    this.questionId,
    this.questionName,
    this.questionText,
    required this.quizId,
    required this.options,
    this.penalty,
    this.defaultMark,
    this.markScored,
    this.questionsStatus,
    required this.chosenAnswer,
    required this.correctAnswer,
    required this.didAnswerCorrectly,
    required this.selectedAnswerIndexes,
    required this.selectedAnswerIds,
    required this.questionType,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    List<dynamic> jsonList = json['answers'] ?? [];
    jsonList
        .removeWhere((element) => element == null || element["answer"] == '');
    List<Answer> answers = jsonList.isNotEmpty
        ? jsonList.map((item) => Answer.fromJson(item)).toList()
        : [];
    answers.sort((a, b) => a.id.compareTo(b.id));

    jsonList = json['selected_answer_indexes'] ?? [];
    List<int> selectedIndexes = jsonList.isNotEmpty
        ? jsonList.toList().map((item) => int.parse(item.toString())).toList()
        : [];
    jsonList = json['selected_answer_ids'] ?? [];
    List<String> selectedIds = jsonList.isNotEmpty
        ? jsonList.map((item) => item.toString()).toList()
        : [];

    List<Answer> chosenAnswersByStudent = answers
        .where((element) => selectedIds.contains(element.answerId))
        .toList();
    String chosenAnswer = '';

    for (var element in chosenAnswersByStudent) {
      chosenAnswer = chosenAnswer.isNotEmpty
          ? chosenAnswer + ', ' + element.answerVal
          : element.answerVal;
    }

    List<Answer> correctAnswersofQstn =
        answers.where((element) => element.correctAnswer).toList();
    String correctAnswer = '';
    List<String> correctANswerIds = [];

    for (var element in correctAnswersofQstn) {
      correctAnswer = correctAnswer.isNotEmpty
          ? correctAnswer + ', ' + element.answerVal
          : element.answerVal;
      correctANswerIds.add(element.answerId);
    }

    double defaultMark = (json["default_mark"] ?? 0) * 1.0;
    double markScored = (json["mark"] ?? 0) * 1.0;

    bool didAnswerCorrectly = markScored >= defaultMark;
    // correctANswerIds.toSet().intersection(selectedIds.toSet()).isNotEmpty;
    int qstnNo = json['question_slot'] ?? json['id'] ?? 0;

    bool isHTML = json["question_type"] == QuestAnswerType.html.value;
    String question = isHTML
        ? /*'<p> $qstnNo.&nbsp;</p>' +*/ (json['question_text'] ?? '')
        : /*qstnNo.toString() + '. ' +*/ (json['question_text'] ?? '');

    String parsedString = question;

    if (parsedString.isNotEmpty && !isHTML) {
      final document = parse(parsedString);
      parsedString =
          parse(document.body?.text).documentElement?.text ?? parsedString;
    }
    return Question(
        id: qstnNo,
        questionId: json['question_id'] ?? '',
        questionName: json["name"],
        questionText: isHTML
            ? question // '<div style="justify-content: start;"> $question </div>'
            : parsedString,
        quizId: json["quiz_id"] ?? '',
        options: answers,
        penalty: (json["penalty"] ?? 0) * 1.0,
        defaultMark: defaultMark,
        markScored: markScored,
        questionsStatus: json['questions_status'] != null
            ? stringToQuestStatus(json['questions_status'])
            : QuestionStatus.unAnswered,
        chosenAnswer: chosenAnswer,
        correctAnswer: correctAnswer,
        didAnswerCorrectly: didAnswerCorrectly,
        selectedAnswerIndexes: selectedIndexes,
        selectedAnswerIds: selectedIds,
        questionType: json["question_type"] ?? '');
  }

  factory Question.fromDBJson(Map<String, dynamic> json) {
    List<dynamic> jsonList = json['answers'] ?? [];
    jsonList
        .removeWhere((element) => element == null || element["answer"] == '');

    jsonList.toSet().toList();
    List<Answer> answers = jsonList.isNotEmpty
        ? jsonList.map((item) => Answer.fromJson(item)).toList()
        : [];
    answers.sort((a, b) => a.id.compareTo(b.id));

    jsonList = json['selected_answer_indexes'] ?? [];
    List<int> selectedIndexes = jsonList.isNotEmpty
        ? jsonList.toList().map((item) => int.parse(item.toString())).toList()
        : [];
    jsonList = json['selected_answer_ids'] ?? [];
    List<String> selectedIds = jsonList.isNotEmpty
        ? jsonList.map((item) => item.toString()).toList()
        : [];

    List<Answer> chosenAnswersByStudent = answers
        .where((element) => selectedIds.contains(element.answerId))
        .toList();
    String chosenAnswer = '';

    for (var element in chosenAnswersByStudent) {
      chosenAnswer = chosenAnswer + ', ' + element.answerVal;
    }

    List<Answer> correctAnswersofQstn =
        answers.where((element) => element.correctAnswer).toList();
    String correctAnswer = '';

    List<String> correctANswerIds = [];

    for (var element in correctAnswersofQstn) {
      correctAnswer = correctAnswer.isNotEmpty
          ? correctAnswer + ', ' + element.answerVal
          : element.answerVal;
      correctANswerIds.add(element.answerId);
    }

    bool didAnswerCorrectly =
        correctANswerIds.toSet().intersection(selectedIds.toSet()).isNotEmpty;

    bool isHTML = (json["question_type"] ?? "") == QuestAnswerType.html.value;
    String parsedString = json['question_text'] ?? '';

    if (parsedString.isNotEmpty && !isHTML) {
      final document = parse(parsedString);
      parsedString =
          parse(document.body?.text).documentElement?.text ?? parsedString;
    }
    return Question(
      id: json['id'] ?? 0,
      questionId: json['question_id'] ?? '',
      questionName: json["question_name"],
      questionText: parsedString,
      quizId: json["quiz_id"] ?? '',
      options: answers,
      penalty: (json["penalty"] ?? 0) * 1.0,
      defaultMark: (json["default_mark"] ?? 0) * 1.0,
      questionsStatus: json['questions_status'] != null
          ? stringToQuestStatus(json['questions_status'])
          : QuestionStatus.unAnswered,
      chosenAnswer: chosenAnswer,
      correctAnswer: correctAnswer,
      didAnswerCorrectly: didAnswerCorrectly,
      selectedAnswerIndexes: selectedIndexes,
      selectedAnswerIds: selectedIds,
      questionType: json["question_type"] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "question_id": questionId,
        "question_name": questionName,
        "question_text": questionText,
        "answers": List<dynamic>.from(options.map((x) => x.toJson())),
        "quiz_id": quizId,
        "selected_answer_indexes": selectedAnswerIndexes,
        "selected_answer_ids": selectedAnswerIds,
        "questions_status": questionsStatus?.value,
        "default_mark": defaultMark,
        "penalty": penalty,
        "question_type": questionType
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [questionText, options];
  }
}

class Answer extends Equatable {
  final int id;
  final String answerId;
  final String answerVal;
  final String orgId;
  final double fraction;
  final bool correctAnswer;

  // final String ansFormat;
  // final DateTime createdAt;
  // final DateTime updatedAt;
  // final String questionId;
  final AnswerStatus answerStatus;
  final String answerType;

  const Answer(
      {required this.id,
      required this.answerId,
      required this.answerVal,
      required this.orgId,
      required this.fraction,
      required this.correctAnswer,
      // required this.ansFormat,
      // required this.createdAt,
      // required this.updatedAt,
      // required this.questionId,
      required this.answerStatus,
      required this.answerType});
  @override
  bool get stringify => true;

  factory Answer.fromJson(Map<String, dynamic> json) {
    bool isPlainText =
        (json["question_type"] ?? '') == QuestAnswerType.plainText.value;
    String parsedString = json["answer"] ?? '';

    if (parsedString.isNotEmpty && isPlainText) {
      final document = parse(parsedString);
      parsedString =
          parse(document.body?.text).documentElement?.text ?? parsedString;
    }

    return Answer(
      id: json["slot"] ?? 0,
      answerId: json["answer_id"] ?? '',
      answerVal: json["answer"] ?? '',
      orgId: json["org_id"] ?? '',
      fraction: json["fraction"] * 1.0 ?? 0.0,
      correctAnswer: json["is_correct_answer"] ?? false,
      // ansFormat: json["ans_format"],
      // createdAt: DateTime.parse(json["created_at"]),
      // updatedAt: DateTime.parse(json["updated_at"]),
      // questionId: json["question_id"],
      answerStatus:
          json["fraction"] > 0 ? AnswerStatus.right : AnswerStatus.wrong,
      answerType: json["answer_type"] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'slot': id,
        "answer_id": answerId,
        "answer": answerVal,
        "org_id": orgId,
        "fraction": fraction,
        "is_correct_answer": correctAnswer,
        // "ans_format": ansFormat,
        // "created_at": createdAt.toIso8601String(),
        // "updated_at": updatedAt.toIso8601String(),
        // "question_id": questionId,
        "answer_status":
            fraction > 0 ? AnswerStatus.right.value : AnswerStatus.wrong.value,
        "answer_type": answerType
      };

  @override
  List<Object?> get props {
    return [answerVal, answerStatus, correctAnswer];
  }
}
