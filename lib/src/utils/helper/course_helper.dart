import '/src/domain/models/course_dashboard/courses_info.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/app_config/preference_config.dart';
import '../constants/strings.dart';

saveSelectedCourse(CoursesInfo courseInfo) async {
  SharedPreferences _prefs = await SharedPreferences.getInstance();
  COURSE_ID = courseInfo.courseId;
  COURSE_NAME = courseInfo.courseName;
  _prefs.setString(PREF_KEY_COURSE_ID, COURSE_ID);
  _prefs.setString(PREF_KEY_COURSE_NAME, COURSE_NAME);
}
