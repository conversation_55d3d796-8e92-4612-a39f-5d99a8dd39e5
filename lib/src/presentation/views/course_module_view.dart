import 'package:auto_route/auto_route.dart';

import '../../utils/constants/strings.dart';
import '/src/domain/models/course_details.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';
import '../widgets/app_bar.dart';

@RoutePage()
class CourseModuleViewScreen extends HookWidget {
  final CourseModule courseModule;

  const CourseModuleViewScreen({super.key, required this.courseModule});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
              title: courseModule.moduleName,
              leadingIconName: BACK_ARROW,
              trailingWidget: Container(),
              leadingBtnAction: () => appRouter.pop())),
    );
  }
}
