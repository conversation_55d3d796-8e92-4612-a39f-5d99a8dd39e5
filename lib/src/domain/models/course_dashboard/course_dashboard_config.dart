import '../../../../src/domain/models/course_dashboard/course_assignments.dart';

class DashboardConfig {
  final Exams exams;
  final String orgId;
  final String status;
  final Subjects subjects;
  final String? courseId;
  final Assignments assignments;
  final CurrentAffairsData currentAffairs;
  final GeneralResources generalResources;

  DashboardConfig({
    required this.exams,
    required this.orgId,
    required this.status,
    required this.subjects,
    this.courseId,
    required this.assignments,
    required this.currentAffairs,
    required this.generalResources,
  });

  factory DashboardConfig.fromJson(Map<String, dynamic> json) {
    return DashboardConfig(
      exams: Exams.fromJson(json['exams']),
      orgId: json['org_id'],
      status: json['status'],
      subjects: Subjects.fromJson(json['subjects']),
      courseId: json['course_id'],
      assignments: Assignments.fromJson(json['assignments']),
      currentAffairs: CurrentAffairsData.from<PERSON>son(json['current_affairs']),
      generalResources: GeneralResources.fromJson(json['general_resources']),
    );
  }
}

class Exams {
  final int showExam;
  final String componentType;

  Exams({required this.showExam, required this.componentType});

  factory Exams.fromJson(Map<String, dynamic> json) {
    return Exams(
      showExam: json['show_exam'] ?? 0,
      componentType: json['component_type'] ?? '',
    );
  }
}

class Subjects {
  final List<dynamic> subjects;
  final int noOfItems;
  final String componentType;

  Subjects({
    required this.subjects,
    required this.noOfItems,
    required this.componentType,
  });

  factory Subjects.fromJson(Map<String, dynamic> json) {
    return Subjects(
      subjects: json['subjects'] ?? 0,
      noOfItems: json['no_of_items'] ?? 0,
      componentType: json['component_type'] ?? '',
    );
  }
}

class Assignments {
  final List<dynamic> assignments;
  final int noOfItems;
  final String componentType;

  Assignments({
    required this.assignments,
    required this.noOfItems,
    required this.componentType,
  });

  factory Assignments.fromJson(Map<String, dynamic> json) {
    return Assignments(
      assignments: json['assignments'] ?? [],
      noOfItems: json['no_of_items'] ?? 0,
      componentType: json['component_type'] ?? "",
    );
  }
}

class CurrentAffairsData {
  final int noOfItems;
  final String componentType;

  CurrentAffairsData({
    required this.noOfItems,
    required this.componentType,
  });

  factory CurrentAffairsData.fromJson(Map<String, dynamic> json) {
    return CurrentAffairsData(
      noOfItems: json['no_of_items'] ?? 0,
      componentType: json['component_type'] ?? '',
    );
  }
}

class GeneralResources {
  final List<Resource> resources;
  final int noOfItems;
  final String componentType;

  GeneralResources({
    required this.resources,
    required this.noOfItems,
    required this.componentType,
  });

  factory GeneralResources.fromJson(Map<String, dynamic> json) {
    return GeneralResources(
      resources: json['resources'] == null
          ? []
          : (json['resources'] as List)
              .map((e) => Resource.fromJson(e))
              .toList(),
      noOfItems: json['no_of_items'] ?? 0,
      componentType: json['component_type'] ?? '',
    );
  }
}

class Resource {
  final int? pageCount;
  final String resourceId;
  final String externalUrl;
  final String resourceName;
  final ResourceType resourceType;
  final String thumbnailUrl;
  final String fileExtension;

  Resource({
    this.pageCount,
    required this.resourceId,
    required this.externalUrl,
    required this.resourceName,
    required this.resourceType,
    required this.thumbnailUrl,
    required this.fileExtension,
  });

  factory Resource.fromJson(Map<String, dynamic> json) {
    return Resource(
      pageCount: json['page_count'],
      resourceId: json['resource_id'],
      externalUrl: json['external_url'],
      resourceName: json['resource_name'],
      resourceType:
          resourceTypeValues.map[json['resource_type'].toLowerCase()] ??
              ResourceType.FILE,
      thumbnailUrl: json['thumbnail_url'],
      fileExtension: json['file_extension'],
    );
  }
}
