import '/src/domain/models/resource_comment.dart';
import 'package:equatable/equatable.dart';
import 'package:floor/floor.dart';

import '../../utils/constants/strings.dart';
import 'source.dart';

class SLUser extends Equatable {
  static final SLUser _singleton = SLUser._internal();

  final int? id;

  final String? email;
  String first_name = '';
  String last_name = '';
  String avatar_url = '';
  final String? bio;
  final String? phonenumber1;
  final String? phonenumber2;
  final String? type;
  final String? status;
  final RoleName? userRole;

  SLUser(
      {this.id,
      this.email,
      this.first_name = '',
      this.last_name = '',
      this.avatar_url = '',
      this.bio,
      this.phonenumber1,
      this.phonenumber2,
      this.type,
      this.userRole,
      this.status});

  SLUser._internal(
      {this.id,
      this.email,
      this.bio,
      this.phonenumber1,
      this.phonenumber2,
      this.type,
      this.userRole,
      this.status});

  static SLUser get shared => _singleton;

  factory SLUser.fromMap(Map<String, dynamic> map) {
    return SLUser(
      id: map['id'] as int,
      email: map['email'] != null ? map['email'] as String : null,
      first_name: map['first_name'] != null ? map['first_name'] as String : '',
      last_name: map['last_name'] != null ? map['last_name'] as String : '',
      avatar_url: map['avatar_url'] != null ? map['avatar_url'] as String : '',
      bio: map['bio'] != null ? map['bio'] as String : null,
      phonenumber1:
          map['phonenumber1'] != null ? map['phonenumber1'] as String : null,
      phonenumber2:
          map['phonenumber2'] != null ? map['phonenumber2'] as String : null,
      type: map['type'] != null ? map['type'] as String : null,
      status: map['status'] != null ? map['status'] as String : null,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      id,
      email,
      first_name,
      last_name,
      avatar_url,
      bio,
      phonenumber1,
      phonenumber2,
      type,
      status
    ];
  }
  SLUser copyWith({
    int? id,
    String? email,
    String? first_name,
    String? last_name,
    String? avatar_url,
    String? bio,
    String? phonenumber1,
    String? phonenumber2,
    String? type,
    String? status,
    RoleName? userRole,
  }) {
    return SLUser(
      id: id ?? this.id,
      email: email ?? this.email,
      first_name: first_name ?? this.first_name,
      last_name: last_name ?? this.last_name,
      avatar_url: avatar_url ?? this.avatar_url,
      bio: bio ?? this.bio,
      phonenumber1: phonenumber1 ?? this.phonenumber1,
      phonenumber2: phonenumber2 ?? this.phonenumber2,
      type: type ?? this.type,
      status: status ?? this.status,
      userRole: userRole ?? this.userRole,
    );
  }
}
