import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../utils/helper/app_date_formatter.dart';

part 'exam_clock_state.dart';

class ExamClockCubit extends Cubit<ExamClockState> {
  ExamClockCubit()
      : super(ExamClockInitial(
            currentTime:
                AppDateFormatter().formatToDateTimeString(DateTime.now())));

  Timer? _examTimer;
  String _examStartTimeString = '';

  /// get current date time like clock
  updateCurrentTime() {
    _examTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _examStartTimeString =
          AppDateFormatter().formatToDateTimeString(DateTime.now());
      emit(ExamCurrentTime(currentTime: _examStartTimeString));
    });
  }

  cancelTimer() {
    _examTimer?.cancel();
    emit(ExamCurrentTime(currentTime: _examStartTimeString));
  }
}
