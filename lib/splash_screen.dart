import 'package:SmartLearn/src/presentation/cubits/app_config/app_config_cubit.dart';

import '/src/presentation/cubits/course_dashboard/course_dashboard_cubit.dart';

import '/src/config/themes/app_theme.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '/src/presentation/cubits/organization/organization_cubit.dart';

import '/src/presentation/cubits/profile/profile_cubit.dart';
import '/src/presentation/cubits/splash_screen/splash_screen_cubit.dart';
import '/src/utils/constants/strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'src/config/app_config/api_config.dart';
import 'src/config/app_config/preference_config.dart';
import 'src/config/router/app_router.dart';
import 'src/domain/models/organization.dart';
import 'src/utils/constants/boolean.dart';
import 'src/utils/constants/helper.dart';

@RoutePage()
class SplashScreen extends HookWidget {
  bool isLoading = true;
  bool isTokenExpired = false;
  Future? futureInst;

  @override
  Widget build(BuildContext context) {
    final _userCubit = BlocProvider.of<UserCubit>(context);
    final _splashScreenCubit = BlocProvider.of<SplashScreenCubit>(context);
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(seconds: 3), () {
        if (!didShowSplashScreen) {
          didShowSplashScreen = true;
          if (USER_ID.isNotEmpty) {
            _checkNavigation(context);
          } else {
            // no user id
            if (didShowWelcomeScreens) {
              kIsWeb
                  ? Beamer.of(context).beamToReplacementNamed('/login-view')
                  : appRouter.push(
                      LoginEmailViewRoute(isTokenExpired: isTokenExpired));
            } else {
              if (!kIsWeb) {
                appRouter.push(OnboardingViewRoute());
              } else {
                Beamer.of(context).beamToReplacementNamed('/onboarding');
              }
            }
          }
        }
      });
    });

    useEffect(() {
      _splashScreenCubit.setLoading(true);

      ///
      /// assigning the future to an instance to avoid
      /// repeat calling of the method
      /// when directly assign the '_userCubit.getUserInfo(USER_ID)'
      /// method to future field of future builder,
      /// it calls the method every time app reloads
      ///
      Future<void>.microtask(() async {
        try {
          SharedPreferences _prefs = await SharedPreferences.getInstance();
          bool _didUserSignedOutManually =
              _prefs.getBool(PREF_KEY_SIGNOUT) ?? false;
          bool _didAppLaunchForFirstTime =
              _prefs.getBool(PREF_KEY_FIRST_LAUNCH) ?? true;
          didShowWelcomeScreens =
              _prefs.getBool(PREF_KEY_SHOW_WELCOME_SCREEN) ?? false;
          _prefs.setBool(PREF_KEY_FROM_SECTION_DETAILS, false);
          _prefs.setBool(PREF_KEY_FROM_SUBJECT_TAB_VIEW, false);
          await _splashScreenCubit.checkTokenAndRefresh();
          SplashScreenState state = _splashScreenCubit.state;
          if (state is UserAuthenticated) {
            if (state.isAuthenticated) {
              await _splashScreenCubit.initUserInfo();
              await Future.delayed(const Duration(seconds: 3), () {
                futureInst = _userCubit.getUserInfo(USER_ID);
                isTokenExpired = false;
              });
              await _splashScreenCubit.getPrivilegeAccessList();
            } else {
              if (_didAppLaunchForFirstTime || _didUserSignedOutManually) {
                isTokenExpired = false;
              } else {
                await clearCurrentUserInfo();
                isTokenExpired = true;
              }
            }
          }

          if (!kIsWeb) {
            await _splashScreenCubit.initCurrentLanguageStatus(context);
            await _appConfigCubit.fetchAppBrandingConfig(context);
          }
        } on Exception catch (e) {
          // TODO
        }
      });

      return null;
    }, []);

    return Scaffold(
      backgroundColor: AppTheme.viewResultButtonColor,
      body: BlocBuilder<SplashScreenCubit, SplashScreenState>(
          builder: (context, state) {
        return Stack(
          children: [
            kIsWeb
                ? Center(
                    child: Container(
                      decoration: const BoxDecoration(
                        color: AppTheme.viewResultButtonColor,
                      ),
                      child: Image.asset(
                        '$ASSETS_PATH/web-splash.png',
                        height: MediaQuery.sizeOf(context).height * 0.75,
                        fit: BoxFit.contain,
                      ),
                    ),
                  )
                : Positioned.fill(
                    child: Container(
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                            image: AssetImage('$ASSETS_PATH/Launch-screen.png'),
                            fit: BoxFit.fill)),
                  )),
            // FutureBuilder(
            //     future:
            //         futureInst ?? Future.delayed(const Duration(seconds: 5)),
            //     builder: (context, snapshot) {
            //       if (snapshot.connectionState == ConnectionState.waiting) {
            //         return Container();
            //       } else {
            //         if (!didShowSplashScreen) {
            //           didShowSplashScreen = true;

            //           if (USER_ID.isNotEmpty) {
            //             _checkNavigation(context);
            //           } else {
            //             // no user id
            //             if (didShowWelcomeScreens) {
            //               appRouter.push(LoginEmailViewRoute(
            //                   isTokenExpired: isTokenExpired));
            //             } else {
            //               if (!kIsWeb) {
            //                 appRouter.push(OnboardingViewRoute());
            //               } else {
            //                 Beamer.of(context).beamToNamed('/onboarding');
            //               }
            //             }
            //           }
            //         }
            //       }
            //       return Container();
            //     }),
          ],
        );
      }),
    );
  }

  _checkNavigation(BuildContext context) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    final _userCubit = BlocProvider.of<UserCubit>(context);
    _userCubit.getUserInfo(USER_ID);
    if (USER_ID.isEmpty) {
      debugPrint("USER_ID empty!!![splashscreen][128]");
      // appRouter.push(LoginEmailViewRoute(isTokenExpired: isTokenExpired));
      kIsWeb
          ? Beamer.of(context).beamToReplacementNamed("/login-view",
              data: {'isTokenExpired': isTokenExpired})
          : appRouter
              .replace(LoginEmailViewRoute(isTokenExpired: isTokenExpired));
    } else if (ORG_ID.isNotEmpty) {
      kIsWeb
          ? Beamer.of(context).beamToReplacementNamed('/topic-view')
          : appRouter.push(CourseDashboardRoute(isFromLogin: false));
    } else {
      final organizationsCubit = BlocProvider.of<OrganizationCubit>(context);
      await organizationsCubit.getUserOrganizations();
      OrganizationState state = organizationsCubit.state;
      if (state is OrganizationSuccess) {
        final List<Organization> orgList = state.organizations;
        if (orgList.isEmpty) {
          await _setDefaultOrg(context, _prefs, organizationsCubit);

          /*
          //// old workflow
          ///
          // kIsWeb
          //     ? Beamer.of(context).beamToReplacementNamed('/welcome')
          //     : appRouter.push(const WelcomeViewRoute());
          */
        } else {
          if (orgList.length > 1) {
            kIsWeb
                ? Beamer.of(context)
                    .beamToReplacementNamed('/organization-view')
                : appRouter.push(OrganizationViewRoute());
          } else {
            ///
            /// if user has only one org,
            /// make it as default without allowing selection
            ///
            ORG_ID = orgList.first.orgId ?? "";
            _prefs.setString(PREF_KEY_ORG_ID, ORG_ID);
            kIsWeb
                ? Beamer.of(context).beamToReplacementNamed('/topic-view')
                : appRouter.push(CourseDashboardRoute(isFromLogin: false));
          }
        }
      } else {
        await _setDefaultOrg(context, _prefs, organizationsCubit);

        // kIsWeb
        //     ? Beamer.of(context).beamToReplacementNamed('/welcome')
        //     : appRouter.push(const WelcomeViewRoute());
      }
    }
  }

  _setDefaultOrg(BuildContext context, SharedPreferences _prefs,
      OrganizationCubit organizationsCubit) async {
    ///
    /// if no org is fetched,
    /// set to the static competitor org
    /// do not navigate to welcome screen and let admin the org
    ///
    ORG_ID = APIConfig.defaultOrgId;
    _prefs.setString(PREF_KEY_ORG_ID, ORG_ID);
    await organizationsCubit.setDefaultOrganization(
        ORG_ID, APIConfig.defaultRollId);
    OrganizationState state = organizationsCubit.state;

    if (state is SetDefaultOrganization) {
      kIsWeb
          ? Beamer.of(context).beamToReplacementNamed('/course-dashboard-view')
          : appRouter.push(CourseDashboardRoute(isFromLogin: false));
    }
  }
}
