import 'courseDetailUserStats.dart';
import 'course_assignments.dart';
import 'examPerformanceCatwise.dart';
import 'userCourseProgress.dart';

class CourseDetailsDashboardObj {
  final double totalProgress;
  final double totalMarks;
  final String totalTimeSpent;
  final int totalAchievements;
  final List<CourseAssignments> courseAssignments;
  final List<CourseDetailUserStatiModel> courseWiseUserStats;
  final UserCourseProgress? userCourseProgress;
  final ExamPerformanceCatwise examPerformanceCatwise;

  CourseDetailsDashboardObj(
      {required this.totalProgress,
      required this.totalMarks,
      required this.totalTimeSpent,
      required this.totalAchievements,
      required this.courseAssignments,
      required this.courseWiseUserStats,
      required this.userCourseProgress,
      required this.examPerformanceCatwise});
}
