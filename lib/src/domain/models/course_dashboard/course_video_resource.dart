// To parse this JSON data, do
//
//     final courseVideoResource = courseVideoResourceFromJson(jsonString);

///
/// get video resource info- new env
///

import 'dart:convert';

import '../check_point_data/check_point_data.dart';

CourseVideoResource courseVideoResourceFromJson(String str) =>
    CourseVideoResource.fromJson(json.decode(str));

String courseVideoResourceToJson(CourseVideoResource data) =>
    json.encode(data.toJson());

class CourseVideoResource {
  String id;
  String instanceId;
  String name;
  String length;
  String orgId;
  String courseId;
  List<CheckPoint> checkpoints;
  String description;
  String externalUrl;
  String currentPoint;
  String moduleSource;
  String courseModuleId;
  int numOfCheckpoints;
  bool isRandomCheckpoint;
  bool isCheckpointEnabled;
  bool alwaysShowCheckpoints;

  CourseVideoResource({
    required this.id,
    required this.instanceId,
    required this.name,
    required this.length,
    required this.orgId,
    required this.courseId,
    required this.checkpoints,
    required this.description,
    required this.externalUrl,
    required this.currentPoint,
    required this.moduleSource,
    required this.courseModuleId,
    required this.numOfCheckpoints,
    required this.isRandomCheckpoint,
    required this.isCheckpointEnabled,
    required this.alwaysShowCheckpoints,
  });

  factory CourseVideoResource.fromJson(Map<String, dynamic> json) =>
      CourseVideoResource(
        id: json["id"],
        instanceId: json["instance_id"] ?? '',
        name: json["name"],
        length: json["length"],
        orgId: json["org_id"],
        courseId: json["course_id"],
        checkpoints: [],
        description: json["description"],
        externalUrl: json['url'] ?? json['external_url'] ?? '',
        currentPoint: json["current_point"],
        moduleSource: json["module_source"],
        courseModuleId: json['course_module_id'] ?? "",
        numOfCheckpoints: json['num_of_checkpoints'] ?? 0,
        isRandomCheckpoint: json['is_random_checkpoint'] ?? false,
        isCheckpointEnabled: json['is_checkpoint_enabled'] ?? false,
        alwaysShowCheckpoints: json['always_show_checkpoints'] ?? false,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "length": length,
        "org_id": orgId,
        "course_id": courseId,
        "checkpoints": List<dynamic>.from(checkpoints.map((x) => x.toJson())),
        "description": description,
        "external_url": externalUrl,
        "current_point": currentPoint,
        "module_source": moduleSource,
        "course_module_id": courseModuleId,
        "num_of_checkpoints": numOfCheckpoints,
        "is_random_checkpoint": isRandomCheckpoint,
        "is_checkpoint_enabled": isCheckpointEnabled,
        "always_show_checkpoints": alwaysShowCheckpoints,
      };
}

// class ResCheckpoint {
//   String id;
//   String name;
//   String status;
//   String startTime;

//   ResCheckpoint({
//     required this.id,
//     required this.name,
//     required this.status,
//     required this.startTime,
//   });

//   factory ResCheckpoint.fromJson(Map<String, dynamic> json) => ResCheckpoint(
//         id: json["id"],
//         name: json["name"],
//         status: json["status"],
//         startTime: json["start_time"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "name": name,
//         "status": status,
//         "start_time": startTime,
//       };
// }
