// part of 'course_details_cubit.dart';

// abstract class CourseDetailsState extends Equatable {
//   final CourseDetails? courseDetailsData;
//   const CourseDetailsState({this.courseDetailsData});

//   @override
//   List<Object> get props => [];
// }

// class CourseDetailsInitial extends CourseDetailsState {}

// class CourseDetailsLoading extends CourseDetailsState {
//   final bool isDataLoading;
//   const CourseDetailsLoading({required this.isDataLoading});

//   @override
//   List<Object> get props => [isDataLoading];
// }

// class CourseDetailsFetched extends CourseDetailsState {
//   const CourseDetailsFetched({ super.courseDetailsData});

//   @override
//   List<Object> get props => [];
// }

// class CourseDetailsError extends CourseDetailsState {
//   final String error;
//   const CourseDetailsError({required this.error});

//   @override
//   List<Object> get props => [error];
// }
