import 'dart:io';

import '/src/domain/repositories/user_repository.dart';
import 'package:supabase/supabase.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../utils/constants/helper.dart';
import '/src/config/app_config/preference_config.dart';

import '/src/domain/models/hive_db_models/hive_video_player.dart';
import '/src/domain/services/hive_db_service.dart';

import '/src/domain/models/milestone_data.dart';
import '/src/domain/models/videoplayer_data.dart';
import '/src/domain/services/db_helper.dart';
import 'dart:async';

import '/src/utils/constants/strings.dart';
import '/src/utils/helper/thumbnail_generator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../domain/models/course.dart';
import '../../../domain/models/course_details.dart';
import '../../../domain/models/resource_comment.dart';
import '../../../domain/repositories/course_respository.dart';

part 'course_list_state.dart';

class CourseListCubit extends Cubit<CourseListState> {
  final CourseRepository _courseRepository;
  final HiveDbHelper _hiveDbHelper;
  final UserRepository _userRepository;
  CourseListCubit(
      this._courseRepository, this._hiveDbHelper, this._userRepository)
      : super(CourseListInitial());

  String topicId = '';
  String orgId = '';
  List<DateTime> months = [];

  Map<String, dynamic> videoPlayerData = {
    "lastWatchTime": 0,
    "checkPointIndex": 0,
  };

  initCourseCubit() {
    emit(CourseListInitial());
  }

  Future<void> getUserCourse() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    topicId = _prefs.getString(PREF_KEY_TOPIC_ID) ?? TOPIC_ID;
    orgId = _prefs.getString(PREF_KEY_ORG_ID) ?? ORG_ID;

    //bool networkStatus = _prefs.getBool("networkStatus") ?? false;
    emit(await _getUserCourse(false, topicId));
  }

  Future<CourseListState> _getUserCourse(
      bool updateBtnTheme, String topicId) async {
    try {
      if (topicId.isNotEmpty) {
        final course = await _courseRepository.getUserCourse(topicId, orgId);
        return CourseListSuccess(course: course, buttonColor: updateBtnTheme);
      } else {
        return CourseListError(error: '');
      }
      //course.sort();
    } on Exception catch (e) {
      return CourseListError(error: e.toString());
    }
  }

  Future<void> handleCourseSelection(String? selectedCourseName) async {
    if (selectedCourseName != null && selectedCourseName.isNotEmpty) {
      emit(UpdateSelectedCourseColor(shouldChangeColor: true));
    } else {
      emit(UpdateSelectedCourseColor(shouldChangeColor: false));
    }
  }

  ///
  /// Course Details
  ///
  ///
  fetchCourseDetails(String courseId) async {
    if (courseId.isEmpty) {
      emit(CourseDetailsEmpty(course: state.course));
      return;
    }
    try {
      final courseDetails =
          await _courseRepository.fetchCourseDetailsInfo(courseId);
      if (courseDetails.isNotEmpty) {
        if ((courseDetails.first.courseModule.isEmpty &&
            courseDetails.first.courseVideos.isEmpty &&
            courseDetails.first.currentAffairs.isEmpty &&
            courseDetails.first.exams.isEmpty)) {
          emit(CourseDetailsEmpty(course: state.course));
        } else {
          final _courseDetails =
              await generateVideoThumbnails(courseDetails.first);
          emit(CourseDetailsFetched(
              courseDetailsData: _courseDetails, course: state.course));
        }
      } else {
        emit(CourseDetailsEmpty(course: state.course));
      }
    } on PostgrestException catch (e) {
      if (e.code == 'PGRST301') {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          fetchCourseDetails(courseId);
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      }
    } on SocketException catch (_) {
      emit(CourseDetailsEmpty(course: state.course));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][fetchCourseDetails] :${e.toString()}");
      emit(CourseDetailsEmpty(course: state.course));
    } catch (e) {
      setLoading(false);
      emit(CourseDetailsEmpty(course: state.course));
    }
  }

  Future<CourseDetails> generateVideoThumbnails(
      CourseDetails courseDetails) async {
    try {
      for (var courseVideoArg in courseDetails.courseVideos) {
        String thumbnail = '';
        bool _isYouTubeVideo = courseVideoArg != null &&
            courseVideoArg.videoURL != '' &&
            courseVideoArg.videoURL.startsWith(YOUTUBE_URL);
        if (_isYouTubeVideo) {
          String videoId = extractVideoId(courseVideoArg.videoURL);
          thumbnail = await getThumbnailUrl(videoId);
          courseVideoArg.videoThumbnail = thumbnail;
        }
      }
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][generateVideoThumbnails] :${e.toString()}");
    }
    return courseDetails;
  }

  String getThumbnailUrl(String videoId) {
    if (videoId.contains(HTTPS)) {
      videoId = videoId.replaceAll(HTTPS, '');
      return 'https://img.youtube.com/vi/$videoId/0.jpg';
    } else {
      return 'https://img.youtube.com/vi/$videoId/0.jpg';
    }
  }

  ///
  /// Current affairs for current month
  ///
  fetchMoreCurrentAffairs() async {
    try {
      final currentAffairs = await _courseRepository.fetchMoreCurrentAffairs();
      if (currentAffairs == null || currentAffairs.isEmpty) {
        currentAffairs
            .sort((a, b) => b.publishedDate.compareTo(a.publishedDate));

        emit(CurrentAffairsFailure(error: 'Empty list'));
      } else {
        emit(CurrentAffairsFetched(
            currentAffairs: currentAffairs, course: state.course));
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][fetchMoreCurrentAffairs] :${e.toString()}");
      emit(CurrentAffairsFailure(error: e.toString()));
    }
  }

  setLoading(bool loadStatus) {
    emit(CourseDetailsLoading(isDataLoading: loadStatus, course: state.course));
  }

  /// remove scroll indicator arrow for affairs items
  removeArrow(bool canScroll) {
    emit(CourseNewsScroll(canScroll: canScroll));
  }

  ///
  /// Course Videos View
  ///
  videoCallback(bool isFulllScreen) {
    emit(CourseVideoStatus(isFullSCreen: isFulllScreen));
  }

  pauseVideo() {
    emit(VideoPlayerStatus());
  }

  handleVideoPlayerError() {
    emit(VideoPlayerError());
  }

  fetchCommentsForId(String instanceId) async {
    try {
      List<ResourseComment> comments =
          await _courseRepository.fetchCommentsForId(instanceId);
      setLoading(false);
      emit(CourseCommentsFetched(comments: comments));
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][CommentsError] :${e.toString()}");
      String errorMsg = e.message;
      emit(CommentsError(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][fetchCommentsForId] :${e.toString()}");
      emit(CommentsError(error: e.toString()));
    }
  }

  uploadComment(Map<String, dynamic> jsonReqBody) async {
    Map<String, dynamic> jsonBody = jsonReqBody;
    try {
      dynamic response = await _courseRepository.uploadCommentsForId(jsonBody);
      setLoading(false);
      if (response != null &&
          response['status'].toLowerCase() == SUCCESS.toLowerCase()) {
        emit(CourseCommentsUploaded(comments: []));
      } else {
        emit(CommentsError(error: 'Failed to upload comment'));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][CommentsError] :${e.toString()}");
      String errorMsg = e.message;
      emit(CommentsError(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][uploadComment] :${e.toString()}");
      emit(CommentsError(error: e.toString()));
    }
  }

  addComment(
      ResourseComment comment, List<ResourseComment> existingComments) async {
    List<ResourseComment> updatedCommentList = existingComments;

    try {
      updatedCommentList.add(comment);
      emit(CommentsAdded(comments: updatedCommentList));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][uploadComment] :${e.toString()}");
      emit(CommentsError(error: e.toString()));
    }
  }

  handleCommentTypeSelection(String type) {
    emit(CommentTypeSelected(commentType: type));
  }

  setQuestionLoading(bool status) {
    emit(SetQuestionLoading(status: status));
  }

  ///
  /// Course News View
  ///
  dateSelectionCallback(
      DateTime selectedDate, List<CurrentAffairs> availableData) {
    final List<CurrentAffairs> filteredList = List.from(availableData.where(
        (element) =>
            element.publishedDate.month == selectedDate.month &&
            element.publishedDate.year == selectedDate.year));
    filteredList.sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
    emit(CourseNewsSelection(
        selectedDate: selectedDate, filteredList: filteredList));
  }

  generateMonthListForAffairs() {
    months = generateMonthList();
    emit(CourseNewsMonthGeneration(monthList: months));
  }

  // set course progress
  setCourseProgress(
      String courseId, String instance, Map<String, dynamic> duration) async {
    try {
      final responseData = await _courseRepository.setCourseProgress(
          courseId, instance, duration);

      if (responseData["status"] == 'success') {
        emit(SetResourceProgressState());
      } else {
        emit(ResourceProgressFailureState(error: responseData.toString()));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][setCourseProgress] :${e.toString()}");
      String errorMsg = e.message;
      emit(ResourceProgressFailureState(error: errorMsg));
    } on Exception catch (e) {
      emit(ResourceProgressFailureState(error: e.toString()));
    }
  }

// To Enable Progress Button
  Future<void> enableSaveProgressBtn(bool value) async {
    if (value == true) {
      emit(CourseListButtonEnable(enable: true));
    } else {
      emit(CourseListButtonEnable(enable: false));
    }
  }

  ///
  /// UI Enchancements
  ///

  changeCarouselIndex(int updatedIndex) {
    emit(CarouselChangeIndex(carouselIndex: updatedIndex));
  }

  //VideoplayerData
  Future<List<VideoPlayerData>> getVideoPlayerData(String id) async {
    return await DbHelper.instance.getVideoPlayerData(id);
  }

  Future<void> insertToVideoData(VideoPlayerData data) async {
    await DbHelper.instance.insertToVideoData(data);
  }

  Future<void> insertToMilestoneData(MilestoneData data) async {
    await DbHelper.instance.insertMilestoneData(data);
  }

  Future<void> getMilestoneData(String id) async {
    await DbHelper.instance.getMilestoneData(id);
  }

  Future<void> insertToHiveVideoData(HiveVideoPlayerData data) async {
    await _hiveDbHelper.hiveInsertOrUpdateVideoData(data);
  }

  Future<HiveVideoPlayerData?> hiveGetVideoPlayerData(String videoId) async {
    return await _hiveDbHelper.hiveGetVideoPlayerData(videoId);
  }

  updatelastWatchTime(int watchTime, int index) {
    videoPlayerData['lastWatchTime'] = watchTime;
    videoPlayerData['checkPointIndex'] = index;
  }

  void updateYoutubePlayerStatus(bool status) {
    emit(YoutubePlayerLoaded(status: status));
  }
}
