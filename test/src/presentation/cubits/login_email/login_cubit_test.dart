import 'package:SmartLearn/src/data/repositories/login_email_repository_impl.dart';
import 'package:SmartLearn/src/data/repositories/organization_repository_impl.dart';
import 'package:SmartLearn/src/presentation/cubits/login_email/login_cubit.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MockEmailloginRepository extends Mock
    implements LoginEmailRepositoryImpl {
  @override
  Future<String> loginEmail(String email, String password) {
    return Future(() => '');
  }
}

class MockorganisationRepository extends Mock
    implements OrganizationRepositoryImpl {}

void main() {
  LoginEmailRepositoryImpl _loginEmailRepositoryImpl;
  OrganizationRepositoryImpl _organizationRepositoryImpl;

  late LoginEmailCubit loginEmailCubit;

  setUp(() {
    SharedPreferences.setMockInitialValues({});
    _loginEmailRepositoryImpl = MockEmailloginRepository();
    _organizationRepositoryImpl = MockorganisationRepository();
    loginEmailCubit =
        LoginEmailCubit(_loginEmailRepositoryImpl, _organizationRepositoryImpl);
  });

  tearDown(() {
    loginEmailCubit.close();
  });

  ///
  group('Login Email Cubit', () {
    WidgetsFlutterBinding.ensureInitialized();

    // TO DO - Complete Test

    // blocTest<LoginEmailCubit, LoginEmailState>('emit [LoginEmailSuccess] when Login event is added',
    //     build: () => loginEmailCubit,
    //     act: (cubit) =>
    //         cubit.loginEmail("<EMAIL>", "123456"),
    //     expect: () => [
    //           LoginEmailSuccess(
    //               id: "c87a6823-02f0-4c7b-b194-981030c3ee03", orgList: organization)
    //         ]);

    blocTest<LoginEmailCubit, LoginEmailState>(
        'emit [LoginEmailLoading] occurs if login the user',
        build: () => loginEmailCubit,
        act: (cubit) => cubit.setLoading(false),
        expect: () => [const LoginEmailLoading(isLoading: false)]);

    blocTest<LoginEmailCubit, LoginEmailState>(
        'emit [LoginEmailErrorValidation] when email validation fails',
        build: () => loginEmailCubit,
        act: (cubit) => cubit.setEmailError('text', false),
        expect: () =>
            [LoginEmailErrorValidation(error: "text", showError: false)]);

    blocTest<LoginEmailCubit, LoginEmailState>(
        'emit [LoginPasswordValidation] when password validation fails',
        build: () => loginEmailCubit,
        act: (cubit) => cubit.setPasswordError('text', false),
        expect: () =>
            [LoginPasswordValidation(PasswordError: "text", showError: false)]);

    blocTest<LoginEmailCubit, LoginEmailState>(
        'emit [LoginButtonColor] when email and password inputs are entered',
        build: () => loginEmailCubit,
        act: (cubit) => cubit.changeBtnStatus("", "", "", ""),
        expect: () => [LoginButtonColor(buttonColor: false)]);
  });
}
