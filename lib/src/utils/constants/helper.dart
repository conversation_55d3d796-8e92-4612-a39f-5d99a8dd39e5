import 'dart:io';
import 'dart:math';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../config/app_config/preference_config.dart';
import '../../config/app_config/sl_config.dart';
import '../../domain/models/device_info.dart';
import '../../domain/services/db_helper.dart';

import '/src/utils/constants/strings.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

/// Check whether the link is a valid url or not
Future<bool> isValidUrl(String url) async {
  try {
    // ignore: unnecessary_null_comparison
    if (url != null &&
        url.trim().isNotEmpty &&
        (url.startsWith(HTTP) || url.startsWith(HTTPS))) {
      return true;
    }
  } on Exception catch (_) {
    // TODO
  }
  return false;
}

/// Get application path
Future<Directory> getApplicationDirectoryPath() async {
  return await getTemporaryDirectory();
}

/// take screenshot of the controller's area and share the image
Future<void> screenshotandShare(
    ScreenshotController _screenshotController) async {
  Uint8List? capturedImage = await _screenshotController.capture();
  int random = generateRandomNum();

  if (capturedImage != null) {
    final tempDir = await getApplicationDirectoryPath();
    File fileToBeUploaded =
        await File('${tempDir.path}/$random image.png').create();
    fileToBeUploaded.writeAsBytesSync(capturedImage);

    // Share the captured image using the share package
    await Share.shareXFiles([XFile(fileToBeUploaded.path)]).then((value) {
      print("Share Results => ${value.status}");
    });
  }
}

/// Share app link
Future<void> shareAppInfo() async {
  String appLink = SLConfig.APP_LINK;
  String message = SLConfig.SHARE_MESSAGE + '\n\n' + appLink;
  if (!kIsWeb) {
    await Share.share(message);
  }
}

String convertToCamelCase(String stringObj) {
  String convertedString = stringObj;
  try {
    convertedString = '';
    if (stringObj.isEmpty) {
      return convertedString;
    }
    String trimmedText = stringObj.trim();
    List<String> words = trimmedText.split(' ');

    for (var i = 0; i < words.length; i++) {
      if (words[i] == '') {
        continue;
      }
      convertedString += words[i].substring(0, 1).toUpperCase() +
          words[i].substring(1, words[i].length).toLowerCase() +
          ' ';
    }
  } on Exception catch (_) {
    // TODO
  }

  return convertedString;
}

Future<File> convertUint8ListToPath(Uint8List? data) async {
  int random = generateRandomNum();
  Uint8List? capturedImage = data;
  File thumnailFile = File(NULL_PATH);

  try {
    if (capturedImage != null) {
      final tempDir = await getApplicationDirectoryPath();
      File fileToBeUploaded = await File(
              '${tempDir.path}/$random image${DateTime.now().millisecondsSinceEpoch}.png')
          .create();
      fileToBeUploaded.writeAsBytesSync(capturedImage);
      thumnailFile = fileToBeUploaded;
    }
  } on Exception catch (_) {
    // TODO
  }
  return thumnailFile;
}

int generateRandomNum() {
  // Create an instance of Random class
  var random = Random();

  // Generate a random integer between 0 and 100
  var randomNumber = random.nextInt(101);
  return randomNumber;
}

String getRandomValueForKey() {
  return Random.secure().nextDouble().toString();
}

clearCurrentUserInfo() async {
  final DbHelper _dbHelper = DbHelper.instance;
  SharedPreferences _prefs = await SharedPreferences.getInstance();
  _prefs.remove(PREF_KEY_QUIZ_ATTEMPT_ID);
  _prefs.remove(PREF_KEY_USER_ID);
  _prefs.remove(PREF_KEY_ORG_ID);
  _prefs.remove(PREF_KEY_TOPIC_ID);
  _prefs.remove(PREF_KEY_TOPIC_NAME);
  _prefs.remove(PREF_KEY_COURSE_ID);
  _prefs.remove(PREF_KEY_COURSE_NAME);
  _prefs.remove(PREF_KEY_SUBSCRIBED_PLANS_LIST);
  _prefs.remove(PREF_KEY_PREVILEGE_ACCESS_LIST);

  USER_ID = '';
  ORG_ID = '';
  TOPIC_ID = '';
  TOPIC_NAME = '';
  COURSE_ID = '';
  COURSE_NAME = '';

  if (!kIsWeb) {
    _dbHelper.clearTable();
    _dbHelper.clearVideoData();
  }
}

Future<bool> checkNetworkStatus() async {
  final result = await InternetConnectionCheckerPlus().hasConnection;
  return result;
  // TODO : Remove Later
  // try {
  //   final result = await InternetAddress.lookup('google.com');
  //   if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
  //     return true;
  //   }
  // } on SocketException catch (_) {
  //   return false;
  // }

  // return false;
}

String convertIntToAlphabet(int num) {
  String alphabetCharacter = numberToAlphabetCharacter(num);
  return alphabetCharacter;
}

String numberToAlphabetCharacter(int number) {
  try {
    if (number >= 1 && number <= 26) {
      return String.fromCharCode(number + 64); // 'A' starts at ASCII code 65
    } else {
      return "";
    }
  } on Exception catch (e) {
    debugPrint('[numberToAlphabetCharacter]: ${e.toString()}');
  }
  return "";
}

Future<DeviceInfo?> fetchDeviceInfo() async {
  try {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo _androidInfo = await deviceInfo.androidInfo;
      final _deviceInfoObj = DeviceInfo.fromAndroidJson(_androidInfo.data, '');
      return _deviceInfoObj;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;

      final _deviceInfoObj = DeviceInfo.fromiOSJson(iosInfo.data, '');
      return _deviceInfoObj;
    } else if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      WebBrowserInfo _webBrowserInfo = await deviceInfo.webBrowserInfo;
      final _deviceInfoObj = DeviceInfo.fromWebJson(_webBrowserInfo.data, '');
      return _deviceInfoObj;
      // TODO: implement later
      /*  WindowsDeviceInfo windowsDeviceInfo = await deviceInfo.windowsInfo;
        return windowsDeviceInfo;
      } else if (Platform.isLinux) {
        LinuxDeviceInfo linuxDeviceInfo = await deviceInfo.linuxInfo;
        return linuxDeviceInfo;
      } else if (Platform.isMacOS) {
        MacOsDeviceInfo macOsDeviceInfo = await deviceInfo.macOsInfo;
       return macOsDeviceInfo;*/
    }
  } on Exception catch (e) {
    debugPrint('[fetchDeviceInfo]: ${e.toString()}');
  }
}

/// navigate to web on selecting a link from html content
Future<bool> launchSelectedUrl(String url) async {
  final Uri _url = Uri.parse(url);
  try {
    launchUrl(_url).then((value) => value);
  } on Exception catch (e) {
    debugPrint("launchUrl => error => ${e.toString()}");
    return false;
  }
  return false;
}

Future<bool> validateImage(String imageUrl) async {
  if (imageUrl.isEmpty) {
    return false;
  }
  try {
    Image.network(imageUrl);
    return true;
  } on Exception catch (e) {
    debugPrint(
        '[validateImage]: The URL is not a valid network image URL--> $e');
  }

  return false;
}

List<DateTime> generateMonthList() {
  List<DateTime> months = [];
  DateTime dateTimeNow = DateTime.now();
  DateTime currentDate =
      DateTime(dateTimeNow.year, dateTimeNow.month, dateTimeNow.day);
  DateTime previousYearDate = DateTime(currentDate.year - 2, 12);

  while (currentDate.isAfter(previousYearDate)) {
    months.add(currentDate);

    if (currentDate.month == 1) {
      currentDate = DateTime(currentDate.year - 1, 12);
    } else {
      currentDate = DateTime(currentDate.year, currentDate.month - 1);
    }
  }
  return months;
}
