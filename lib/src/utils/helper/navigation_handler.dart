import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../config/router/app_router.dart';
import '../../presentation/views/ppt_viewer.dart';

handleBackNavFromScreens(BuildContext context, NavigationTo navigateBackTo) {
  if (kIsWeb) {
    Beamer.of(context).beamBack();
    return;
  }
  switch (navigateBackTo) {
    case NavigationTo.details_dashboard:
      appRouter.popUntil(
          (route) => route.settings.name == CourseDetailsDashboardRoute.name);
      break;
    case NavigationTo.section_details:
      appRouter.popUntil(
          (route) => route.settings.name == SectionDetailsViewRoute.name);
      break;
    case NavigationTo.course_details:
      appRouter.popUntil(
          (route) => route.settings.name == CourseDetailsViewRoute.name);
      break;
    case NavigationTo.exam_list:
      appRouter
          .popUntil((route) => route.settings.name == ExamListViewRoute.name);
      break;
    case NavigationTo.subjects_tab_view:
      appRouter.popUntil(
          (route) => route.settings.name == SubjectsDetailedTabViewRoute.name);
      break;
  }
}

handleBackNavigationWithWeb(BuildContext context, NavigationTo navigateBackTo) {
  if (navigateBackTo == NavigationTo.details_dashboard) {
    kIsWeb
        ? Beamer.of(context).beamToNamed('/course-details-dashboard')
        : appRouter.popUntil(
            (route) => route.settings.name == CourseDetailsDashboardRoute.name);
  } else if (navigateBackTo == NavigationTo.section_details) {
    kIsWeb
        ? Beamer.of(context).beamToNamed('/section-details')
        : appRouter.popUntil(
            (route) => route.settings.name == SectionDetailsViewRoute.name);
  } else if (navigateBackTo == NavigationTo.course_details) {
    kIsWeb
        ? Beamer.of(context).beamToReplacementNamed('/course-details')
        : appRouter.popUntil(
            (route) => route.settings.name == CourseDetailsViewRoute.name);
  } else if (navigateBackTo == NavigationTo.subjects_tab_view) {
    kIsWeb
        ? Beamer.of(context).beamToNamed('/exam-list-tab')
        : appRouter.popUntil((route) =>
            route.settings.name == SubjectsDetailedTabViewRoute.name);
  }
}
