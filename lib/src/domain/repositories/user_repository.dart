import 'dart:io';

import 'package:supabase/supabase.dart';

import '../models/organization.dart';
import '../models/profile.dart';

abstract class UserRepository {
  Future<Profile> getUserInfo(String userId);

  Future<dynamic> updateUserInfo(String userId, String firstName,
      String lastName, String avatarUrl, File? file);
  Future<List<Organization>> getUserOrganizations(String userId);
  Future<dynamic> userSignout();
  Future<dynamic> checkTokenAndRefresh();
}
