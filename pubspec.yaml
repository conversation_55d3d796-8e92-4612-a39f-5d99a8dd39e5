name: SmartLearn
description: SmartLearn Mobile App
publish_to: "none"
version: 1.0.5+1

environment:
  sdk: ">=3.2.0 <4.0.0"

dependencies:
  auto_route: ^7.8.4
  auto_size_text: ^3.0.0
  awesome_dio_interceptor: ^0.0.2+4
  beamer: ^1.5.6
  bloc_test: ^9.1.1
  cached_network_image: ^3.2.3
  carousel_slider: ^4.2.1
  chewie: ^1.5.0
  country_list_pick: ^1.0.1+6
  cupertino_icons: ^1.0.5
  device_info_plus: ^9.1.1
  dio: ^4.0.6
  dropdown_button2: ^2.3.9
  equatable: ^2.0.5
  firebase_analytics: ^10.5.0
  firebase_core: ^2.24.2
  firebase_crashlytics: ^3.3.6
  firebase_messaging: ^14.7.5
  floor: ^1.4.1
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.2
  flutter_hooks: ^0.18.5+1
  flutter_html: ^3.0.0-beta.2
  flutter_image_compress: ^2.1.0
  flutter_launcher_icons: ^0.13.1
  flutter_local_notifications: ^16.1.0
  flutter_localizations:
    sdk: flutter
  flutter_native_splash: ^2.3.5
  flutter_tex: ^4.0.3+4
  # flutter_windowmanager: ^0.2.0
  get: ^4.6.5
  get_it: ^7.2.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  # image_cropper: ^8.0.2
  image_picker: ^1.0.7
  internet_connection_checker_plus: ^1.0.1
  intl: ^0.18.0
  ionicons: ^0.2.2
  jiffy: ^6.2.1
  mockito: ^5.4.0
  mocktail: ^0.3.0
  oktoast: ^3.3.1
  path_provider: ^2.0.15
  percent_indicator: ^4.2.3
  permission_handler: ^10.2.0
  pinput: ^3.0.1
  pod_player: ^0.2.0
  retrofit: ^3.3.0
  rxdart: ^0.27.7
  screenshot: ^2.1.0
  sentry_flutter: ^7.10.1
  share_plus: ^7.2.1
  shared_preferences: ^2.1.0
  side_sheet: ^1.0.4
  side_sheet_material3: ^0.0.2
  signature: ^5.4.0
  sqflite: ^2.2.8+4
  supabase: ^1.11.3
  supabase_flutter: ^1.6.2
  syncfusion_flutter_charts: ^21.2.4
  timer_count_down: ^2.2.2
  url_launcher: ^6.2.1
  video_thumbnail: ^0.5.6
  # webview_flutter: ^4.4.2
  youtube_explode_dart: ^1.12.4
  youtube_player_flutter: ^8.1.2
  youtube_player_iframe_plus: ^2.3.2
  zoom_widget: ^2.0.1
  universal_html: ^2.2.4
  json_annotation: ^4.8.1
  flutter_widget_from_html_core: ^0.14.11
  scrollable_positioned_list: ^0.3.8
  awesome_circular_chart: ^1.0.0
  d_chart: ^2.6.9
  pie_chart: ^5.4.0
  webview_flutter: ^4.6.0
  flutter_dotenv: ^5.2.1
  confetti: ^0.6.0
  package_info_plus: ^5.0.1

dev_dependencies:
  analyzer: ^5.13.0
  auto_route_generator: ^7.3.2
  build_runner: ^2.4.6
  change_app_package_name: ^1.5.0
  connectivity_plus: ^4.0.1
  floor_generator: ^1.4.1
  flutter_lints: ^1.0.4
  flutter_pdfview: ^1.3.1
  flutter_speed_dial: ^7.0.0
  flutter_test:
    sdk: flutter
  flutter_timer_countdown: ^1.0.3
  hive_generator: ^2.0.1
  introduction_screen: ^3.1.8
  json_serializable: ^6.7.1

  lints: ^1.0.1
  modal_side_sheet: ^0.0.1
  photo_view: ^0.15.0
  retrofit_generator: ^4.1.2
  sqflite_common_ffi: ^2.3.0+2
  test: ^1.22.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/launcher/
    - assets/enhancement/
    - .env

  fonts:
    - family: Butler
      fonts:
        - asset: assets/fonts/Butler_Bold.ttf
        - asset: assets/fonts/Butler_Light.ttf
        - asset: assets/fonts/Butler_Regular.ttf

    - family: IBM
      fonts:
        - asset: assets/fonts/IBMPlexSans-Bold.ttf
        - asset: assets/fonts/IBMPlexSans-Light.ttf
        - asset: assets/fonts/IBMPlexSans-Regular.ttf

    - family: OpenSans
      fonts:
        - asset: fonts/OpenSans-Bold.ttf
        - asset: fonts/OpenSans-BoldItalic.ttf
        - asset: fonts/OpenSans-ExtraBold.ttf
        - asset: fonts/OpenSans-ExtraBoldItalic.ttf
        - asset: fonts/OpenSans-Italic.ttf
        - asset: fonts/OpenSans-Light.ttf
        - asset: fonts/OpenSans-LightItalic.ttf
        - asset: fonts/OpenSans-Medium.ttf
        - asset: fonts/OpenSans-MediumItalic.ttf
        - asset: fonts/OpenSans-Regular.ttf
        - asset: fonts/OpenSans-SemiBold.ttf
        - asset: fonts/OpenSans-SemiBoldItalic.ttf

    - family: Poppins
      fonts:
        - asset: fonts/Poppins-Black.ttf
        - asset: fonts/Poppins-BlackItalic.ttf
        - asset: fonts/Poppins-Bold.ttf
        - asset: fonts/Poppins-BoldItalic.ttf
        - asset: fonts/Poppins-ExtraBold.ttf
        - asset: fonts/Poppins-ExtraBoldItalic.ttf
        - asset: fonts/Poppins-ExtraLight.ttf
        - asset: fonts/Poppins-ExtraLightItalic.ttf
        - asset: fonts/Poppins-Italic.ttf
        - asset: fonts/Poppins-Light.ttf
        - asset: fonts/Poppins-LightItalic.ttf
        - asset: fonts/Poppins-Medium.ttf
        - asset: fonts/Poppins-MediumItalic.ttf
        - asset: fonts/Poppins-Regular.ttf
        - asset: fonts/Poppins-SemiBold.ttf
        - asset: fonts/Poppins-SemiBoldItalic.ttf
        - asset: fonts/Poppins-Thin.ttf
        - asset: fonts/Poppins-ThinItalic.ttf

# # Flutter Launcher Icons Configuration
# flutter_launcher_icons:
#   android: "launcher_icon"
#   ios: true
#   image_path: "assets/launcher/app_icon.png"
#   min_sdk_android: 21
#   web:
#     generate: true
#     image_path: "assets/launcher/app_icon.png"
#     background_color: "#hexcode"
#     theme_color: "#hexcode"
#   windows:
#     generate: true
#     image_path: "assets/launcher/app_icon.png"
#     icon_size: 48
#   macos:
#     generate: true
#     image_path: "assets/launcher/app_icon.png"

# # Flutter Native Splash Configuration
# flutter_native_splash:
#   color: "#FFFFFF"
#   image: "assets/launcher/Launch-screen.png"
#   branding: "assets/launcher/logo.png"
#   color_dark: "#000000"
#   image_dark: "assets/launcher/Launch-screen.png"
#   branding_dark: "assets/launcher/logo.png"
#   android_12:
#     image: "assets/launcher/Launch-screen.png"
#     icon_background_color: "#FFFFFF"
#     image_dark: "assets/launcher/Launch-screen.png"
#     icon_background_color_dark: "#000000"
#   web: false
