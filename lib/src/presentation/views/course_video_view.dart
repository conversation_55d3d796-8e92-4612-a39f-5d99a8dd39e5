// ignore_for_file: must_be_immutable

import 'dart:async';

import '../../config/themes/app_dynamic_theme.dart';
import '../widgets/res_comment_widget.dart';
import '/src/config/enums/resource_activity_status.dart';
import '/src/presentation/cubits/app_config/app_config_cubit.dart';
import '/src/utils/resources/user_activity_res.dart';
import 'package:flutter/rendering.dart';

import '../../domain/models/tabbar_item.dart';
import '../widgets/tab_bar_widget.dart';
import '/src/presentation/widgets/video_controls_widget.dart';

import 'ppt_viewer.dart';
import '../../../src/domain/models/course_dashboard/course_video_resource.dart';
import 'package:flutter/services.dart';
import '../../utils/helper/navigation_handler.dart';
import '../cubits/section_details/section_details_cubit.dart';
import '/src/utils/constants/helper.dart';
import 'package:flutter/gestures.dart';

import '../../utils/helper/privilege_access_mapper.dart';
import '/src/presentation/widgets/alert_popup/confirmation_popup.dart';
import 'package:beamer/beamer.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/foundation.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/models/hive_db_models/hive_video_player.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../widgets/app_bar.dart';
import '../widgets/profile_placeholder_widget.dart';
import '/src/config/enums/checkpoint_type.dart';
import 'package:auto_route/auto_route.dart';

import '/src/presentation/widgets/loading_indicator.dart';

import '/src/domain/models/milestone.dart';
import '/src/domain/models/milestone_data.dart';
import '/src/domain/models/question_data.dart';
import '/src/domain/models/videoplayer_data.dart';
import '/src/presentation/cubits/exam/exam_cubit.dart';
import 'package:timer_count_down/timer_controller.dart';

import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/resource_comment.dart';
import '../../utils/helper/thumbnail_generator.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../../domain/models/sl_user.dart';
import '/src/presentation/cubits/connectivity/connectivity_state.dart';
import '/src/presentation/widgets/textfield_widget.dart';
import '/src/utils/constants/lists.dart';
import '/src/utils/helper/app_date_formatter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import '../../config/themes/app_theme.dart';
import '../cubits/connectivity/connectivity_cubit.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '../widgets/button_widget.dart';
import '../widgets/connectivity_widget.dart';
import '/src/config/router/app_router.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/config/themes/theme_colors.dart';
import '/src/presentation/cubits/course_list/course_list_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:youtube_player_iframe_plus/youtube_player_iframe_plus.dart'
    as IFrame;
import 'package:universal_html/js.dart' as js;

import '../../utils/constants/strings.dart';
import 'package:confetti/confetti.dart';
import 'package:provider/provider.dart' as provider;

@RoutePage()
class CourseVideoViewScreen extends HookWidget with WidgetsBindingObserver {
  final CourseVideoResource courseVideo;
  final ResourceProgress? courseProgress;
  final bool isFromSectionDetails;
  final NavigationTo navigateBackTo;
  final List<CheckPoint> checkPoints;
  final bool isFromExamScreen;
  final bool isExamPassed;

  CourseVideoViewScreen(
    this.courseVideo, {
    super.key,
    this.isFromSectionDetails = false,
    required this.courseProgress,
    this.checkPoints = const [],
    this.isFromExamScreen = false,
    this.isExamPassed = false,
    required this.navigateBackTo,
  });

  YoutubePlayerController? _youtubePlayerController;
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  IFrame.YoutubePlayerController? _webPlayerController;

  ValueNotifier<int> counter = ValueNotifier<int>(0);
  ValueNotifier<int> prevVideoDuration = ValueNotifier<int>(0);
  ValueNotifier<List<String>> selectedAnswerIds =
      ValueNotifier<List<String>>([]);
  ValueNotifier<int> selectedQuestionIndex = ValueNotifier<int>(0);
  ValueNotifier<bool> isPlayerReady = ValueNotifier<bool>(false);
  ValueNotifier<bool> readMore = ValueNotifier<bool>(false);

  final TextEditingController _promptController = TextEditingController();
  final countdownController = CountdownController(autoStart: true);
  final _commentListController = ScrollController();

  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();

  final _formKey = GlobalKey<FormState>();
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  List<ResourseComment> _resourceComments = [];
  List<ResourseComment> _resourceLikes = [];
  final List<CheckPoint> _checkPoints = [];
  final List<Milestone> _milestoneDurations = [];
  final List<CheckPoint> _completedCheckPoints = [];
  List<Question> _questions = [];

  List<Positioned> stackList = [];

  List<int> selectedAnswerIndexes = [];
  List<int> questionIndex = [];

  QuestionData? _questionData;
  CourseListCubit? _courseCubitObj;
  Timer? _timer;

  double _currentDuration = 0.0;

  bool _fullScreenView = false;
  bool _isYoutubeVideo = true;
  bool didSaveProgress = false;
  bool _didSelectProgressBtn = false;
  bool isExamLoading = false;

  String videoURL = '';

  int lastWatchTime = 0;

  ValueNotifier<bool> isPlayerClosed = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _pauseVideo = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _enableSaveProgressBtn = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _isDataLoading = ValueNotifier<bool>(false);
  final ValueNotifier<SeekDurations> _selectedDurations = ValueNotifier(
      const SeekDurations(
          left: 5, right: 5, isVideoOnPause: false)); // default values
  final ValueNotifier<AddCommentFunc> _commentNotifier = ValueNotifier(
      AddCommentFunc(enableSubmitBtn: false, selectedCommentType: 'Feedback'));
  final ValueNotifier<int> tabIndex = ValueNotifier<int>(0);
  final ValueNotifier<bool> _isLiked = ValueNotifier<bool>(false);
  final ValueNotifier<int> _likes = ValueNotifier<int>(0);

  final ConfettiController _confettiController =
      ConfettiController(duration: const Duration(milliseconds: 700));
  final ValueNotifier<double> _likeScale = ValueNotifier<double>(1.0);

  late ValueNotifier<bool> _hasAccessToAddComments;
  late ValueNotifier<bool> _hasAccessToSaveProgress;

  final double _horizontalMargin = 16.0;

  BuildContext? currentContext;
  late TabController _tabController;
  AppDynamicTheme? _appDynamicTheme;
  final _userActivityLog = UserActivityLog.instance;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // This method is called when the app's lifecycle state changes.
    // You can respond to the different lifecycle states here.
    _pauseOrDisposeVideo(pauseVideo: true);

    switch (state) {
      case AppLifecycleState.resumed:
        // The app is in the foreground.
        _pauseOrDisposeVideo(pauseVideo: true);
        break;
      case AppLifecycleState.inactive:
        // The app is in an inactive state (e.g., switching apps).
        break;
      case AppLifecycleState.paused:
        // The app is in the background.
        _pauseOrDisposeVideo(pauseVideo: true);
        break;
      case AppLifecycleState.detached:
        // The app is detached (not running).
        break;
      case AppLifecycleState.hidden:
        // All views of an application are hidden.
        break;
    }
  }

  _pauseOrDisposeVideo({required bool pauseVideo}) {
    if (_chewieController != null) {
      pauseVideo ? _chewieController?.pause() : _chewieController!.dispose();
    }
    if (_youtubePlayerController != null) {
      pauseVideo
          ? _youtubePlayerController!.pause()
          : _youtubePlayerController!.dispose();
    }
    if (_videoPlayerController != null) {
      pauseVideo
          ? _videoPlayerController!.pause()
          : _videoPlayerController!.dispose();
    }
    if (_webPlayerController != null) {
      pauseVideo
          ? _webPlayerController!.pause()
          : _webPlayerController!.close();
    }
    _pauseVideo.value = pauseVideo;
  }

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    _fullScreenView = false;
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    _pauseOrDisposeVideo(pauseVideo: true);
    // TO DO: context should not be equal to null
    didChangeAppLocale(locales, currentContext);
  }

  _initCheckpointInfo(
      BuildContext context, CourseListCubit _courseCubit) async {
    if (courseProgress != null && courseProgress!.progress >= 100) {
      return;
    }

    if (courseVideo.isCheckpointEnabled && checkPoints.isNotEmpty) {
      _checkPoints.addAll(checkPoints);
      _sortCheckpoints(_checkPoints);
      _sortCheckpoints(checkPoints);
    }
    _courseCubitObj = _courseCubit;
    if (isFromExamScreen) {
      _manageCheckPoints(context, _courseCubit);
    } else {
      _getLatsWatchTime(_courseCubit);
    }
  }

  Future<bool> _checkAccessToGetCheckPoints() async {
    // decides whether to fetch checkpoints
    String screen = PrivilegeAccessConsts.SCREEN_CHECKPOINT;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_CHECK_POINT;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _checkAccessPrivilege() async {
    // decides whether to disable/enable comment  button
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_ADD_COMMENT;
    _hasAccessToAddComments.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to disable/enable save progress
    screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_UPDATE_COURSE_PROGRESS;
    _hasAccessToSaveProgress.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);
  }

  Future<bool> _checkAccessToGetComments() async {
    // decides whether to fetch comments
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_COMMENT;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _initWebYoutubePlayer(BuildContext context, CourseListCubit cubit) {
    videoURL = courseVideo.externalUrl;
    _isYoutubeVideo = videoURL != '' && checkForValidYoutubeUrls(videoURL);
    if (_isYoutubeVideo) {
      List<CheckPoint> mileStones = [];
      mileStones.addAll(checkPoints ?? []);
      int startDuration = 0;
      if (courseProgress != null) {
        startDuration = (courseProgress?.progress ?? 0.0).toInt();
        // ((courseVideo.progress * (courseVideo.totalDuration * 60)) / 60)
        //     .round();
        lastWatchTime = startDuration;
      }
      String vedioId = extractVideoId(courseVideo.externalUrl);
      _webPlayerController = IFrame.YoutubePlayerController(
        initialVideoId: vedioId,
        params: IFrame.YoutubePlayerParams(
          showControls: checkPoints.isEmpty,
          enableKeyboard: checkPoints.isEmpty,
          startAt: Duration(
            seconds: lastWatchTime,
          ),
        ),
      );
      _webPlayerController?.seekTo(Duration(seconds: lastWatchTime));

      _webPlayerController?.listen((event) {
        // Temporary list to check milestone is completed or not

        _currentDuration =
            ((_webPlayerController?.value.position.inSeconds) ?? 0).toDouble();

        if (_webPlayerController?.value.isReady == true) {
          isPlayerReady.value = true;
        }

        // _webPlayerController
        //     ?.seekTo(Duration(seconds: _currentDuration.toInt()));

        final mileStoneContains = _checkPoints.any((element) {
          int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
          return startTimeInSec ==
              _webPlayerController!.value.position.inSeconds;
        });
        if (mileStoneContains) {
          _webPlayerController?.pause();

          final mileStoneRemoveIndex = _checkPoints.indexWhere((element) {
            int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
            return startTimeInSec ==
                _webPlayerController!.value.position.inSeconds;
          });
          _checkPoints.remove(_checkPoints[mileStoneRemoveIndex]);
          final mileStoneIndex = mileStones.indexWhere((element) {
            int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
            return startTimeInSec ==
                _webPlayerController!.value.position.inSeconds;
          });
          _courseCubitObj?.updatelastWatchTime(
              _webPlayerController?.value.position.inSeconds ?? 0,
              mileStoneIndex);
          _showCheckPointInfoPopup(context, mileStones[mileStoneIndex]);
        }
        cubit.enableSaveProgressBtn(false);
        if (_currentDuration >= 20.0 &&
            (isFromSectionDetails || isFromExamScreen)) {
          cubit.enableSaveProgressBtn(true);
        }

        final videoCompleted = _webPlayerController!.value.position.inSeconds ==
            _webPlayerController!.metadata.duration.inSeconds - 4;

        if (videoCompleted) {
          _webPlayerController?.seekTo(const Duration(seconds: 1));
          _webPlayerController?.pause();
          disableClickOnPlayer();
          _showVideoCompletePopup(context);
        }
      });
    } else {
      _initChewieVideoPlayer(cubit, context);
    }
  }

  ///
  /// initialize video players
  ///

  _initPlayer(CourseListCubit cubit, BuildContext context) {
    videoURL = courseVideo.externalUrl;

    try {
      bool _isYouTubeVideo =
          videoURL != '' && checkForValidYoutubeUrls(videoURL);
      if (kIsWeb) {
        _initWebYoutubePlayer(context, cubit);
      } else {
        if (lastWatchTime <= 0) {
          lastWatchTime = (courseProgress?.progress ?? 0.0).toInt();
        }
        if (_isYouTubeVideo) {
          ///Youtube video
          ///use youtube player
          ///
          _initYoutubePlayer(cubit, context);
        } else {
          ///Supabase Hosted Video
          ///use video player
          ///
          _initChewieVideoPlayer(cubit, context);
        }
        _saveVideoProgressToDB(context, cubit);
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  _initYoutubePlayer(CourseListCubit cubit, BuildContext context) {
    try {
      _isYoutubeVideo = true;
      String videoId = extractVideoId(videoURL);
      // int startDuration = 0;
      // if (courseProgress != null) {
      //   startDuration = (courseProgress?.progress ?? 0.0).toInt();
      //   // ((courseVideo.progress * (courseVideo.totalDuration * 60)) / 60)
      //   //     .round();
      //   lastWatchTime = startDuration;
      // }
      _youtubePlayerController = YoutubePlayerController(
        initialVideoId: videoId,
        flags: YoutubePlayerFlags(
          useHybridComposition: false,
          enableCaption: false,
          startAt: lastWatchTime,
          disableDragSeek: (courseProgress?.progress ?? 0) >= 100
              ? false
              : (courseVideo.isCheckpointEnabled && checkPoints.isNotEmpty)
                  ? true
                  : false,
        ),
      );
      _youtubePlayerController?.seekTo(Duration(seconds: lastWatchTime));
      // Temporary list to check milestone is completed or not
      List<CheckPoint> mileStones = [];
      mileStones.addAll(checkPoints);
      _youtubePlayerController?.addListener(() {
        _pauseVideo.value = _youtubePlayerController != null &&
            _youtubePlayerController!.value.isPlaying;

        final mileStoneContains = _checkPoints.any((element) {
          int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
          return startTimeInSec ==
              _youtubePlayerController!.value.position.inSeconds;
        });
        if (mileStoneContains) {
          isPlayerReady.value = false;
          _youtubePlayerController?.pause();

          final mileStoneRemoveIndex = _checkPoints.indexWhere((element) {
            int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
            return startTimeInSec ==
                _youtubePlayerController!.value.position.inSeconds;
          });
          _checkPoints.remove(_checkPoints[mileStoneRemoveIndex]);
          final mileStoneIndex = mileStones.indexWhere((element) {
            int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
            return startTimeInSec ==
                _youtubePlayerController!.value.position.inSeconds;
          });
          _courseCubitObj?.updatelastWatchTime(
              _youtubePlayerController?.value.position.inSeconds ?? 0,
              mileStoneIndex);
          _showCheckPointInfoPopup(context, mileStones[mileStoneIndex]);
        }

        cubit.enableSaveProgressBtn(false);
        _currentDuration =
            ((_youtubePlayerController?.value.position.inSeconds) ?? 0)
                .toDouble();
        if (_currentDuration >= 20.0 && isFromSectionDetails) {
          cubit.enableSaveProgressBtn(true);
        }
      });
    } on Exception catch (e) {
      debugPrint('[course video view][_initYoutubePlayer]: $e');
      _handlePlayerError();
      cubit.handleVideoPlayerError();
    }
  }

  _initChewieVideoPlayer(CourseListCubit cubit, BuildContext context) async {
    _isYoutubeVideo = false;
    await initializeVideoPlayer(cubit, context);
  }

  Future<void> initializeVideoPlayer(
      CourseListCubit cubit, BuildContext context) async {
    try {
      Uri url = Uri.parse(videoURL);

      _videoPlayerController = VideoPlayerController.networkUrl(url);
      await _videoPlayerController?.initialize();
      cubit.videoCallback(false);
      await _createChewieController(_videoPlayerController, cubit, context);
      await Future.wait([]);
    } on Exception catch (e) {
      debugPrint('[course video view][initializePlayer]: $e');
      _handlePlayerError();
      cubit.handleVideoPlayerError();
    }
  }

  _handlePlayerError() {
    if (currentContext != null) {
      disableClickOnPlayer();

      _showVideoPlayerErrorPopup(currentContext!);
    }
  }

  Future<void> _createChewieController(_videoPlayerController,
      CourseListCubit cubit, BuildContext context) async {
    int startDuration = 0;
    if (courseProgress != null) {
      startDuration = (courseProgress?.progress ?? 0.0).toInt();
      // ((courseVideo.progress * (courseVideo.totalDuration * 60)) / 60).round();
    }
    _chewieController = ChewieController(
      errorBuilder: (context, errorMessage) {
        debugPrint(
            '[CourseVideoScreen][_createChewieController][errorBuilder]: $errorMessage');
        return Text(errorMessage);
      },
      videoPlayerController: _videoPlayerController,
      autoPlay: true,
      showOptions: false,
      startAt: Duration(seconds: startDuration),
      controlsSafeAreaMinimum: const EdgeInsets.all(8),
      cupertinoProgressColors: ChewieProgressColors(
          backgroundColor: LmsColors.white,
          playedColor: AppTheme.primaryBlue,
          handleColor: AppTheme.primaryBlue,
          bufferedColor: LmsColors.white),
      customControls: const CupertinoControls(
          backgroundColor: Colors.black, iconColor: Colors.white),
      subtitleBuilder: (context, dynamic subtitle) => Container(
        padding: const EdgeInsets.all(10.0),
        child: subtitle is InlineSpan
            ? RichText(
                text: subtitle,
              )
            : Text(
                subtitle.toString(),
                style: LMSFonts.regularFont(14, AppTheme.commentBoxContent),
              ),
      ),
      hideControlsTimer: const Duration(seconds: 5),
    );
    _chewieController?.play();
    List<CheckPoint> mileStones = [];
    mileStones.addAll(checkPoints);
    _chewieController?.addListener(() {
      final mileStoneContains = _checkPoints.any((element) {
        int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
        return startTimeInSec ==
            _chewieController?.videoPlayerController.value.duration.inSeconds;
      });
      if (mileStoneContains) {
        isPlayerReady.value = false;
        _chewieController?.pause();

        final mileStoneRemoveIndex = _checkPoints.indexWhere((element) {
          int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
          return startTimeInSec ==
              _chewieController?.videoPlayerController.value.duration.inSeconds;
        });
        _checkPoints.remove(_checkPoints[mileStoneRemoveIndex]);
        final mileStoneIndex = mileStones.indexWhere((element) {
          int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
          return startTimeInSec ==
              _chewieController!.videoPlayerController.value.position.inSeconds;
        });
        _courseCubitObj?.updatelastWatchTime(
            _chewieController!.videoPlayerController.value.position.inSeconds,
            mileStoneIndex);
        _showCheckPointInfoPopup(context, mileStones[mileStoneIndex]);
      }

      cubit.enableSaveProgressBtn(false);
      _currentDuration =
          ((_chewieController!.videoPlayerController.value.position.inSeconds))
              .toDouble();
      if (_currentDuration >= 20.0 && isFromSectionDetails) {
        cubit.enableSaveProgressBtn(true);
      }
    });
    cubit.enableSaveProgressBtn(false);

    _currentDuration =
        ((_videoPlayerController?.value.position.inSeconds) ?? 0).toDouble();
    if (_currentDuration >= 20.0 && isFromSectionDetails) {
      cubit.enableSaveProgressBtn(true);
    }
  }

  Future<bool> _changePlayerStatus() async {
    if (!_fullScreenView) {
      isPlayerClosed.value = true;
    } else {
      _youtubePlayerController?.toggleFullScreenMode();
    }
    return isPlayerClosed.value;
  }

  _saveVideoProgressToDB(
      BuildContext context, CourseListCubit _courseCubit) async {
    if ((courseProgress?.progress ?? 0.0) >= 100.0) {
      return;
    }
    if (prevVideoDuration !=
        _youtubePlayerController?.value.position.inSeconds) {
      if (!kIsWeb) {
        _courseCubit.insertToVideoData(VideoPlayerData(
          videoId: courseVideo.id,
          startTime: DateTime.now().millisecondsSinceEpoch,
          endTime: DateTime.now().millisecondsSinceEpoch,
          milestonesCompleted: _completedCheckPoints.length,
          lastWatchTime:
              _youtubePlayerController?.value.position.inSeconds ?? 0,
          milestoneDurations: _completedCheckPoints.toString(),
        ));
      } else {
        final videoData = HiveVideoPlayerData(
            videoId: courseVideo.id,
            startTime: DateTime.now().millisecondsSinceEpoch,
            endTime: DateTime.now().millisecondsSinceEpoch,
            mileStonesCompleted: _completedCheckPoints.length,
            lastWatchTime: 0,
            mileStoneDurations: _completedCheckPoints.toString());
        _courseCubit.insertToHiveVideoData(videoData);
      }
      prevVideoDuration.value =
          _youtubePlayerController?.value.position.inSeconds ?? 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    _appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    final _courseCubit = BlocProvider.of<CourseListCubit>(context);
    Size screenSize = MediaQuery.of(context).size;
    _hasAccessToAddComments = useState<bool>(false);
    _hasAccessToSaveProgress = useState<bool>(false);
    _tabController =
        useTabController(initialLength: 2, initialIndex: tabIndex.value);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);

      _courseCubit.initCourseCubit();
      _courseCubitObj?.setQuestionLoading(true);

      _initCheckpointInfo(context, _courseCubit);
      _courseCubitObj?.setQuestionLoading(false);
      if (kIsWeb) {
        _initWebYoutubePlayer(context, _courseCubit);
      } else {
        _initPlayer(_courseCubit, context);
      }

      Future<void>.microtask(() async {
        await _checkAccessPrivilege();

        await _setResourceActivityLog(context,
            status: ResourceActivityStatus.started);

        if (courseVideo.id.isNotEmpty) {
          await _fetchComments(context, _courseCubit);
        }
      });

      _commentController.addListener(() {
        _commentNotifier.value = _commentNotifier.value
            .copyWith(enableSubmitBtn: _commentController.text.isNotEmpty);
        // enableCommentSubmitBtn.value = _commentController.text.isNotEmpty;
      });
      return () {
        _commentController.removeListener(() {});
        _commentFocusNode.dispose();
        _confettiController.dispose();

        // Save video player data to SQLite when the user exits the video screen

        _pauseOrDisposeVideo(pauseVideo: false);
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return BlocBuilder<CourseListCubit, CourseListState>(
      builder: (context1, state) {
        if (state is CourseVideoStatus) {
          _fullScreenView = state.isFullSCreen;
        } else if (state is CourseCommentsFetched) {
          _isDataLoading.value = false;
          _resourceComments = state.comments;
        } else if (state is CommentsAdded) {
          _isDataLoading.value = false;
          _resourceComments = state.comments;
        }

        if (state is CourseListButtonEnable) {
          _enableSaveProgressBtn.value = state.enable;
        }
        if (state is SetQuestionLoading) {
          isExamLoading = state.status;
        }
        if (state is CommentsError) {
          _isDataLoading.value = false;
        }

        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) async {
            if (didPop) {
              return;
            }
            await _onBackTapped(context, _courseCubit.state);
            _changePlayerStatus();
          },
          child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: Material(
              child: ValueListenableBuilder(
                  valueListenable: _isDataLoading,
                  builder: (context, _, __) {
                    return Stack(
                      children: [
                        ValueListenableBuilder(
                            valueListenable: _pauseVideo,
                            builder: (context, _, __) {
                              return Container(
                                  child: kIsWeb
                                      ? _isYoutubeVideo
                                          ? _mainContainer(
                                              context1,
                                              _courseCubit,
                                              state,
                                              screenSize,
                                              videoPlayerWidget: IFrame
                                                  .YoutubePlayerIFramePlus(
                                                aspectRatio: 16 / 7,
                                                controller:
                                                    _webPlayerController,
                                              ),
                                            )
                                          : _chewiePlayerWidget(context,
                                              _courseCubit, state, screenSize)
                                      : _isYoutubeVideo
                                          ? _youtubeWidget(context, state,
                                              _courseCubit, screenSize)
                                          : _chewiePlayerWidget(context,
                                              _courseCubit, state, screenSize));
                            }),
                        // _textFieldWidget(screenSize),

                        _fullScreenView
                            ? Container()
                            : _hasAccessToAddComments.value &&
                                    (courseProgress?.progress ?? 0) < 100
                                ? _commentBtn(context, _courseCubit)
                                : Container(),
                        SafeArea(child: ConnectivityStatusWidget()),
                        isExamLoading || _isDataLoading.value
                            ? LoadingIndicatorClass()
                            : Container(),
                      ],
                    );
                  }),
            ),
          ),
        );
      },
    );
  }

  Future<void> _setResourceActivityLog(BuildContext context,
      {required ResourceActivityStatus status}) async {
    await _userActivityLog.setResourceActivityLog(
        context: context,
        resourceType: 'video',
        status: status,
        currentDuration: _currentDuration,
        id: courseVideo.id,
        result: 'success');
  }

  Widget _youtubeWidget(BuildContext context, CourseListState state,
      CourseListCubit _courseCubit, Size screenSize) {
    // (10*2)--> left right margin
    // (8*2)--> actions padding from YoutubePlayer
    // (16 * 2)--> horizontal margin given to screen
    // 10--> sizedbox width
    final double bottomActionWidth = _fullScreenView
        ? screenSize.width - ((10 * 2) + (8 * 2))
        : screenSize.width - ((10 * 2) + (8 * 2) + (16 * 2));
    return YoutubePlayerBuilder(
        player: YoutubePlayer(
          bottomActions:
              courseVideo.isCheckpointEnabled && checkPoints.isNotEmpty
                  ? [
                      Align(
                        child: Container(
                          width: bottomActionWidth,
                          margin: const EdgeInsets.only(left: 10, right: 10),
                          decoration: BoxDecoration(
                            color: AppTheme.border,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            children: [
                              SizedBox(width: _fullScreenView ? 30 : 10),
                              CurrentPosition(),
                              const SizedBox(width: 10),
                              Expanded(
                                child: AbsorbPointer(
                                  child: Stack(
                                    children: [
                                      ProgressBar(
                                        controller: _youtubePlayerController,
                                        // isExpanded: true,
                                        colors: const ProgressBarColors(
                                          backgroundColor: AppTheme.whiteColor,
                                          playedColor: AppTheme.bgColor,
                                          handleColor: AppTheme.bgColor,
                                        ),
                                      ),
                                      // TODO : checkPoint Marks
                                      // ...stackList
                                    ],
                                  ),
                                ),
                              ),
                              _rewindButton(),
                              RemainingDuration(),
                              FullScreenButton(),
                            ],
                          ),
                        ),
                      ),
                    ]
                  : null,
          controller: _youtubePlayerController!,
          showVideoProgressIndicator: true,
          progressColors: const ProgressBarColors(
              backgroundColor: Colors.white,
              playedColor: AppTheme.primaryBlue,
              handleColor: AppTheme.primaryBlue,
              bufferedColor: Colors.white),
          onEnded: (metaData) {
            _youtubePlayerController?.pause();
            _courseCubit.insertToVideoData(VideoPlayerData(
              videoId: courseVideo.id,
              startTime: DateTime.now().millisecondsSinceEpoch,
              endTime: DateTime.now().millisecondsSinceEpoch,
              milestonesCompleted: _completedCheckPoints.length,
              lastWatchTime: 0,
              milestoneDurations: _completedCheckPoints.toString(),
            ));
          },
          onReady: () async {
            // Setting the last watched time in the YouTube player to resume the video
            _youtubePlayerController?.seekTo(Duration(seconds: lastWatchTime));
            isPlayerReady.value = true;
            // await FirebaseAnalytics.instance.logEvent(
            //   name: "video_session_started",
            //   parameters: {
            //     "content_type": "video",
            //     "item_id": 01,
            //     "start_at": DateTime.now().toString(),
            //   },
            // );
          },
        ),
        onEnterFullScreen: () =>
            context.read<CourseListCubit>().videoCallback(true),
        onExitFullScreen: () =>
            context.read<CourseListCubit>().videoCallback(false),
        builder: (context, player) {
          Widget _youtubePlayerWidget = ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(16)),
            child: ValueListenableBuilder(
                valueListenable: isPlayerClosed,
                builder: (context, isPlayer, _) {
                  return isPlayer
                      ? const SizedBox()
                      : _youtubePlayerController != null
                          ? player
                          : _thumbnailPlaceHolder(context);
                }),
          );
          return _mainContainer(context, _courseCubit, state, screenSize,
              videoPlayerWidget: _youtubePlayerWidget);
        });
  }

  Widget _rewindButton() {
    return IconButton(
      onPressed: () {
        if (kIsWeb) {
          if (_webPlayerController?.value.isReady == true &&
              _webPlayerController!.value.position.inSeconds > 5) {
            _webPlayerController?.seekTo(Duration(
                seconds: _webPlayerController!.value.position.inSeconds - 5));
          }
        } else {
          if (_youtubePlayerController?.value.isReady == true &&
              _youtubePlayerController!.value.position.inSeconds > 5) {
            _youtubePlayerController?.seekTo(Duration(
                seconds:
                    _youtubePlayerController!.value.position.inSeconds - 5));
          }
        }
      },
      icon: const Icon(
        Icons.fast_rewind,
        color: AppTheme.whiteColor,
      ),
    );
  }

  Widget _mainContainer(BuildContext context, CourseListCubit _courseCubit,
      CourseListState state, Size screenSize,
      {required Widget videoPlayerWidget}) {
    return Scaffold(
      key: _scaffoldKey,
      resizeToAvoidBottomInset: false,
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
            title: COURSE_NAME,
            trailingWidget: Container(),
            leadingIconName: BACK_ARROW,
            leadingBtnAction: () => _onBackTapped(context, state),
          )),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16), topRight: Radius.circular(16)),
        ),
        clipBehavior: Clip.antiAlias,
        child: LayoutBuilder(builder: (context, constraints) {
          return Align(
            child: SizedBox(
              width: constraints.maxWidth < 720 ? double.infinity : 800,
              child: Stack(
                children: [
                  _customScrollWidget(
                    videoPlayerWidget,
                    context,
                    constraints,
                    _courseCubit,
                  ),
                  // _hasAccessToSaveProgress.value
                  //     ? _saveProgressBtn(
                  //         context, _courseCubit, state, screenSize)
                  //     : Container(),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget webPlayerWidget(
    Widget videoPlayerWidget,
    BuildContext context,
    CourseListCubit courseCubit,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
      child: Column(
        children: [
          ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: videoPlayerWidget),
          const SizedBox(height: 5),
          courseVideo.isCheckpointEnabled &&
                  kIsWeb &&
                  checkPoints.isNotEmpty &&
                  _isYoutubeVideo
              ? webPlayerCustomControls(context)
              : Container(),
          _userEngagementWidgets(context),
          _videoTitle(),
          const SizedBox(height: 1),
          _videoDesc(context),
          Expanded(
            child: Scrollbar(
              controller: _commentListController,
              thumbVisibility: true,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: ScrollConfiguration(
                  behavior: ScrollConfiguration.of(context)
                      .copyWith(scrollbars: false, dragDevices: {
                    PointerDeviceKind.touch,
                    PointerDeviceKind.mouse,
                  }),
                  child: ListView(
                    shrinkWrap: true,
                    controller: _commentListController,
                    children: [
                      _resourceComments.isNotEmpty
                          ? const Divider(
                              thickness: 1,
                              height: 1,
                              color: AppTheme.dividerColorInReview,
                            )
                          : Container(),
                      SizedBox(height: _resourceComments.isEmpty ? 0 : 15),
                      _commentsHead(),
                      _chatListView(
                        context,
                        courseCubit,
                      ),
                      const SizedBox(height: 60),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _customScrollWidget(
    Widget videoPlayerWidget,
    BuildContext context,
    BoxConstraints constraints,
    CourseListCubit courseCubit,
  ) {
    final courseCubit = BlocProvider.of<CourseListCubit>(context);
    double width = MediaQuery.sizeOf(context).width;
    return Positioned(
      //  (_enableSaveProgressBtn.value == true ||
      //               courseProgess?.markedAsDone == true) &&
      //           _hasAccessToSaveProgress.value
      //       ? 50
      top: 0,
      bottom: 0,
      right: 0,
      left: 0,
      child: kIsWeb
          ? webPlayerWidget(videoPlayerWidget, context, courseCubit)
          : CustomScrollView(
              primary: true,
              slivers: [
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Container(
                    margin: EdgeInsets.symmetric(
                        horizontal: _horizontalMargin,
                        vertical: _horizontalMargin),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        videoPlayerWidget,
                        const SizedBox(height: 5),
                        courseVideo.isCheckpointEnabled && kIsWeb
                            ? webPlayerCustomControls(context)
                            : Container(),
                        _userEngagementWidgets(context),
                        _videoTitle(),
                        const SizedBox(height: 1),
                        _videoDesc(context),
                        SizedBox(height: _resourceComments.isEmpty ? 50 : 10),
                        _resourceComments.isNotEmpty
                            ? const Divider(
                                thickness: 1,
                                height: 1,
                                color: AppTheme.dividerColorInReview,
                              )
                            : Container(),
                        SizedBox(height: _resourceComments.isEmpty ? 0 : 8),

                        CommentFeatureWidget(
                          resourceId: courseVideo.id,
                          comments: _resourceComments,
                          commentController: _commentController,
                          tabIndex: tabIndex,
                          commentNotifier: _commentNotifier,
                          courseCubit: courseCubit,
                          isFullScreen: _fullScreenView,
                          enableClickOnPlayer: enableClickOnPlayer,
                        ),

                        // _resourceComments.isEmpty
                        //     ? Container()
                        //     : _commentsHead(),
                        // SizedBox(height: _resourceComments.isEmpty ? 0 : 12),
                        // Flexible(child: _chatListView(context, courseCubit)),
                        const SizedBox(height: 60),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget webPlayerCustomControls(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.border,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10),
      width: MediaQuery.sizeOf(context).width,
      child: IFrame.YoutubeValueBuilder(
          controller: _webPlayerController,
          builder: (context1, value) {
            return Row(
              children: [
                // TODO : implement play or pause button
                // IconButton(
                //   onPressed: () {
                //     print("value.playerState => ${value.playerState}");
                //     print(
                //         "value.playerState => ${_webPlayerController?.value.playerState}");
                //     if (value.playerState == IFrame.PlayerState.unknown) {
                //       print("Webplayercontrller");
                //       print(
                //           "value.playerState => ${_webPlayerController?.value.playerState}");
                //       _webPlayerController?.play();
                //       print(
                //           "value.playerState => ${_webPlayerController?.value.playerState}");
                //     }
                //     value.playerState == IFrame.PlayerState.unknown
                //         ? _webPlayerController?.play()
                //         : _webPlayerController?.pause();
                //   },
                //   icon: Icon(
                //     value.playerState == IFrame.PlayerState.playing
                //         ? Icons.pause
                //         : Icons.play_arrow,
                //   ),
                // ),
                // const SizedBox(width: 10),
                Text(_formattedTime(timeInSecond: value.position.inSeconds)),
                const SizedBox(width: 10),
                Expanded(
                  child: LinearProgressIndicator(
                    value: _webPlayerController?.value.isReady ?? false
                        ? _getVideoPercentage()
                        : 0,
                    backgroundColor: AppTheme.whiteColor,
                    color: AppTheme.bgColor,
                  ),
                ),
                const SizedBox(width: 10),
                _rewindButton(),
                const SizedBox(width: 10),
                Text(_formattedTime(
                    timeInSecond: value.metaData.duration.inSeconds))
              ],
            );
          }),
    );
  }

  Widget _thumbnailPlaceHolder(BuildContext context) {
    double screenWidth = MediaQuery.sizeOf(context).width;
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
              color: AppTheme.pitchBlack.withOpacity(0.2),
              blurRadius: 23.0,
              offset: const Offset(0, 1),
              spreadRadius: 1),
        ],
      ),
      child: Image.asset(
        '$ASSETS_PATH/video_play.png',
      ),
    );
  }

  _saveProgressAction(
      BuildContext context, CourseListCubit _courseCubit) async {
    // if (courseProgress?.progress == 100.0) {
    //   return;
    // }
    _courseCubit.setLoading(true);

    CourseListState state = _courseCubit.state;
    _didSelectProgressBtn = true;
    if (kIsWeb) {
      _webPlayerController?.pause();
    } else if (_isYoutubeVideo) {
      _youtubePlayerController?.pause();
    } else {
      _videoPlayerController?.pause();
    }
    Map<String, dynamic> duration = kIsWeb
        ? _getWebPlayerDurationValue(state)
        : _isYoutubeVideo
            ? _getYoutubeDurationValue(state)
            : _getVideoDurationValue(state);

    if (courseVideo.courseId != null) {
      await _courseCubit.setCourseProgress(
          courseVideo.courseId, courseVideo.id, duration);
    }
    state = _courseCubit.state;

    await _userActivityLog.setResProgressActivityLog(
        context: context,
        screen: 'Course Video',
        resourceType: 'Video',
        id: courseVideo.id,
        result: state is SetResourceProgressState ? 'success' : 'error',
        responseStatus: state is SetResourceProgressState
            ? 'Progress updated'
            : state is ResourceProgressFailureState
                ? state.error
                : 'Progress update failed');

    if (state is SetResourceProgressState) {
      await _saveVideoProgressToDB(context, _courseCubit);
    }
    _courseCubit.setLoading(false);
  }

  /// social media btns like- like, comment
  Widget _userEngagementWidgets(BuildContext context) {
    int commentCount = _resourceComments
        .where((element) => element.resActivityTpe == ResActivityType.comment)
        .toList()
        .length;
    return Column(
      children: [
        _customSeekControlWidgets(),
        UserEngagementRow(
          resourceId: courseVideo.id,
          isLiked: _isLiked,
          likes: _likes,
          likeScale: _likeScale,
          confettiController: _confettiController,
          appDynamicTheme: _appDynamicTheme,
          commentCount: commentCount,
          enableLike: (courseProgress?.progress ?? 0) < 100,
          onCommentTapped: () {},
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  // Widget _likeBtn(BuildContext context, int count) {
  //   return ValueListenableBuilder<bool>(
  //     valueListenable: _isLiked,
  //     builder: (context, isLiked, _) {
  //       return Stack(
  //         alignment: Alignment.center,
  //         children: [
  //           // Sparkles
  //           Positioned(
  //             child: ConfettiWidget(
  //               confettiController: _confettiController,
  //               blastDirectionality: BlastDirectionality.explosive,
  //               emissionFrequency: 0.5,
  //               numberOfParticles: 10,
  //               gravity: 0.02,
  //               minimumSize: const Size(3, 3),
  //               maximumSize: const Size(5, 5),
  //               colors: const [
  //                 Colors.yellow,
  //                 Colors.pink,
  //                 Colors.orange,
  //                 Colors.purple,
  //                 Colors.blue,
  //               ],
  //             ),
  //           ),
  //           // Bouncing Like Button
  //           ValueListenableBuilder<double>(
  //             valueListenable: _likeScale,
  //             builder: (context, scale, _) {
  //               return Transform.scale(
  //                 scale: scale,
  //                 child: Row(
  //                   children: [
  //                     IconButton(
  //                       iconSize: 28,
  //                       padding: EdgeInsets.zero,
  //                       constraints:
  //                           const BoxConstraints(), // Removes extra space
  //                       icon: Icon(
  //                         isLiked ? Icons.favorite : Icons.favorite_border,
  //                         color: isLiked
  //                             ? AppTheme.primaryRed
  //                             : AppTheme.coursePecentageTxtGrey,
  //                         size: 28,
  //                       ),
  //                       onPressed: () {
  //                         if (!isLiked) {
  //                           // Animate bounce
  //                           _likeScale.value = 1.3;
  //                           Future.delayed(const Duration(milliseconds: 100),
  //                               () {
  //                             _likeScale.value = 1.0;
  //                           });
  //                           // Show sparkles
  //                           _confettiController.play();
  //                         }

  //                         _isLiked.value = !isLiked;
  //                         _likes.value += _isLiked.value ? 1 : -1;
  //                         // Optionally: Call API to update like status here
  //                       },
  //                     ),
  //                     const SizedBox(width: 4),
  //                     Text(_likes.value.toString(),
  //                         style: LMSFonts.mediumFont(
  //                             14,
  //                             _appDynamicTheme?.primaryTextColor ??
  //                                 AppTheme.primaryTextColorBlack,
  //                             1.0)),
  //                   ],
  //                 ),
  //               );
  //             },
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }

  // /// Widget to display a comment icon with the number of comments
  // Widget _commentCountWidget({int count = 0}) {
  //   return Row(
  //     mainAxisSize: MainAxisSize.min,
  //     children: [
  //       Image.asset(
  //         '$ASSETS_PATH/comment.png',
  //         width: 28,
  //         height: 28,
  //         color: AppTheme.coursePecentageTxtGrey,
  //       ),
  //       const SizedBox(width: 5),
  //       Text(count.toString(),
  //           style: LMSFonts.mediumFont(
  //               14,
  //               _appDynamicTheme?.primaryTextColor ??
  //                   AppTheme.primaryTextColorBlack,
  //               1.0)),
  //     ],
  //   );
  // }

  Widget _customSeekControlWidgets() {
    return Visibility(
      visible: !(courseVideo.isCheckpointEnabled && checkPoints.isNotEmpty),
      child: ValueListenableBuilder<SeekDurations>(
        valueListenable: _selectedDurations,
        builder: (context, durations, _) {
          return VideoControlWidget(
            onTap: _handleSeek,
            durations: const [5, 10, 15, 20, 30],
            isVideoOnPause: !(_youtubePlayerController != null &&
                _youtubePlayerController!.value.isPlaying),
            selectedLeft: durations.left,
            selectedRight: durations.right,
            handleRightDurationSelection: (value) {
              _selectedDurations.value = durations.copyWith(right: value);
            },
            handleLeftDurationSelection: (value) {
              _selectedDurations.value = durations.copyWith(left: value);
            },
            handlePlayPause: () {
              _selectedDurations.value = durations.copyWith(
                isVideoOnPause: !_selectedDurations.value.isVideoOnPause,
              );
              _togglePlayPause(durations.isVideoOnPause);
            },
          );
        },
      ),
    );
  }

  void _handleSeek(int sec) {
    _pauseVideo.value = true;

    final currentPosition = _getCurrentVideoPosition();
    final newPosition = currentPosition + Duration(seconds: sec);

    _seekTo(newPosition);
    _pauseVideo.value = false;
  }

  Duration _getCurrentVideoPosition() {
    if (kIsWeb) {
      return _webPlayerController?.value.position ?? Duration.zero;
    } else if (_isYoutubeVideo) {
      return _youtubePlayerController?.value.position ?? Duration.zero;
    } else {
      return _videoPlayerController?.value.position ?? Duration.zero;
    }
  }

  void _seekTo(Duration position) {
    if (kIsWeb) {
      _webPlayerController?.seekTo(Duration(seconds: position.inSeconds));
    } else if (_isYoutubeVideo) {
      _youtubePlayerController?.seekTo(Duration(seconds: position.inSeconds));
    } else {
      _videoPlayerController?.seekTo(Duration(seconds: position.inSeconds));
    }
  }

  void _togglePlayPause(bool pauseMode) {
    final shouldPause = pauseMode;
    if (kIsWeb) {
      if (_webPlayerController?.value.isReady == true) {
        shouldPause
            ? _webPlayerController?.pause()
            : _webPlayerController?.play();
      } else {
        _webPlayerController?.pause();
      }
    } else if (_isYoutubeVideo) {
      if (_youtubePlayerController?.value.isReady == true) {
        shouldPause
            ? _youtubePlayerController?.pause()
            : _youtubePlayerController?.play();
      } else {
        _youtubePlayerController?.pause();
      }
    } else {
      if (_videoPlayerController?.value.isInitialized == true) {
        shouldPause
            ? _videoPlayerController?.pause()
            : _videoPlayerController?.play();
      } else {
        _videoPlayerController?.pause();
      }
    }
  }

  _videoTitle() {
    return Visibility(
      visible: !_fullScreenView,
      child: Container(
        alignment: Alignment.centerLeft,
        child: Text(
          courseVideo.name.trim(),
          textAlign: TextAlign.left,
          style:
              LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
        ),
      ),
    );
  }

  _videoDesc(BuildContext context) {
    return Visibility(
        visible: !_fullScreenView,
        child: kIsWeb
            ? LayoutBuilder(
                builder: (context, constraints) {
                  TextPainter textPainter = TextPainter(
                    textDirection: TextDirection.ltr,
                    text: TextSpan(
                      text: courseVideo.description,
                      style: LMSFonts.regularFontWithHeight(
                        14,
                        AppTheme.courseVideoPrimaryTextColor,
                        1.5,
                      ),
                    ),
                    maxLines: 4,
                  );
                  textPainter.layout(
                      maxWidth: constraints
                          .maxWidth); // Determine if scrolling is needed based on text length and container width

                  if (textPainter.didExceedMaxLines) {
                    return ScrollConfiguration(
                      behavior: ScrollConfiguration.of(context)
                          .copyWith(scrollbars: true, dragDevices: {
                        PointerDeviceKind.touch,
                        PointerDeviceKind.mouse,
                      }),
                      child: SizedBox(
                        height: MediaQuery.sizeOf(context).height * 0.10,
                        child: SingleChildScrollView(
                          child: Text(
                            courseVideo.description,
                            style: LMSFonts.regularFontWithHeight(
                              14,
                              AppTheme.courseVideoPrimaryTextColor,
                              1.5,
                            ),
                          ),
                        ),
                      ),
                    );
                  } else {
                    return Container(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        courseVideo.description,
                        style: LMSFonts.regularFontWithHeight(
                          14,
                          AppTheme.courseVideoPrimaryTextColor,
                          1.5,
                        ),
                        maxLines: 4,
                      ),
                    );
                  }
                },
              )
            :
            // child:
            Container(
                alignment: Alignment.centerLeft,
                child: ValueListenableBuilder(
                  valueListenable: readMore,
                  builder: (context, value, child) {
                    final text = courseVideo.description;
                    final textPainter = TextPainter(
                      textDirection: TextDirection.ltr,
                      text: TextSpan(
                        text: text,
                        style: LMSFonts.regularFontWithHeight(
                          14,
                          AppTheme.courseVideoPrimaryTextColor,
                          1.5,
                        ),
                      ),
                      maxLines: 4,
                    )..layout(maxWidth: MediaQuery.sizeOf(context).width);

                    bool isOverflowing = textPainter.didExceedMaxLines;

                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          text,
                          maxLines: value ? null : 4,
                          overflow: value ? null : TextOverflow.ellipsis,
                          style: LMSFonts.regularFontWithHeight(
                            14,
                            AppTheme.courseVideoPrimaryTextColor,
                            1.5,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Align(
                          alignment: Alignment.topRight,
                          child: Visibility(
                              visible: isOverflowing,
                              child: InkWell(
                                  onTap: () {
                                    readMore.value = !readMore.value;
                                  },
                                  child: Text(
                                    value ? "View less" : "View more",
                                    style: LMSFonts.regularFontWithHeight(
                                        10, AppTheme.nextBtnColor, 1),
                                  ))),
                        )
                      ],
                    );
                  },
                ),
              ));
  }

  void _showProgressDialog(
      BuildContext context, Map<String, dynamic> duration) {
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
            alertType: AlertType.success,
            title: SLStrings.getTranslatedString(KEY_SUCCESS),
            content: SLStrings.getTranslatedString(
                KEY_PROGRESS_SUCESS_ALERT_CONTENT),
            cancelBtnText: "",
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
            onCancelTap: () {},
            onContinueTap: didSaveProgress == false
                ? () {
                    Navigator.of(context).pop();
                    appRouter.popForced(duration);
                  }
                : () {
                    kIsWeb
                        ? Beamer.of(context).beamBack()
                        : appRouter.popForced(duration);
                  });
      },
    ).then((value) => enableClickOnPlayer());
  }

  _onBackTapped(BuildContext context, CourseListState state) async {
    try {
      if (courseProgress != null && (courseProgress!.progress) >= 100) {
        Map<String, dynamic> duration = _getDurationForVideo(100, state);
        await _setResourceActivityLog(context,
            status: ResourceActivityStatus.completed);
        kIsWeb ? Beamer.of(context).beamBack() : appRouter.popForced(duration);
      } else {
        _isDataLoading.value = true;
        final _courseCubit = BlocProvider.of<CourseListCubit>(context);

        Map<String, dynamic> duration = _isYoutubeVideo
            ? _getYoutubeDurationValue(state)
            : _getVideoDurationValue(state);

        await _saveProgressAction(context, _courseCubit);
        await _setResourceActivityLog(context,
            status: ResourceActivityStatus.inProgress);
        Future.delayed(const Duration(milliseconds: 500), () {
          _isDataLoading.value = true;
          isPlayerClosed.value = true;

          if (kIsWeb) {
            Beamer.of(context).beamBack();
          } else {
            if (isFromExamScreen) {
              handleBackNavFromScreens(context, navigateBackTo);
            } else {
              appRouter.popForced(duration);
            }
          }
          _isDataLoading.value = false;
        });
      }
    } on MissingPluginException catch (e) {
      debugPrint("[_onBackTapped] [MissingPluginException] : ${e.toString()}");
    } on Exception catch (e) {
      debugPrint("[_onBackTapped] : ${e.toString()}");
    }
  }

  Map<String, dynamic> _getWebPlayerDurationValue(CourseListState state) {
    double progressValue = _webPlayerController != null
        ? (_currentDuration / _webPlayerController!.metadata.duration.inSeconds)
            .abs()
        : 0.0;

    return _getDurationForVideo(progressValue, state);
  }

  Map<String, dynamic> _getYoutubeDurationValue(CourseListState state) {
    //  courseProgress?.progress == 100.0
    //           ? 100.0
    //           :

    double progressValue = _youtubePlayerController != null
        ? (_currentDuration /
                _youtubePlayerController!.metadata.duration.inSeconds)
            .abs()
        : 0.0;

    return _getDurationForVideo(progressValue, state);
  }

  Map<String, dynamic> _getVideoDurationValue(CourseListState state) {
    Map<String, dynamic> args = {};
    if (_videoPlayerController != null &&
        _videoPlayerController!.value.isInitialized) {
      Duration totalDuration =
          _videoPlayerController?.value.duration ?? const Duration();
      double progressValue = courseProgress?.progress == 100.0
          ? 100.0
          : (_currentDuration / totalDuration.inSeconds).abs();

      args = _getDurationForVideo(progressValue, state);
    }
    return args;
  }

  Map<String, dynamic> _getDurationForVideo(
      double progressValue, CourseListState state) {
    // Ensure progressValue is within the range [0, 1]
    progressValue = //(progressValue.isNaN || progressValue.isInfinite|| )
        progressValue.isFinite ? progressValue.clamp(0.0, 1.0) : 0.0;

    // Update video progress in the state
    state.videoProgress = progressValue;

    // Calculate progress percentage
    String progress = (progressValue * 100).toStringAsFixed(2);
    progress = progress == 'NaN' ? '0' : progress;

    // Format duration
    Duration duration = Duration(seconds: _currentDuration.toInt());
    String time = formatDuration(duration);

    return {'progress': progress, 'time_spent': time};
  }

  String formatDuration(Duration duration) {
    int hours = duration.inHours;
    int minutes = duration.inMinutes.remainder(60);
    int seconds = duration.inSeconds.remainder(60);

    String hoursString = hours > 1 ? HOURS : HOUR;
    String minutesString = minutes > 1 ? MINUTES : MINUTE;
    String secondsString = seconds > 1 ? SECONDS : SECOND;

    return '$hours $hoursString $minutes $minutesString $seconds $secondsString';
  }

  Widget _chewiePlayerWidget(BuildContext context, CourseListCubit _courseCubit,
      CourseListState state, Size size) {
    return _mainContainer(context, _courseCubit, state, size,
        videoPlayerWidget: _videoPlayer(size, context));
  }

  _videoPlayer(Size size, BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: _chewieController != null
            ? Chewie(
                controller: _chewieController!,
              )
            : _thumbnailPlaceHolder(context),
      ),
    );
  }

  ///
  /// comments
  ///

  Widget _commentsHead() {
    return Visibility(
      visible: _resourceComments.isNotEmpty && courseVideo.id.isNotEmpty,
      child: Text(
        SLStrings.getTranslatedString(KEY_VIDEO_COMMENTS),
        style: LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
      ),
    );
  }

  Widget _commentsTabItem(String label, bool isActive) {
    return Container(
        height: 35,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isActive ? Colors.white : Colors.transparent,
        ),
        width: double.infinity / 1,
        margin: const EdgeInsets.symmetric(vertical: 2),
        child: Tab(text: label));
  }

  Widget _chatListView(BuildContext mainContext, CourseListCubit courseCubit) {
    int feedbackCount = _resourceComments
        .where((element) => element.commentType == CommentType.FEEDBACK)
        .toList()
        .length;

    int suggestionCount = _resourceComments
        .where((element) => element.commentType == CommentType.SUGGESTION)
        .toList()
        .length;
    return Visibility(
      visible: courseVideo.id.isNotEmpty && _resourceComments.isNotEmpty,
      child: ValueListenableBuilder<int>(
        valueListenable: tabIndex,
        builder: (context, updatedTabIndex, __) {
          return DefaultTabController(
            length: 2,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade300,
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: AppTheme.courseVideoPrimaryTextColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: AppTheme.transparentColor,
                    padding: EdgeInsets.zero,
                    tabs: [
                      _commentsTabItem(
                          'Feedback ($feedbackCount)', tabIndex.value == 0),
                      _commentsTabItem(
                          'Suggestion ($suggestionCount)', tabIndex.value == 1),
                    ],
                    onTap: (index) {
                      tabIndex.value = index;
                      _commentNotifier.value = _commentNotifier.value.copyWith(
                        selectedCommentType:
                            index == 0 ? 'Feedback' : 'Suggestion',
                      );
                    },
                  ),
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: IndexedStack(
                    index: updatedTabIndex,
                    children: [
                      _buildCommentList(
                        mainContext,
                        courseCubit,
                        _resourceComments
                            .where((comment) =>
                                comment.commentType == CommentType.FEEDBACK)
                            .toList(),
                      ),
                      _buildCommentList(
                        mainContext,
                        courseCubit,
                        _resourceComments
                            .where((comment) =>
                                comment.commentType == CommentType.SUGGESTION)
                            .toList(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCommentList(BuildContext mainContext,
      CourseListCubit courseCubit, List<ResourseComment> comments) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(comments.length, (index) {
        final comment = comments[index];
        return Column(
          children: [
            _chatItem(mainContext, courseCubit, comment),
            if (comment.children != null)
              Padding(
                padding: const EdgeInsets.only(left: 34),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(
                    comment.children!.length,
                    (childIndex) => _chatItem(mainContext, courseCubit,
                        comment.children![childIndex]),
                  ),
                ),
              ),
          ],
        );
      }),
    );
  }

  Widget _chatItem(BuildContext mainContext, CourseListCubit courseCubit,
      ResourseComment commentObj) {
    return Card(
      color: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          commentObj.profilePic != null
              ? _profileIcon(commentObj.profilePic)
              : const SizedBox.shrink(),
          const SizedBox(width: 14),
          _userCommentInfo(mainContext, courseCubit, commentObj),
          const SizedBox(width: 10),
          _commentTypeDateInfo(commentObj),
          const SizedBox(width: 5),
        ],
      ),
    );
  }

  Widget _profileIcon(String avatar) {
    return Container(
      height: 40,
      width: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          width: 2,
          color: AppTheme.whiteColor,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.blackColor.withOpacity(0.1),
            blurRadius: 1,
            spreadRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: CircleAvatar(
        backgroundColor: AppTheme.transparentColor,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(5.0),
          child: avatar != null && avatar.trim().isNotEmpty
              ? CachedNetworkImage(
                  imageUrl: avatar,
                  fit: BoxFit.cover,
                  placeholder: (context, url) =>
                      const CircularProgressIndicator(),
                  errorWidget: (context, url, error) => _placeHolderWidget(),
                )
              : _placeHolderWidget(),
        ),
      ),
    );
  }

  Widget _placeHolderWidget() {
    return const ProfilePlaceholderWidget(maxWidth: 44 * 2);
  }

  Widget _userCommentInfo(BuildContext mainContext, CourseListCubit courseCubit,
      ResourseComment commentObj) {
    return Expanded(
      child: Container(
        color: Colors.transparent,
        child: Align(
          alignment: Alignment.topLeft,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // const SizedBox(height: 2),
              Text(
                commentObj.user,
                style: LMSFonts.mediumFont(
                    14, AppTheme.courseVideoPrimaryTextColor, 1.2),
              ),
              const SizedBox(height: 4),
              Text(
                commentObj.message.toString().trim(),
                style: LMSFonts.regularFontWithHeight(
                    12, AppTheme.courseVideoPrimaryTextColor, 1.2),
              ),
              const SizedBox(height: 5),
              commentObj.roleName == RoleName.ADMIN
                  ? GestureDetector(
                      onTap: () {
                        //reply action
                        _showCommentBox(mainContext, courseCubit,
                            parentId: commentObj.parentId,
                            roleName: commentObj.roleName);
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Row(
                          children: [
                            const Icon(
                              Icons.reply,
                              color: AppTheme.coursePecentageTxtGrey,
                              size: 16,
                            ),
                            Text(
                              ' Reply',
                              style: LMSFonts.regularFontWithHeight(
                                  12, AppTheme.coursePecentageTxtGrey, 1.2),
                            ),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
              SizedBox(height: commentObj.commentedTime != null ? 5 : 0),
            ],
          ),
        ),
      ),
    );
  }

  Widget _commentTypeDateInfo(ResourseComment commentObj) {
    return Container(
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const SizedBox(height: 2),
          commentStatusIcon(commentObj.commentStatus),
          const SizedBox(height: 7),
          Text(
            commentObj.commentedTime != null
                ? AppDateFormatter().formatDatedmmmmy(commentObj.commentedTime!)
                : '',
            style: LMSFonts.regularFontWithHeight(
                12, AppTheme.courseCommentDate, 1.2),
          ),
        ],
      ),
    );
  }

  Widget commentStatusIcon(CommentStatus commentStatus) {
    IconData iconData;
    Color iconColor;

    switch (commentStatus.name.toLowerCase()) {
      case 'approved':
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case 'pending':
        iconData = Icons.hourglass_empty;
        iconColor = Colors.orange;
        break;
      case 'rejected':
        iconData = Icons.cancel;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.help_outline;
        iconColor = Colors.grey;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 20,
    );
  }

  Widget _commentBtn(BuildContext context, CourseListCubit courseCubit) {
    return Visibility(
      visible: courseVideo.id.isNotEmpty && !_fullScreenView,
      child: Positioned(
        bottom: 8,
        right: 8,
        child: BlocBuilder<ConnectivityCubit, ConnectivityState>(
          builder: (context, state) {
            return ValueListenableBuilder(
                valueListenable: isPlayerReady,
                builder: (context, playerReady, _) {
                  return IconButton(
                      iconSize: 50,
                      icon: Image.asset('$ASSETS_PATH/comment_icon.png'),
                      onPressed: state is! InternetConnected || !playerReady
                          ? null
                          : () async {
                              if (kIsWeb) {
                                disableClickOnPlayer();
                                _webPlayerController?.pause();
                              } else {
                                _youtubePlayerController?.pause();
                              }
                              _commentController.clear();
                              // disableClickOnPlayer();
                              _showCommentBox(context, courseCubit,
                                  parentId: null, roleName: null);
                            });
                });
          },
        ),
      ),
    );
  }

  _showCommentBox(BuildContext mainContext, CourseListCubit courseCubit,
      {required String? parentId, required RoleName? roleName}) {
    showDialog(
        context: mainContext,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return BlocBuilder<CourseListCubit, CourseListState>(
            builder: (context, state) {
              return ValueListenableBuilder<AddCommentFunc>(
                  valueListenable: _commentNotifier,
                  builder: (context, commentNotifierObj, _) {
                    return AddCommentDialouge(
                      commentController: _commentController,
                      courseCubit: courseCubit,
                      selectedType: _commentNotifier
                              .value.selectedCommentType.isEmpty
                          ? (tabIndex.value == 0 ? 'Feedback' : 'Suggestion')
                          : commentNotifierObj.selectedCommentType,
                      enableSubmitBtn: commentNotifierObj.enableSubmitBtn,
                      enableDropdown:
                          parentId == null, // the comment is not a reply
                      onSubmit: () async {
                        if (commentNotifierObj.enableSubmitBtn) {
                          _commentNotifier.value = commentNotifierObj.copyWith(
                            enableSubmitBtn: false,
                          );

                          await _addNewComment(
                            context,
                            courseCubit,
                            _commentController.text.trim(),
                            parentId: parentId,
                            roleName: roleName,
                          );
                          _commentController.clear();
                        }
                      },
                      onTypeSelected: (val) {
                        _commentNotifier.value = commentNotifierObj.copyWith(
                          selectedCommentType: val,
                        );
                      },
                      onChanged: (val) {
                        _commentNotifier.value = commentNotifierObj.copyWith(
                            enableSubmitBtn:
                                _commentController.text.trim().isNotEmpty);
                      },
                      onClose: () {
                        kIsWeb ? enableClickOnPlayer() : null;
                        Navigator.of(context).pop();
                      },
                    );
                  });
            },
          );
        });
  }

  _fetchComments(BuildContext context, CourseListCubit cubit) async {
    bool _hasAccess = await _checkAccessToGetComments();
    if (_hasAccess) {
      String instanceId = courseVideo.id;
      await cubit.setLoading(true);
      await cubit.fetchCommentsForId(instanceId);
      CourseListState state = cubit.state;
      if (state is CourseCommentsFetched) {
        _isDataLoading.value = false;
        _resourceComments = state.comments
            .where(
                (element) => element.resActivityTpe == ResActivityType.comment)
            .toList();

        _resourceLikes = state.comments
            .where((element) => element.resActivityTpe == ResActivityType.like)
            .toList();
        int likeCount = _resourceLikes.length;
        _isLiked.value = _resourceLikes.any((like) =>
            like.user ==
            SLUser.shared.first_name + ' ' + SLUser.shared.last_name);

        _likes.value = likeCount;
      } else if (state is CommentsError) {
        _showCommentError(context, cubit, state.error);
      }
      await cubit.setLoading(false);
    } else {
      // no access to fetch comments
    }
  }

  _addNewComment(BuildContext context, CourseListCubit cubit, String comment,
      {required String? parentId, required RoleName? roleName}) async {
    /// locally update first
    /// upload to server in background
    ///
    String commentType = _commentNotifier.value.selectedCommentType;

    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    try {
      ResourseComment _studentComment = ResourseComment(
          commentId: '',
          parentId: parentId ?? '',
          message: comment,
          subject: TOPIC_NAME,
          commentType: CommentType.values.firstWhere((e) =>
              e.toString().split('.').last.toLowerCase() ==
              commentType.toLowerCase()),
          user: SLUser.shared.first_name + ' ' + SLUser.shared.last_name,
          commentedTime: DateTime.now(),
          profilePic: SLUser.shared.avatar_url,
          commentStatus: CommentStatus.PENDING,
          roleName: SLUser.shared.userRole ?? RoleName.STUDENT,
          resActivityTpe: ResActivityType.comment);

      Map<String, dynamic> jsonReqBody = parentId != null
          ? {
              "instance_id": courseVideo.id,
              "user_id": USER_ID,
              "comment_data": {
                "parent_id": parentId,
                "subject": TOPIC_ID,
                "message": comment,
                "type": commentType,
                'activity_type': ResActivityType.comment.name
              }
            }
          : {
              "instance_id": courseVideo.id,
              "user_id": USER_ID,
              "comment_data": {
                "subject": TOPIC_ID,
                "message": comment,
                "type": commentType,
                "activity_type": ResActivityType.comment.name
              }
            };

      await cubit.setLoading(true);
      await cubit.uploadComment(jsonReqBody);
      CourseListState state = cubit.state;
      Navigator.pop(context);

      ///
      /// activity log
      ///
      final isSuccess = state is CourseCommentsUploaded;
      final logResult = isSuccess
          ? 'success'
          : state is CommentsError
              ? state.error
              : 'unknown error';
      final actionDetails = isSuccess ? 'Comment added' : 'Add comment failure';
      final actionComment = isSuccess
          ? 'Commented at ${DateTime.now()}'
          : 'Comment failed at ${DateTime.now()}';

      final reqJson = {
        'activity_type': 'Course_Resource',
        'screen_name': 'Course Video',
        'action_details': actionDetails,
        'target_id': courseVideo.id,
        'action_comment': actionComment,
        'log_result': logResult,
      };

      await _appConfigCubit.setUserActivityLog(reqJson);

      ///

      if (state is CommentsError) {
        _showCommentError(context, cubit, state.error);
      } else {
        _commentController.clear();
        parentId = null;
        roleName = null;
        await cubit.addComment(_studentComment, _resourceComments);
        cubit.setLoading(false);
        _showSubmissionDialgoue(context);
      }
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  _showCommentError(BuildContext context, CourseListCubit cubit, String error) {
    _isDataLoading.value = false;
    cubit.setLoading(false);
    showPGExceptionPopup(context, error);
  }

  // Function to show the dialogue
  void _showSubmissionDialgoue(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: SingleActionDialogue(
            title: '',
            message: SLStrings.getTranslatedString(KEY_COMMENT_SUBMITTED),
            iconWidget: const Icon(Icons.check_circle, size: 60),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: false,
            handleOkCallback: () {
              if (kIsWeb) {
                enableClickOnPlayer();
                _webPlayerController?.play();
              } else {
                _youtubePlayerController?.play();
              }
              Navigator.of(context).pop();
            },
          ),
        );
      },
    );
  }

  ///
  /// checkpoint
  ///
  ///

  void disableClickOnPlayer() {
    if (kIsWeb) {
      try {
        String jsCode = '''
              var iframes = document.getElementsByTagName("iframe"); 
              for(var i = 0; i < iframes.length; i++) { 
                 iframes[i].style.pointerEvents = "none";
              } 
          ''';
        js.context.callMethod("eval", [jsCode]);
        // ignore: avoid_catches_without_on_clauses
      } catch (e) {
        debugPrint('Error _fixYoutubeOnWeb: $e');
      }
    }
  }

  void enableClickOnPlayer() {
    if (kIsWeb) {
      try {
        String jsCode = '''
              var iframes = document.getElementsByTagName("iframe"); 
              for(var i = 0; i < iframes.length; i++) { 
                 iframes[i].style.pointerEvents = "auto";
              } 
          ''';
        js.context.callMethod("eval", [jsCode]);
        // ignore: avoid_catches_without_on_clauses
      } catch (e) {
        debugPrint('Error _fixYoutubeOnWeb: $e');
      }
    }
  }

  // checkpoint
  // custom popup opens depending on the type of milestone
  void _showCheckPointInfoPopup(BuildContext context, CheckPoint item) {
    // bool isActiveRouteInWeb = false;
    // bool isCurrentPageInWeb = false;
    // if (kIsWeb) {
    //   var beamerRouter = Beamer.of(context);
    //   isActiveRouteInWeb = beamerRouter.active;
    //   var routeNameInWeb = beamerRouter.configuration.uri.path;
    //   isCurrentPageInWeb = routeNameInWeb == '/video-view';
    // }

    // var route = ModalRoute.of(context);

    disableClickOnPlayer();
    if (_fullScreenView) {
      _youtubePlayerController?.toggleFullScreenMode();
    }
    // FirebaseAnalytics.instance.logEvent(
    //   name: "milestone_popup_opened",
    //   parameters: {
    //     "content_type": item.checkpointType,
    //     "item_id": item.checkpointId,
    //     "pop_start_at": DateTime.now().toString(),
    //     "milestone_index": item.courseModuleId
    //   },
    // );
    //start timer
    // startCounter();

    /// decides to show popup
    /// checks whether the current route is valid
    /// (If logout occured before popup appears)
    bool shouldShowPopup = checkForActiveRoute(context,
        webCurrentRouteName: '/video-view',
        mobileCurrentRouteName: CourseVideoViewRoute.name);

    // kIsWeb
    //     ? isActiveRouteInWeb && isCurrentPageInWeb
    //     : route != null &&
    //         route.isActive &&
    //         route.settings.name == CourseVideoViewRoute.name;

    if (shouldShowPopup) {
      // Implement your custom popup logic here
      if (item.checkpointType == CheckPointType.alert.value) {
        _showAlertCheckpointPopup(context, item);
      } else if (item.checkpointType == CheckPointType.prompt.value) {
        _showFormFieldCheckpointPopup(context, item);
      } else if (item.checkpointType == CheckPointType.exam.value) {
        _showExamCheckpointPopup(context, item);
      }
    }
  }

  _showAlertCheckpointPopup(BuildContext context, CheckPoint item) {
    return showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Text(SLStrings.getTranslatedString(KEY_POPUP_TITLE)),
            content: Text(SLStrings.getTranslatedString(KEY_POPUP_MSG)),
            actions: <Widget>[
              ValueListenableBuilder(
                  valueListenable: counter,
                  builder: (BuildContext context, int counter, Widget? _) {
                    return Text(_formattedTime(timeInSecond: counter));
                  }),
              TextButton(
                child: Text(SLStrings.getTranslatedString(KEY_YES)),
                onPressed: () {
                  Navigator.of(context).pop();
                  _onClosePopup(item, SLStrings.getTranslatedString(KEY_YES));
                },
              ),
              TextButton(
                child: Text(SLStrings.getTranslatedString(KEY_NO)),
                onPressed: () {
                  Navigator.of(context).pop();
                  _onClosePopup(item, SLStrings.getTranslatedString(KEY_NO));
                },
              ),
            ],
          ),
        );
      },
    ).then((value) => enableClickOnPlayer());
  }

  _showFormFieldCheckpointPopup(BuildContext context, CheckPoint item) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return PopScope(
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              content: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Form(
                  key: _formKey,
                  child: _fullScreenView
                      ? Row(
                          children: [
                            SizedBox(
                                width: 100,
                                child: Text(SLStrings.getTranslatedString(
                                    KEY_PROMPT_TEXT))),
                            Expanded(
                              child: TextFormField(
                                controller: _promptController,
                                validator: (value) {
                                  if (value == null || value.length <= 3) {
                                    return SLStrings.getTranslatedString(
                                        KEY_PROMPT_VALIDATION_MSG);
                                  }
                                },
                              ),
                            ),
                          ],
                        )
                      : SizedBox(
                          height: 150,
                          child: Column(
                            children: [
                              Text(SLStrings.getTranslatedString(
                                  KEY_PROMPT_TEXT)),
                              TextFormField(
                                controller: _promptController,
                                validator: (value) {
                                  if (value == null || value.length <= 3) {
                                    return SLStrings.getTranslatedString(
                                        KEY_PROMPT_VALIDATION_MSG);
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                ),
              ),
              actions: [
                ValueListenableBuilder(
                    valueListenable: counter,
                    builder: (BuildContext context, int counter, Widget? _) {
                      return Text(_formattedTime(timeInSecond: counter));
                    }),
                TextButton(
                  child: Text(SLStrings.getTranslatedString(KEY_SUBMIT),
                      style: LMSFonts.buttonStyle(16)),
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      Navigator.of(context).pop();
                      _onClosePopup(item, _promptController.text);
                    }
                  },
                ),
              ],
            ),
            canPop: false,
          );
        }).then((value) => enableClickOnPlayer());
  }

  // void navigateToExamIntro(BuildContext context, CheckPoint item) {
  //   _courseCubitObj?.setQuestionLoading(true);

  //   _fetchExamQuestions(context, item.instanceId ?? "", item);
  // }

  _showExamCheckpointPopup(BuildContext context, CheckPoint item) {
    return showDialog(
        context: context,
        builder: (context1) => PopScope(
              canPop: false,
              child: SingleActionDialogue(
                title: '',
                message: SLStrings.getTranslatedString(
                    KEY_CHECKPOINT_EXAM_NAVIGATION_MSG),
                buttonText: SLStrings.getTranslatedString(KEY_OK),
                isDefaultAction: false,
                handleOkCallback: () {
                  _courseCubitObj?.setQuestionLoading(true);

                  Navigator.of(context1).pop();
                  _fetchExamQuestions(context, item.instanceId ?? "", item);
                },
              ),
            ));
  }

  _showVideoCompletePopup(BuildContext context) {
    return showDialog(
        context: context,
        builder: (context) => PopScope(
            canPop: false,
            child: SingleActionDialogue(
                title: '',
                message:
                    SLStrings.getTranslatedString(KEY_CHECKPOINT_COMPLETE_MSG),
                buttonText: SLStrings.getTranslatedString(KEY_OK),
                isDefaultAction: false,
                handleOkCallback: () {
                  Navigator.of(context).pop();
                }))).then((value) {
      enableClickOnPlayer();

      Beamer.of(context).beamToNamed('/section-details');
    });
  }

  Future<void> _fetchExamQuestions(
      BuildContext context, String examId, CheckPoint item) async {
    final scaffoldContext = _scaffoldKey.currentState?.context ?? context;
    final examCubit = BlocProvider.of<ExamCubit>(context);
    final appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    await examCubit.startCheckpointQuiz(item.checkpointId ?? '');
    final state = examCubit.state;

    bool isSuccess = state is ExamQuestionsFetched;
    String logResult = isSuccess
        ? 'success'
        : state is ExamPGException
            ? (state).error
            : state is ExamsQuestionsFailureState
                ? (state).error
                : 'unknown error';
    String actionDetails =
        isSuccess ? 'Checkpoint exam started' : 'Checkpoint exam start failure';
    String actionComment = isSuccess
        ? 'Checkpoint exam started at ${DateTime.now()}'
        : 'Checkpoint exam start failed at ${DateTime.now()}';

    if (state is ExamPGException) {
      _courseCubitObj?.setQuestionLoading(false);
      disableClickOnPlayer();
      _showPGExceptionPopup(scaffoldContext, state.error);
    } else if (state is ExamQuestionsFetched) {
      if (state.examData.isNotEmpty) {
        _questionData = state.examData.first;
        _questions = _questionData?.questions ?? [];
      } else {
        _questions = [];
      }

      _courseCubitObj?.setQuestionLoading(false);
      if (isSuccess) {
        if (kIsWeb) enableClickOnPlayer();
        examCubit.updateNavigationStatus(true);
        if (kIsWeb) {
          Beamer.of(context).beamToNamed('/exam-view', data: {
            'examDuration': _questionData?.duration,
            'shouldShowTimer': true,
            'isFromViewResult': false,
            'quizAttemptId': _questionData?.quizAttemptId,
            'questionData': _questionData,
            'isFromVideoView': true
          });
        } else {
          await appRouter.push(ExamViewRoute(
            examDuration: _questionData?.duration ?? 0,
            shouldShowTimer: true,
            isFromViewResult: false,
            quizAttemptId: _questionData?.quizAttemptId ?? '',
            questionData: _questionData!,
            isFromVideoView: true,
            navigateBackTo: navigateBackTo,
          ));
        }
      } else {
        final route = ModalRoute.of(scaffoldContext);
        if (route != null && route.isActive) {
          disableClickOnPlayer();
          _showQuestionsEmptyPopup(scaffoldContext);
        }
      }
    }

    // Logging user activity
    final reqJson = {
      'activity_type': 'Checkpoint',
      'screen_name': 'Course Video View',
      'action_details': actionDetails,
      'target_id': _questionData?.questions.first.quizId ?? '',
      'action_comment': actionComment,
      'log_result': logResult,
    };
    await appConfigCubit.setUserActivityLog(reqJson);
  }

  _showQuestionsEmptyPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        // return CustomAlertDialog(title: SORRY, message: QUESTION_NOT_AVAILABLE);
        return PopScope(
          canPop: false,
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: SLStrings.getTranslatedString(KEY_QUESTION_NOT_AVAILABLE),
            cancelBtnText: "",
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
            alertType: AlertType.error,
            onCancelTap: () {},
            onContinueTap: () {
              isPlayerClosed.value = true;
              if (kIsWeb) {
                Navigator.of(context).pop();
                Beamer.of(context).beamBack();
              } else {
                handleBackNavFromScreens(context, navigateBackTo);
              }
            },
          ),
        );
      },
    ).then((value) => enableClickOnPlayer());
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            alertType: AlertType.error,
            onCancelTap: () => appRouter.popForced(),
            onContinueTap: () {
              isPlayerClosed.value = true;
              if (kIsWeb) {
                Navigator.of(context).pop();
                Beamer.of(context).beamBack();
              } else {
                handleBackNavFromScreens(context, navigateBackTo);
              }
            },
            cancelBtnText: '',
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
          ),
        );
      },
    ).then((value) => enableClickOnPlayer());
  }

  /// sort checkpoints received from api
  _sortCheckpoints(List<CheckPoint> checkpoints) {
    checkpoints.sort((a, b) {
      // Extract the start time strings from the maps
      String startTimeA = a.startTime ?? "";
      String startTimeB = b.startTime ?? "";

      // Convert the start time strings to DateTime objects for comparison
      DateTime dateTimeA = DateTime.parse("1970-01-01 $startTimeA");
      DateTime dateTimeB = DateTime.parse("1970-01-01 $startTimeB");

      // Compare the DateTime objects
      return dateTimeA.compareTo(dateTimeB);
    });
  }

  /// manage checkpoints fetched
  /// check whether
  _manageCheckPoints(BuildContext context, CourseListCubit _courseCubit) {
    if (!kIsWeb) {
      _courseCubit.getVideoPlayerData(courseVideo.id).then((value) {
        if (value.isNotEmpty) {
          _getCompletedCheckpoints(value.first.milestoneDurations);
        }
      });
    } else {
      _courseCubit.hiveGetVideoPlayerData(courseVideo.id).then((value) {
        if (value != null) {
          _getCompletedCheckpoints(value.mileStoneDurations);
        }
      });
    }
    if (isExamPassed) {
      _completedCheckPoints.add(
          _checkPoints[_courseCubitObj?.videoPlayerData['checkPointIndex']]);
      lastWatchTime = _courseCubitObj?.videoPlayerData['lastWatchTime'] + 1;
      _checkPoints.removeRange(
          0, _courseCubitObj?.videoPlayerData['checkPointIndex'] + 1);
      _saveProgressAction(context, _courseCubit);
    } else {
      final lastCheckpointIndex =
          _courseCubitObj?.videoPlayerData['checkPointIndex'];
      if (lastCheckpointIndex == 0) {
        lastWatchTime = 0;
      } else {
        int startTimeInSec = convertCheckPointTimetoSec(
            _checkPoints[lastCheckpointIndex - 1].startTime);
        _checkPoints.removeRange(0, lastCheckpointIndex);
        lastWatchTime = startTimeInSec + 1;
      }
    }
  }

  Future<void> _getLatsWatchTime(CourseListCubit _courseCubit) async {
    //checking the last watchtime of the video from sqlite
    if (!kIsWeb) {
      _courseCubit.getVideoPlayerData(courseVideo.id).then((value) {
        debugPrint('value => ${value.toString()}');
        if (value.isNotEmpty) {
          lastWatchTime = value.first.lastWatchTime;
          _checkPoints.removeRange(0, value.first.milestonesCompleted);
          _getCompletedCheckpoints(value.first.milestoneDurations);
        }
      });
    } else {
      // TODO: disabling for testing in web
      // _courseCubit.hiveGetVideoPlayerData(courseVideo.id).then((value) {
      //   if (value != null) {
      //     lastWatchTime = value.lastWatchTime;
      //     _webPlayerController?.seekTo(Duration(seconds: value.lastWatchTime));
      //     _checkPoints.removeRange(0, value.mileStonesCompleted);
      //     _getCompletedCheckpoints(value.mileStoneDurations);
      //   }
      // });
    }
  }

  _getCompletedCheckpoints(String item) {
    // Extracting content between '(' and ')'
    RegExp regExp = RegExp(r"\((.*?)\)");
    Iterable<Match> matches = regExp.allMatches(item);

    List<String> extractedValues = [];

    // Extracting values from each match
    for (Match match in matches) {
      extractedValues.add(match.group(1)!);
    }
    for (String value in extractedValues) {
      List<String> valuesList = value.split(', ').map((e) => e.trim()).toList();
      CheckPoint checkpoint = CheckPoint(
        orgId: valuesList[0],
        sequence: int.parse(valuesList[1]),
        createdAt: DateTime.parse(valuesList[2]),
        createdBy: valuesList[3],
        startTime: valuesList[4],
        updatedAt: DateTime.parse(valuesList[5]),
        updatedBy: valuesList[6],
        instanceId: valuesList[7],
        moduleName: valuesList[8],
        isMandatory: valuesList[9] == "true" ? true : false,
        checkpointId: valuesList[10],
        moduleTypeId: valuesList[11],
        checkpointName: valuesList[12],
        checkpointType: valuesList[13],
        courseModuleId: valuesList[14],
      );
      _completedCheckPoints.add(checkpoint);
    }
  }

  /// convert checkpoint start time in String("hh:mm:ss") to Duration in seconds
  int convertCheckPointTimetoSec(String? time) {
    List<String> parts = time!.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);
    int seconds = int.parse(parts[2]);
    Duration duration =
        Duration(hours: hours, minutes: minutes, seconds: seconds);
    int timeInSeconds = duration.inSeconds;
    return timeInSeconds;
  }

  _formattedTime({required int timeInSecond}) {
    int sec = timeInSecond % 60;
    int min = (timeInSecond / 60).floor();
    String minute = min.toString().length <= 1 ? "0$min" : "$min";
    String second = sec.toString().length <= 1 ? "0$sec" : "$sec";
    return "$minute : $second";
  }

// Upon the user completing a milestone session, the response will be saved to SQLite and transmitted to Firebase Analytics.
  void _onClosePopup(CheckPoint item, String response) async {
    _timer?.cancel();
    _youtubePlayerController?.play();
    final data = Milestone(
      id: item.courseModuleId ?? "",
      milestoneId: item.checkpointId ?? '',
      type: item.checkpointType ?? "",
      milestoneDuration: counter.value,
    );
    _milestoneDurations.add(data);
    _courseCubitObj?.insertToMilestoneData(MilestoneData(
      videoId: item.courseModuleId ?? "",
      milestoneId: item.checkpointId ?? "",
      sessionStart: DateTime.now()
          .subtract(Duration(seconds: counter.value))
          .millisecondsSinceEpoch,
      sessionEnd: DateTime.now().millisecondsSinceEpoch,
      sessionResponse: response,
      sessionStatus: _milestoneDurations.length == checkPoints.length ? 1 : 0,
    ));
    // FirebaseAnalytics.instance.logEvent(
    //   name: "milestone_popup_closed",
    //   parameters: {
    //     "content_type": item.checkpointType,
    //     "item_id": item.checkpointId,
    //     "popup_close_at": DateTime.now().toString(),
    //     "milestone_duration": counter.value,
    //     "session_response": response,
    //   },
    // );
    counter.value = 0;
  }

  _showVideoPlayerErrorPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: SLStrings.getTranslatedString(KEY_VIDEO_PLAYER_ERROR),
            alertType: AlertType.error,
            onCancelTap: () => appRouter.popForced(),
            onContinueTap: () {
              isPlayerClosed.value = true;
              handleBackNavFromScreens(context, navigateBackTo);
            },
            cancelBtnText: '',
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
          ),
        );
      },
    ).then((value) => enableClickOnPlayer());
  }

  double _getVideoPercentage() {
    if (_webPlayerController?.value.isReady == true) {
      double totalDuration =
          _webPlayerController?.metadata.duration.inMilliseconds.toDouble() ??
              0;
      double currentPosition =
          _webPlayerController?.value.position.inMilliseconds.toDouble() ?? 0;
      double _percentagePlayed = (currentPosition / totalDuration);
      if (_percentagePlayed.isFinite) {
        return _percentagePlayed;
      } else {
        return 0.0;
      }
    } else {
      return 0.0;
    }
  }
}

class AddCommentDialouge extends StatelessWidget {
  final TextEditingController commentController;
  final CourseListCubit courseCubit;
  final String selectedType;
  final bool enableSubmitBtn;
  final bool enableDropdown; // disable dropdown if the comment is a reply.
  final VoidCallback onSubmit;
  final StringCallback onTypeSelected;
  final StringCallback onChanged;
  final VoidCallback onClose;

  const AddCommentDialouge({
    super.key,
    required this.commentController,
    required this.courseCubit,
    required this.selectedType,
    required this.enableSubmitBtn,
    this.enableDropdown = true,
    required this.onSubmit,
    required this.onTypeSelected,
    required this.onChanged,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      },
      child: Center(
        child: Material(
          color: Colors.transparent,
          child: LayoutBuilder(builder: (context, constraints) {
            double webWidth = 379.6;
            // constraints.maxWidth / 5;
            double mobileWidth = constraints.maxWidth;
            double allowedWidth = constraints.maxWidth > 1200
                ? webWidth
                : constraints.maxWidth >= 500
                    ? webWidth
                    : mobileWidth;
            return Container(
              width: allowedWidth,
              margin: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
              decoration: BoxDecoration(
                  color: AppTheme.whiteColor,
                  borderRadius: BorderRadius.circular(8.0)),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 25, horizontal: 20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    enableDropdown
                        ? Align(
                            alignment: Alignment.topLeft,
                            child: Text(
                              SLStrings.getTranslatedString(
                                  KEY_COMMENT_BOX_TITLE),
                              textAlign: TextAlign.center,
                              style: LMSFonts.semiBoldFont(
                                  16, AppTheme.courseVideoPrimaryTextColor),
                            ),
                          )
                        : const SizedBox.shrink(),
                    SizedBox(height: enableDropdown ? 13 : 0),
                    enableDropdown
                        ? _commentTypeOptions(context)
                        : const SizedBox.shrink(),
                    const SizedBox(height: 14),
                    Text(
                      selectedType == FEEDBACK
                          ? SLStrings.getTranslatedString(
                              KEY_COMMENT_BOX_SUB_TITLE_FEEDBACK)
                          : SLStrings.getTranslatedString(
                              KEY_COMMENT_BOX_SUB_TITLE_SUGGESTION),
                      style:
                          LMSFonts.regularFont(14, AppTheme.commentBoxContent),
                    ),
                    const SizedBox(height: 13),
                    _commentBoxTextField(),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(child: _cancelBtnComment(context)),
                        // const SizedBox(width: 2),
                        Expanded(child: _addCommentBtn(context, selectedType))
                      ],
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _commentTypeOptions(BuildContext context) {
    print("selectedType: $selectedType");
    return DropdownButtonHideUnderline(
      child: DropdownButton2<String>(
        isExpanded: true,
        items: resourseCommentType
            .map((String item) => DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: LMSFonts.regularFont(14, AppTheme.commentBoxContent),
                    overflow: TextOverflow.ellipsis,
                  ),
                ))
            .toList(),
        value: selectedType,
        onChanged: (String? newValue) {
          if (newValue != null) onTypeSelected(newValue);
        },
        buttonStyleData: ButtonStyleData(
          height: 35,
          width: double.infinity,
          padding: const EdgeInsets.only(left: 13, right: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: AppTheme.courseVideoPrimaryTextColor),
            color: Colors.white,
          ),
          elevation: 0,
        ),
        dropdownStyleData: DropdownStyleData(
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.courseVideoPrimaryTextColor),
            borderRadius: BorderRadius.circular(10),
            color: Colors.white,
          ),
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 35,
        ),
      ),
    );
  }

  Widget _commentTypeChip(BuildContext context, bool isSelected, String title) {
    return ChoiceChip(
      elevation: 1.0,
      selected: isSelected,
      label: Text(title),
      labelStyle:
          LMSFonts.boldFont(16, isSelected ? Colors.white : AppTheme.iconColor),
      backgroundColor: LmsColors.white,
      disabledColor: LmsColors.white,
      selectedColor: AppTheme.iconColor,
      onSelected: (bool selectedStatus) {
        onTypeSelected(title);
      },
    );
  }

  Widget _commentBoxTextField() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.commentTextFieldBg.withOpacity(0.15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: TextField(
        controller: commentController,
        keyboardType: TextInputType.multiline,
        maxLines: 5,
        onChanged: (val) => onChanged(val),
        style: LMSFonts.regularFont(14, AppTheme.commentBoxContent),
        decoration: InputDecoration(
          fillColor: AppTheme.commentTextFieldBg.withOpacity(0.15),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 13, vertical: 7),
          hintText: SLStrings.getTranslatedString(KEY_COMMENT_BOX_PLACEHOLDER),
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
        ),
      ),
    );
  }

  Widget _addCommentBtn(BuildContext context, String commentType) {
    final _connectivityCubit = BlocProvider.of<ConnectivityCubit>(context);
    Size popupSize = MediaQuery.of(context).size;
    return Align(
      alignment: Alignment.bottomCenter,
      child: ButtonWidget(
          child: Text(
            SLStrings.getTranslatedString(KEY_ADD_COMMENT) + commentType,
            style: LMSFonts.buttonStyle(14),
            textAlign: TextAlign.center,
          ),
          textColor: LmsColors.white,
          // minimumSize: Size(popupSize.width / 2.2, 35),
          color: AppTheme.submitBtnColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(popupSize.width, 35),
          onPressed: enableSubmitBtn ? onSubmit : null),
    );
  }

  Widget _cancelBtnComment(BuildContext context) {
    Size popupSize = MediaQuery.of(context).size;
    return Align(
      alignment: Alignment.bottomCenter,
      child: ButtonWidget(
          child: Text(
            SLStrings.getTranslatedString(KEY_CANCEL),
            style: LMSFonts.buttonStyle(14),
            textAlign: TextAlign.center,
          ),
          textColor: AppTheme.submitBtnColor,
          // minimumSize: Size(popupSize.width / 2.2, 35),
          color: AppTheme.whiteColor,
          shape: RoundedRectangleBorder(
            side: const BorderSide(color: AppTheme.submitBtnColor),
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(popupSize.width, 35),
          onPressed: onClose),
    );
  }

  // Function to show the dialogue
  void _showSubmissionDialgoue(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: SLStrings.getTranslatedString(KEY_COMMENT_SUBMITTED),
          iconWidget: const Icon(Icons.check_circle, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    ).then((value) => onSubmit!());
  }
}

/// class to store the valuenotifier objects
/// that required for add comment feature
class AddCommentFunc {
  final String selectedCommentType;
  final bool enableSubmitBtn;

  AddCommentFunc(
      {required this.selectedCommentType, required this.enableSubmitBtn});

  AddCommentFunc copyWith({
    String? selectedCommentType,
    bool? enableSubmitBtn,
  }) {
    return AddCommentFunc(
      selectedCommentType: selectedCommentType ?? this.selectedCommentType,
      enableSubmitBtn: enableSubmitBtn ?? this.enableSubmitBtn,
    );
  }
}

class UserEngagementRow extends StatelessWidget {
  final ValueNotifier<bool> isLiked;
  final ValueNotifier<int> likes;
  final ValueNotifier<double> likeScale;
  final ConfettiController confettiController;
  final AppDynamicTheme? appDynamicTheme;
  final String resourceId;
  final int commentCount;
  final bool enableLike;
  final VoidCallback onCommentTapped;

  const UserEngagementRow({
    Key? key,
    required this.resourceId,
    required this.isLiked,
    required this.likes,
    required this.likeScale,
    required this.confettiController,
    required this.appDynamicTheme,
    required this.commentCount,
    required this.enableLike,
    required this.onCommentTapped,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _likeBtn(context),
        const SizedBox(width: 12),
        _commentCountWidget(),
      ],
    );
  }

  Widget _likeBtn(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: isLiked,
      builder: (context, liked, _) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // Sparkles
            Positioned(
              child: ConfettiWidget(
                confettiController: confettiController,
                blastDirectionality: BlastDirectionality.explosive,
                emissionFrequency: 0.5,
                numberOfParticles: 10,
                gravity: 0.02,
                minimumSize: const Size(3, 3),
                maximumSize: const Size(5, 5),
                colors: const [
                  Colors.yellow,
                  Colors.pink,
                  Colors.orange,
                  Colors.purple,
                  Colors.blue,
                ],
              ),
            ),
            // Bouncing Like Button
            ValueListenableBuilder<double>(
              valueListenable: likeScale,
              builder: (context, scale, _) {
                return Transform.scale(
                  scale: scale,
                  child: Row(
                    children: [
                      IconButton(
                        iconSize: 28,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        icon: Icon(
                          liked ? Icons.favorite : Icons.favorite_border,
                          color: liked
                              ? AppTheme.primaryRed
                              : AppTheme.coursePecentageTxtGrey,
                          size: 28,
                        ),
                        onPressed: enableLike
                            ? () {
                                if (!liked) {
                                  likeScale.value = 1.3;
                                  Future.delayed(
                                      const Duration(milliseconds: 100), () {
                                    likeScale.value = 1.0;
                                  });
                                  confettiController.play();
                                }
                                isLiked.value = !liked;
                                likes.value += isLiked.value ? 1 : -1;
                                _updateLike(context);
                              }
                            : null,
                      ),
                      const SizedBox(width: 4),
                      ValueListenableBuilder<int>(
                        valueListenable: likes,
                        builder: (context, likeCount, _) {
                          return Text(
                            likeCount.toString(),
                            style: LMSFonts.mediumFont(
                              14,
                              appDynamicTheme?.primaryTextColor ??
                                  AppTheme.primaryTextColorBlack,
                              1.0,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  _updateLike(BuildContext context) async {
    final courseCubit =
        provider.Provider.of<CourseListCubit>(context, listen: false);
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    try {
      Map<String, dynamic> jsonReqBody = {
        "instance_id": resourceId,
        "user_id": USER_ID,
        "comment_data": {
          "parent_id": null,
          "subject": TOPIC_ID,
          "message": '',
          "type": 'Feedback',
          'activity_type': ResActivityType.like.name
        }
      };

      await courseCubit.updateResourceLike(jsonReqBody);
      CourseListState state = courseCubit.state;

      ///
      /// activity log
      ///
      final isSuccess = state is ResourceLikeSuccess;
      final logResult = isSuccess
          ? 'success'
          : state is ResourceLikeError
              ? state.error
              : 'unknown error';
      final actionDetails = isSuccess ? 'Like added' : 'Add like failure';
      final actionComment = isSuccess
          ? 'Liked at ${DateTime.now()}'
          : 'Like failed at ${DateTime.now()}';

      final reqJson = {
        'activity_type': 'Like',
        'screen_name': 'Course Video',
        'action_details': actionDetails,
        'target_id': resourceId,
        'action_comment': actionComment,
        'log_result': logResult,
      };

      await _appConfigCubit.setUserActivityLog(reqJson);

      ///

      if (state is ResourceLikeError) {
        isLiked.value = !isLiked.value;
        likes.value += isLiked.value ? 1 : -1;
        showPGExceptionPopup(context, state.error);
      }
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  Widget _commentCountWidget() {
    return GestureDetector(
      onTap: onCommentTapped,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            '$ASSETS_PATH/comment.png',
            width: 28,
            height: 28,
            color: AppTheme.coursePecentageTxtGrey,
          ),
          const SizedBox(width: 5),
          Text(
            commentCount.toString(),
            style: LMSFonts.mediumFont(
              14,
              appDynamicTheme?.primaryTextColor ??
                  AppTheme.primaryTextColorBlack,
              1.0,
            ),
          ),
        ],
      ),
    );
  }
}

mixin CourseVideoViewScreenMixin {
  // TO DO: Remove later: Checkpoint

  /// corresponding answer/option index of the question
  // final List<int> _selectedAnswerIndexes = [];

  /// list of all the answers student attended
  // final List<Map<String, dynamic>> _answeredQuestionList = [];

  /// json to submit to api
  // Map<String, dynamic> _examJson = {};

  /*
  _startExam(BuildContext context, ExamCubit _examCubit,
      QuestionData questionData, CheckPoint item) async {
    try {
      int examDuration = questionData.duration;
      _examCubit.setLoading(true);
      await _examCubit.startExam(
          questionData.questions, questionData.questions[0].quizId ?? '');

      ExamState _state = _examCubit.state;
      if (_state is ExamPGException) {
        /// no quiz id
        /// request has been rejected by admin
        /// show popup
        _examCubit.setLoading(false);
        _showPGExceptionPopup(context, _state.error);
      } else if (_examCubit.state is ExamQuizIdFailure) {
        /// no quiz id
        /// request has been rejected by admin
        /// show popup
        _examCubit.setLoading(false);
        _showInvalidQuizIdPopup(context);
      } else {
        _examCubit.setLoading(true);
        SharedPreferences _pref = await SharedPreferences.getInstance();
        String quizAttemptId = _pref.getString('quiz_attempt_id') ?? '';
        if (isFromSectionDetails == true) {
          _pref.setBool('isFromSectionDetails', true);
        } else {
          _pref.setBool('isFromSectionDetails', false);
        }
        if (quizAttemptId.isNotEmpty) {
          await _examCubit.saveQstnsToDB(questionData);
          // appRouter.popForced();
          showExamPopup(context, item);
          // appRouter.popForced();
          // appRouter.push(
          //   ExamViewRoute(
          //       examDuration: examDuration,
          //       shouldShowTimer: true,
          //       isFromViewResult: false,
          //       questionData: questionData,
          //       quizAttemptId: quizAttemptId),
          // );
          _examCubit.setLoading(false);
        } else {
          appRouter.popForced();
          _examCubit.setLoading(false);
          _showInvalidQuizIdPopup(context);
        }
      }
    } on Exception catch (e) {
      debugPrint('[Exam Intro View][StartExam]: $e');
    }
  }


  showExamPopup(BuildContext context, CheckPoint item) {
    showDialog(context: context, builder: (context) => examSlider(item));
  }


  Widget examSlider(CheckPoint item) {
    return PopScope(
      onWillPop: () async => false,
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Container(
          width: 500,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            // color: const Color.fromARGB(255, 238, 219, 219),
          ),
          child: BlocBuilder<ExamCubit, ExamState>(
            builder: (context, state) {
              final _examCubit = BlocProvider.of<ExamCubit>(context);
              if (state is ExamListChangeIndex) {
                _currentIndex = state.changedIndex;
              } else if (state is ExamMilestoneCheckBoxStatus) {
                _checkStatus = state.status;
                selectedAnswerIndexes = state.selectedAnswerIndexes;
              }
              return !_fullScreenView
                  ? Container(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                              alignment: Alignment.topLeft,
                              child: Countdown(
                                  controller: countdownController,
                                  seconds: _questionData!.duration * 60,
                                  build: (context, time) {
                                    Duration hours =
                                        Duration(seconds: time.toInt());
                                    String countDownValue = hours.toString();
                                    countDownValue =
                                        countDownValue.split('.').first;
                                    return Text(
                                      countDownValue,
                                      style: LMSFonts.mediumFont(
                                          15, LmsColors.appBarGreen, 1.0),
                                    );
                                  })),
                          CarouselSlider.builder(
                            carouselController: _carouselController,
                            itemCount: _questions.length,
                            itemBuilder: (context, index, realIndex) {
                              final tempList = List.generate(
                                  _questions.length, (int index) => -1,
                                  growable: false);
                              final tempStringList = List.generate(
                                  _questions.length, (index) => '');
                              if (selectedAnswerIndexes.isEmpty) {
                                selectedAnswerIndexes.addAll(tempList);
                                // selectedAnswerIds.addAll(tempStringList);
                              }
                              return SizedBox(
                                  height: 450,
                                  child: Column(
                                    children: [
                                      Text("Question : ${index + 1}"),
                                      const SizedBox(height: 5),
                                      Container(
                                        height: 150,
                                        alignment: Alignment.center,
                                        child: TeXView(
                                          child: TeXViewDocument(
                                              _questions[index].questionText ??
                                                  "No Data"),
                                          renderingEngine:
                                              const TeXViewRenderingEngine
                                                  .katex(),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 350,
                                        child: ListView.separated(
                                            shrinkWrap: true,
                                            itemBuilder: (context, listIndex) {
                                              return Container(
                                                  padding:
                                                      const EdgeInsets.all(8),
                                                  margin:
                                                      const EdgeInsets.all(5),
                                                  decoration: BoxDecoration(
                                                    border: Border.all(),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                    color: selectedAnswerIds
                                                            .value
                                                            .contains(_questions[
                                                                    index]
                                                                .options[
                                                                    listIndex]
                                                                .answerId)
                                                        ? AppTheme
                                                            .primaryAppColor
                                                        : Colors.white,
                                                    // color: selectedAnswerIndexes
                                                    //         .isEmpty
                                                    //     ? Colors.white
                                                    //     : selectedAnswerIndexes
                                                    //                 .length ==
                                                    //             index
                                                    //         ? Colors.white
                                                    //         : selectedAnswerIndexes[
                                                    //                     index] ==
                                                    //                 listIndex +
                                                    //                     1
                                                    //             ? Color
                                                    //                 .fromARGB(
                                                    //                     255,
                                                    //                     155,
                                                    //                     227,
                                                    //                     243)
                                                    //             : Colors.white,
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      Flexible(
                                                        child: TeXView(
                                                          child: TeXViewInkWell(
                                                              rippleEffect:
                                                                  false,
                                                              onTap: (_) async {
                                                                print(
                                                                    "_onAnswerSelected => $index, $listIndex, ${_questions[index].options[listIndex].id},");
                                                                print(
                                                                    "_onAnswerSelected2 => ${_questions[index].options[listIndex]},");
                                                                await _onAnswerSelected(
                                                                    _examCubit,
                                                                    _questions[
                                                                        index],
                                                                    _questions[index]
                                                                            .options[listIndex]
                                                                            .id -
                                                                        1,
                                                                    index,
                                                                    selectedQuestionIndex,
                                                                    selectedAnswerIds);
                                                              },
                                                              // onTap: (_) {
                                                              //   final answerId =
                                                              //       _questions[
                                                              //               index]
                                                              //           .options[
                                                              //               listIndex]
                                                              //           .id;
                                                              //   selectedAnswerIndexes
                                                              //       .removeAt(
                                                              //           index);
                                                              //   selectedAnswerIndexes
                                                              //       .insert(
                                                              //           index,
                                                              //           answerId);
                                                              //   selectedAnswerIds[
                                                              //       index] = _questions[
                                                              //               index]
                                                              //           .options[
                                                              //               listIndex]
                                                              //           .answerId ??
                                                              //       '';
                                                              //   _examCubit
                                                              //       .updateCheckBox(
                                                              //           false,
                                                              //           selectedAnswerIndexes);
                                                              // },
                                                              child: TeXViewDocument(_questions[
                                                                          index]
                                                                      .options[
                                                                          listIndex]
                                                                      .answerVal ??
                                                                  "No Data"),
                                                              id: listIndex
                                                                  .toString()),
                                                        ),
                                                      ),
                                                    ],
                                                  ));
                                            },
                                            separatorBuilder:
                                                (BuildContext context,
                                                        int index) =>
                                                    const Divider(),
                                            itemCount: _questions[index]
                                                    .options
                                                    .length ??
                                                0),
                                      ),
                                    ],
                                  ));
                            },
                            options: CarouselOptions(
                              height: 550,
                              viewportFraction: 1,
                              enableInfiniteScroll: false,
                              onPageChanged: (index, _) {
                                _examCubit.changeIndex(index);
                              },
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              _currentIndex != 0
                                  ? IconButton(
                                      onPressed: () {
                                        _carouselController.previousPage();
                                      },
                                      icon: BACK_ARROW)
                                  : const SizedBox(),
                              _currentIndex != (_questions.length - 1)
                                  ? IconButton(
                                      onPressed: () {
                                        _carouselController.nextPage();
                                      },
                                      icon: const Icon(Icons.arrow_forward_ios))
                                  : IconButton(
                                      onPressed: () async {
                                        await _submitExam(context, _examCubit);
                                        // if (!selectedAnswerIndexes
                                        //     .contains(-1)) {
                                        //   Navigator.of(context).pop();
                                        //   onClosePopup(item,
                                        //       selectedAnswerIds.toString());
                                        // } else {
                                        //   final index =
                                        //       selectedAnswerIndexes.indexWhere(
                                        //           (element) => element == -1);
                                        //   _carouselController
                                        //       .animateToPage(index);
                                        // }
                                      },
                                      icon: const Icon(Icons.check)),
                            ],
                          ),
                        ],
                      ),
                    )
                  : Container(
                      child: Column(
                        children: [
                          CarouselSlider.builder(
                              carouselController: _carouselController,
                              itemCount: _questions.length,
                              itemBuilder: (context, index, _) {
                                final tempList = List.generate(
                                    _questions.length, (int index) => -1,
                                    growable: false);
                                final tempStringList = List.generate(
                                    _questions.length ?? 0, (index) => '');
                                if (selectedAnswerIndexes.isEmpty) {
                                  selectedAnswerIndexes.addAll(tempList);
                                  // selectedAnswerIds.addAll(tempStringList);
                                }
                                return Container(
                                  height: 300,
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 350,
                                        child: Column(
                                          children: [
                                            Text("Question : ${index + 1}"),
                                            const SizedBox(height: 5),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: TeXView(
                                                child: TeXViewDocument(
                                                    _questions[index]
                                                            .questionText ??
                                                        "No Data"),
                                                renderingEngine:
                                                    const TeXViewRenderingEngine
                                                        .katex(),
                                              ),
                                            ),
                                            Container(
                                              margin: EdgeInsets.only(top: 90),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  _currentIndex != 0
                                                      ? IconButton(
                                                          onPressed: () {
                                                            _carouselController
                                                                .previousPage();
                                                          },
                                                          icon: const Icon(Icons
                                                              .arrow_back_ios))
                                                      : const SizedBox(),
                                                  _currentIndex !=
                                                          (_questions.length -
                                                              1)
                                                      ? IconButton(
                                                          onPressed: () {
                                                            _carouselController
                                                                .nextPage();
                                                          },
                                                          icon: const Icon(Icons
                                                              .arrow_forward_ios))
                                                      : IconButton(
                                                          onPressed: () {
                                                            if (!selectedAnswerIndexes
                                                                .contains(-1)) {
                                                              Navigator.of(
                                                                      context)
                                                                  .pop();
                                                              onClosePopup(
                                                                  item,
                                                                  selectedAnswerIds
                                                                      .toString());
                                                            } else {
                                                              final index = selectedAnswerIndexes
                                                                  .indexWhere(
                                                                      (element) =>
                                                                          element ==
                                                                          -1);
                                                              _carouselController
                                                                  .animateToPage(
                                                                      index);
                                                            }
                                                          },
                                                          icon: const Icon(
                                                              Icons.check)),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        child: Column(
                                          children: [
                                            ListView.separated(
                                                shrinkWrap: true,
                                                itemBuilder:
                                                    (context, listIndex) {
                                                  return Container(
                                                      padding:
                                                          EdgeInsets.fromLTRB(
                                                              20, 2, 0, 2),
                                                      decoration: BoxDecoration(
                                                        border: Border.all(),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(16),
                                                        color: selectedAnswerIndexes
                                                                .isEmpty
                                                            ? Colors.white
                                                            : selectedAnswerIndexes
                                                                        .length ==
                                                                    index
                                                                ? Colors.white
                                                                : selectedAnswerIndexes[
                                                                            index] ==
                                                                        listIndex +
                                                                            1
                                                                    ? Color
                                                                        .fromARGB(
                                                                            255,
                                                                            155,
                                                                            227,
                                                                            243)
                                                                    : Colors
                                                                        .white,
                                                      ),
                                                      child: TeXView(
                                                        child: TeXViewInkWell(
                                                            rippleEffect: false,
                                                            onTap: (_) {
                                                              selectedAnswerIndexes[
                                                                  index] = _questions[
                                                                          index]
                                                                      .options[
                                                                          listIndex]
                                                                      .id ??
                                                                  -1;
                                                              // selectedAnswerIds[
                                                              //     index] = _questions[
                                                              //             index]
                                                              //         .options[
                                                              //             listIndex]
                                                              //         .answerId ??
                                                              //     '';
                                                              _examCubit
                                                                  .updateCheckBox(
                                                                      false,
                                                                      selectedAnswerIndexes);
                                                            },
                                                            child: TeXViewDocument(
                                                                _questions[index]
                                                                        .options[
                                                                            listIndex]
                                                                        .answerVal ??
                                                                    "No Data"),
                                                            id: listIndex
                                                                .toString()),
                                                      ));
                                                },
                                                separatorBuilder:
                                                    (BuildContext context,
                                                            int index) =>
                                                        const Divider(),
                                                itemCount: _questions[index]
                                                        .options
                                                        .length ??
                                                    0),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                              options: CarouselOptions(
                                height: 250,
                                viewportFraction: 1,
                                enableInfiniteScroll: false,
                                onPageChanged: (index, _) {
                                  _examCubit.changeIndex(index);
                                },
                              )),
                        ],
                      ),
                    );
            },
          ),
        ),
      ),
    );
  }



  _onAnswerSelected(
    ExamCubit examCubit,
    Question quest,
    int answerItemIndex,
    int questionIndex,
    ValueNotifier<int> selectedQuestionIndex,
    ValueNotifier<List<String>> selectedAnswerIds,
  ) async {
    String tempSelectedAnswerId = quest.options[answerItemIndex].answerId;

    if (selectedAnswerIds.value.contains(tempSelectedAnswerId)) {
      // remove if answer is already selected()- toggle action
      selectedAnswerIds.value.remove(tempSelectedAnswerId);
      _selectedAnswerIndexes.remove(answerItemIndex);
      // ignore: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member
      selectedAnswerIds.notifyListeners();
      quest.selectedAnswerIndexes.remove(answerItemIndex);
      quest.selectedAnswerIds.remove(tempSelectedAnswerId);
    } else {
      // add into list if answer is not selected()toggle action
      selectedAnswerIds.value.add(quest.options[answerItemIndex].answerId);
      // ignore: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member
      selectedAnswerIds.notifyListeners();
      _selectedAnswerIndexes.add(answerItemIndex);
      quest.selectedAnswerIndexes.add(answerItemIndex);
      quest.selectedAnswerIds.add(tempSelectedAnswerId);
    }
    if (quest.selectedAnswerIds.isNotEmpty) {
      quest.questionsStatus = QuestionStatus.answered;
    } else {
      quest.questionsStatus = QuestionStatus.unAnswered;
    }
    examCubit.updateColor(
        quest.options[answerItemIndex].answerVal, _selectedAnswerIndexes);
    _saveAnswer(quest, selectedQuestionIndex.value, examCubit);
  }

  _saveAnswer(Question answeredQuestion, int selectedQuestionIndex,
      ExamCubit examCubit) async {
    examCubit.saveAnswer(_questionData!, answeredQuestion,
        selectedQuestionIndex, _answeredQuestionList, _examJson);
  }

  _finalizeSubmitJson() {
    String quizId = _questionData?.questions[0].quizId ?? '';
    Map<String, dynamic> _newQuestion = {};
    List<String> _answeredQuestionIds =
        _answeredQuestionList.map((e) => e["question_id"].toString()).toList();

    for (var unAnsweredQstn in _questionData!.questions) {
      _answeredQuestionIds.toSet().toList();
      if (_answeredQuestionIds.contains(unAnsweredQstn.questionId)) {
        continue;
      } else {
        if (unAnsweredQstn.questionsStatus != QuestionStatus.answered) {
          _newQuestion = {
            "order_id": unAnsweredQstn.id,
            "question_id": unAnsweredQstn.questionId,
            "question_with_options": unAnsweredQstn.questionText,
            "response_summary": '',
            "selected_answer_ids": unAnsweredQstn.selectedAnswerIds,
          };
          _answeredQuestionList.add(_newQuestion);
          _answeredQuestionIds.add(unAnsweredQstn.questionId ?? '');
        }
      }
    }
    _answeredQuestionList.sort(
      (a, b) {
        int orderID1 = a["order_id"];
        int orderID2 = b["order_id"];
        return orderID1.compareTo(orderID2);
      },
    );
    _examJson = {
      "org_id": ORG_ID,
      "quiz_id": quizId,
      "user_id": USER_ID,
      "submit_datas": _answeredQuestionList,
    };
  }

  /// Submit the selected details to server
  _submitExam(BuildContext parentContext, ExamCubit _examCubit) async {
    countdownController.pause();
    ExamState state = _examCubit.state;
    _finalizeSubmitJson();
    _examCubit.setLoading(true);

    await _examCubit.submitExam(_examJson, _questionData!);
    state = _examCubit.state;
    _examCubit.setLoading(true);
    if (state is ExamsQuestionsFailureState) {
      if (state.error.contains(FAILED_HOST_LOOKUP)) {
        _examCubit.setLoading(false);
        _showExamError(parentContext, _examCubit, EXAM_NETWORK);
      } else {
        _examCubit.setLoading(false);
        _showExamError(parentContext, _examCubit, state.error);
      }
    } else {
      _examCubit.enableSubmitBtn(false);
      _examCubit.setLoading(true);
      String quizId = _questionData?.questions.first.quizId ?? '';
      SharedPreferences _pref = await SharedPreferences.getInstance();
      String quizAttemptId = _pref.getString('quiz_attempt_id') ?? '';
      await _examCubit.calculateQuizGrades(quizId, quizAttemptId);
    }
  }

  _showExamError(BuildContext context, ExamCubit examCubit, String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return CustomAlertDialog(
          title: WARNING,
          message: error.isNotEmpty ? error : EXAM_ALREADY_ATTEND,
        );
      },
    ).then((value) async {
      appRouter.popForced();
    });
  }



  List<Map<String, dynamic>> parseMilestones(String inputString) {
    // Remove unnecessary characters and split the string into individual Milestone strings
    List<String> milestoneStrings = inputString
        .replaceAll('[', '')
        .replaceAll(']', '')
        .split('Milestone')
        .where((element) => element.trim().isNotEmpty)
        .toList();

    // Parse each Milestone string and convert it to a Map
    List<Map<String, dynamic>> milestoneMaps =
        milestoneStrings.map((milestoneString) {
      // Extracting id, milestone, type, and milestoneDuration using regular expressions
      RegExp exp = RegExp(
          r'id: (\d+), milestone: (\d+), type: (\w+), milestoneDuration: (\d+)');
      var match = exp.firstMatch(milestoneString);

      // Creating a map from the extracted values
      return {
        "id": int.parse(match!.group(1)!),
        "milestone": int.parse(match.group(2)!),
        "type": match.group(3)!,
        "milestoneDuration": int.parse(match.group(4)!),
      };
    }).toList();

    return milestoneMaps;
  }

  List<Milestone> parseToDurationList(String jsonString) {
    List<dynamic> parsedJson = json.decode(jsonString);

    // Map each dynamic element in the list to a Milestone object
    List<Milestone> milestones =
        parsedJson.map((json) => Milestone.fromJson(json)).toList();

    return milestones;
  }

  List<Milestone> convertMilestoneDurations(String durations) {
    List<Milestone> durationList = [];
    List<Map<String, dynamic>> milestones = parseMilestones(durations);
    durationList =
        milestones.map((e) => Milestone.fromJson(jsonEncode(e))).toList();
    return durationList;
  }

    void startCounter() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      counter.value++;
    });
  }

  // TO DO: Checkpoint- Remove later
  List<Positioned> _setCheckPoints(
      BuildContext context, CourseListCubit _courseCubit) {
    if (courseVideo.isCheckpointEnabled && checkPoints.isNotEmpty) {
      final startTimes = checkPoints.map((e) {
        return convertCheckPointTimetoSec(e.startTime) * 1000;
      }).toList();
      final items = startTimes.map((e) {
        double currentPosition = 0.0;
        // if(isFromExamScreen){
        //   e /
        // }
        if (_youtubePlayerController?.metadata.duration.inMilliseconds != 0) {
          currentPosition =
              e / _youtubePlayerController!.metadata.duration.inMilliseconds;
          print("currentPosition => $currentPosition");
        }
        return Positioned(
            left: _fullScreenView
                ? (MediaQuery.sizeOf(context).width * 0.75) * currentPosition
                : (MediaQuery.sizeOf(context).width * 0.60) * currentPosition,
            child: Container(
              color: Colors.red,
              height: 2,
              width: _fullScreenView ? 5 : 2,
            ));
      }).toList();
      // stackList = [
      //   LinearProgressIndicator(
      //     value: _youtubePlayerController?.value.isReady ?? false
      //         ? _getVideoPercentage()
      //         : 0,
      //     backgroundColor: AppTheme.whiteColor,
      //     color: AppTheme.bgColor,
      //   ),
      // ];
      stackList.addAll(items);
      return stackList;
    } else {
      return stackList;
    }
  }
*/
}
