import '/src/presentation/cubits/exam/exam_cubit.dart';

import '/src/utils/constants/nums.dart';
import 'package:flutter/cupertino.dart';

import '/src/domain/services/localizations/string_keys.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../utils/constants/locale_change_helper.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../config/themes/lms_fonts.dart';
import '../widgets/connectivity_widget.dart';
import '../widgets/empty_screen_view.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/profile_placeholder_widget.dart';
import '/src/config/themes/app_theme.dart';

import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';

import '../../domain/models/rank.dart';

import '../cubits/rank/rank_list_cubit.dart';
import '../widgets/app_bar.dart';

@RoutePage()

// ignore: must_be_immutable
class RankListViewScreen extends HookWidget with WidgetsBindingObserver {
  final bool isCourseRankList;
  final List<Rank> rankListForExam;

  RankListViewScreen(this.isCourseRankList, this.rankListForExam, {Key? key})
      : super(key: key);

  List<Rank> _rankList = [];
  List<Rank> _rankWithCompetitors = [];
  Rank? _currentUserRank;
  List<Rank> _topPerformers = [];

  bool _isRankListEmpty = false;

  BuildContext? currentContext;
  ScrollController _controller = ScrollController();
  final scrollController = ScrollController();

  @override
  void didChangeLocales(List<Locale>? locales) async {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    await didChangeAppLocale(locales, currentContext);
  }

  _compareRanks() {
    /// show rank comparison if the current user is
    /// not among the 1st 10 rank holders.

    _rankList.sort((a, b) => (a.rankValue).compareTo(b.rankValue));
    List<Rank> _currentUserRankList =
        _rankList.where((rank) => rank.userId == USER_ID).toList();
    if (_currentUserRankList.isNotEmpty) {
      _currentUserRank = _currentUserRankList.first;
      // _currentUserRank =
      //     _rankList.where((rank) => rank.userId == USER_ID).toList().first;
    }

    List<Rank> topRankHolders = List.from(_rankList);
    topRankHolders.removeWhere((element) => element.rankValue > 10);

    int _currentUserIndexInTopList =
        topRankHolders.indexWhere((element) => element.userId == USER_ID);

    if (_currentUserRank != null) {
      if (_rankList.isNotEmpty && _currentUserIndexInTopList == -1) {
        // current user not in top 10 rank holders

        int currentUserRankIndex = _rankList
            .indexWhere((rank) => rank.userId == _currentUserRank!.userId);

        _rankWithCompetitors.add(_rankList[currentUserRankIndex - 1]);
        _rankWithCompetitors.add(_rankList[currentUserRankIndex]);
        if (currentUserRankIndex != _rankList.length - 1) {
          _rankWithCompetitors.add(_rankList[currentUserRankIndex + 1]);
        }
      }
    }
    _topPerformers =
        _rankList.where((element) => element.rankValue >= 4).toList();
    if (_topPerformers.length > 10) {
      _topPerformers.removeRange(10, _topPerformers.length);
    }
    _rankList.removeWhere((element) => element.rankValue > 10);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final _rankCubit = BlocProvider.of<RankListCubit>(context);
    final _examCubit = BlocProvider.of<ExamCubit>(context);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      Future<void>.microtask(() async {
        _rankCubit.setLoading(true);
        if (isCourseRankList) {
          await _rankCubit.fetchCourseRankList();
          _rankList = _rankCubit.state.rankList;
        } else {
          _rankList = rankListForExam;
        }
        _isRankListEmpty = _rankList.isEmpty;
        _compareRanks();
        _rankCubit.setLoading(false);
      });
      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: SLStrings.getTranslatedString(KEY_RANK_APPBAR_TITLE),
              trailingWidget: Container(),
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {
                kIsWeb
                    ? isCourseRankList
                        ? Beamer.of(context)
                            .beamToReplacementNamed("/course-details")
                        : Beamer.of(context).beamBack(data: {'tabIndex': 0})
                    : appRouter.pop();
              },
            )),
        body: BlocBuilder<RankListCubit, RankListState>(
          builder: (context, state) {
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              child: Scrollbar(
                controller: scrollController,
                child: Stack(
                  children: [
                    _isRankListEmpty
                        ? EmptyScreenView(
                            title: SLStrings.getTranslatedString(
                                KEY_RANK_LIST_EMPTY))
                        : ScrollConfiguration(
                            behavior: ScrollConfiguration.of(context).copyWith(
                              scrollbars: false,
                              dragDevices: {
                                PointerDeviceKind.touch,
                                PointerDeviceKind.mouse,
                              },
                            ),
                            child:
                                LayoutBuilder(builder: (context, constraints) {
                              return Align(
                                alignment: Alignment.topCenter,
                                child: SizedBox(
                                  width: constraints.maxWidth < 720
                                      ? double.infinity
                                      : 800,
                                  child: SingleChildScrollView(
                                    controller: scrollController,
                                    child: rankListWidget(context, constraints),
                                  ),
                                ),
                              );
                            }),
                          ),
                    ConnectivityStatusWidget(),
                    state is RankListLoading && state.isDataLoading
                        ? LoadingIndicatorClass()
                        : Container(),
                  ],
                ),
              ),
            );
          },
        ));
  }

  Widget rankListWidget(BuildContext context, BoxConstraints constraints) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 15),
          _titleWidget(SLStrings.getTranslatedString(KEY_TOP_RANK_HOLDERS), 16,
              AppTheme.titleColor),
          const SizedBox(height: 15),
          _topStudentWidget(context, constraints),
          const SizedBox(height: 15),
          !isCourseRankList ? _indicationWidget() : Container(),
          SizedBox(height: _rankList.length >= 3 ? 15 : 0),
          _rankList.length > 3 && _topPerformers.isNotEmpty
              ? _titleWidget(SLStrings.getTranslatedString(KEY_RANK_TITLE), 16,
                  AppTheme.titleColor)
              : Container(),
          SizedBox(height: _rankList.length >= 3 ? 15 : 0),
          _listViewWidget(true),
          _rankWithCompetitors.isNotEmpty
              ? _divider(AppTheme.blackColor.withOpacity(0.10))
              : Container(),
          _rankWithCompetitors.isNotEmpty
              ? _titleWidget(SLStrings.getTranslatedString(KEY_YOUR_RANK), 16,
                  AppTheme.titleColor)
              : Container(),
          _rankWithCompetitors.isNotEmpty
              ? _subTitleWidget(
                  YOUR_RANK_WITH_COMPETITORS, 14, AppTheme.iconColor)
              : Container(),
          const SizedBox(height: 15),
          _listViewWidget(false),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _topStudentWidget(BuildContext context, BoxConstraints constraints) {
    final _topStudents =
        _rankList.where((element) => element.rankValue <= 3).toList();
    return Visibility(
      visible: _rankList.isNotEmpty,
      child: Container(
        constraints: const BoxConstraints(maxHeight: 325),
        width: MediaQuery.sizeOf(context).width,
        decoration: BoxDecoration(
          color: AppTheme.topStudentBgColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.only(
            right: 3,
          ),
          child: CupertinoScrollbar(
            controller: _controller,
            thickness: 7.0,
            thumbVisibility: true,
            radius: const Radius.circular(30),
            child: ListView.builder(
              controller: _controller,
              shrinkWrap: true,
              itemCount: _topStudents.length,
              padding: const EdgeInsets.only(bottom: 16),
              itemBuilder: (context, index) => rankListItem(
                  _topStudents[index], context, constraints, index),
            ),
          ),
        ),
      ),
    );
  }

  Widget rankListItem(Rank userRank, BuildContext context,
      BoxConstraints constraints, int index) {
    return Container(
      padding:
          const EdgeInsets.only(left: horizontalMargin, top: horizontalMargin),
      child: Stack(
        alignment: Alignment.centerRight,
        children: [
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.only(right: 34),
            //         constraints.maxWidth > 720 ? width * 0.05 : width * 0.065),
            child: Row(
              children: [
                avatarWidget(userRank),
                middleRowWidget(context, constraints, userRank),
                const SizedBox(width: 32),
              ],
            ),
          ),
          userBadge(userRank.rankValue, context, constraints),
        ],
      ),
    );
  }

  Widget avatarWidget(Rank userRank) {
    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.fromLTRB(16, 15, 15, 15),
          height: 60,
          width: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            border: Border.all(
              width: 2,
              color: AppTheme.whiteColor,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 1,
                offset: const Offset(0, 1),
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 1,
                offset: const Offset(1, 0),
              ),
            ],
          ),
          child: CircleAvatar(
            backgroundColor: Colors.transparent,
            child: userRank.avatar.trim().isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: userRank.avatar,
                    placeholder: (context, url) =>
                        const CircularProgressIndicator(),
                    errorWidget: (context, url, error) =>
                        const ProfilePlaceholderWidget(maxWidth: 44 * 2),
                  )
                : const ProfilePlaceholderWidget(maxWidth: 44 * 2),
          ),
        ),
        _rankWidget(7, 6, AppTheme.viewResultButtonColor,
            userRank.rankValue.toString(), 50),
      ],
    );
  }

  Widget middleRowWidget(
      BuildContext context, BoxConstraints constraints, Rank userRank) {
    return Expanded(
      child: SizedBox(
        width: constraints.maxWidth < 720
            ? MediaQuery.sizeOf(context).width * 0.35
            : MediaQuery.sizeOf(context).width * 0.25,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            userInfoWidget(context, constraints, userRank),
            markWidget(context, constraints, userRank),
          ],
        ),
      ),
    );
  }

  Widget userInfoWidget(
      BuildContext context, BoxConstraints constraints, Rank userRank) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: constraints.minWidth < 720
                ? 500
                : MediaQuery.sizeOf(context).width * 0.35,
            child: Text(
              userRank.userName,
              style: LMSFonts.semiBoldFont(16, AppTheme.titleColor),
            ),
          ),
          isCourseRankList
              ? RichText(
                  text: TextSpan(children: [
                  TextSpan(
                      text:
                          '${SLStrings.getTranslatedString(KEY_RANK_EXAM_ATTENDED)}: ',
                      style: LMSFonts.regularFontWithHeight(
                          14, AppTheme.examInfoTextColor, 1)),
                  TextSpan(
                      text: userRank.examAttended,
                      style: LMSFonts.boldFont(14, AppTheme.examInfoTextColor)),
                ]))
              : _examCountWidget(userRank, false, false, context),
        ],
      ),
    );
  }

  Widget markWidget(
      BuildContext context, BoxConstraints constraints, Rank userRank) {
    return Padding(
      padding: const EdgeInsets.only(top: 3),
      child: Text(
        userRank.mark.toString(),
        style: LMSFonts.boldFont(14, AppTheme.viewResultButtonColor),
        textAlign: TextAlign.right,
      ),
    );
  }

  Widget userBadge(
      int rankValue, BuildContext context, BoxConstraints constraints) {
    return Positioned(
      right: 11, top: 0, bottom: 0,
      // top: 16,
      child: Align(
        child: Image.asset(
          rankValue == 1
              ? '$ASSETS_PATH/badge_first.png'
              : rankValue == 2
                  ? '$ASSETS_PATH/badge_second.png'
                  : '$ASSETS_PATH/badge_third.png',
          height: 45,
          width: 45,
        ),
      ),
    );
  }

  // TODO : REMOVE LATER
  // Widget _currentUserMarkDetails() {
  //   return Row(
  //     children: [
  //       const Spacer(),
  //       userContentWidget(
  //           MARK, (_currentUserRank?.mark ?? 0).toString(), LmsColors.white),
  //       const Spacer(),
  //       Visibility(
  //         visible: _currentUserRank != null &&
  //             _currentUserRank?.rankLevel.value != null &&
  //             _currentUserRank!.rankLevel.value.isNotEmpty,
  //         child: userContentWidget(LEVEL,
  //             (_currentUserRank?.rankLevel.value ?? ''), LmsColors.white),
  //       ),
  //       _currentUserRank != null &&
  //               _currentUserRank?.rankLevel.value != null &&
  //               _currentUserRank!.rankLevel.value.isNotEmpty
  //           ? const Spacer()
  //           : Container(),
  //       Visibility(
  //         visible: isCourseRankList,
  //         child: userContentWidget(RANK_EXAM_ATTENDED,
  //             _currentUserRank?.examAttended ?? '0', LmsColors.white),
  //       ),
  //       isCourseRankList ? const Spacer() : Container(),
  //       Visibility(
  //         visible: !isCourseRankList,
  //         child: userContentWidget(
  //             CORRECT,
  //             (_currentUserRank?.correctCount ?? 0).toString(),
  //             LmsColors.white),
  //       ),
  //       !isCourseRankList ? const Spacer() : Container(),
  //       !isCourseRankList
  //           ? userContentWidget(WRONG,
  //               (_currentUserRank?.wrongCount ?? 0).toString(), LmsColors.white)
  //           : Container(),
  //       !isCourseRankList ? const Spacer() : Container(),
  //       !isCourseRankList
  //           ? userContentWidget(
  //               SKIPPED,
  //               (_currentUserRank?.skippedCount ?? 0).toString(),
  //               LmsColors.white)
  //           : Container(),
  //       !isCourseRankList ? const Spacer() : Container(),
  //     ],
  //   );
  // }

  Widget userContentWidget(String title, String value, Color color) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(title,
            textAlign: TextAlign.center,
            style: LMSFonts.mediumFont(14, color, 1.0)),
        const SizedBox(height: 5),
        Text(value,
            textAlign: TextAlign.center, style: LMSFonts.boldFont(15, color)),
      ],
    );
  }

  Widget _titleWidget(String title, double fontSize, Color color) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: LMSFonts.semiBoldFont(fontSize, color),
    );
  }

  Widget _subTitleWidget(String title, double fontSize, Color color) {
    return Padding(
        padding: const EdgeInsets.only(bottom: 5, top: 5),
        child: _titleWidget(title, fontSize, color)
        //  Text(
        //   title,
        //   textAlign: TextAlign.center,
        //   style: LMSFonts.mediumFont(12, AppTheme.primaryTextColor, 1.0),
        // ),
        );
  }

  Widget _rankWidget(
      double bottom, double right, Color color, String value, double radius) {
    return Positioned(
      bottom: bottom,
      right: right,
      child: Container(
        alignment: Alignment.center,
        height: 25,
        width: 25,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(radius),
        ),
        child: Text(
          value,
          style: LMSFonts.semiBoldFont(16, AppTheme.whiteColor),
        ),
      ),
    );
  }

  Widget _profileWidget(String avatar, String rank) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(100),
            border: Border.all(
              color: AppTheme.whiteColor,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                  color: AppTheme.blackColor.withOpacity(0.25),
                  spreadRadius: 1,
                  offset: const Offset(0, 1)),
            ],
          ),
          child: CircleAvatar(
            backgroundColor: Colors.transparent,
            radius: 30,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(50.0),
              child: avatar.trim().isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: avatar,
                      placeholder: (context, url) =>
                          const CircularProgressIndicator(),
                      errorWidget: (context, url, error) =>
                          const ProfilePlaceholderWidget(maxWidth: 44 * 2),
                    )
                  : const ProfilePlaceholderWidget(maxWidth: 44 * 2),
            ),
          ),
        ),
        _rankWidget(-2, -5, AppTheme.viewResultButtonColor, rank, 3),
      ],
    );
  }

  Widget _listViewWidget(bool isTopPerformersView) {
    List<Rank> _rankListData =
        isTopPerformersView ? _topPerformers : _rankWithCompetitors;
    return ListView.separated(
      primary: false,
      shrinkWrap: true,
      itemCount: _rankListData.length,
      padding: const EdgeInsets.only(bottom: 10),
      separatorBuilder: (context, index) =>
          _divider(AppTheme.blackColor.withOpacity(0.10)),
      itemBuilder: (BuildContext context, int indexx) {
        //TODO : REMOVE LATER
        // int index = isTopPerformersView
        //     ? _rankList.length > 3
        //         ? indexx + 3
        //         : indexx
        //     : indexx;
        return SizedBox(
          width: MediaQuery.sizeOf(context).width,
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _profileWidget(_rankListData[indexx].avatar,
                      _rankListData[indexx].rankValue.toString()),
                  const SizedBox(width: 13.0),
                  Expanded(
                      child: _studentDetailsWidget(
                          indexx, isTopPerformersView, context)),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _studentDetailsWidget(
      int index, bool isTopPerformersView, BuildContext context) {
    Rank rankVal = isTopPerformersView
        ? _topPerformers[index]
        : _rankWithCompetitors[index];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          // width: MediaQuery.sizeOf(context).width * .65,
          child: Text(rankVal.userName,
              style: LMSFonts.boldFont(14, AppTheme.primaryTextColorBlack)),
        ),
        const SizedBox(height: 3),
        isCourseRankList
            ? examDetails(rankVal, context)
            : _examCountWidget(rankVal, isTopPerformersView, true, context),
      ],
    );
  }

  Widget examDetails(Rank rankVal, BuildContext context) {
    return Container(
      child: examInfoWidget(rankVal, context),
    );
  }

// for exam rank list
  Widget _examCountWidget(Rank _rank, bool isTopPerformersView,
      bool _isRankWithCompetitor, BuildContext context) {
    final width = MediaQuery.sizeOf(context).width;
    // Rank _rank = _rankList[index];
    return IntrinsicWidth(
      child: Row(
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          isTopPerformersView
              ? _examInfoDetailsWidget(SLStrings.getTranslatedString(KEY_MARK),
                  _rank.mark.toString())
              : _isRankWithCompetitor
                  ? _examInfoDetailsWidget(
                      SLStrings.getTranslatedString(KEY_MARK),
                      _rank.mark.toString())
                  : Container(),
          SizedBox(
              width: isTopPerformersView
                  ? width * 0.1
                  : _isRankWithCompetitor
                      ? width * 0.10
                      : 0),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _squareIconWithTitle(
                  _rank.correctCount.toString(), AppTheme.correctColor),
              SizedBox(width: width * .075),
              _squareIconWithTitle(
                  _rank.wrongCount.toString(), AppTheme.wrongColor),
              SizedBox(width: width * .075),
              _squareIconWithTitle(
                  _rank.skippedCount.toString(), AppTheme.skippColor)
            ],
          ),
        ],
      ),
    );
  }

  Widget examInfoWidget(Rank rankVal, BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _examInfoDetailsWidget(KEY_MARKS, rankVal.mark.toString()),
        const SizedBox(width: 10),
        Expanded(
          child: _examInfoDetailsWidget(
              KEY_RANK_EXAM_ATTENDED, rankVal.examAttended.toString()),
        )
      ],
    );
  }

  Widget _examInfoDetailsWidget(String trKey, String value) {
    return Container(
      child: RichText(
          text: TextSpan(children: [
        TextSpan(
          text: '${SLStrings.getTranslatedString(trKey)} : ',
          style:
              LMSFonts.regularFontWithHeight(14, AppTheme.examInfoTextColor, 0),
        ),
        TextSpan(
          text: value,
          style: LMSFonts.boldFont(14, AppTheme.examInfoTextColor),
        ),
      ])),
    );
  }

  Widget _examCountText(String text) {
    return Text(text,
        style: LMSFonts.mediumFont(12, AppTheme.primaryTextColorBlack, 1.0));
  }

  Widget _divider(Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10, top: 10, left: 10, right: 10),
      color: color,
      height: 0.8,
    );
  }

  Widget _indicationWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 25),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _squareIconWithTitle(
              SLStrings.getTranslatedString(KEY_CORRECT), AppTheme.correctColor,
              indication: true),
          _squareIconWithTitle(
              SLStrings.getTranslatedString(KEY_WRONG), AppTheme.wrongColor,
              indication: true),
          _squareIconWithTitle(
              SLStrings.getTranslatedString(KEY_SKIPPED), AppTheme.skippColor,
              indication: true),
        ],
      ),
    );
  }

  Widget _squareIconWithTitle(String title, Color color,
      {bool indication = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: indication ? 16 : 10,
          height: indication ? 16 : 10,
          decoration: BoxDecoration(color: color),
        ),
        SizedBox(width: indication ? 10 : 3),
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            title,
            style: LMSFonts.regularFontWithHeight(
                14, AppTheme.resultDescriptionColor, 0),
          ),
        ),
      ],
    );
  }
}
