// To parse this JSON data, do
//
//     final planCourseResult = planCourseResultFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'plan_course_result.g.dart';

PlanCourseResult planCourseResultFromJson(String str) =>
    PlanCourseResult.fromJson(json.decode(str));

String planCourseResultToJson(PlanCourseResult data) =>
    json.encode(data.toJson());

@JsonSerializable()
class PlanCourseResult {
  @JsonKey(name: "result")
  List<PlanCourse>? result;
  @JsonKey(name: "status")
  String? status;

  PlanCourseResult({
    this.result,
    this.status,
  });

  factory PlanCourseResult.fromJson(Map<String, dynamic> json) =>
      _$PlanCourseResultFromJson(json);

  Map<String, dynamic> toJson() => _$PlanCourseResultToJson(this);
}

@JsonSerializable()
class PlanCourse {
  @JsonKey(name: "course_id")
  String? courseId;
  @JsonKey(name: "resources")
  List<Resource>? resources;
  @JsonKey(name: "course_name")
  String? courseName;
  @JsonKey(name: "summary")
  String? courseDesc;
  @JsonKey(name: "mapping_type")
  String? mappingType;
  @JsonKey(name: "resource_intro_data")
  List<ResourcesIntro>? intro;

  PlanCourse(
      {this.courseId,
      this.resources,
      this.courseName,
      this.courseDesc,
      this.mappingType,
      this.intro});

  factory PlanCourse.fromJson(Map<String, dynamic> json) =>
      _$PlanCourseFromJson(json);

  Map<String, dynamic> toJson() => _$PlanCourseToJson(this);
}

@JsonSerializable()
class Resource {
  @JsonKey(name: "name")
  String? name;
  @JsonKey(name: "type")
  String? type;
  @JsonKey(name: "description")
  String? description;
  @JsonKey(name: "resource_id")
  String? resourceId;

  Resource({
    this.name,
    this.type,
    this.description,
    this.resourceId,
  });

  factory Resource.fromJson(Map<String, dynamic> json) =>
      _$ResourceFromJson(json);

  Map<String, dynamic> toJson() => _$ResourceToJson(this);
}

@JsonSerializable()
class ResourcesIntro {
  @JsonKey(name: "id")
  String? id;
  @JsonKey(name: "name")
  String? intro_name;

  ResourcesIntro({this.id, this.intro_name});

  factory ResourcesIntro.fromJson(Map<String, dynamic> json) =>
      _$ResourcesIntroFromJson(json);

  Map<String, dynamic> toJson() => _$ResourcesIntroToJson(this);
}
