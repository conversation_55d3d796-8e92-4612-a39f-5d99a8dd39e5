import '../../utils/constants/nums.dart';
import '../../utils/constants/strings.dart';
import '/src/config/themes/lms_fonts.dart';
import 'package:flutter/material.dart';
import '../../config/themes/app_theme.dart';

class AppBarWidget extends StatelessWidget {
  final String title;
  final String leadingIconName;
  final Widget trailingWidget;
  final bool isFromLogin;
  final bool isHomeScreen;
  final VoidCallback leadingBtnAction;
  final double toolbarHeight;

  const AppBarWidget({
    super.key,
    required this.title,
    required this.leadingIconName,
    this.isFromLogin = false,
    this.isHomeScreen = false,
    this.toolbarHeight = appbarToolbarHeight,
    required this.trailingWidget,
    required this.leadingBtnAction,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
        toolbarHeight: toolbarHeight,
        centerTitle: false,
        automaticallyImplyLeading: false,
        leadingWidth: isFromLogin
            ? 10
            : leadingIconName.isNotEmpty
                ? isHomeScreen
                    ? 57
                    : 45
                : 10,
        leading: isFromLogin
            ? Container()
            : leadingIconName.isNotEmpty
                ? InkWell(
                    onTap: () {},
                    highlightColor: AppTheme.primaryAppColor,
                    child: GestureDetector(
                      onTap: () => leadingBtnAction(),
                      child: Container(
                        width: 55,
                        height: 55,
                        margin: EdgeInsets.only(left: isHomeScreen ? 12 : 0),
                        child: Image.asset(
                          '$ASSETS_PATH/$leadingIconName.png',
                          width: 45,
                          height: 45,
                        ),
                      ),
                    ),
                  )
                : Container(),
        title: Row(children: [
          // Container(
          //   color: Colors.red,
          //   width: 50,
          //   alignment: Alignment.centerLeft,
          //   child: Align(
          //     alignment: Alignment.centerLeft,
          //     child: IconButton(
          //       icon: leadingIcon, //Icon(Icons.arrow_back),
          //       onPressed: () => leadingBtnAction(),
          //     ),
          //   ),
          // ),
          Expanded(
            child: Text(
              title,
              maxLines: 2,
              style: LMSFonts.appBarStyle(),
            ),
          ),
          trailingWidget,
        ]));
  }
}

// class AppBarWithTabBarView extends StatelessWidget {
//   final String title;
//   final Icon leadingIcon;
//   final Widget? trailingWidget;
//   final List<String> tabChildrenTitles;
//   final VoidCallback leadingBtnAction;

//   const AppBarWithTabBarView({
//     super.key,
//     required this.title,
//     required this.leadingIcon,
//     this.trailingWidget,
//     required this.tabChildrenTitles,
//     required this.leadingBtnAction,
//   });

//   TabBar get _tabBar => TabBar(
//         indicatorColor: AppTheme.primaryAppColor,
//         tabs: tabChildrenTitles.map((item) {
//           return _tabBarItem(item);
//         }).toList(),
//       );

//   @override
//   Widget build(BuildContext context) {
//     return AppBar(
//       centerTitle: false,
//       automaticallyImplyLeading: false,
//       leading: IconButton(
//         icon: leadingIcon,
//         onPressed: () => leadingBtnAction(),
//       ),
//       title: Row(children: [
//         Expanded(
//           child: Text(title),
//         ),
//         trailingWidget ?? Container(),
//       ]),
//       bottom: PreferredSize(
//         preferredSize: _tabBar.preferredSize,
//         child: Theme(data: AppTheme.tabBarTheme, child: _tabBar),
//       ),
//     );
//   }

//   Widget _tabBarItem(String tabName) {
//     return Tab(
//       child: Text(
//         tabName,
//         style: LMSFonts.tabItem(),
//       ),
//     );
//   }
// }

class TransparentAppBarWidget extends StatelessWidget {
  final String title;
  final Icon leadingIcon;
  final Widget trailingWidget;
  final VoidCallback leadingBtnAction;

  const TransparentAppBarWidget({
    super.key,
    required this.title,
    required this.leadingIcon,
    required this.trailingWidget,
    required this.leadingBtnAction,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: false,
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: leadingIcon,
          onPressed: () => leadingBtnAction(),
        ),
        title: Row(children: [
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                  color: AppTheme.pitchBlack, fontWeight: FontWeight.w600),
            ),
          ),
          trailingWidget,
        ]));
  }
}
