import '../../config/app_config/api_config.dart';
import '../../domain/models/category/user_category.dart';
import '../../domain/models/resource_comment.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/strings.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/models/course_details.dart';
import '../../domain/repositories/course_respository.dart';

class CourseRepositoryImpl implements CourseRepository {
  CourseRepositoryImpl();

  // TODO: Remove later
  // API unused
  // replaced with 'V_CATEGORY_BY_HIERARCHY'
  // @override
  // Future<List<Course>> getUserCourse(String topicId, String orgId) async {
  // final course = await Supabase.instance.client
  //     .from(APIConfig.V_COURSE)
  //     .select()
  //     .eq("category_id", topicId)
  //     .eq('org_id', ORG_ID)
  //     .eq('category_publish_status', PUBLISHED_STATUS);

  // List<dynamic> userListt = course;
  // List<Map<String, dynamic>> mapList =
  //     userListt.map((item) => item as Map<String, dynamic>).toList();
  // List<CourseOld> courseList =
  //     mapList.map((json) => CourseOld.fromJson(json)).toList();
  // courseList.sort((a, b) => (a.full_name ?? '').compareTo(b.full_name ?? ''));

  //   return [];
  // }

  /// Course Details
  @override
  Future<List<CourseDetails>> fetchCourseDetailsInfo(
      String courseId, String orgId, String userId) async {
    List<CourseDetails> _courseDetailsList = [];
    bool isNetworkAvailable = await checkNetworkStatus();
    if (isNetworkAvailable) {
      // final response = await Supabase.instance.client
      //     .from(APIConfig.GET_COURSE_DETAILS)
      //     .select()
      //     .eq('course_id', courseId)
      //     .onError((error, stackTrace) {
      //   return _courseDetailsList;
      // });
      Map<String, dynamic> reqBody = {
        "course_id": courseId,
        "org_id": orgId,
      };

      final response = await Supabase.instance.client
          .rpc(APIConfig.GET_COURSE_DETAILS, params: reqBody);

      List<dynamic> responseData = response;
      if (responseData.isEmpty) {
        return _courseDetailsList;
      } else {
        CourseDetails? _courseDetails = CourseDetails.fromJson(responseData[0]);
        _courseDetailsList = [_courseDetails];
        return _courseDetailsList;
      }
    } else {
      return _courseDetailsList;
    }
  }

  /// Course Details- current affairs by date
  @override
  Future<List<CurrentAffairs>> fetchMoreCurrentAffairs() async {
    final response = await Supabase.instance.client
        .from(APIConfig.V_BULLETIN_BOARD)
        .select()
        .filter('type', 'eq', 'Current_Affairs')
        .eq('org_id', ORG_ID);
    List<dynamic> responseData = response;
    List<CurrentAffairs> _currentAffairs = List<CurrentAffairs>.from(
        responseData.map((x) => CurrentAffairs.fromJson(x))).toList();
    return _currentAffairs;
  }

  @override
  Future<List<ResourseComment>> fetchCommentsForId(
      String instanceId, String activityType) async {
    List<ResourseComment> commentsFetched = [];

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_COMMENTS_FN, params: {
      'instance_id': instanceId,
      'org_id': ORG_ID,
      'activity_type': activityType
    });

    List<dynamic> responseData = response ?? [];

    commentsFetched = List<ResourseComment>.from(
        responseData.map((x) => ResourseComment.fromJson(x, ''))).toList();

    for (int i = 0; i < commentsFetched.length; i++) {
      final comment = commentsFetched[i];
      if (comment.profilePic != null && comment.profilePic!.isNotEmpty) {
        bool isValid = await validateImage(comment.profilePic!);
        if (!isValid) {
          commentsFetched[i] = comment.copyWith(profilePic: '');
        }
      }
    }

    return commentsFetched;
  }

  @override
  Future uploadCommentsForId(Map<String, dynamic> jsonBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.ADD_COMMENTS_FN, params: jsonBody);
    Map<String, dynamic> responseData = response ?? [];
    return responseData;
  }

  // set course progress

  @override
  Future<dynamic> setCourseProgress(
      String course, String instance, Map<String, dynamic> duration) async {
    Map<String, dynamic> progressData = {
      "course_id": course,
      "instance_id": instance,
      "org_id": ORG_ID,
      "progress_data": duration,
      "user_id": USER_ID
    };

    final response = await Supabase.instance.client
        .rpc(APIConfig.SET_COURSE_PROGRESS_FN, params: progressData);

    if (response != null) {
      Map<String, dynamic> responseData = response;
      return responseData;
    } else {
      return false;
    }
  }

  @override
  Future setSkippedResource(Map<String, dynamic> jsonReq) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.SET_SKIPPED_RESOURCE, params: jsonReq);

    if (response != null) {
      Map<String, dynamic> responseData = response;
      return responseData;
    } else {
      return false;
    }
  }

  @override
  Future<List<ResourseComment>> fetchLikeCount(String instanceId) {
    // TODO: implement fetchLikeCount
    throw UnimplementedError();
  }

  @override
  Future setLikeCount(Map<String, dynamic> jsonBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.SET_LIKE, params: jsonBody);
    Map<String, dynamic> responseData = response ?? [];
    return responseData;
  }
}
