part of 'exam_cubit.dart';

abstract class ExamState extends Equatable {
  final bool button;
  final int? index;
  final int? radioValue;
  final Uint8List? imageData;

  final List<Exam> exams;
  final List<Exam> attemptedExams;
  final List<QuestionData> examData;
  final List<Question> questions;
  final List<ExamReview> examReviewData;
  final List<ExamSummary> examSummaryResult;
  final bool enableSubmitBtn;
  ExamState(
      {this.imageData,
      this.button = false,
      this.index = 0,
      this.radioValue,
      this.exams = const [],
      this.attemptedExams = const [],
      this.examData = const [],
      this.questions = const [],
      this.examSummaryResult = const [],
      this.examReviewData = const [],
      this.enableSubmitBtn = false});

  @override
  List<Object> get props => [button];
}

class ExamListInitial extends ExamState {}

class ExamsFetched extends ExamState {
  ExamsFetched({super.exams, super.attemptedExams});
  @override
  List<Object> get props => [attemptedExams];
}

class AttemptedExamsFetched extends ExamState {
  AttemptedExamsFetched({super.attemptedExams});
  @override
  List<Object> get props => [attemptedExams];
}

class ExamsFailureState extends ExamState {
  final String error;
  ExamsFailureState({required this.error});
  @override
  List<Object> get props => [questions];
}

class ExamQuestionsFetched extends ExamState {
  ExamQuestionsFetched({super.examData});
  @override
  List<Object> get props => [examData];
}

class ExamsStarted extends ExamState {
  ExamsStarted({super.questions});
  @override
  List<Object> get props => [questions];
}

class ExamPGException extends ExamState {
  final String error;
  ExamPGException({required this.error});
  @override
  List<Object> get props => [error];
}

class ExamQuizIdFailure extends ExamState {
  final String error;
  ExamQuizIdFailure({required this.error});
  @override
  List<Object> get props => [error];
}

class ExamSaveAnswer extends ExamState {
  QuestionData questionData;
  Map<String, dynamic> examJsonBody;
  ExamSaveAnswer({required this.questionData, required this.examJsonBody});
  @override
  List<Object> get props => [questionData, examJsonBody];
}

class ExamsSubmitted extends ExamState {
  ExamsSubmitted();
}

class ExamsQuestionsFailureState extends ExamState {
  final String error;
  ExamsQuestionsFailureState({required this.error});
  @override
  List<Object> get props => [error];
}

class ExamsLoading extends ExamState {
  final bool loadingStatus;
  ExamsLoading({required this.loadingStatus});
  @override
  List<Object> get props => [loadingStatus];
}

class ExamQuestionsLoading extends ExamState {
  final bool loadingStatus;
  ExamQuestionsLoading({required this.loadingStatus});
  @override
  List<Object> get props => [loadingStatus];
}

class ExamsLoadingWithoutIndicator extends ExamState {
  final bool loadingStatus;
  ExamsLoadingWithoutIndicator({required this.loadingStatus});
  @override
  List<Object> get props => [loadingStatus];
}

class ExamSubmitBtnState extends ExamState {
  final bool enableSubmitBtn;
  ExamSubmitBtnState({required this.enableSubmitBtn});
  @override
  List<Object> get props => [enableSubmitBtn];
}

class ExamListButtonColor extends ExamState {
  final bool buttonColor;
  final List<int> indexes;

  ExamListButtonColor({
    required this.buttonColor,
    required this.indexes,
  });

  @override
  List<Object> get props => [buttonColor, indexes];
}

class ExamListShowIcon extends ExamState {
  final bool button;

  ExamListShowIcon({
    required this.button,
  });

  @override
  List<Object> get props => [button];
}

class ExamListChangeIndex extends ExamState {
  final int changedIndex;

  ExamListChangeIndex({
    required this.changedIndex,
  });

  @override
  List<Object> get props => [changedIndex];
}

class ExamQuestionScrollToIndex extends ExamState {
  final int scrollToIndex;

  ExamQuestionScrollToIndex({required this.scrollToIndex});

  @override
  List<Object> get props => [scrollToIndex];
}

class ExamListChangeRadioButton extends ExamState {
  final int RadioValue;

  ExamListChangeRadioButton({
    required this.RadioValue,
  });

  @override
  List<Object> get props => [RadioValue];
}

class DrawingPadStateChange extends ExamState {
  final bool controllerStatus;

  DrawingPadStateChange({super.imageData, required this.controllerStatus});

  @override
  List<Object> get props => [controllerStatus];
}

class DrawingComplete extends ExamState {
  final File fileImage;

  DrawingComplete({required this.fileImage});

  @override
  List<Object> get props => [fileImage];
}

// Exam Review Screen

class ExamReviewFetched extends ExamState {
  ExamReviewFetched({super.examReviewData});
  @override
  List<Object> get props => [examReviewData];
}

class ExamReviewFailureState extends ExamState {
  final String error;
  ExamReviewFailureState({required this.error});
  @override
  List<Object> get props => [error];
}

class ExamReviewLoading extends ExamState {
  final bool loadingStatus;
  ExamReviewLoading({required this.loadingStatus});

  @override
  List<Object> get props => [loadingStatus];
}

class NetworkStatus extends ExamState {
  final bool isNetworkAvailable;
  NetworkStatus({required this.isNetworkAvailable});

  @override
  List<Object> get props => [isNetworkAvailable];
}

class ExamsQuizGradeState extends ExamState {
  final bool status;
  ExamsQuizGradeState({required this.status});

  @override
  List<Object> get props => [status];
}

class ExamsQuizFailureState extends ExamState {
  final String error;
  ExamsQuizFailureState({required this.error});
  @override
  List<Object> get props => [error];
}

class ExamResultFetched extends ExamState {
  ExamResultFetched({super.examData});
  @override
  List<Object> get props => [examData];
}

class ExamResultSummaryFetched extends ExamState {
  ExamResultSummaryFetched({super.examSummaryResult});

  List<Object> get props => [examSummaryResult];
}

class HtmlRenderFinished extends ExamState {
  HtmlRenderFinished({super.examData});
  @override
  List<Object> get props => [examData];
}

class ExamSpeedDialState extends ExamState {
  final bool enableSpeedDial;
  ExamSpeedDialState({required this.enableSpeedDial});
  List<Object> get props => [enableSpeedDial];
}

class ExamSubmissionStatus extends ExamState {
  final bool status;
  ExamSubmissionStatus({required this.status});
  @override
  List<Object> get props => [status];
}

class ExamMilestoneCheckBoxStatus extends ExamState {
  final bool status;
  final List<int> selectedAnswerIndexes;
  ExamMilestoneCheckBoxStatus(
      {required this.status, required this.selectedAnswerIndexes});

  @override
  List<Object> get props => [
        status,
        selectedAnswerIndexes,
      ];
}

class CheckPointQuizSubmitted extends ExamState {
  final String sessionStatus;
  CheckPointQuizSubmitted({required this.sessionStatus});
  @override
  List<Object> get props => [sessionStatus];
}
