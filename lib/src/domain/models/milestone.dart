// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class Milestone {
  final String id;
  final String milestoneId;
  final String type;
  int? milestoneDuration;
  Milestone({
    required this.id,
    required this.milestoneId,
    required this.type,
    this.milestoneDuration,
  });

  Milestone copyWith({
    String? id,
    String? milestoneId,
    String? type,
    int? milestoneDuration,
  }) {
    return Milestone(
      id: id ?? this.id,
      milestoneId: milestoneId ?? this.milestoneId,
      type: type ?? this.type,
      milestoneDuration: milestoneDuration ?? this.milestoneDuration,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'milestoneId': milestoneId,
      'type': type,
      'milestoneDuration': milestoneDuration,
    };
  }

  factory Milestone.fromMap(Map<String, dynamic> map) {
    return Milestone(
      id: map['id'] as String,
      milestoneId: map['milestoneId'] as String,
      type: map['type'] as String,
      milestoneDuration: map['milestoneDuration'] != null
          ? map['milestoneDuration'] as int
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory Milestone.fromJson(String source) =>
      Milestone.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'Milestone(id: $id, milestoneId: $milestoneId, type: $type, milestoneDuration: $milestoneDuration)';
  }

  @override
  bool operator ==(covariant Milestone other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.milestoneId == milestoneId &&
        other.type == type &&
        other.milestoneDuration == milestoneDuration;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        milestoneId.hashCode ^
        type.hashCode ^
        milestoneDuration.hashCode;
  }
}
