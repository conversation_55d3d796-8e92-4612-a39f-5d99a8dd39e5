// To parse this JSON data, do
//
//     final courseAssignments = courseAssignmentsFromJson(jsonString);

import 'dart:convert';

List<CourseAssignments> courseAssignmentsFromJson(String str) =>
    List<CourseAssignments>.from(
        json.decode(str).map((x) => CourseAssignments.fromJson(x)));

String courseAssignmentsToJson(List<CourseAssignments> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CourseAssignments {
  String summary;
  double? progress;
  DateTime? validTo;
  String courseId;
  int? pageCount;
  String? timeSpent;
  String courseName;
  String resourceId;
  String courseModuleId;
  int? currentPage;
  int moduleOrder;
  String? resourceUrl;
  String? videoLength;
  String? currentPoint;
  String resourceName;
  ResourceType resourceType;
  int sectionOrder;
  String? thumbnailUrl;
  String? fileExtension;
  bool isPartOfPlan;
  String courseShortName;
  bool isCourseExpired;
  int? numOfCheckpoints;
  bool isCheckpointEnabled;
  bool isEnabled;
  bool isSkipped;

  CourseAssignments({
    required this.summary,
    required this.progress,
    required this.validTo,
    required this.courseId,
    required this.pageCount,
    required this.timeSpent,
    required this.courseName,
    required this.resourceId,
    required this.courseModuleId,
    required this.currentPage,
    required this.moduleOrder,
    required this.resourceUrl,
    required this.videoLength,
    required this.currentPoint,
    required this.resourceName,
    required this.resourceType,
    required this.sectionOrder,
    required this.thumbnailUrl,
    required this.fileExtension,
    required this.isPartOfPlan,
    required this.courseShortName,
    required this.isCourseExpired,
    required this.numOfCheckpoints,
    required this.isCheckpointEnabled,
    required this.isEnabled,
    required this.isSkipped,
  });

  factory CourseAssignments.fromJson(Map<String, dynamic> json) {
    double progress = (json["progress"] ?? 0) * 1.0;
    return CourseAssignments(
      summary: json["summary"] ?? '',
      progress: progress,
      validTo:
          json["valid_to"] != null ? DateTime.tryParse(json["valid_to"]) : null,
      courseId: json["course_id"] ?? '',
      pageCount: json["page_count"] != null ? json["page_count"] as int? : null,
      timeSpent: json["time_spent"] as String?,
      courseName: json["course_name"] ?? '',
      resourceId: json["resource_id"] ?? '',
      courseModuleId: json["course_module_id"] ?? '',
      currentPage:
          json["current_page"] != null ? json["current_page"] as int? : null,
      moduleOrder:
          json["module_order"] != null ? json["module_order"] as int : 0,
      resourceUrl: json["resource_url"] as String?,
      videoLength: json["video_length"] as String?,
      currentPoint: json["current_point"] as String?,
      resourceName: json["resource_name"] ?? '',
      resourceType: json["resource_type"] != null
          ? resourceTypeValues.fromString(json["resource_type"]) ??
              ResourceType.FILE
          : ResourceType.FILE,
      sectionOrder:
          json["section_order"] != null ? json["section_order"] as int : 0,
      thumbnailUrl: json["thumbnail_url"] as String?,
      fileExtension: json["file_extension"] as String?,
      isPartOfPlan: json["is_part_of_plan"] != null
          ? json["is_part_of_plan"] as bool
          : false,
      courseShortName: json["course_short_name"] ?? '',
      isCourseExpired: json["is_course_expired"] != null
          ? json["is_course_expired"] as bool
          : false,
      numOfCheckpoints: json["num_of_checkpoints"] != null
          ? json["num_of_checkpoints"] as int?
          : null,
      isCheckpointEnabled: json["is_checkpoint_enabled"] != null
          ? json["is_checkpoint_enabled"] as bool
          : false,
      isEnabled: (progress > 0.0) || (json["resource_status"] == 1),
      isSkipped: json["resource_status"] == 1,
    );
  }

  Map<String, dynamic> toJson() => {
        "summary": summary,
        "progress": progress,
        "valid_to": validTo?.toIso8601String(),
        "course_id": courseId,
        "page_count": pageCount,
        "time_spent": timeSpent,
        "course_name": courseName,
        "resource_id": resourceId,
        "current_page": currentPage,
        "module_order": moduleOrder,
        "resource_url": resourceUrl,
        "video_length": videoLength,
        "current_point": currentPoint,
        "resource_name": resourceName,
        "resource_type": resourceTypeValues.reverse[resourceType],
        "section_order": sectionOrder,
        "thumbnail_url": thumbnailUrl,
        "file_extension": fileExtension,
        "is_part_of_plan": isPartOfPlan,
        "course_module_id": courseModuleId,
        "course_short_name": courseShortName,
        "is_course_expired": isCourseExpired,
        "num_of_checkpoints": numOfCheckpoints,
        "is_checkpoint_enabled": isCheckpointEnabled,
      };
}

enum ResourceType { FILE, QUIZ, URL, PAGE, IMAGE, VIDEO }

enum FILEEXTENSIONTYPE { PPT, IMG }

final resourceTypeValues = EnumValues({
  "file": ResourceType.FILE,
  "quiz": ResourceType.QUIZ,
  "url": ResourceType.URL,
  "video": ResourceType.URL,
  "page": ResourceType.PAGE,
  "image": ResourceType.IMAGE,
});

class EnumValues<T> {
  final Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(Map<String, T> map)
      : map = map.map((key, value) => MapEntry(key.toLowerCase(), value));

  T? fromString(String? key) {
    if (key == null) return null;
    return map[key
        .toLowerCase()]; // Convert input to lowercase for case-insensitive lookup
  }

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
