import '../course_dashboard/course_assignments.dart';
import '/src/utils/constants/strings.dart';
import 'package:equatable/equatable.dart';

import '../../../config/enums/section_status.dart';

class SectionDetails extends Equatable {
  final String? sectionId;
  final String? courseId;
  final String? name;
  final String? summary;
  final int? sectionOrder;
  final List<Resources> resources;
  List<Modules> modules;
  List<Folder> folders;

  SectionDetails({
    this.sectionId,
    this.courseId,
    this.name,
    this.summary,
    this.sectionOrder,
    required this.resources,
    this.modules = const [],
    this.folders = const [],
  });

  factory SectionDetails.fromJson(Map<String, dynamic> json) {
    List<dynamic> jsonModuleList = json['modules'] ?? [];
    jsonModuleList.removeWhere((element) => element == null);
    jsonModuleList.removeWhere((element) =>
        element['module_name'] == null || element['module_name'] == '');
    List<Modules> moduleList = jsonModuleList.isNotEmpty
        ? jsonModuleList.map((e) => Modules.fromJson(e)).toList()
        : [];
    List<dynamic> jsonList = json['resources'] ?? [];
    jsonList.removeWhere((element) => element == null);
    jsonList.removeWhere(
        (element) => element['name'] == null || element['name'] == '');
    List<Resources> resourcesList = jsonList.isNotEmpty
        ? jsonList.map((e) => Resources.fromJson(e)).toList()
        : [];

    List<dynamic> jsonFoldersList = json['folders'] ?? [];
    jsonFoldersList.removeWhere((element) => element == null);
    jsonFoldersList.removeWhere((element) =>
        element['folder_name'] == null || element['folder_name'] == '');
    List<Folder> foldersList = jsonFoldersList.isNotEmpty
        ? jsonFoldersList.map((e) => Folder.fromJson(e)).toList()
        : [];
    foldersList.sort((a, b) =>
        a.folderName.toLowerCase().compareTo(b.folderName.toLowerCase()));

    return SectionDetails(
      sectionId: json['section_id'] ?? '',
      courseId: json['course_id'] ?? '',
      name: json['name'] ?? '',
      summary: json['summary'] ?? '',
      sectionOrder: json['section_order'],
      resources: resourcesList,
      modules: moduleList,
      folders: foldersList,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      sectionId,
      courseId,
      name,
      summary,
      sectionOrder,
      resources,
      modules
    ];
  }
}

class Folder {
  String folderId;
  List<Modules> folderModules;
  String sectionId;
  String folderDesc;
  String folderName;

  Folder({
    required this.folderId,
    required this.folderModules,
    required this.sectionId,
    required this.folderDesc,
    required this.folderName,
  });

  factory Folder.fromJson(Map<String, dynamic> json) => Folder(
        folderId: json["folder_id"],
        folderModules: List<Modules>.from(
            json["resources"].map((x) => Modules.fromJson(x))),
        sectionId: json["section_id"],
        folderDesc: json["folder_desc"],
        folderName: json["folder_name"],
      );

  // Map<String, dynamic> toJson() => {
  //       "folder_id": folderId,
  //       "resources": List<dynamic>.from(resources.map((x) => x.toJson())),
  //       "section_id": sectionId,
  //       "folder_desc": folderDesc,
  //       "folder_name": folderName,
  //     };
}

class Resources extends Equatable {
  final String id;
  final String name;
  final String type;
  final String url;
  final String createdAt;
  final String updatedAt;
  final String orgId;
  String status;
  bool expansionStatus = false;

  Resources({
    this.id = '',
    this.name = '',
    this.type = '',
    this.url = '',
    this.createdAt = '',
    this.updatedAt = '',
    this.orgId = '',
    this.status = '',
  });

  factory Resources.fromJson(Map<String, dynamic> json) {
    return Resources(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      url: json['url'] ?? '',
      createdAt: json['createdAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
      orgId: json['orgId'] ?? '',
      status: SectionStatus.notStarted.value,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      id,
      name,
      type,
      url,
      createdAt,
      updatedAt,
      orgId,
      status,
      expansionStatus
    ];
  }
}

class Modules extends Equatable {
  final String courseId;
  final String moduleId;
  final String instanceId;
  final String sectionId;
  final ResourceType moduleType;
  final String moduleSource;
  final String moduleName;
  final int remainingAttempts;

  final bool isPremium;
  final int pageCount;
  final String timeSpent;
  final String externalUrl;
  final int moduleOrder;
  final dynamic videoLength;
  final String courseModuleId;
  final int attemptsRemaining;
  bool didMarkedAsDone;
  double progress;
  String status;
  bool expansionStatus = false;
  final String fileExtension;
  bool isEnabled = false;
  bool isSkipped = false;

  Modules({
    this.courseId = '',
    this.moduleId = '',
    this.instanceId = '',
    this.sectionId = '',
    this.moduleType = ResourceType.FILE,
    this.moduleSource = '',
    this.moduleName = '',
    this.status = '',
    this.didMarkedAsDone = false,
    this.remainingAttempts = 0,
    this.progress = -1,
    this.fileExtension = '',
    this.isPremium = false,
    this.pageCount = 0,
    this.timeSpent = '',
    this.externalUrl = '',
    this.moduleOrder = 0,
    this.videoLength = '',
    this.courseModuleId = '',
    this.attemptsRemaining = 0,
    this.isEnabled = false,
    this.isSkipped = false,
  });

  factory Modules.fromJson(Map<String, dynamic> json) {
    double progress = (json["progress"] ?? 0) * 1.0;
    return Modules(
      courseId: json['course_id'] ?? COURSE_ID,
      moduleId: json['module_id'] ?? '',
      instanceId: json['instance'] ?? '',
      sectionId: json['section_id'] ?? '',
      moduleType: json['module_type'] != null
          ? resourceTypeValues.fromString(json['module_type'])!
          : ResourceType.FILE,
      moduleSource: json['module_source'] == null
          ? ''
          : json['module_source'].toString().isEmpty
              ? json['module_type'] ?? ''
              : json['module_source'],
      moduleName: json['module_name'] ?? '',
      status: SectionStatus.notStarted.value,
      didMarkedAsDone: json['marked_as_done'] ?? false,
      remainingAttempts: json['attempts_remaining'] ?? 0,
      progress: progress,
      fileExtension: json["extension"] ?? "",
      isPremium: json["is_premium"] ?? false,
      pageCount: json["page_count"] ?? 0,
      timeSpent: json["time_spent"] ?? "",
      externalUrl: json["external_url"] ?? '',
      moduleOrder: json["module_order"] ?? 0,
      // videoLength: json["video_length"],
      courseModuleId: json["course_module_id"] ?? '',
      isSkipped: json['resource_status'] == 1,
      isEnabled: progress > 0.0 || json["resource_status"] == 1,
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      courseId,
      moduleId,
      instanceId,
      sectionId,
      moduleType,
      moduleSource,
      moduleName,
      status,
      didMarkedAsDone,
      progress
    ];
  }
}
