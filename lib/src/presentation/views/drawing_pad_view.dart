import 'dart:developer';
import 'dart:typed_data';

import 'package:auto_route/auto_route.dart';

import '/src/utils/constants/nums.dart';

import '/src/presentation/cubits/exam/exam_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/themes/lms_fonts.dart';
import '/src/config/themes/theme_colors.dart';
import '/src/presentation/widgets/button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:signature/signature.dart';

import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../utils/constants/strings.dart';
import '../widgets/app_bar.dart';

@RoutePage()

// ignore: must_be_immutable
class DrawingPadViewScreen extends HookWidget {
  // initialize the signature controller
  final SignatureController _signatureController = SignatureController(
    penStrokeWidth: 1,
    penColor: AppTheme.primaryTextColorBlack,
    exportBackgroundColor: LmsColors.transparent,
    exportPenColor: AppTheme.primaryTextColorBlack,
    onDrawStart: () => debugPrint('onDrawStart called!'),
    onDrawEnd: () => debugPrint('onDrawEnd called!'),
  );

  late TextEditingController _textEditingController;
  late FocusNode _focusNode;
  bool _isControllerDisabled = false;

  @override
  Widget build(BuildContext context) {
    _textEditingController = useTextEditingController();
    _focusNode = useFocusNode();

    useEffect(() {
      _signatureController.addListener(() {
        _isControllerDisabled = _signatureController.disabled;
      });
      return null;
    }, const []);

    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      },
      child: Scaffold(
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: FORMULA_WRITING_PAD,
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () => appRouter.pop(),
              trailingWidget: Container(),
            )),
        body: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
          return BlocBuilder<ExamCubit, ExamState>(
            builder: (context, state) {
              if (state is DrawingPadStateChange) {
                _isControllerDisabled = state.controllerStatus;
              }
              return Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16)),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      //SIGNATURE CANVAS
                      _signaturePadView(constraints, context, state),
                      //OK AND CLEAR BUTTONS
                      _textFieldWidget(constraints),
                      _buttonWidget(context, constraints),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }

  Widget _toolBarOptions(BuildContext context, ExamState state) {
    return Positioned(
      right: 0,
      bottom: 0,
      top: 0,
      child: Container(
        margin: const EdgeInsets.only(right: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            IconButtonWidget(
                keyObj: const Key('exportPNG'),
                iconObj: const Icon(Icons.image),
                toolTipVal: TOOLTIP_EXPORT,
                onTapped: () => context.read<ExamCubit>().exportImage(context,
                    _signatureController, _textEditingController.text)),
            IconButtonWidget(
                iconObj: const Icon(Icons.undo),
                toolTipVal: TOOLTIP_UNDO,
                onTapped: () => context
                    .read<ExamCubit>()
                    .undoLastEdit(_signatureController)),
            IconButtonWidget(
                iconObj: const Icon(Icons.redo),
                toolTipVal: TOOLTIP_REDO,
                onTapped: () => context
                    .read<ExamCubit>()
                    .redoLastEdit(_signatureController)),

            //CLEAR CANVAS
            IconButtonWidget(
                keyObj: const Key('clear'),
                iconObj: const Icon(Icons.clear),
                toolTipVal: TOOLTIP_CLEAR,
                onTapped: () =>
                    context.read<ExamCubit>().clearAll(_signatureController)),

            // STOP Edit
            IconButtonWidget(
                keyObj: const Key('stop'),
                iconObj: Icon(
                  _isControllerDisabled ? Icons.pause : Icons.play_arrow,
                ),
                toolTipVal:
                    _isControllerDisabled ? TOOLTIP_PAUSE : TOOLTIP_PLAY,
                onTapped: () => context
                    .read<ExamCubit>()
                    .toggleController(_signatureController)),
          ],
        ),
      ),
    );
  }

  Widget _signaturePadView(
      BoxConstraints constraints, BuildContext context, ExamState state) {
    return SizedBox(
      height: constraints.maxWidth,
      child: Stack(
        children: [
          Signature(
            key: const Key('signature'),
            controller: _signatureController,
            height: constraints.maxWidth,
            backgroundColor: Colors.grey[300]!,
          ),
          _toolBarOptions(context, state),
        ],
      ),
    );
  }

  Widget _textFieldWidget(BoxConstraints constraints) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            COMMENTS,
            style: LMSFonts.mediumFont(18, AppTheme.primaryTextColorBlack, 1.0),
          ),
          const SizedBox(height: 10),
          TextField(
            controller: _textEditingController,
            focusNode: _focusNode,
            keyboardType: TextInputType.multiline,
            maxLines: 10,
            style: LMSFonts.regularFontWithHeight(
                18, AppTheme.primaryTextColorBlack, 1.0),
            decoration: const InputDecoration(
                hintText: ENTER_REMARKS,
                focusedBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(width: 1, color: AppTheme.primaryBlue)),
                enabledBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(width: 1, color: AppTheme.primaryBlue))),
          ),
        ],
      ),
    );
  }

  Widget _buttonWidget(BuildContext context, BoxConstraints constraints) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: ButtonWidget(
            child: SizedBox(
              width: double.infinity,
              child: Text(
                COMMON_BUTTON_TEXT,
                style: LMSFonts.buttonStyle(20.0),
                textAlign: TextAlign.center,
              ),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(buttonRadius),
            ),
            onPressed: () async {
              /// TO DO: Submit data
              context.read<ExamCubit>().exportImage(
                  context, _signatureController, _textEditingController.text);
            }),
      ),
    );
  }
}

class IconButtonWidget extends StatelessWidget {
  final Key? keyObj;
  final Icon iconObj;
  final String toolTipVal;
  final VoidCallback onTapped;

  const IconButtonWidget(
      {super.key,
      this.keyObj,
      required this.iconObj,
      required this.toolTipVal,
      required this.onTapped});

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      elevation: 2,
      child: IconButton(
        key: keyObj,
        icon: iconObj,
        padding: EdgeInsets.zero,
        onPressed: () => onTapped(),
        tooltip: toolTipVal,
      ),
    );
  }
}

@RoutePage()
class DrawingReviewViewScreen extends HookWidget {
  final Uint8List imageData;
  final String remarks;

  const DrawingReviewViewScreen(this.imageData, this.remarks, {super.key});

  @override
  Widget build(BuildContext context) {
    final examCubit = BlocProvider.of<ExamCubit>(context);

    return Scaffold(
      backgroundColor: AppTheme.bgColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBarWidget(
          title: FORMULA_WRITING_PAD,
          trailingWidget: Container(),
          leadingIconName: BACK_ARROW,
          leadingBtnAction: () => appRouter.pop(),
        ),
      ),
      body: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  color: Colors.grey[300],
                  child: Image.memory(imageData),
                ),
              ),
              const SizedBox(height: 10),
              Visibility(
                visible: remarks.trim().isNotEmpty,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    COMMENTS,
                    style: LMSFonts.mediumFont(
                        18, AppTheme.primaryTextColorBlack, 1.0),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Visibility(
                visible: remarks.trim().isNotEmpty,
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  decoration: BoxDecoration(
                      color: AppTheme.ternaryBlue,
                      borderRadius: BorderRadius.circular(12.0)),
                  child: Text(
                    remarks,
                    style: LMSFonts.regularFontWithHeight(
                        18, AppTheme.primaryTextColorBlack, 1.0),
                  ),
                ),
              ),
              Expanded(child: Container()),
              _buttonWidget(context, constraints, examCubit),
            ],
          ),
        );
      }),
    );
  }

  Widget _buttonWidget(
      BuildContext context, BoxConstraints constraints, ExamCubit examCubit) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: ButtonWidget(
            child: SizedBox(
              width: double.infinity,
              child: Text(
                COMMON_BUTTON_TEXT,
                style: LMSFonts.buttonStyle(20.0),
                textAlign: TextAlign.center,
              ),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(buttonRadius),
            ),
            onPressed: () async {
              /// TO DO: Submit data- for future purpose
              await examCubit.saveImage(imageData, context);
              appRouter.popUntil(
                  (route) => route.settings.name == ExamListViewRoute.name);
            }),
      ),
    );
  }
}
