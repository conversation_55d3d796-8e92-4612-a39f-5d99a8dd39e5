<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.smartlearn.app">
    <uses-permission
        android:name="android.permission.CAMERA"/>
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true"/>
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission
        android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission
        android:name="android.permission.INTERNET"/>
    <uses-permission
        android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission
        android:name="android.permission.WAKE_LOCK"/>
    <application
        android:label="SmartLearn"
        android:name="${applicationName}"
        android:icon="@mipmap/launcher_icon"
        android:allowBackup="false"
        android:fullBackupOnly="false"
        android:usesCleartextTraffic="true">
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            android:requestLegacyExternalStorage="true">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <action
                    android:name="FLUTTER_NOTIFICATION_CLICK"/>
                <category
                    android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <!-- <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:theme="@style/Theme.AppCompat.Light.NoActionBar"
                android:resource="@drawable/launch_background"/> -->
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2"/>
    </application>
    <uses-permission
        android:name="android.permission.INTERNET"/>
</manifest>