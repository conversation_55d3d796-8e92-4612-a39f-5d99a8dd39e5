import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../config/app_config/preference_config.dart';
import '../../../domain/services/localizations/string_keys.dart';
import '../../../domain/services/notification/notification_handler.dart';
import '../service_cubit/service_cubit.dart';
import '/src/domain/services/localizations/sl_strings.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart' as provider;

import '../../../domain/services/provider/language_provider.dart';
import '/src/utils/constants/strings.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase/supabase.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../domain/models/organization.dart';
import '../../../domain/repositories/login_email_repository.dart';
import '../../../domain/repositories/organization_repository.dart';
import '../../../utils/constants/helper.dart';
import '../validation/validation_cubit.dart';

part 'login_state.dart';

class LoginEmailCubit extends Cubit<LoginEmailState> with LoginValidationMixin {
  final LoginEmailRepository _LoginEmailRepository;

  final OrganizationRepository _organizationRepository;

  LoginEmailCubit(this._LoginEmailRepository, this._organizationRepository)
      : super(LoginEmailInitial());
  bool isLoading = false;
  String? error;

  Future<void> loginEmail(String email, String password) async {
    try {
      String userId = await _LoginEmailRepository.loginEmail(email, password);
      // ignore: unnecessary_null_comparison
      if (userId != null && userId.isNotEmpty) {
        if (USER_ID != '') {
          clearCurrentUserInfo();
        }
        SharedPreferences _prefs = await SharedPreferences.getInstance();
        _prefs.setString(PREF_KEY_USER_ID, userId);
        USER_ID = userId;

        final organizations =
            await _organizationRepository.getUserOrganizations(userId);
        if (!kIsWeb) {
          await initPushNotification();
        }
        emit(LoginEmailSuccess(userId: userId, orgList: organizations));
      } else {
        emit(LoginEmailError(
            error: SLStrings.getTranslatedString(KEY_LOGIN_FAILURE)));
      }
    } on AuthException catch (e) {
      debugPrint('[LoginEmailCubit][loginEmail][AuthException]: $e');
      emit(LoginEmailError(error: e.message));
    } on SocketException catch (_) {
      debugPrint('[LoginEmailCubit][loginEmail][SocketException]');
      emit(LoginEmailError(
          error: SLStrings.getTranslatedString(KEY_NO_NETWORK_TRY_LATER)));
    } on Exception catch (e) {
      debugPrint('[LoginEmailCubit][loginEmail][Exception]: $e');
      emit(LoginEmailError(error: e.toString()));
    }
  }

  void setLoading(bool text) {
    isLoading = text;
    emit(LoginEmailLoading(isLoading: isLoading));
  }

  void setEmailError(String text, bool showError) {
    emit(LoginEmailErrorValidation(error: text, showError: showError));
  }

  void setPasswordError(String text, bool showError) {
    emit(LoginPasswordValidation(PasswordError: text, showError: showError));
  }

  void changeBtnStatus(String email, String password, String? emailError,
      String? passwordError) {
    if (email.isNotEmpty && password.isNotEmpty) {
      if ((emailError == null || emailError.isEmpty) &&
          (passwordError == null || passwordError.isEmpty)) {
        emit(LoginButtonColor(buttonColor: true));
      } else {
        emit(LoginButtonColor(buttonColor: false));
      }
    } else {
      emit(LoginButtonColor(buttonColor: false));
    }
  }

  /// Validate email using streams when focus loses
  final _emailController = BehaviorSubject<String>();
  Stream<String> get email =>
      _emailController.stream.transform(validateEmailInput);

  Function(String) get changeEmail => _emailController.sink.add;
  void clearEmailError() => _emailController.sink.addError('');

  /// Validate password using streams when focus loses
  final _passwordController = BehaviorSubject<String>();
  Stream<String> get password =>
      _passwordController.stream.transform(validatePasswordInput);

  Function(String) get changePassword => _passwordController.sink.add;
  void clearPasswordError() => _passwordController.sink.addError('');

  dismissionSessionExpiredAlert() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    // Timer _timer = Timer(const Duration(seconds: 5), () {
      _prefs.setBool(PREF_KEY_SIGNOUT, true);
      emit(const AlertMessageVisibility(showAlert: true));
    // });
  }

  handleLanguageSelection(BuildContext context, String selectedLang) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.setString(PREF_KEY_CURRENT_LANG, selectedLang);
    SLStrings.currentLanguage = selectedLang;
    SLStrings.currentLocale = Locale(selectedLang);
    provider.Provider.of<LanguageProvider>(context, listen: false)
        .changeCurrentLanguage(selectedLang);
    emit(ChangeAppLangauge(selectedLang: selectedLang));
  }

  refreshWindow() {
    emit(ChangeAppLangauge(selectedLang: SLStrings.currentLanguage));
  }

  // Push Notification
  initPushNotification() async {
    PushNotificationService _pushNotificationService =
        PushNotificationService();
    await _pushNotificationService.initNotifications();
  }

  /// upload fcm token
  uploadFcmToken(BuildContext context) async {
    final _serviceCubit = BlocProvider.of<ServiceCubit>(context);
    _serviceCubit.uploadFCMToken();
  }

}

mixin LoginValidationMixin {
  final validateEmailInput =
      StreamTransformer<String, String>.fromHandlers(handleData: (email, sink) {
    var error = EmailValidationError.validate(email);
    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(email);
    }
  });

  final validatePasswordInput = StreamTransformer<String, String>.fromHandlers(
      handleData: (password, sink) {
    var error;
    if (password == null || password == '') {
      error = SLStrings.getTranslatedString(KEY_PASSWORD_REQUIRED);
    } else if (password.length < 6) {
      error = SLStrings.getTranslatedString(KEY_PASSWORD_LENGTH_MISMATCH);
    }
    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(password);
    }
  });
}
