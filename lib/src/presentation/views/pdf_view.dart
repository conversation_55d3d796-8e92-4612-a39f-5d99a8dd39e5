import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';

import '/src/config/themes/app_theme.dart';

import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';

import '../widgets/app_bar.dart';

@RoutePage()
class PDFViewScreen extends HookWidget {
  final String urlPDFPath;
  PDFViewScreen({Key? key, required this.urlPDFPath}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    useEffect(() {
      Future<void>.microtask(() async {});
      return null;
    }, const []);
    return Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: '',
              trailingWidget: Container(),
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {
                kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop();
              },
            )),
        body: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: Stack(children: [
              Container(
                  width: MediaQuery.sizeOf(context).width,
                  margin: const EdgeInsets.only(left: 16, right: 16, top: 16),
                  child: Stack(children: <Widget>[
                    PDFView(
                      filePath: urlPDFPath,
                    ),
                  ]))
            ])));
  }
}
