import 'package:SmartLearn/src/config/themes/app_theme.dart';

import '../../config/themes/app_dynamic_theme.dart';
import '/src/presentation/views/feedback_rating_view.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart' as provider;
import 'textfield_widget.dart';

class VideoControlWidget extends StatelessWidget {
  final List<int> durations;
  final IntCallback onTap;
  final IntCallback handleRightDurationSelection;
  final IntCallback handleLeftDurationSelection;
  final int selectedLeft;
  final int selectedRight;
  final VoidCallback handlePlayPause;
  final bool isVideoOnPause;

  const VideoControlWidget({
    super.key,
    required this.onTap,
    required this.durations,
    required this.handleRightDurationSelection,
    required this.handleLeftDurationSelection,
    required this.selectedLeft,
    required this.selectedRight,
    required this.handlePlayPause,
    required this.isVideoOnPause,
  });

  @override
  Widget build(BuildContext context) {
    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    return Center(
      child: Container(
        margin: const EdgeInsets.only(bottom: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _durationDropdown(
              selected: selectedLeft,
              onChanged: handleLeftDurationSelection,
              borderColor: Colors.black12,
            ),
            const SizedBox(width: 10),
            _circularButton(selectedLeft, false, appDynamicTheme),
            const SizedBox(width: 10),
            _circularIconButton(
              icon: isVideoOnPause ? Icons.play_arrow : Icons.pause,
              onTap: handlePlayPause,
              backgroundColor: AppTheme.coursePecentageTxtGrey,
            ),
            const SizedBox(width: 10),
            _circularButton(selectedRight, true, appDynamicTheme),
            const SizedBox(width: 10),
            _durationDropdown(
              selected: selectedRight,
              onChanged: handleRightDurationSelection,
              borderColor: Colors.black12,
            ),
          ],
        ),
      ),
    );
  }

  Widget _durationDropdown({
    required int selected,
    required ValueChanged<int> onChanged,
    Color borderColor = Colors.black12,
    double borderWidth = 1,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        // border: Border.all(color: borderColor, width: borderWidth),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<int>(
        value: selected,
        underline: const SizedBox(),
        onChanged: (value) {
          if (value != null) onChanged(value);
        },
        items: durations
            .map((value) => DropdownMenuItem(
                value: value,
                child:
                    Text('$value' 's', style: const TextStyle(fontSize: 12))))
            .toList(),
      ),
    );
  }

  Widget _circularButton(
      int value, bool isForward, AppDynamicTheme appDynamicTheme) {
    return GestureDetector(
      onTap: () => onTap(isForward ? value : -value),
      child: Container(
        // width: 60,
        height: 55,
        child: Row(
          textDirection: isForward ? TextDirection.rtl : TextDirection.ltr,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isForward
                  ? Icons.fast_forward_rounded
                  : Icons.fast_rewind_rounded,
              size: 30,
              color: AppTheme.coursePecentageTxtGrey, // softer tone
            ),
            // Text(
            //   '$value' 's',
            //   style: const TextStyle(
            //     fontSize: 12,
            //     fontWeight: FontWeight.w500, // lighter than bold
            //     color: Colors.black,
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _circularIconButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color backgroundColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 22,
        backgroundColor: backgroundColor,
        child: Icon(icon, color: Colors.white),
      ),
    );
  }
}

class SeekDurations {
  final int left;
  final int right;
  final bool isVideoOnPause;

  const SeekDurations(
      {required this.left, required this.right, required this.isVideoOnPause});

  SeekDurations copyWith({int? left, int? right, bool? isVideoOnPause}) {
    return SeekDurations(
      left: left ?? this.left,
      right: right ?? this.right,
      isVideoOnPause: isVideoOnPause ?? this.isVideoOnPause,
    );
  }
}
