// To parse this JSON data, do
//
//     final courseFileResource = courseFileResourceFromJson(jsonString);

import 'dart:convert';

List<CourseFileResource> courseFileResourceFromJson(String str) =>
    List<CourseFileResource>.from(
        json.decode(str).map((x) => CourseFileResource.fromJson(x)));

String courseFileResourceToJson(List<CourseFileResource> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CourseFileResource {
  String id;
  String url;
  String name;
  String orgId;
  String status;
  dynamic comments;
  String courseId;
  DateTime createdAt;
  int pageCount;
  DateTime updatedAt;
  dynamic approvedBy;
  String description;
  dynamic approvedDate;
  String moduleSource;
  String courseModuleId;
  dynamic numOfCheckpoints;
  bool isRandomCheckpoint;
  bool isCheckpointEnabled;
  double progress;

  CourseFileResource({
    required this.id,
    required this.url,
    required this.name,
    required this.orgId,
    required this.status,
    required this.comments,
    required this.courseId,
    required this.createdAt,
    required this.pageCount,
    required this.updatedAt,
    required this.approvedBy,
    required this.description,
    required this.approvedDate,
    required this.moduleSource,
    required this.courseModuleId,
    required this.numOfCheckpoints,
    required this.isRandomCheckpoint,
    required this.isCheckpointEnabled,
    required this.progress,
  });

  factory CourseFileResource.fromJson(Map<String, dynamic> json) =>
      CourseFileResource(
        id: json["id"],
        url: json["url"],
        name: json["name"],
        orgId: json["org_id"],
        status: json["status"],
        comments: json["comments"],
        courseId: json["course_id"],
        createdAt: json["created_at"] != null
            ? DateTime.parse(json["created_at"])
            : DateTime.now(),
        pageCount: json["page_count"],
        updatedAt: json["created_at"] != null
            ? DateTime.parse(json["updated_at"])
            : DateTime.now(),
        approvedBy: json["approved_by"],
        description: json["description"],
        approvedDate: json["approved_date"],
        moduleSource: json["module_source"],
        courseModuleId: json["course_module_id"],
        numOfCheckpoints: json["num_of_checkpoints"],
        isRandomCheckpoint: json["is_random_checkpoint"],
        isCheckpointEnabled: json["is_checkpoint_enabled"],
        progress: 0.0,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "url": url,
        "name": name,
        "org_id": orgId,
        "status": status,
        "comments": comments,
        "course_id": courseId,
        "created_at": createdAt.toIso8601String(),
        "page_count": pageCount,
        "updated_at": updatedAt.toIso8601String(),
        "approved_by": approvedBy,
        "description": description,
        "approved_date": approvedDate,
        "module_source": moduleSource,
        "course_module_id": courseModuleId,
        "num_of_checkpoints": numOfCheckpoints,
        "is_random_checkpoint": isRandomCheckpoint,
        "is_checkpoint_enabled": isCheckpointEnabled,
      };
}
