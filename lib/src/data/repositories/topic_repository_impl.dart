import '../../config/app_config/api_config.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/models/topic.dart';
import '../../domain/repositories/topic_repository.dart';

class TopicRepositoryImpl implements TopicRepository {
  TopicRepositoryImpl();

  @override
  Future<List<TopicList>> getTopicList(String orgId) async {
    final response = await Supabase.instance.client
        .from(APIConfig.V_CATEGORY)
        .select()
        .eq("org_id", orgId);

    List<dynamic> topics = response;
    List<Map<String, dynamic>> mapList =
        topics.map((item) => item as Map<String, dynamic>).toList();

    List<TopicList> topicList =
        mapList.map((json) => TopicList.fromJson(json)).toList();
    return topicList;
  }
}
