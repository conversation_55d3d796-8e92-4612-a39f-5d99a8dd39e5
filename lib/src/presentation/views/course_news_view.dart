import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';

import '../../utils/constants/locale_change_helper.dart';

import '/src/config/themes/app_theme.dart';
import '/src/domain/services/localizations/sl_strings.dart';
import '/src/domain/services/localizations/string_keys.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import '../../utils/constants/nums.dart';
import '../../utils/constants/strings.dart';
import '../widgets/button_widget.dart';
import '/src/utils/constants/helper.dart';
import 'package:screenshot/screenshot.dart';
import '/src/domain/models/course_details.dart';
import '/src/utils/helper/app_date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';
import '../../config/themes/lms_fonts.dart';
import '../../config/themes/theme_colors.dart';
import '../widgets/app_bar.dart';

@RoutePage()
class CourseNewsViewScreen extends HookWidget with WidgetsBindingObserver {
  final CurrentAffairs currentAffairs;

  CourseNewsViewScreen({
    super.key,
    required this.currentAffairs,
  });

  final ScreenshotController _screenshotController = ScreenshotController();

  BuildContext? currentContext;

  static const double _horizontalMargin =
      16.0; // html takes padding 7 by default

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);
    didChangeAppLocale(locales, currentContext);
  }

  /// Capture current screen as screenshot and share the image
  Future<void> captureScreenshotandShare() async {
    await screenshotandShare(_screenshotController);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    String appBarTitle =
        AppDateFormatter().formatDateMd(currentAffairs.publishedDate);
    return Scaffold(
      backgroundColor: AppTheme.bgColor,
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
              title: appBarTitle,
              leadingIconName: BACK_ARROW,
              trailingWidget: Container(),
              leadingBtnAction: () =>
                  kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop())),
      body: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
        return Container(
            height: constraints.maxHeight,
            decoration: const BoxDecoration(
              color: AppTheme.whiteColor,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: Align(
              child: Container(
                width: constraints.maxWidth < 720 ? double.infinity : 800,
                margin:
                    const EdgeInsets.symmetric(horizontal: _horizontalMargin),
                child: Column(
                  children: [
                    const SizedBox(height: 12),
                    kIsWeb ? Container() : shareButton(),
                    ScrollConfiguration(
                      behavior: ScrollConfiguration.of(context).copyWith(
                        dragDevices: {
                          PointerDeviceKind.touch,
                          PointerDeviceKind.mouse,
                        },
                      ),
                      child: Expanded(
                        child: Screenshot(
                          controller: _screenshotController,
                          child: Container(
                            color: AppTheme.whiteColor,
                            child: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    padding:
                                        const EdgeInsets.symmetric(vertical: 1),
                                    decoration: BoxDecoration(
                                      color: LmsColors.white,
                                      borderRadius: BorderRadius.circular(11),
                                    ),
                                    child: Column(
                                      children: [
                                        currentAffairsItem(),
                                        HtmlWidget(
                                          currentAffairs.newsContent
                                                  .trim()
                                                  .isNotEmpty
                                              ? currentAffairs.newsContent
                                              : currentAffairs.newsTitle,
                                          // style: LMSFonts
                                          //     .htmlStyeForCurrentAffairs(
                                          //         MediaQuery.sizeOf(context)
                                          //                 .width -
                                          //             (17 * 2)),
                                          onTapUrl: (url) =>
                                              launchSelectedUrl(url),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // TO DO: Implement in new design
                                  // Expanded(child: Container()),
                                  // Align(
                                  //   alignment: Alignment.bottomCenter,
                                  //   child: Text(
                                  //     'SMART LEARN\nCITRUS INFORMATICS\n123456789',
                                  //     textAlign: TextAlign.center,
                                  //     style: LMSFonts.regularFontWithHeight(
                                  //         12, AppTheme.primaryBlue, 1.1),
                                  //   ),
                                  // ),
                                ],
                              ),
                              //   ),
                              // ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ));
      }),
    );
  }

  Widget currentAffairsItem() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 3),
          child: Image.asset('$ASSETS_PATH/file_light.png',
              color: AppTheme.answerOptedColor, height: 25, width: 25),
        ),
        const SizedBox(width: 11),
        Expanded(
          child: Text(
            currentAffairs.newsTitle,
            style: LMSFonts.boldFont(16, AppTheme.examIntroTextColor),
          ),
        ),
      ],
    );
  }

  Widget shareButton() {
    return Container(
      decoration: const BoxDecoration(
        borderRadius:
            BorderRadiusDirectional.vertical(top: Radius.circular(16)),
        color: Colors.white,
      ),
      height: 60,
      child: Align(
        alignment: Alignment.topRight,
        child: ButtonWidget(
            minimumSize: const Size(10, 10),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(buttonRadius),
                side: const BorderSide(
                  color: AppTheme.examInfoTextColor,
                )),
            color: AppTheme.whiteTextColor,
            child: Text(SLStrings.getTranslatedString(KEY_SHARE),
                style: LMSFonts.semiBoldFont(16, AppTheme.examInfoTextColor)),
            onPressed: () async {
              await captureScreenshotandShare();
            }),
      ),
    );
  }
}
