// ignore_for_file: public_member_api_docs, sort_constructors_first
import '/src/domain/models/question_data.dart';

class CheckPointQuiz {
  String? status;
  List<QuestionData>? questions;
  String? quizAttemptId;
  String? moduleSessionId;
  CheckPointQuiz({
    this.status,
    this.questions,
    this.quizAttemptId,
    this.moduleSessionId,
  });

  CheckPointQuiz.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['questions'] != null) {
      questions = <QuestionData>[];
      json['questions'].forEach((v) {
        questions?.add(QuestionData.fromJson(v, ""));
      });
    }
    quizAttemptId = json['quiz_attempt_id'];
    moduleSessionId = json['module_session_id'];
  }
}
