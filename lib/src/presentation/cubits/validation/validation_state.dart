part of 'validation_cubit.dart';

abstract class ValidationState extends Equatable {
  final String? emailError;
  final String? passwordError;
  final String? phoneNumError;
  const ValidationState(
      {this.emailError, this.passwordError, this.phoneNumError});

  @override
  List<Object?> get props => [];
}

class validationInitial extends ValidationState {}

class InputDataValidationSuccess extends ValidationState {
  final String? message;

  const InputDataValidationSuccess({this.message});
}

class EmailValidationFailure extends ValidationState {
  const EmailValidationFailure({super.emailError});
}

class PasswordValidationFailure extends ValidationState {
  const PasswordValidationFailure({super.passwordError});
}

class PhoneNumberValidationFailure extends ValidationState {
  const PhoneNumberValidationFailure({super.phoneNumError});
}

class InputDataValidationFailure extends ValidationState {
  const InputDataValidationFailure(
      {super.emailError, super.passwordError, super.phoneNumError});
}

class SubmitButtonStatus extends ValidationState {
  final bool enableButton;

  const SubmitButtonStatus({required this.enableButton});

  @override
  List<Object> get props => [enableButton];
}

class ValidatedAllFields extends ValidationState {
  const ValidatedAllFields();

  @override
  List<Object> get props => [];
}
