class NewExams {
  static List<Map<String, dynamic>> examDetails() {
    return [
      {
        "cseb": "M 1",
        "questions": "50",
        "marks": "50",
        "passmark": "40",
        "min": "50",
      },
      {
        "cseb": "M 2",
        "questions": "50",
        "marks": "50",
        "passmark": "40",
        "min": "50",
      },
      {
        "cseb": "M 3",
        "questions": "50",
        "marks": "50",
        "passmark": "40",
        "min": "50",
      },
      {
        "cseb": "M 4",
        "questions": "50",
        "marks": "50",
        "passmark": "40",
        "min": "50",
      },
      {
        "cseb": "M 5",
        "questions": "50",
        "marks": "50",
        "passmark": "40",
        "min": "50",
      },
      {
        "cseb": "M 6",
        "questions": "50",
        "marks": "50",
        "passmark": "40",
        "min": "50",
      },
      {
        "cseb": "M 7",
        "questions": "50",
        "marks": "50",
        "passmark": "40",
        "min": "50",
      }
    ];
  }

  static List passedDetails() {
    return [
      {
        "exams": "No passed exams found!",
        "listOfExams": "Passed Exams will be listed here",
        "contents":
            "Once you pass all the exams in the current level, your level will be upgraded and you can explore more contents",
        "profile":
            "You can change to any level and load its contents from your profile section",
      },
    ];
  }

  static List attemptedDetails() {
    return [
      {
        "exams": "No attempted exams in this level",
        "listOfExams":
            "You can re attend the questionnaire which you have not secured pass mark",
        "contents": "you can see passed exams in the next tab",
        "profile":
            "You can change to any level and load its contents from your profile section",
      },
    ];
  }

  static List noNewExamDetails() {
    return [
      {
        "exams": "No New Exam For You",
        "listOfExams":
            "You have attended all new exams.You can re-attend the exams which you have not secured pass mark in next tab.",
        //"contents": "you can see passed exams in the next tab",
        "profile":
            "Exam are based on level and you can change to any level and load its content from your profile section.",
      },
    ];
  }

  static const List<String> reportQuestionsOptions = [
    'Wrong question',
    'Wrong option are found',
    'Not relevent question',
    'other',
  ];
}
