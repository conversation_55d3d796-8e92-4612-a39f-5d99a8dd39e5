import '/src/config/app_config/db_config.dart';
import '/src/domain/models/hive_db_models/hive_examtable.dart';
import '/src/domain/models/hive_db_models/hive_milestone_data.dart';
import '/src/domain/models/hive_db_models/hive_video_player.dart';
import '/src/domain/models/question_data.dart';
import 'package:hive_flutter/hive_flutter.dart';

class HiveDbHelper {
  Future<void> hiveInit() async {
    await Hive.initFlutter();
  }

  Future<void> registerHiveAdapters() async {
    Hive.registerAdapter<HiveExamTable>(HiveExamTableAdapter());
    Hive.registerAdapter<HiveVideoPlayerData>(HiveVideoPlayerDataAdapter());
    Hive.registerAdapter<HiveMileStoneData>(HiveMileStoneDataAdapter());
    // final List<TypeAdapter> adapters = [
    //   HiveExamTableAdapter(),
    //   HiveVideoPlayerDataAdapter(),
    //   HiveMileStoneDataAdapter(),
    // ];

    // for (final adapter in adapters) {
    //   if (!Hive.isAdapterRegistered(adapter.typeId)) {
    //     Hive.registerAdapter(adapter);
    //   }
    // }
  }

  Future<void> hiveInsertOrUpdate(HiveExamTable value) async {
    final examTable =
        await Hive.openBox<HiveExamTable>(DBConfig.HIVE_EXAM_BOX_NAME);
    final data = examTable.get(value.examId);
    if (data == null) {
      await examTable.put(value.examId, value);
    } else {
      await examTable.delete(value.examId);
      await examTable.put(value.examId, value);
    }
  }

  Future<HiveExamTable?> fetchQuestions(String quizId) async {
    final examTable =
        await Hive.openBox<HiveExamTable>(DBConfig.HIVE_EXAM_BOX_NAME);
    final data = examTable.get(quizId);
    return data;
  }

  Future<void> hiveInsertOrUpdateVideoData(HiveVideoPlayerData data) async {
    final videoDataTable = await Hive.openBox<HiveVideoPlayerData>(
        DBConfig.HIVE_VIDEO_PLAYER_DATA);
    final results = videoDataTable.get(data.videoId);
    if (results == null) {
      await videoDataTable.put(data.videoId, data);
    } else {
      await videoDataTable.delete(data.videoId);
      await videoDataTable.put(data.videoId, data);
    }
  }

  Future<HiveVideoPlayerData?> hiveGetVideoPlayerData(String videoId) async {
    final videoDataTable = await Hive.openBox<HiveVideoPlayerData>(
        DBConfig.HIVE_VIDEO_PLAYER_DATA);
    final results = videoDataTable.get(videoId);
    return results;
  }
}
