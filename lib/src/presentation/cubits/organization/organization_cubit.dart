import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase/supabase.dart';
import '../../../config/app_config/preference_config.dart';
import '../../../domain/models/organization.dart';
import '../../../domain/models/privilege_access/privilege_access.dart';
import '../../../domain/models/topic.dart';
import '../../../domain/repositories/organization_repository.dart';
import '../../../utils/constants/strings.dart';

part 'organization_state.dart';

class OrganizationCubit extends Cubit<OrganizationState> {
  final OrganizationRepository _organizationRepository;
  bool loadingStatus = false;

  OrganizationCubit(this._organizationRepository)
      : super(const OrganizationLoading(isDataLoading: false));

  Future<void> getUserOrganizations() async {
    // SharedPreferences _prefs = await SharedPreferences.getInstance();
    // _prefs.getString("userId") ?? '';
    String userId = USER_ID;

    if (userId.isNotEmpty) {
      emit(await _getUserOrganizations(userId, false));
    }
  }

  Future<OrganizationState> _getUserOrganizations(
      String userId, bool buttonColor) async {
    try {
      final organizations =
          await _organizationRepository.getUserOrganizations(userId);
      organizations.sort((a, b) => (a.orgName ?? '')
          .toLowerCase()
          .compareTo((b.orgName ?? '').toLowerCase()));
      return OrganizationSuccess(
          organizations: organizations, buttonColor: buttonColor);
    } on Exception catch (e) {
      return OrganizationError(error: e.toString(), buttonColor: false);
    }
  }

  Future<void> handleOrgSelection(String? selectedOrgName) async {
    if (selectedOrgName != null && selectedOrgName.isNotEmpty) {
      emit(const UpdateSelectedOrgColor(shouldUpdateColor: true));
    } else {
      emit(const UpdateSelectedOrgColor(shouldUpdateColor: false));
    }
  }

  setLoading(bool loadStatus) {
    loadingStatus = loadStatus;
    if (loadStatus) emit(OrganizationLoading(isDataLoading: loadStatus));
  }

  /// get privilege access list
  getPrivilegeAccessList() async {
    SharedPreferences _pref = await SharedPreferences.getInstance();
    try {
      if (USER_ID.isNotEmpty && ORG_ID.isNotEmpty) {
        List<PrivilegeAccess> _privilegeAccess =
            await _organizationRepository.getUserPrevileges(USER_ID, ORG_ID);
        if (_privilegeAccess.isNotEmpty) {
          _pref.setString(PREF_KEY_PREVILEGE_ACCESS_LIST,
              jsonEncode(_privilegeAccess.first));
          emit(PrivilegeAccessFetched(privilegeAccess: _privilegeAccess.first));
        } else {
          emit(const PrivilegeAccessError(
              error: 'Error while fetching privilege access!'));
        }
      } else {
        emit(const PrivilegeAccessError(
            error: 'Error while fetching privilege access!'));
      }
    } on PostgrestException catch (e) {
      String error = e.message;
      debugPrint(
          '[OrganizationCubit][getPrivilegeAccessList][PostgrestException]: $error');
      emit(PrivilegeAccessError(error: error));
    } on Exception catch (e) {
      debugPrint(
          '[OrganizationCubit][getPrivilegeAccessList][Exception]: ${e.toString()}');
      emit(PrivilegeAccessError(error: e.toString()));
    }
  }
}
