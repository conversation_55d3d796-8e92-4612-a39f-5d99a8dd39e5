// To parse this JSON data, do
//
//     final brandingConfig = brandingConfigFromJson(jsonString);

import 'dart:convert';

import 'package:SmartLearn/src/config/app_config/app_branding.dart';

List<BrandingConfig> brandingConfigFromJson(String str) =>
    List<BrandingConfig>.from(
        json.decode(str).map((x) => BrandingConfig.fromJson(x)));

String brandingConfigToJson(List<BrandingConfig> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BrandingConfig {
 String? active = AppBranding.lightTheme['active'];
  String? orgId = AppBranding.lightTheme['org_id'];
  String? favicon = AppBranding.lightTheme['favicon'];
  String? appLogo = AppBranding.lightTheme['app_logo'];
  DateTime? validTo; // Assuming no string mapping
  String? mainLogo = AppBranding.lightTheme['main_logo'];
  DateTime? createdAt;
  String? fontColor = AppBranding.lightTheme['font_color'];
  String? themeName = AppBranding.lightTheme['theme_name'];
  DateTime? updatedAt;
  DateTime? validFrom;
  String? fontFamily = AppBranding.lightTheme['font_family'];
  String? footerText = AppBranding.lightTheme['footer_text'];
  String? bannerImage = AppBranding.lightTheme['banner_image'];
  String? welcomeText = AppBranding.lightTheme['welcome_text'];
  String? fontBaseSize = AppBranding.lightTheme['font_base_size'];
  String? toastInfoColor = AppBranding.lightTheme['toast_info_color'];
  String? footerTextColor = AppBranding.lightTheme['footer_text_color'];
  String? navbarTextColor = AppBranding.lightTheme['navbar_text_color'];
  String? toastErrorColor = AppBranding.lightTheme['toast_error_color'];
  String? sidebarTextColor = AppBranding.lightTheme['sidebar_text_color'];
  String? topBarTextColor = AppBranding.lightTheme['top_bar_text_color'];
  String? toastSuccessColor = AppBranding.lightTheme['toast_success_color'];
  String? toastWarningColor = AppBranding.lightTheme['toast_warning_color'];
  String? appBackgroundColor = AppBranding.lightTheme['app_background_color'];
  String? buttonPrimaryColor = AppBranding.lightTheme['button_primary_color'];
  String? sidebarActiveColor = AppBranding.lightTheme['sidebar_active_color'];
  String? topBarActiveColor = AppBranding.lightTheme['top_bar_active_color'];
  String? buttonInfoTextColor =
      AppBranding.lightTheme['button_info_text_color'];
  String? buttonSecondaryColor =
      AppBranding.lightTheme['button_secondary_color'];
  String? buttonDismissBgColor =
      AppBranding.lightTheme['button_dismiss_bg_color'];
  String? footerBackgroundColor =
      AppBranding.lightTheme['footer_background_color'];
  String? navbarBackgroundColor =
      AppBranding.lightTheme['navbar_background_color'];
  String? navbarTextColorHover =
      AppBranding.lightTheme['navbar_text_color_hover'];
  String? sidebarBackgroundColor =
      AppBranding.lightTheme['sidebar_background_color'];
  String? topBarBackgroundColor =
      AppBranding.lightTheme['top_bar_background_color'];
  String? buttonDismissTextColor =
      AppBranding.lightTheme['button_dismiss_text_color'];
  String? buttonPrimaryTextColor =
      AppBranding.lightTheme['button_primary_text_color'];
  String? buttonSecondaryTextColor =
      AppBranding.lightTheme['button_secondary_text_color'];
  String? buttonInfoBackgroundColor =
      AppBranding.lightTheme['button_info_background_color'];
  String? sidebarActiveBackgroundColor =
      AppBranding.lightTheme['sidebar_active_background_color'];

  BrandingConfig({
    this.active,
    this.orgId,
    this.favicon,
    this.appLogo,
    this.validTo,
    this.mainLogo,
    this.createdAt,
    this.fontColor,
    this.themeName,
    this.updatedAt,
    this.validFrom,
    this.fontFamily,
    this.footerText,
    this.bannerImage,
    this.welcomeText,
    this.fontBaseSize,
    this.toastInfoColor,
    this.footerTextColor,
    this.navbarTextColor,
    this.toastErrorColor,
    this.sidebarTextColor,
    this.topBarTextColor,
    this.toastSuccessColor,
    this.toastWarningColor,
    this.appBackgroundColor,
    this.buttonPrimaryColor,
    this.sidebarActiveColor,
    this.topBarActiveColor,
    this.buttonInfoTextColor,
    this.buttonSecondaryColor,
    this.buttonDismissBgColor,
    this.footerBackgroundColor,
    this.navbarBackgroundColor,
    this.navbarTextColorHover,
    this.sidebarBackgroundColor,
    this.topBarBackgroundColor,
    this.buttonDismissTextColor,
    this.buttonPrimaryTextColor,
    this.buttonSecondaryTextColor,
    this.buttonInfoBackgroundColor,
    this.sidebarActiveBackgroundColor,
  });

  BrandingConfig copyWith({
    String? active,
    String? orgId,
    String? favicon,
    String? appLogo,
    DateTime? validTo,
    String? mainLogo,
    DateTime? createdAt,
    String? fontColor,
    String? themeName,
    DateTime? updatedAt,
    DateTime? validFrom,
    String? fontFamily,
    String? footerText,
    String? bannerImage,
    String? welcomeText,
    String? fontBaseSize,
    String? toastInfoColor,
    String? footerTextColor,
    String? navbarTextColor,
    String? toastErrorColor,
    String? sidebarTextColor,
    String? topBarTextColor,
    String? toastSuccessColor,
    String? toastWarningColor,
    String? appBackgroundColor,
    String? buttonPrimaryColor,
    String? sidebarActiveColor,
    String? topBarActiveColor,
    String? buttonInfoTextColor,
    String? buttonSecondaryColor,
    String? buttonDismissBgColor,
    String? footerBackgroundColor,
    String? navbarBackgroundColor,
    String? navbarTextColorHover,
    String? sidebarBackgroundColor,
    String? topBarBackgroundColor,
    String? buttonDismissTextColor,
    String? buttonPrimaryTextColor,
    String? buttonSecondaryTextColor,
    String? buttonInfoBackgroundColor,
    String? sidebarActiveBackgroundColor,
  }) =>
      BrandingConfig(
        active: active ?? this.active,
        orgId: orgId ?? this.orgId,
        favicon: favicon ?? this.favicon,
        appLogo: appLogo ?? this.appLogo,
        validTo: validTo ?? this.validTo,
        mainLogo: mainLogo ?? this.mainLogo,
        createdAt: createdAt ?? this.createdAt,
        fontColor: fontColor ?? this.fontColor,
        themeName: themeName ?? this.themeName,
        updatedAt: updatedAt ?? this.updatedAt,
        validFrom: validFrom ?? this.validFrom,
        fontFamily: fontFamily ?? this.fontFamily,
        footerText: footerText ?? this.footerText,
        bannerImage: bannerImage ?? this.bannerImage,
        welcomeText: welcomeText ?? this.welcomeText,
        fontBaseSize: fontBaseSize ?? this.fontBaseSize,
        toastInfoColor: toastInfoColor ?? this.toastInfoColor,
        footerTextColor: footerTextColor ?? this.footerTextColor,
        navbarTextColor: navbarTextColor ?? this.navbarTextColor,
        toastErrorColor: toastErrorColor ?? this.toastErrorColor,
        sidebarTextColor: sidebarTextColor ?? this.sidebarTextColor,
        topBarTextColor: topBarTextColor ?? this.topBarTextColor,
        toastSuccessColor: toastSuccessColor ?? this.toastSuccessColor,
        toastWarningColor: toastWarningColor ?? this.toastWarningColor,
        appBackgroundColor: appBackgroundColor ?? this.appBackgroundColor,
        buttonPrimaryColor: buttonPrimaryColor ?? this.buttonPrimaryColor,
        sidebarActiveColor: sidebarActiveColor ?? this.sidebarActiveColor,
        topBarActiveColor: topBarActiveColor ?? this.topBarActiveColor,
        buttonInfoTextColor: buttonInfoTextColor ?? this.buttonInfoTextColor,
        buttonSecondaryColor: buttonSecondaryColor ?? this.buttonSecondaryColor,
        buttonDismissBgColor: buttonDismissBgColor ?? this.buttonDismissBgColor,
        footerBackgroundColor:
            footerBackgroundColor ?? this.footerBackgroundColor,
        navbarBackgroundColor:
            navbarBackgroundColor ?? this.navbarBackgroundColor,
        navbarTextColorHover: navbarTextColorHover ?? this.navbarTextColorHover,
        sidebarBackgroundColor:
            sidebarBackgroundColor ?? this.sidebarBackgroundColor,
        topBarBackgroundColor:
            topBarBackgroundColor ?? this.topBarBackgroundColor,
        buttonDismissTextColor:
            buttonDismissTextColor ?? this.buttonDismissTextColor,
        buttonPrimaryTextColor:
            buttonPrimaryTextColor ?? this.buttonPrimaryTextColor,
        buttonSecondaryTextColor:
            buttonSecondaryTextColor ?? this.buttonSecondaryTextColor,
        buttonInfoBackgroundColor:
            buttonInfoBackgroundColor ?? this.buttonInfoBackgroundColor,
        sidebarActiveBackgroundColor:
            sidebarActiveBackgroundColor ?? this.sidebarActiveBackgroundColor,
      );

  factory BrandingConfig.fromJson(Map<String, dynamic> json) => BrandingConfig(
        active: json["active"],
        orgId: json["org_id"],
        favicon: json["favicon"],
        appLogo: json["app_logo"],
        validTo:
            json["valid_to"] == null ? null : DateTime.parse(json["valid_to"]),
        mainLogo: json["main_logo"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        fontColor: json["font_color"],
        themeName: json["theme_name"],
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        validFrom: json["valid_from"] == null
            ? null
            : DateTime.parse(json["valid_from"]),
        fontFamily: json["font_family"],
        footerText: json["footer_text"],
        bannerImage: json["banner_image"],
        welcomeText: json["welcome_text"],
        fontBaseSize: json["font_base_size"],
        toastInfoColor: json["toast_info_color"],
        footerTextColor: json["footer_text_color"],
        navbarTextColor: json["navbar_text_color"],
        toastErrorColor: json["toast_error_color"],
        sidebarTextColor: json["sidebar_text_color"],
        topBarTextColor: json["top_bar_text_color"],
        toastSuccessColor: json["toast_success_color"],
        toastWarningColor: json["toast_warning_color"],
        appBackgroundColor: json["app_background_color"],
        buttonPrimaryColor: json["button_primary_color"],
        sidebarActiveColor: json["sidebar_active_color"],
        topBarActiveColor: json["top_bar_active_color"],
        buttonInfoTextColor: json["button_info_text_color"],
        buttonSecondaryColor: json["button_secondary_color"],
        buttonDismissBgColor: json["button_dismiss_bg_color"],
        footerBackgroundColor: json["footer_background_color"],
        navbarBackgroundColor: json["navbar_background_color"],
        navbarTextColorHover: json["navbar_text_color_hover"],
        sidebarBackgroundColor: json["sidebar_background_color"],
        topBarBackgroundColor: json["top_bar_background_color"],
        buttonDismissTextColor: json["button_dismiss_text_color"],
        buttonPrimaryTextColor: json["button_primary_text_color"],
        buttonSecondaryTextColor: json["button_secondary_text_color"],
        buttonInfoBackgroundColor: json["button_info_background_color"],
        sidebarActiveBackgroundColor: json["sidebar_active_background_color"],
      );

  Map<String, dynamic> toJson() => {
        "active": active,
        "org_id": orgId,
        "favicon": favicon,
        "app_logo": appLogo,
        "valid_to": validTo?.toIso8601String(),
        "main_logo": mainLogo,
        "created_at": createdAt?.toIso8601String(),
        "font_color": fontColor,
        "theme_name": themeName,
        "updated_at": updatedAt?.toIso8601String(),
        "valid_from": validFrom?.toIso8601String(),
        "font_family": fontFamily,
        "footer_text": footerText,
        "banner_image": bannerImage,
        "welcome_text": welcomeText,
        "font_base_size": fontBaseSize,
        "toast_info_color": toastInfoColor,
        "footer_text_color": footerTextColor,
        "navbar_text_color": navbarTextColor,
        "toast_error_color": toastErrorColor,
        "sidebar_text_color": sidebarTextColor,
        "top_bar_text_color": topBarTextColor,
        "toast_success_color": toastSuccessColor,
        "toast_warning_color": toastWarningColor,
        "app_background_color": appBackgroundColor,
        "button_primary_color": buttonPrimaryColor,
        "sidebar_active_color": sidebarActiveColor,
        "top_bar_active_color": topBarActiveColor,
        "button_info_text_color": buttonInfoTextColor,
        "button_secondary_color": buttonSecondaryColor,
        "button_dismiss_bg_color": buttonDismissBgColor,
        "footer_background_color": footerBackgroundColor,
        "navbar_background_color": navbarBackgroundColor,
        "navbar_text_color_hover": navbarTextColorHover,
        "sidebar_background_color": sidebarBackgroundColor,
        "top_bar_background_color": topBarBackgroundColor,
        "button_dismiss_text_color": buttonDismissTextColor,
        "button_primary_text_color": buttonPrimaryTextColor,
        "button_secondary_text_color": buttonSecondaryTextColor,
        "button_info_background_color": buttonInfoBackgroundColor,
        "sidebar_active_background_color": sidebarActiveBackgroundColor,
      };
}
