import 'package:flutter/material.dart';
import '../../domain/models/branding_config/branding_config.dart';
import '../router/custom_transition.dart';
import 'app_theme.dart';

class AppDynamicTheme extends ChangeNotifier {
  // Theme colors
  Color primaryColor = AppTheme.primaryAppColor;
  Color appBackgroundColor = AppTheme.whiteColor;
  Color primaryTextColor = AppTheme.primaryTextColorBlack;
  Color appbarColor = AppTheme.primaryAppColor;
  Color appbarTextColor = AppTheme.whiteTextColor;
  Color appBarActiveColor = AppTheme.primaryAppColor;
  Color iconColor = AppTheme.iconColor;
  Color dividerColor = AppTheme.primaryAppColor;
  Color buttonBgColour = AppTheme.submitBtnColor;
  Color buttonPrimaryColor = AppTheme.primaryAppColor;
  Color buttonSecondaryColor = AppTheme.secondaryAppColor;
  Color buttonInfoTextColor = AppTheme.whiteColor;
  Color buttonDismissBgColor = AppTheme.whiteColor;
  Color buttonDismissTextColor = AppTheme.blackColor;
  Color buttonPrimaryTextColor = AppTheme.whiteColor;
  Color buttonSecondaryTextColor = AppTheme.whiteColor;
  Color buttonInfoBackgroundColor = AppTheme.infoBlue;
  Color sidebarTextColor = AppTheme.primaryTextColorBlack;
  Color sidebarActiveColor = AppTheme.whiteColor;
  Color sidebarBackgroundColor = AppTheme.sidemenuBGColor;
  Color sidebarActiveBackgroundColor = AppTheme.sidemenuBGColor;
  Color navbarTextColor = AppTheme.primaryBlue;
  Color navbarBackgroundColor = AppTheme.whiteColor;
  Color navbarTextColorHover = AppTheme.primaryBlue;
  Color footerTextColor = AppTheme.blackColor;
  Color footerBackgroundColor = AppTheme.whiteColor;
  Color toastInfoColor = AppTheme.infoBlue;
  Color toastErrorColor = AppTheme.primaryRed;
  Color toastSuccessColor = AppTheme.okBtnGreen;
  Color toastWarningColor = AppTheme.warningYellow;
  double fontBaseSize = 16.0;

  String footerText = '';
  String bannerImage = '';
  String welcomeText = '';
  String fontFamily = '';
  String themeName = '';
  String appLogo = '';
  String mainLogo = '';

  AppDynamicTheme({
    this.primaryColor = AppTheme.primaryAppColor,
    this.primaryTextColor = AppTheme.primaryTextColorBlack,
    this.appBackgroundColor = AppTheme.whiteColor,
    this.appbarColor = AppTheme.primaryAppColor,
    this.appbarTextColor = AppTheme.whiteTextColor,
  });

  ///
  /// Theme
  ///

  ThemeData get themeData {
    return ThemeData(
      useMaterial3: false,
      fontFamily: fontFamily,
      textTheme: TextTheme(
        bodyMedium: TextStyle(
          color: primaryTextColor,
          fontSize: fontBaseSize,
          fontFamily: fontFamily,
        ),
        bodyLarge: TextStyle(
          color: primaryTextColor,
          fontSize: fontBaseSize * 1.2,
          fontFamily: fontFamily,
        ),
        titleLarge: TextStyle(
          color: primaryTextColor,
          fontSize: fontBaseSize * 1.4,
          fontWeight: FontWeight.w600,
          fontFamily: fontFamily,
        ),
      ),
      appBarTheme: AppBarTheme(
        elevation: 0,
        backgroundColor: appbarColor,
        titleTextStyle: TextStyle(
          color: appbarTextColor,
          fontSize: fontBaseSize * 1.375, // 22.0 for base 16.0
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(
          color: appBarActiveColor,
        ),
      ),
      scaffoldBackgroundColor: appbarColor, // to show the curves around edges
      primaryColor: primaryColor,
      splashColor: Colors.transparent,
      colorScheme: ColorScheme.fromSwatch().copyWith(
        primary: primaryColor,
        secondary: buttonSecondaryColor,
        onPrimary: primaryTextColor,
        background: appbarColor,
        surface: sidebarBackgroundColor,
        onSurface: sidebarTextColor,
      ),
      iconTheme: IconThemeData(
        color: iconColor,
        size: 24,
      ),
      dividerTheme: DividerThemeData(
        color: dividerColor,
      ),
      radioTheme: RadioThemeData(
        fillColor:
            MaterialStateColor.resolveWith((states) => buttonPrimaryColor),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonBgColour,
          foregroundColor: buttonPrimaryTextColor,
          shadowColor: Colors.white,
          disabledBackgroundColor: Colors.grey[300]?.withOpacity(0.4),
          disabledForegroundColor: Colors.grey,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: TextStyle(
            fontSize: fontBaseSize * 1.25,
            fontFamily: fontFamily,
            color: buttonPrimaryTextColor,
          ),
        ),
      ),
      // textButtonTheme: TextButtonThemeData(
      //   style: TextButton.styleFrom(
      //     foregroundColor: buttonSecondaryTextColor,
      //     backgroundColor: buttonSecondaryColor,
      //     textStyle: TextStyle(
      //       fontSize: fontBaseSize,
      //       fontFamily: fontFamily,
      //     ),
      //   ),
      // ),
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: navbarBackgroundColor,
        indicatorColor: navbarTextColorHover,
        labelTextStyle: MaterialStateProperty.all(
          TextStyle(
            color: navbarTextColor,
            fontSize: fontBaseSize * 0.875,
            fontFamily: fontFamily,
          ),
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: toastInfoColor,
        contentTextStyle: TextStyle(
          color: buttonInfoTextColor,
          fontSize: fontBaseSize,
          fontFamily: fontFamily,
        ),
        actionTextColor: buttonInfoTextColor,
      ),
      pageTransitionsTheme: PageTransitionsTheme(builders: {
        TargetPlatform.android: CustomPageTransitionsBuilder(),
        TargetPlatform.iOS: CustomPageTransitionsBuilder(),
      }),
    );
  }

  // ThemeData get tabBarTheme => ThemeData(
  //     scaffoldBackgroundColor:
  //         appBackgroundColor, // to show the curves around edges
  //     colorScheme: ColorScheme.light(
  //       primary: primaryColor,
  //       secondary: AppTheme.secondaryAppColor,
  //       background: appBackgroundColor,
  //     ),
  //     iconTheme: IconThemeData(
  //       color: iconColor,
  //       size: 24,
  //     ),
  //     dividerTheme: DividerThemeData(color: dividerColor));

  // factory AppDynamicTheme.fromBrandingConfig(BrandingConfig config) {
  //   return AppDynamicTheme(
  //     primaryColor: _parseColor(config.topBarBackgroundColor ?? '#00B9C7'),
  //     primaryTextColor: _parseColor(config.fontColor ?? '#000'),
  //     appBackgroundColor: _parseColor(config.appBackgroundColor ?? '#00B9C7'),
  //     appbarColor: _parseColor(config.topBarBackgroundColor ?? '#00B9C7'),
  //     appbarTextColor: _parseColor(config.topBarTextColor ?? '#ffffff'),
  //   )
  //     ..appBarActiveColor = _parseColor(config.topBarActiveColor ?? '#ff0000')
  //     ..iconColor = AppTheme.iconColor
  //     ..dividerColor = AppTheme.dividerColor
  //     ..buttonBgColour = _parseColor(config.buttonPrimaryColor ?? '#0000ff')
  //     ..buttonPrimaryColor = _parseColor(config.buttonPrimaryColor ?? '#0000ff')
  //     ..buttonSecondaryColor =
  //         _parseColor(config.buttonSecondaryColor ?? '#607d8b')
  //     ..buttonInfoTextColor =
  //         _parseColor(config.buttonInfoTextColor ?? '#0000ff')
  //     ..buttonDismissBgColor =
  //         _parseColor(config.buttonDismissBgColor ?? '#9e9e9e')
  //     ..buttonDismissTextColor =
  //         _parseColor(config.buttonDismissTextColor ?? '#000')
  //     ..buttonPrimaryTextColor =
  //         _parseColor(config.buttonPrimaryTextColor ?? '#ffffff')
  //     ..buttonSecondaryTextColor =
  //         _parseColor(config.buttonSecondaryTextColor ?? '#0000ff')
  //     ..buttonInfoBackgroundColor =
  //         _parseColor(config.buttonInfoBackgroundColor ?? '#b3e5fc')
  //     ..sidebarTextColor = _parseColor(config.sidebarTextColor ?? '#000')
  //     ..sidebarActiveColor = _parseColor(config.sidebarActiveColor ?? '#0000ff')
  //     ..sidebarBackgroundColor =
  //         _parseColor(config.sidebarBackgroundColor ?? '#00B9C7')
  //     ..sidebarActiveBackgroundColor =
  //         _parseColor(config.sidebarActiveBackgroundColor ?? '#00B9C7')
  //     ..navbarTextColor = _parseColor(config.navbarTextColor ?? '#ffffff')
  //     ..navbarBackgroundColor =
  //         _parseColor(config.navbarBackgroundColor ?? '#3f51b5')
  //     ..navbarTextColorHover =
  //         _parseColor(config.navbarTextColorHover ?? '#ffff00')
  //     ..footerTextColor = _parseColor(config.footerTextColor ?? '#ffffff')
  //     ..footerBackgroundColor =
  //         _parseColor(config.footerBackgroundColor ?? '#1a237e')
  //     ..toastInfoColor = _parseColor(config.toastInfoColor ?? '#0000ff')
  //     ..toastErrorColor = _parseColor(config.toastErrorColor ?? '#ff0000')
  //     ..toastSuccessColor = _parseColor(config.toastSuccessColor ?? '#008000')
  //     ..toastWarningColor = _parseColor(config.toastWarningColor ?? '#ffc107')
  //     ..fontBaseSize = double.parse(config.fontBaseSize ?? '16.0')
  //     ..footerText = config.footerText ?? ''
  //     ..bannerImage = config.bannerImage ?? ''
  //     ..welcomeText = config.welcomeText ?? ''
  //     ..fontFamily = config.fontFamily ?? ''
  //     ..themeName = config.themeName ?? ''
  //     ..appLogo = config.appLogo ?? ''
  //     ..mainLogo = config.mainLogo ?? '';
  // }

  // // Method to update theme dynamically
  // void updateFromBrandingConfig(BrandingConfig config) {
  //   primaryColor = _parseColor(
  //       (config.topBarBackgroundColor?.isNotEmpty ?? false)
  //           ? config.topBarBackgroundColor!
  //           : '#00B9C7');
  //   primaryTextColor = _parseColor(
  //       (config.fontColor?.isNotEmpty ?? false) ? config.fontColor! : '#000');
  //   appBackgroundColor = _parseColor(
  //       (config.appBackgroundColor?.isNotEmpty ?? false)
  //           ? config.appBackgroundColor!
  //           : '#00B9C7');
  //   appbarColor = _parseColor(
  //       (config.topBarBackgroundColor?.isNotEmpty ?? false)
  //           ? config.topBarBackgroundColor!
  //           : '#00B9C7');
  //   appbarTextColor = _parseColor((config.topBarTextColor?.isNotEmpty ?? false)
  //       ? config.topBarTextColor!
  //       : '#ffffff');
  //   appBarActiveColor = _parseColor(
  //       (config.topBarActiveColor?.isNotEmpty ?? false)
  //           ? config.topBarActiveColor!
  //           : '#ff0000');
  //   iconColor = AppTheme.iconColor;
  //   dividerColor = AppTheme.dividerColor;
  //   buttonBgColour = _parseColor(
  //       (config.buttonPrimaryColor?.isNotEmpty ?? false)
  //           ? config.buttonPrimaryColor!
  //           : '#0000ff');
  //   buttonPrimaryColor = _parseColor(
  //       (config.buttonPrimaryColor?.isNotEmpty ?? false)
  //           ? config.buttonPrimaryColor!
  //           : '#0000ff');
  //   buttonSecondaryColor = _parseColor(
  //       (config.buttonSecondaryColor?.isNotEmpty ?? false)
  //           ? config.buttonSecondaryColor!
  //           : '#607d8b');
  //   buttonInfoTextColor = _parseColor(
  //       (config.buttonInfoTextColor?.isNotEmpty ?? false)
  //           ? config.buttonInfoTextColor!
  //           : '#0000ff');
  //   buttonDismissBgColor = _parseColor(
  //       (config.buttonDismissBgColor?.isNotEmpty ?? false)
  //           ? config.buttonDismissBgColor!
  //           : '#9e9e9e');
  //   buttonDismissTextColor = _parseColor(
  //       (config.buttonDismissTextColor?.isNotEmpty ?? false)
  //           ? config.buttonDismissTextColor!
  //           : '#000');
  //   buttonPrimaryTextColor = _parseColor(
  //       (config.buttonPrimaryTextColor?.isNotEmpty ?? false)
  //           ? config.buttonPrimaryTextColor!
  //           : '#ffffff');
  //   buttonSecondaryTextColor = _parseColor(
  //       (config.buttonSecondaryTextColor?.isNotEmpty ?? false)
  //           ? config.buttonSecondaryTextColor!
  //           : '#0000ff');
  //   buttonInfoBackgroundColor = _parseColor(
  //       (config.buttonInfoBackgroundColor?.isNotEmpty ?? false)
  //           ? config.buttonInfoBackgroundColor!
  //           : '#b3e5fc');
  //   sidebarTextColor = _parseColor(
  //       (config.sidebarTextColor?.isNotEmpty ?? false)
  //           ? config.sidebarTextColor!
  //           : '#fff');
  //   sidebarActiveColor = _parseColor(
  //       (config.sidebarActiveColor?.isNotEmpty ?? false)
  //           ? config.sidebarActiveColor!
  //           : '#000');
  //   sidebarBackgroundColor = _parseColor(
  //       (config.sidebarBackgroundColor?.isNotEmpty ?? false)
  //           ? config.sidebarBackgroundColor!
  //           : '#00B9C7');
  //   sidebarActiveBackgroundColor = _parseColor(
  //       (config.sidebarActiveBackgroundColor?.isNotEmpty ?? false)
  //           ? config.sidebarActiveBackgroundColor!
  //           : '#00B9C7');
  //   navbarTextColor = _parseColor((config.navbarTextColor?.isNotEmpty ?? false)
  //       ? config.navbarTextColor!
  //       : '#00B9C7');
  //   navbarBackgroundColor = _parseColor(
  //       (config.navbarBackgroundColor?.isNotEmpty ?? false)
  //           ? config.navbarBackgroundColor!
  //           : '#3f51b5');
  //   navbarTextColorHover = _parseColor(
  //       (config.navbarTextColorHover?.isNotEmpty ?? false)
  //           ? config.navbarTextColorHover!
  //           : '#ffff00');
  //   footerTextColor = _parseColor((config.footerTextColor?.isNotEmpty ?? false)
  //       ? config.footerTextColor!
  //       : '#ffffff');
  //   footerBackgroundColor = _parseColor(
  //       (config.footerBackgroundColor?.isNotEmpty ?? false)
  //           ? config.footerBackgroundColor!
  //           : '#1a237e');
  //   toastInfoColor = _parseColor((config.toastInfoColor?.isNotEmpty ?? false)
  //       ? config.toastInfoColor!
  //       : '#0000ff');
  //   toastErrorColor = _parseColor((config.toastErrorColor?.isNotEmpty ?? false)
  //       ? config.toastErrorColor!
  //       : '#ff0000');
  //   toastSuccessColor = _parseColor(
  //       (config.toastSuccessColor?.isNotEmpty ?? false)
  //           ? config.toastSuccessColor!
  //           : '#008000');
  //   toastWarningColor = _parseColor(
  //       (config.toastWarningColor?.isNotEmpty ?? false)
  //           ? config.toastWarningColor!
  //           : '#ffc107');
  //   fontBaseSize = double.tryParse((config.fontBaseSize?.isNotEmpty ?? false)
  //           ? config.fontBaseSize!
  //           : '16.0') ??
  //       16.0;
  //   footerText =
  //       (config.footerText?.isNotEmpty ?? false) ? config.footerText! : '';
  //   bannerImage =
  //       (config.bannerImage?.isNotEmpty ?? false) ? config.bannerImage! : '';
  //   welcomeText =
  //       (config.welcomeText?.isNotEmpty ?? false) ? config.welcomeText! : '';
  //   fontFamily =
  //       (config.fontFamily?.isNotEmpty ?? false) ? config.fontFamily! : '';
  //   themeName =
  //       (config.themeName?.isNotEmpty ?? false) ? config.themeName! : '';
  //   appLogo = (config.appLogo?.isNotEmpty ?? false) ? config.appLogo! : '';
  //   mainLogo = (config.mainLogo?.isNotEmpty ?? false) ? config.mainLogo! : '';

  //   notifyListeners(); // Notify listeners of the change
  // }

  static Color _parseColor(String hex) {
    try {
      // Remove any leading '#' and ensure proper hex format
      String cleanedHex = hex.replaceFirst('#', '');
      if (cleanedHex.length == 3) {
        // Handle shorthand hex (e.g., #FFF)
        cleanedHex = cleanedHex.split('').map((c) => c + c).join();
      }
      return Color(int.parse('0xff$cleanedHex'));
    } on Exception catch (e) {
      debugPrint('Error parsing color: $hex, error: $e');
      return Colors.green; // Fallback color for invalid hex
    }
  }
}
