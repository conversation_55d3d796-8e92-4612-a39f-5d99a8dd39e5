class MilestoneData {
  int? id;
  late String videoId;
  late String milestoneId;
  late int sessionStart;
  late int sessionEnd;
  late String sessionResponse;
  late int sessionStatus;

  MilestoneData(
      {this.id,
      required this.videoId,
      required this.milestoneId,
      required this.sessionStart,
      required this.sessionEnd,
      required this.sessionResponse,
      required this.sessionStatus});

  MilestoneData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    milestoneId = json['milestone_id'];
    sessionStart = json['session_start'];
    sessionEnd = json['session_end'];
    sessionResponse = json['session_response'];
    sessionStatus = json['session_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = id;
    data['video_id'] = videoId;
    data['milestone_id'] = milestoneId;
    data['session_start'] = sessionStart;
    data['session_end'] = sessionEnd;
    data['session_response'] = sessionResponse;
    data['session_status'] = sessionStatus;
    return data;
  }
}
