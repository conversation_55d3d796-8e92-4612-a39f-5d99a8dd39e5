import 'package:SmartLearn/src/domain/models/check_point_data/check_point_data.dart';
import 'package:flutter/material.dart';

import '../../config/app_config/api_config.dart';
import '/src/domain/models/resource_file.dart';

import '../../domain/models/course_progress.dart';
import '../../domain/models/page_content.dart';
import '../../domain/models/responses/section_details.dart';
import '../../domain/repositories/section_repository.dart';
import '../../utils/constants/strings.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/models/course_details.dart';

class SectionRepositoryImpl implements SectionRepository {
  SectionRepositoryImpl();

  @override
  Future<SectionDetails> fetchSectionDetailsInfo(String sectionId) async {
    final response = await Supabase.instance.client.rpc(
        APIConfig.GET_SECTION_DETAILS_FN,
        params: {"section_id": sectionId, "user_id": USER_ID});
    List<dynamic> responsedData = response;
    SectionDetails _sectionDetails = SectionDetails.fromJson(responsedData[0]);

    return _sectionDetails;
  }

// Study Material View
// Fetch Page Content
  @override
  Future<PageContent?> fetchPageContent(String instance) async {
    String orgId = ORG_ID;
    final responser = await Supabase.instance.client.rpc(
        APIConfig.VIEW_RESOURCE_PAGE,
        params: {"org_id": orgId, "page_id": instance});
    if (responser != null) {
      Map<String, dynamic> response = responser;
      PageContent pageContent = PageContent.fromJson(response);

      return pageContent;
    }
  }

  @override
  Future<CourseVideo?> fetchResourceVideoInfo(String instance) async {
    String orgId = ORG_ID;
    final response = await Supabase.instance.client.rpc(
        APIConfig.VIEW_RESOURCE_URL,
        params: {"org_id": orgId, "url_id": instance});
    if (response != null) {
      List<dynamic> json = response;
      CourseVideo courseVideo = CourseVideo.fromJson(json.first);
      return courseVideo;
    }
    return null;
  }

  @override
  Future<ResourseFile?> fetchResourceImageInfo(String instance) async {
    String orgId = ORG_ID;
    final response = await Supabase.instance.client.rpc(
        APIConfig.VIEW_RESOURCE_FILE,
        params: {"org_id": orgId, "file_id": instance});
    if (response != null) {
      List<dynamic> json = response;
      ResourseFile resourseFile = ResourseFile.fromJson(json.first);
      return resourseFile;
    }
    return null;
  }

  // get Course progress
  @override
  Future<CourseProgress?> getCourseProgress(String instance) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_COURSE_PROGRESS_FN, params: {
      "instance_ids": [instance],
      "user_id": USER_ID
    });

    if (response != null) {
      List<dynamic> json = response;
      CourseProgress courseProgress = CourseProgress.fromJson(json.first);
      return courseProgress;
    } else {
      CourseProgress courseProgress = CourseProgress(
        progress: 0.00,
        timeSpent: "0.00",
        instanceId: instance,
        markedAsDone: false,
      );
      return courseProgress;
    }
  }

  @override
  Future<CheckPointData?> fetchVideoCheckPointInfo(String moduleId) async {
    String orgId = ORG_ID;
    final response = await Supabase.instance.client.rpc(
        APIConfig.GET_RESOURCE_CHECKPOINT_DATA,
        params: {"course_module_id": moduleId, "org_id": orgId});
    if (response != null) {
      CheckPointData checkPointData = CheckPointData.fromJson(response);
      return checkPointData;
    }
    return response;
  }
}
