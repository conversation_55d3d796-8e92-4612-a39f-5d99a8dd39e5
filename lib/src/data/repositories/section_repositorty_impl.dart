import '../../../src/domain/models/check_point_data/check_point_data.dart';
import '../../config/app_config/api_config.dart';
import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../domain/models/course_dashboard/course_page_resource.dart';
import '../../domain/models/course_dashboard/course_video_resource.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/responses/section_details.dart';
import '../../domain/repositories/section_repository.dart';
import '../../utils/constants/strings.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SectionRepositoryImpl implements SectionRepository {
  SectionRepositoryImpl();

  @override
  Future<SectionDetails> fetchSectionDetailsInfo(String sectionId) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_SECTION_DETAILS_FN, params: {
      "section_id": sectionId,
      "user_id": USER_ID,
      "course_id": COURSE_ID,
      "org_id": ORG_ID
    });

    List<dynamic> responsedData = response;
    SectionDetails _sectionDetails = SectionDetails.fromJson(responsedData[0]);
    _sectionDetails.modules
        .sort((a, b) => a.moduleOrder.compareTo(b.moduleOrder));

    return _sectionDetails;
  }

// Study Material View
// Fetch Page Content
  @override
  Future<CoursePageResource?> fetchPageContent(
      Map<String, dynamic> jsonReq) async {
    final responser = await Supabase.instance.client
        .rpc(APIConfig.VIEW_RESOURCE_PAGE, params: jsonReq);
    if (responser != null) {
      Map<String, dynamic> response = responser;
      CoursePageResource pageContent = CoursePageResource.fromJson(response);

      return pageContent;
    }
    return null;
  }

  @override
  Future<CourseVideoResource?> fetchResourceVideoInfo(
      Map<String, dynamic> jsonReq) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.VIEW_RESOURCE_URL, params: jsonReq);
    if (response != null) {
      List<dynamic> json = response;
      CourseVideoResource courseVideo =
          CourseVideoResource.fromJson(json.first);
      return courseVideo;
    }
    return null;
  }

  @override
  Future<CourseFileResource?> fetchResourceImageInfo(
      Map<String, dynamic> jsonReq) async {
    String orgId = ORG_ID;
    final response = await Supabase.instance.client
        .rpc(APIConfig.VIEW_RESOURCE_FILE, params: jsonReq);
    if (response != null) {
      List<dynamic> json = response;
      CourseFileResource resourseFile = CourseFileResource.fromJson(json.first);
      return resourseFile;
    }
    return null;
  }

  // get Course progress
  @override
  Future<ResourceProgress?> getCourseProgress(String instance) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_COURSE_PROGRESS_FN, params: {
      "instance_ids": [instance],
      "user_id": USER_ID,
      "course_id": COURSE_ID,
    });

    if (response != null) {
      List<dynamic> json = response;
      ResourceProgress courseProgress = ResourceProgress.fromJson(json.first);
      return courseProgress;
    } else {
      ResourceProgress courseProgress = ResourceProgress(
        progress: 0.00,
        timeSpent: "0.00",
        instanceId: instance,
        markedAsDone: false,
      );
      return courseProgress;
    }
  }

  @override
  Future<CheckPointData?> fetchVideoCheckPointInfo(String moduleId) async {
    String orgId = ORG_ID;
    final response = await Supabase.instance.client.rpc(
        APIConfig.GET_RESOURCE_CHECKPOINT_DATA,
        params: {"course_module_id": moduleId, "org_id": orgId});
    if (response != null) {
      CheckPointData checkPointData = CheckPointData.fromJson(response);
      return checkPointData;
    }
    return response;
  }
}
