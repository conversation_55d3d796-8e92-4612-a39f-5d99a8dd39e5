part of 'course_feedback_cubit.dart';

abstract class CourseFeedbackState extends Equatable {
  const CourseFeedbackState();

  @override
  List<Object> get props => [];
}

class CourseFeedbackInitial extends CourseFeedbackState {}

class CourseFeedbackUpdate extends CourseFeedbackState {
  final bool changeColor;
  final int uptoIndex;
  CourseFeedbackUpdate(this.changeColor, this.uptoIndex);

  @override
  List<Object> get props => [changeColor, uptoIndex];
}
