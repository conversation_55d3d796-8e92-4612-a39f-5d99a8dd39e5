import '../cubits/app_config/app_config_cubit.dart';
import '/src/utils/constants/helper.dart';
import 'package:flutter/gestures.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/models/subscription_plan/plan_course_result.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/empty_screen_view.dart';
import 'package:beamer/beamer.dart';

import '/src/presentation/widgets/alert_popup/confirmation_popup.dart';
import 'package:flutter/foundation.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/utils/constants/nums.dart';

import '/src/domain/models/subscription_plan/subscription_plan.dart';

import '/src/config/themes/lms_fonts.dart';
import '/src/presentation/cubits/subscription/subscription_cubit.dart';
import '/src/presentation/widgets/connectivity_widget.dart';
import '/src/presentation/widgets/loading_indicator.dart';
import '/src/utils/helper/random_color_generator.dart';
import 'package:auto_route/auto_route.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../utils/constants/strings.dart';
import '../widgets/app_bar.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

@RoutePage()
class SubscriptionViewScreen extends HookWidget with WidgetsBindingObserver {
  SubscriptionViewScreen({super.key});

  final subScriptionCardColors = RandomColorGenerator.subscriptionCardColor();

  final ScrollController _scrollController = ScrollController();
  final ItemScrollController _dotsScrollController = ItemScrollController();
  final CarouselController _carouselController = CarouselController();
  final TextEditingController _searchBarController = TextEditingController();

  final FocusNode _searchBarFocusNode = FocusNode();

  final ValueNotifier<List<SubscriptionPlan>> _searchResult =
      ValueNotifier<List<SubscriptionPlan>>([]);
  final ValueNotifier<bool> _isDataLoading = ValueNotifier(false);
  ValueNotifier<bool> showSelectBtn = ValueNotifier(false);
  ValueNotifier<String> selectedPlanId = ValueNotifier('');

  List<SubscriptionPlan> _subscriptionPlans = [];
  List<PlanCourse> _courseForSelectedPlan = [];

  bool didApprove = false;

  ValueNotifier<int> selectedIndex = ValueNotifier(0);

  BuildContext? currentContext;

  GlobalKey dataKey = GlobalKey();

  _checkPermission() async {
    String screen = PrivilegeAccessConsts.SCREEN_SUBSCRIPTION;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_USER_SUBSCRIPTION_LIST;
    bool hasAccessToSelectPlan =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    showSelectBtn.value = hasAccessToSelectPlan;
  }

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    // TO DO: context should not be equal to null
    didChangeAppLocale(locales, currentContext);
  }

  void _scrollToIndex(int index, GlobalKey key) async {
    _dotsScrollController.scrollTo(
        index: index, duration: const Duration(milliseconds: 100));
  }

  _handleCarousalIndexChange(SubscriptionCubit _subscriptionCubit, int index) {
    selectedIndex.value = index;
    _subscriptionCubit.onIndexChange(index);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final _subscriptionCubit = BlocProvider.of<SubscriptionCubit>(context);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      //_subscriptionPlans = _subscriptionCubit.subscriptionPlans;

      Future<void>.microtask(() async {
        // _subscriptionCubit.setLoading(true);
        await _checkPermission();
        // if (_subscriptionPlans.isEmpty) {
        _isDataLoading.value = true;

        await _subscriptionCubit.getSubscriptionPlans();

        _isDataLoading.value = false;
        //}  _subscriptionCubit.setLoading(false);

        _subscriptionPlans = _subscriptionCubit.subscriptionPlans;
        _searchResult.value = List.from(_subscriptionPlans);

        selectedIndex.value = _searchResult.value.length > 1 ? 1 : 0;
        selectedPlanId.value = _searchResult.value.isNotEmpty
            ? _searchResult.value[selectedIndex.value].id ?? ""
            : '';
      });

      return () {
        currentContext = null;
        WidgetsBinding.instance.removeObserver(this);
      };
    }, const []);

    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
            title: SLStrings.getTranslatedString(KEY_SUBSCRIPTION),
            trailingWidget: Container(),
            leadingIconName: BACK_ARROW,
            leadingBtnAction: () {
              kIsWeb ? Beamer.of(context).beamBack() : appRouter.popForced();
            },
          ),
        ),
        body: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          ),
          child: BlocBuilder<SubscriptionCubit, SubscriptionState>(
              builder: (BuildContext context, SubscriptionState state) {
            if (state is IsLoading) {
            } else if (state is SubscriptionsFetched) {
              _subscriptionPlans = _subscriptionCubit.subscriptionPlans;
              // _searchResult.value = _subscriptionPlans;
            } else if (state is SubscriptionApproved) {
              if (state.isApproved) {
                didApprove = true;
                selectedPlanId.value = state.selectedPlanId;
              }
            } else if (state is OnSubscriptionIndexChange) {
              selectedIndex.value = state.selectedIndex;
            }

            return LayoutBuilder(builder: (context, constraints) {
              return ValueListenableBuilder(
                  valueListenable: _isDataLoading,
                  builder: (context, __, _) {
                    return Stack(
                      children: [
                        Align(
                          child: Container(
                            height: constraints.maxHeight,
                            width: constraints.maxWidth < 720
                                ? constraints.maxWidth
                                : 800,
                            padding: const EdgeInsets.only(
                                left: 15, right: 15, top: 15),
                            child: ScrollConfiguration(
                              behavior:
                                  ScrollConfiguration.of(context).copyWith(
                                dragDevices: {
                                  PointerDeviceKind.touch,
                                  PointerDeviceKind.mouse,
                                },
                              ),
                              child: CustomScrollView(
                                controller: _scrollController,
                                slivers: [
                                  SliverFillRemaining(
                                    hasScrollBody: false,
                                    child: ValueListenableBuilder(
                                        valueListenable: _searchResult,
                                        builder: (context, _, __) {
                                          return Column(
                                            mainAxisSize: MainAxisSize.min,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              _searchBarWidget(
                                                  _subscriptionCubit),
                                              const SizedBox(height: 10),
                                              _emptyScreen(constraints),
                                              _titleWidget(),
                                              SizedBox(
                                                  height: _searchResult
                                                          .value.isEmpty
                                                      ? 0
                                                      : 16),
                                              _iconWidget(constraints),
                                              SizedBox(
                                                  height: _searchResult
                                                          .value.isEmpty
                                                      ? 0
                                                      : 16),
                                              _planNameInfoWidget(
                                                  context, _subscriptionCubit),
                                              SizedBox(
                                                  height: _searchResult
                                                          .value.isEmpty
                                                      ? 0
                                                      : 10),
                                              _descriptions(constraints),
                                              SizedBox(
                                                  height: _searchResult
                                                          .value.isEmpty
                                                      ? 0
                                                      : 18),
                                              _subscriptions(
                                                  context,
                                                  constraints,
                                                  _subscriptionCubit),
                                              SizedBox(
                                                  height: _searchResult
                                                          .value.isEmpty
                                                      ? 0
                                                      : 22),
                                            ],
                                          );
                                        }),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        ConnectivityStatusWidget(),
                        _isDataLoading.value
                            ? LoadingIndicatorClass()
                            : Container(),
                      ],
                    );
                  });
            });
          }),
        ),
      ),
    );
  }

  _searchBarWidget(SubscriptionCubit subsrciptionCubit) {
    return Visibility(
      visible: !_isDataLoading.value,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.searchBarBg,
          borderRadius: BorderRadius.circular(44.0),
        ),
        child: TextField(
          controller: _searchBarController,
          focusNode: _searchBarFocusNode,
          textInputAction: TextInputAction.search,
          decoration: InputDecoration(
            hintText: SLStrings.getTranslatedString(KEY_SERACH_HINT_FOR_PLAN),
            hintStyle:
                LMSFonts.regularFont(14, AppTheme.searchBarPlaceholderText),
            border: InputBorder.none,
            prefixIcon: Image.asset(
              '$ASSETS_PATH/search_icon.png',
              height: 24,
              width: 24,
            ),
            suffixIcon: GestureDetector(
              onTap: () {
                _searchBarController.clear();
                _searchResult.value = _subscriptionPlans;
              },
              child: const Icon(
                Icons.close,
                size: 24,
                color: AppTheme.primaryAppColor,
              ),
            ),
          ),
          onChanged: (val) => onQueryChanged(subsrciptionCubit, val),
        ),
      ),
    );
  }

  ///handle plan search
  void onQueryChanged(SubscriptionCubit _subscriptionCubit, String newQuery) {
    _searchResult.value = [];

    for (var plan in _subscriptionPlans) {
      if ((plan.name ?? '')
          .toLowerCase()
          .contains(newQuery.trim().toLowerCase())) {
        // if category exists
        // result found
        _searchResult.value.add(plan);
      } else {
        //no result
      }
    }

    if (_searchResult.value.isNotEmpty) {
      _scrollToIndex(0, dataKey);
      _carouselController.jumpToPage(0);
      _handleCarousalIndexChange(_subscriptionCubit, 0);
    }
  }

  Widget _titleWidget() {
    return Visibility(
      visible: _searchResult.value.isNotEmpty,
      child: Align(
        alignment: Alignment.topLeft,
        child: Text(SLStrings.getTranslatedString(KEY_UPGRADE_PREMIUM),
            style: LMSFonts.semiBoldFont(
              16,
              AppTheme.examIntroTextColor,
            )),
      ),
    );
  }

  Widget _iconWidget(BoxConstraints constraints) {
    return Visibility(
      visible: _searchResult.value.isNotEmpty,
      child: Image.asset(
        '$ASSETS_PATH/subscription-icon.png',
        height: constraints.maxWidth < 720 ? 200 : 300,
        width: constraints.maxWidth < 720 ? 180 : 230,
      ),
    );
  }

  Widget _planNameInfoWidget(
      BuildContext context, SubscriptionCubit _subscriptionCubit) {
    return ValueListenableBuilder(
        valueListenable: selectedPlanId,
        builder: (context, newId, _) {
          return Visibility(
            visible: _searchResult.value.isNotEmpty,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _planNameWidget(),
                _moreDetailsIcon(context, _subscriptionCubit),
              ],
            ),
          );
        });
  }

  Widget _planNameWidget() {
    String name = _searchResult.value.isNotEmpty &&
            selectedIndex.value < _searchResult.value.length
        ? (_searchResult.value[selectedIndex.value].name ?? '').trim()
        : '';
    return Expanded(
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          name,
          style: LMSFonts.semiBoldFont(18, AppTheme.primaryTextColorBlack),
        ),
      ),
    );
  }

  Widget _moreDetailsIcon(
      BuildContext context, SubscriptionCubit _subscriptionCubit) {
    return GestureDetector(
      onTap: () async {
        String id = _searchResult.value[selectedIndex.value].id ?? '';
        debugPrint('tapped item-----------${selectedPlanId.value}');
        _isDataLoading.value = true;
        _courseForSelectedPlan =
            await _subscriptionCubit.getCourseLinkedtoPlan(id);
        _isDataLoading.value = false;
        _courseForSelectedPlan.isNotEmpty
            ? appRouter.push(SubscriptionInfoViewRoute(
                planName: _searchResult.value[selectedIndex.value].name ?? '',
                planInfo: _courseForSelectedPlan))
            : _showResourcesListEmptyView(context);
      },
      child: const Icon(
        Icons.question_mark,
        size: 20,
        color: Colors.black,
      ),
    );
  }

  Widget _descriptions(BoxConstraints constraints) {
    String desc = _searchResult.value.isNotEmpty &&
            selectedIndex.value < _searchResult.value.length
        ? _searchResult.value[selectedIndex.value].description ?? ''
        : '';
    List<String> _descriptions = desc.isNotEmpty ? desc.split('. ') : [];

    return Visibility(
      visible: _searchResult.value.isNotEmpty,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          // color: AppTheme.skippedFillColor.withOpacity(0.7),
          color: AppTheme.descrptionBg,
        ),
        height: constraints.maxWidth < 720
            ? constraints.maxHeight / 5
            : constraints.maxHeight / 6,
        padding: const EdgeInsets.symmetric(
            horizontal: horizontalMargin, vertical: 5),
        child: RawScrollbar(
          thumbColor: Colors.white,
          thumbVisibility: true,
          controller: _scrollController,
          radius: const Radius.circular(20),
          mainAxisMargin: 10,
          thickness: 5,
          child: ListView.separated(
            shrinkWrap: true,
            primary: true,
            padding: const EdgeInsets.symmetric(
              vertical: 10,
            ),
            itemCount: _descriptions.length,
            itemBuilder: ((context, index) => Align(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _listViewLeadingWidget(),
                      const SizedBox(width: 10),
                      Flexible(
                        child: Text(
                          _descriptions[index].trim(),
                          style:
                              LMSFonts.mediumFont(12, AppTheme.whiteColor, 1),
                        ),
                      ),
                      const SizedBox(width: 10),
                    ],
                  ),
                )),
            separatorBuilder: (context, index) => Divider(
              indent: 30,
              endIndent: 11,
              thickness: 1,
              color: AppTheme.whiteColor.withOpacity(0.35),
            ),
          ),
        ),
      ),
    );
  }

  Widget _listViewLeadingWidget() {
    return Container(
      height: 20,
      width: 20,
      margin: const EdgeInsets.only(top: 3),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: AppTheme.whiteColor),
      ),
      child: Image.asset(
        '$ASSETS_PATH/done_button.png',
        color: AppTheme.whiteColor,
      ),
    );
  }

  Widget _subscriptions(BuildContext context, BoxConstraints constraints,
      SubscriptionCubit _subscriptionCubit) {
    return Visibility(
      visible: _searchResult.value.isNotEmpty,
      child: IntrinsicHeight(
          child: Column(
        children: [
          Expanded(
            child: _sliderWidget(context, constraints, _subscriptionCubit),
          ),
          const SizedBox(height: 15),
          _carouselDotsWidget(context, constraints, _subscriptionCubit),
          const SizedBox(height: 22),
          Text(
            SLStrings.getTranslatedString(KEY_SUBSCRIPTION_DESCRIPTION),
            textAlign: TextAlign.center,
            style: LMSFonts.regularFont(14, AppTheme.examIntroTextColor),
          )
        ],
      )),
    );
  }

  Widget _sliderWidget(BuildContext context, BoxConstraints constraints,
      SubscriptionCubit _subscriptionCubit) {
    return SizedBox(
      width: constraints.maxWidth,
      height: 128,
      child: CarouselSlider.builder(
        carouselController: _carouselController,
        itemCount: _searchResult.value.length,
        itemBuilder: (context, index, realIndex) {
          int colorIndex = index % subScriptionCardColors.length;
          return GestureDetector(
            onTap: () {
              _carouselController.animateToPage(index);
              _handleCarousalIndexChange(_subscriptionCubit, index);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
              decoration: BoxDecoration(
                  color: subScriptionCardColors[colorIndex]['bg'],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: subScriptionCardColors[colorIndex]['border'] ??
                        AppTheme.whiteColor,
                  )),
              width: !kIsWeb ? 170 : (800 / 3),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _searchResult.value[index].subscriptionFrequencyType.name,
                    style:
                        LMSFonts.mediumFont(13, AppTheme.examIntroTextColor, 1),
                  ),
                  const SizedBox(height: 5),
                  _amountWidget(index),
                  const SizedBox(height: 5),
                  _buttonWidget(context, _subscriptionCubit, index)
                ],
              ),
            ),
          );
        },
        options: CarouselOptions(
          initialPage: constraints.maxWidth < 720
              ? selectedIndex.value
              : _searchResult.value.length > 3
                  ? 2
                  : selectedIndex.value,
          enableInfiniteScroll: false,
          viewportFraction: !kIsWeb ? 0.43 : 0.3,
          // padEnds: false,
          enlargeCenterPage: true,
          onPageChanged: (index, reason) {
            _scrollToIndex(index, dataKey);
            _handleCarousalIndexChange(_subscriptionCubit, index);
          },
        ),
      ),
    );
  }

  Widget _amountWidget(int index) {
    bool isInr = _searchResult.value[index].currency != null &&
        (_searchResult.value[index].currency == Currency.CURRENCY_INR ||
            _searchResult.value[index].currency == Currency.INR);
    return Flexible(
      child: RichText(
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        text: TextSpan(
          children: [
            WidgetSpan(
              child: Transform.translate(
                offset: const Offset(0.0, -12.2),
                child: Text(
                  isInr ? '₹ ' : '\$ ',
                  style: LMSFonts.semiBoldFontForSymbol(
                      12, AppTheme.examIntroTextColor),
                ),
              ),
            ),
            TextSpan(
                text: _searchResult.value[index].price.toString(),
                style: LMSFonts.semiBoldFont(26, AppTheme.examIntroTextColor)),
          ],
        ),
      ),
    );
  }

  Widget _buttonWidget(
      BuildContext context, SubscriptionCubit _subscriptionCubit, int index) {
    bool isApproved = _searchResult.value[index].userSubscriptionStatus ==
        PlanStatusEnum.APPROVED;
    bool isPlanSubscribed = _searchResult.value[index].isUserSubscribed;
    bool isHighLevelUser = _searchResult.value[index].isHighLevelUser;
    return ValueListenableBuilder(
        valueListenable: showSelectBtn,
        builder: (context, __, _) {
          return Visibility(
            visible: !isHighLevelUser,
            child: InkWell(
              onTap: isPlanSubscribed || isApproved
                  ? null
                  : () {
                      _showSubscriptionConfirmation(
                          context, _subscriptionCubit, index);
                    },
              child: Container(
                height: 25,
                padding:
                    const EdgeInsets.symmetric(horizontal: 14, vertical: 2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: isApproved
                      ? AppTheme.examDateGreen
                      : isPlanSubscribed
                          ? AppTheme.primaryOrange
                          : AppTheme.iconEnabledColor,
                ),
                child: Text(
                  isApproved
                      ? SLStrings.getTranslatedString(KEY_APPROVED)
                      : isPlanSubscribed
                          ? SLStrings.getTranslatedString(KEY_PENDING)
                          : SLStrings.getTranslatedString(KEY_SELECT),
                  style: LMSFonts.semiBoldFont(12, AppTheme.whiteColor),
                ),
              ),
            ),
          );
        });
  }

  _showSubscriptionConfirmation(BuildContext mainContext,
      SubscriptionCubit _subscriptionCubit, int index) {
    showDialog(
      context: mainContext,
      barrierDismissible: false,
      builder: (context) {
        return MultiActionDialogue(
          alertType: AlertType.confirm,
          title: SLStrings.getTranslatedString(KEY_PLAN_CONFIRMATION_TITLE),
          content: SLStrings.getTranslatedString(KEY_PLAN_CONFIRMATION_CONTENT),
          cancelBtnText: SLStrings.getTranslatedString(KEY_NO),
          onCancelTap: () {
            kIsWeb ? Navigator.of(context).pop() : appRouter.popForced();
          },
          onContinueTap: () async {
            Navigator.pop(context);
            await _subscribeSelectedPlan(
                mainContext, _subscriptionCubit, index);
          },
          confirmBtnText: SLStrings.getTranslatedString(KEY_YES),
        );
      },
    );
  }

  Future<void> _subscribeSelectedPlan(BuildContext context,
      SubscriptionCubit subscriptionCubit, int index) async {
    debugPrint("object => ${_searchResult.value[index].price}");

    _isDataLoading.value = true;
    final planId = _searchResult.value[index].id ?? '';
    selectedPlanId.value = planId;

    await subscriptionCubit.submitSelectedPlan(planId);

    final state = subscriptionCubit.state;
    final isSuccess =
        state is SubscriptionApproved && state.selectedPlanId == planId;

    // Set activity log
    await _setActivityLog(context, planId, isSuccess, state);

    if (isSuccess) {
      _searchResult.value[index].isUserSubscribed = true;
      await subscriptionCubit.updatePlansList(planId, state.isApproved);

      if (checkForActiveRoute(context,
          webCurrentRouteName: '/subscription-view',
          mobileCurrentRouteName: SubscriptionViewRoute.name)) {
        _showSubscribedPopup(context);
      }
    } else if (state is SubscriptionsFailure) {
      if (ModalRoute.of(context)?.isCurrent == true) {
        showPGExceptionPopup(context, state.error);
      }
    }

    _isDataLoading.value = false;
  }

  Future<void> _setActivityLog(BuildContext context, String planId,
      bool isSuccess, SubscriptionState state) async {
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    final logResult = isSuccess ? 'success' : 'error';
    final actionDetails = isSuccess
        ? 'Plan subscribed'
        : state is SubscriptionsFailure
            ? state.error
            : 'Plan subscription failure';
    final actionComment = isSuccess
        ? 'Subscription successful at ${DateTime.now()}'
        : 'Subscription failed at ${DateTime.now()}';

    final logPayload = {
      'activity_type': 'Subscription',
      'screen_name': 'Subscription View',
      'action_details': actionDetails,
      'target_id': planId,
      'action_comment': actionComment,
      'log_result': logResult,
    };

    await _appConfigCubit.setUserActivityLog(logPayload);
  }

  Widget _carouselDotsWidget(BuildContext context, BoxConstraints constraints,
      SubscriptionCubit _subscriptionCubit) {
    return SizedBox(
      height: 10,
      child: ScrollablePositionedList.builder(
        scrollDirection: Axis.horizontal,
        itemScrollController: _dotsScrollController,
        itemCount: _searchResult.value.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return InkWell(
            onTap: () {
              _scrollToIndex(index, dataKey);
              _carouselController.jumpToPage(index);
              _handleCarousalIndexChange(_subscriptionCubit, index);
            },
            child: Container(
              width: 10,
              margin: const EdgeInsets.only(right: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: selectedIndex.value == index
                    ? AppTheme.onBoardingSelectedIndex
                    : AppTheme.carousalUnSelectedDotColor,
              ),
            ),
            // child: CircleAvatar(
            //   radius: 5,
            //   backgroundColor: selectedIndex.value == index
            //       ? AppTheme.onBoardingSelectedIndex
            //       : AppTheme.carousalUnSelectedDotColor,
            // )
          );
        },
      ),
    );
  }

  // _savePlanId(String planId) async {
  //   SharedPreferences _prefs = await SharedPreferences.getInstance();
  //   _subscribedPlans =
  //       _prefs.getStringList(PREF_KEY_SUBSCRIBED_PLANS_LIST) ?? [];
  //   if (_subscribedPlans.contains(planId)) {
  //   } else {
  //     _subscribedPlans.add(planId);
  //   }
  //   _prefs.setStringList(PREF_KEY_SUBSCRIBED_PLANS_LIST, _subscribedPlans);
  // }

  _showSubscribedPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SUCCESS),
          message: SLStrings.getTranslatedString(KEY_SUBSCRIPTION_ADDED),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: false,
          handleOkCallback: () {
            if (kIsWeb) {
              Navigator.pop(context);
            }
            kIsWeb
                ? Beamer.of(context).beamToReplacementNamed('/profile-review')
                : appRouter.replace(ProfileReviewRoute());
          },
        );
      },
    );
  }

  Widget _emptyScreen(BoxConstraints constraints) {
    return Visibility(
      visible: _searchResult.value.isEmpty && !_isDataLoading.value,
      child: SizedBox(
          height: constraints.maxHeight,
          child: EmptyScreenView(
              title: SLStrings.getTranslatedString(KEY_PLAN_EMPTY))),
    );
  }

  _showResourcesListEmptyView(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: SLStrings.getTranslatedString(KEY_RESOURCE_EMPTY),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: false,
          handleOkCallback: () {
            Navigator.pop(context);
          },
        );
      },
    );
  }

  _showPlanCourseList(BuildContext mainContext) {
    showDialog(
      context: mainContext,
      builder: (context) {
        return Material(
          type: MaterialType.transparency,
          child: LayoutBuilder(builder: (context, constraints) {
            double webWidth = 379.6;
            // constraints.maxWidth / 5;
            double mobileWidth = constraints.maxWidth;
            double allowedWidth = constraints.maxWidth > 1200
                ? webWidth
                : constraints.maxWidth >= 500
                    ? webWidth
                    : mobileWidth;
            return Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  // width: allowedWidth,
                  // constraints: BoxConstraints(maxHeight: 400, minHeight: 100),
                  margin: const EdgeInsets.symmetric(horizontal: 22),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20.0),
                      color: Colors.white),
                  child: _planCourseList(),
                ),
              ],
            );
          }),
        );
      },
    );
  }

  Widget _planCourseList() {
    // return Column(
    //   children: List.generate(
    //       _courseForSelectedPlan.length,
    //       (planCourseIndex) =>
    //           Text(_courseForSelectedPlan[planCourseIndex].courseName ?? '')),
    // );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text('The course included in this plan,'),
        const SizedBox(height: 10),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 15),
              child: Image.asset(
                '$ASSETS_PATH/course.png',
                height: 25,
                width: 25,
                color: AppTheme.sidemenuBGColor,
              ),
            ),
            const SizedBox(width: 5),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Text(
                  (_courseForSelectedPlan[0].courseName ?? ''),
                  style: LMSFonts.boldFont(14, AppTheme.sidemenuBGColor),
                ),
              ),
            )
          ],
        ),
        SizedBox(
          height: 200,
          child: ListView.builder(
              itemCount: _courseForSelectedPlan.length,
              itemBuilder: (BuildContext context, int planCourseIndex) {
                return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Theme(
                        data: Theme.of(context)
                            .copyWith(dividerColor: Colors.transparent),
                        child: ExpansionTile(
                          expandedCrossAxisAlignment: CrossAxisAlignment.start,
                          onExpansionChanged: (value) {},
                          trailing: const Icon(Icons.arrow_drop_down_outlined,
                              size: 30, color: AppTheme.primaryOrange),
                          title: Row(
                            children: [
                              Image.asset(
                                '$ASSETS_PATH/file_light.png',
                                height: 25,
                                width: 25,
                                color: AppTheme.primaryOrange,
                              ),
                              const SizedBox(width: 5),
                              Text(SLStrings.getTranslatedString(KEY_RESOURCES),
                                  style: const TextStyle(
                                      fontSize: 14,
                                      color: AppTheme.primaryOrange)),
                            ],
                          ),
                          children: [_planResourceList(planCourseIndex)],
                        ),
                      ),
                      _courseForSelectedPlan[planCourseIndex].intro != null &&
                              _courseForSelectedPlan[planCourseIndex]
                                  .intro!
                                  .isNotEmpty
                          ? Theme(
                              data: Theme.of(context)
                                  .copyWith(dividerColor: Colors.transparent),
                              child: ExpansionTile(
                                onExpansionChanged: (value) {},
                                trailing: const Icon(
                                    Icons.arrow_drop_down_outlined,
                                    size: 30,
                                    color: AppTheme.primaryOrange),
                                title: Row(
                                  children: [
                                    Image.asset(
                                      '$ASSETS_PATH/video_light.png',
                                      height: 25,
                                      width: 25,
                                      color: AppTheme.primaryOrange,
                                    ),
                                    const SizedBox(width: 5),
                                    Text(
                                        SLStrings.getTranslatedString(
                                            KEY_VIDEOS),
                                        style: const TextStyle(
                                            fontSize: 14,
                                            color: AppTheme.primaryOrange)),
                                  ],
                                ),
                                children: [
                                  _availableVideoList(planCourseIndex)
                                ],
                              ),
                            )
                          : Container()
                    ]);
              }),
        ),
      ],
    );
  }

  Widget _planResourceList(int planCourseIndex) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(
        (_courseForSelectedPlan[planCourseIndex].resources ?? []).length,
        (planResourceIndex) => Padding(
          padding: const EdgeInsets.only(left: 20),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Image.asset(
                '$ASSETS_PATH/item.png',
                height: 25,
                width: 25,
              ),
              const SizedBox(width: 5),
              Expanded(
                child: Text(
                  (_courseForSelectedPlan[planCourseIndex]
                          .resources![planResourceIndex]
                          .name ??
                      ''),
                  style: LMSFonts.regularFont(14, AppTheme.headerTextColor),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _availableVideoList(int planCourseIndex) {
    return Padding(
        padding: const EdgeInsets.only(left: 0),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(
                (_courseForSelectedPlan[planCourseIndex].intro ?? []).length,
                (planResourceIndex) => Padding(
                    padding: const EdgeInsets.only(left: 20),
                    child: Row(
                      children: [
                        Image.asset(
                          '$ASSETS_PATH/item.png',
                          height: 25,
                          width: 25,
                        ),
                        Expanded(
                            child: Text(
                          (_courseForSelectedPlan[planCourseIndex]
                                  .intro![planResourceIndex]
                                  .intro_name ??
                              ''),
                          style: LMSFonts.regularFont(
                              14, AppTheme.headerTextColor),
                          textAlign: TextAlign.left,
                        ))
                      ],
                    )))));
  }
}
