// ignore_for_file: non_constant_identifier_names

import '../../utils/constants/strings.dart';

///
/// API Config URLs
///
///
// ignore_for_file: constant_identifier_names

class APIConfig {
// Default value for supabase dev env
  static const DEFAULT_VALUE_DEV =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtmcHdzenh1aXZ5ZGpmdGlreHRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODA1ODM3ODEsImV4cCI6MTk5NjE1OTc4MX0.YSTLSPdTd9PCJbI8z803SR0gAsAfsUdLwmVWiVz6I0o';

// Default value for supabase stage env
  static const DEFAULT_VALUE_STAGE =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBucG5yd3R4Zm5vZmR4cGVwbHZ3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTE5OTYzMzIsImV4cCI6MjAwNzU3MjMzMn0.ojhCvsxEbcLETqdnkUnjn0I5xpMG_qdoSSf_4WMhokA';

// FOR DEV ENVIRONMENT
  static const String SUPABASE_URL_DEV = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: 'https://kfpwszxuivydjftikxts.supabase.co',
  );
  static const String SUPABASE_ANONN_KEY_DEV = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: DEFAULT_VALUE_DEV,
  );

  // FOR STAGING ENV
  static const String SUPABASE_URL_STAGE = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: 'https://pnpnrwtxfnofdxpeplvw.supabase.co',
  );
  static const String SUPABASE_ANONN_KEY_STAGE = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: DEFAULT_VALUE_STAGE,
  );

  // Assign the keys as per Env
  static const SUPABASE_URL = SUPABASE_URL_DEV;
  static const SUPABASE_ANONN_KEY = SUPABASE_ANONN_KEY_DEV;

  // env
  static const BRANCH_ENV_DEV = 'dev';
  static const BRANCH_ENV_STAGE = 'stage';

  // email redirect url
  static const ROUTER_DOMAIN = 'http://10.10.12.237/app/smartlearn-admin';

  static const EMAIL_REDIRECT_URL =
      '$ROUTER_DOMAIN/welcome-64e7264ae8fade72537b4696/edit?branch=$BRANCH_ENV_DEV';

  // reset password email redirect url
  // TO DO

  ///
  /// User repository
  ///

  static const String V_PERSON = 'v_person';
  static String PROFILE_STORAGE = '$ORG_ID/avatars/$USER_ID';
  static const String UPDATE_PROFILE_FN = 'update_profile';

  ///
  /// Topic repository
  ///

  static const String V_CATEGORY = 'v_category';

  ///
  /// Course repository
  ///

  static const String V_COURSE = 'v_course';
  static const String V_COURSE_DETAILS = 'v_course_details';
  static const String V_BULLETIN_BOARD =
      'v_view_bulletin_board'; // to fetch more current affairs
  static const String GET_COMMENTS_FN = 'get_comments_of_instance';
  static const String ADD_COMMENTS_FN = 'add_comment';
  static const String SET_COURSE_PROGRESS_FN = 'set_course_progress';

  ///
  /// Org repository
  ///

  static const String V_USER_ORGS = 'v_user_orgs';

  ///
  /// Section repository
  ///

  static const String GET_SECTION_DETAILS_FN = 'get_section_details';

  static const String VIEW_RESOURCE_PAGE = 'view_resource_page';
  static const String VIEW_RESOURCE_URL = 'view_resource_url';
  static const String VIEW_RESOURCE_FILE = 'view_resource_file';
  static const String GET_COURSE_PROGRESS_FN = 'get_course_progress';
  static const String GET_RESOURCE_CHECKPOINT_DATA =
      'get_check_point_by_course_module_id';

  ///
  /// Exam repository
  ///

  static const String GET_QUIZ_LIST_FN = 'get_quizes_of_course';
  static const String GET_ATTEMPTED_QUIZ_LIST_FN =
      'get_attempted_quizes_of_user';
  static const String GET_QUESTIONS_OF_QUIZ_FN = 'get_questions_of_quiz';
  static const String START_QUIZ_FN = 'start_quiz';
  static const String SUBMIT_QUIZ_FN = 'submit_quiz_answers';
  static const String V_FETCH_REVIEW_LIST = 'v_submit_answers';
  static const String CALCULATE_RESULT_FN = 'calculate_quiz_grades';
  static const String FETCH_RESULTS_FN = 'get_quiz_right_answers';
  static const String FETCH_CHECKPOINT_QUIZ = 'start_checkpoint_quiz';
  static const String END_CHECKPOINT_QUIZ = 'end_checkpoint_quiz';

  ///
  /// Rank repository
  ///

  static const String FETCH_RANK_LIST_FOR_COURSE = 'fetch_rank_list_for_course';
  static const String FETCH_RANK_LIST_FOR_EXAM = 'fetch_rank_list_for_quiz';

  ///
  /// Push Notification
  ///
  static const String INSERT_FCM_TOKEN = 'insert_push_notification';

  ///
  /// Device info
  ///
  static const String UPLOAD_DEVICE_INFO = 'insert_device_info';

  ///
  /// Privilege access list
  ///
  static const String GET_PRIVILEGE_ACCESS_LIST = 'get_user_role_privilege';

  ///
  /// Subscription
  ///
  static const String V_SUBSCRIPTION_PLAN_LIST =
      'v_subscription_plan'; // fetch all plans available to an org
  static const String GET_SUBSCRIPTION_PLAN_LIST =
      'get_subscription_plans_for_user'; // fetch plans specific to org and user
  static const String SUBMIT_SUBSCRIPTION_PLAN =
      'add_subscription_plan_for_user';
  static const String GET_PLAN_STATUS = 'get_user_purchase_plan_status';
  static const String GET_COURSE_FOR_PLAN = 'get_course_resource_list_by_plan';
}
