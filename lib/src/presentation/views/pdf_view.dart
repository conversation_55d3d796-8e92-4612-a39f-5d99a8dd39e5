import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';

import '../../config/enums/resource_activity_status.dart';
import '../../config/themes/app_dynamic_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../domain/models/resource_comment.dart';
import '../../domain/models/sl_user.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/nums.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../../utils/resources/user_activity_res.dart';
import '../cubits/app_config/app_config_cubit.dart';
import '../cubits/connectivity/connectivity_cubit.dart';
import '../cubits/connectivity/connectivity_state.dart';
import '../cubits/course_list/course_list_cubit.dart';
import '../widgets/alert_popup/confirmation_popup.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/profile_placeholder_widget.dart';
import '/src/config/themes/app_theme.dart';

import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';

import '../widgets/app_bar.dart';
import 'course_video_view.dart';
import 'ppt_viewer.dart';

@RoutePage()
class PDFViewScreen extends HookWidget with WidgetsBindingObserver {
  final CourseFileResource courseFileResource;
  final List<CheckPoint> checkPoints;
  final bool isFromExamScreen;
  final bool isExamPassed;
  final NavigationTo navigateBackTo;

  PDFViewScreen({
    super.key,
    required this.courseFileResource,
    required this.checkPoints,
    required this.isFromExamScreen,
    required this.isExamPassed,
    required this.navigateBackTo,
  });

  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  late TabController _tabController;
  final ConfettiController _confettiController =
      ConfettiController(duration: const Duration(milliseconds: 700));

  final ValueNotifier<bool> _isDataLoading = ValueNotifier<bool>(false);
  final ValueNotifier<AddCommentFunc> _commentNotifier = ValueNotifier(
      AddCommentFunc(enableSubmitBtn: false, selectedCommentType: 'Feedback'));
  final ValueNotifier<int> tabIndex = ValueNotifier<int>(0);
  ValueNotifier<bool> readMore = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _isLiked = ValueNotifier<bool>(false);
  final ValueNotifier<int> _likes = ValueNotifier<int>(0);
  final ValueNotifier<double> _likeScale = ValueNotifier<double>(1.0);

  List<ResourseComment> _resourceComments = [];
  List<ResourseComment> _resourceLikes = [];

  final AppDynamicTheme? _appDynamicTheme =
      null; // Set this as per your theme logic
  bool _isCompleted = false;

  @override
  Widget build(BuildContext context) {
    final _courseCubit = BlocProvider.of<CourseListCubit>(context);
    _tabController =
        useTabController(initialLength: 2, initialIndex: tabIndex.value);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);

      _commentController.addListener(() {
        _commentNotifier.value = _commentNotifier.value
            .copyWith(enableSubmitBtn: _commentController.text.isNotEmpty);
      });
      Future<void>.microtask(() async {
        await _fetchComments(context, _courseCubit);
        await _setResourceActivityLog(context,
            status: ResourceActivityStatus.started);
      });

      return () {
        _commentController.removeListener(() {});
        WidgetsBinding.instance.removeObserver(this);
      };
    }, const []);
    return Scaffold(
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
            title: COURSE_NAME,
            trailingWidget: Container(),
            leadingIconName: BACK_ARROW,
            leadingBtnAction: () async {
              await _setResourceActivityLog(context,
                  status: ResourceActivityStatus.started);
              kIsWeb ? Beamer.of(context).beamBack() : appRouter.popForced();
            },
          )),
      body: BlocBuilder<CourseListCubit, CourseListState>(
          builder: (context1, state) {
        if (state is CourseCommentsFetched) {
          _isDataLoading.value = false;
          _resourceComments = state.comments;
        } else if (state is CommentsAdded) {
          _isDataLoading.value = false;
          _resourceComments = state.comments;
        }
        return ValueListenableBuilder(
          valueListenable: _isDataLoading,
          builder: (context, valueNotifierAttributeValue, child) {
            int commentCount = _resourceComments
                .where((element) =>
                    element.resActivityTpe == ResActivityType.comment)
                .toList()
                .length;
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              clipBehavior: Clip.antiAlias,
              child: Stack(
                children: [
                  Positioned(
                    top: 0,
                    right: 0,
                    left: 0,
                    bottom: 0,
                    child: CustomScrollView(
                      primary: true,
                      slivers: [
                        SliverFillRemaining(
                          hasScrollBody: false,
                          child: Container(
                            margin: const EdgeInsets.only(
                                left: horizontalMargin,
                                right: horizontalMargin,
                                bottom: horizontalMargin),
                            child: Column(
                              children: [
                                const SizedBox(height: 16),
                                _resourceTitle(),
                                const SizedBox(height: 8),
                                Container(
                                  height: MediaQuery.sizeOf(context).height / 2,
                                  decoration: BoxDecoration(
                                    // color: Colors.amber,
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                        width: 2, color: Colors.black),
                                  ),
                                  child: PDFView(
                                    filePath: courseFileResource.url,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                _resourceDesc(context),
                                const SizedBox(height: 5),
                                UserEngagementRow(
                                  resourceId: courseFileResource.id,
                                  isLiked: _isLiked,
                                  likes: _likes,
                                  likeScale: _likeScale,
                                  confettiController: _confettiController,
                                  appDynamicTheme: _appDynamicTheme,
                                  commentCount: commentCount,
                                  enableLike: !_isCompleted,
                                  onCommentTapped: () {},
                                ),
                                const SizedBox(height: 5),
                                _commentsHead(),
                                SizedBox(
                                    height: _resourceComments.isEmpty ? 0 : 10),
                                Flexible(
                                  child: _chatListView(context, _courseCubit),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (_isDataLoading.value)
                    Center(
                      child: LoadingIndicatorClass(),
                    ),
                  _commentBtn(context, _courseCubit)
                ],
              ),
            );
          },
        );
      }),
    );
  }

  _resourceTitle() {
    return Container(
      alignment: Alignment.centerLeft,
      child: Text(
        courseFileResource.name.trim(),
        textAlign: TextAlign.left,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
      ),
    );
  }

  _resourceDesc(BuildContext context) {
    return Visibility(
      visible: courseFileResource.description.trim().isNotEmpty,
      child: Container(
          alignment: Alignment.centerLeft,
          child: ValueListenableBuilder(
            valueListenable: readMore,
            builder: (context, value, child) {
              final text = courseFileResource.description.trim();
              final textPainter = TextPainter(
                textDirection: TextDirection.ltr,
                text: TextSpan(
                  text: text,
                  style: LMSFonts.regularFontWithHeight(
                    14,
                    AppTheme.courseVideoPrimaryTextColor,
                    1.5,
                  ),
                ),
                maxLines: 4,
              )..layout(maxWidth: MediaQuery.sizeOf(context).width);

              bool isOverflowing = textPainter.didExceedMaxLines;

              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    text,
                    maxLines: 4,
                    overflow: TextOverflow.ellipsis,
                    style: LMSFonts.regularFontWithHeight(
                      14,
                      AppTheme.courseVideoPrimaryTextColor,
                      1.5,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Align(
                    alignment: Alignment.topRight,
                    child: Visibility(
                        visible: isOverflowing,
                        child: InkWell(
                            onTap: () {
                              readMore.value = !readMore.value;
                            },
                            child: Text(
                              value ? "View less" : "View more",
                              style: LMSFonts.regularFontWithHeight(
                                  10, AppTheme.nextBtnColor, 1),
                            ))),
                  )
                ],
              );
            },
          )),
    );
  }

  Future<void> _setResourceActivityLog(BuildContext context,
      {required ResourceActivityStatus status}) async {
    final _userActivityLog = UserActivityLog.instance;
    await _userActivityLog.setResourceActivityLog(
        context: context,
        resourceType: 'PDF',
        status: status,
        currentDuration: 0.0,
        id: courseFileResource.id,
        result: 'success');
  }

  ///
  /// comments
  ///
  ///
  Widget _commentsHead() {
    return Visibility(
      visible: _resourceComments.isNotEmpty,
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          SLStrings.getTranslatedString(KEY_VIDEO_COMMENTS),
          style:
              LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
        ),
      ),
    );
  }

  Widget _commentsTabItem(String label, bool isActive) {
    return Container(
        height: 35,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isActive ? Colors.white : Colors.transparent,
        ),
        margin: const EdgeInsets.symmetric(vertical: 2),
        child: Tab(text: label));
  }

  Widget _chatListView(BuildContext mainContext, CourseListCubit courseCubit) {
    int feedbackCount = _resourceComments
        .where((element) => element.commentType == CommentType.FEEDBACK)
        .toList()
        .length;

    int suggestionCount = _resourceComments
        .where((element) => element.commentType == CommentType.SUGGESTION)
        .toList()
        .length;
    return Visibility(
      visible: courseFileResource.id.isNotEmpty && _resourceComments.isNotEmpty,
      child: ValueListenableBuilder<int>(
        valueListenable: tabIndex,
        builder: (context, updatedTabIndex, __) {
          return DefaultTabController(
            length: 2,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade300,
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: AppTheme.courseVideoPrimaryTextColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: AppTheme.transparentColor,
                    padding: EdgeInsets.zero,
                    tabs: [
                      _commentsTabItem(
                          'Feedback ($feedbackCount)', tabIndex.value == 0),
                      _commentsTabItem(
                          'Suggestion ($suggestionCount)', tabIndex.value == 1),
                    ],
                    onTap: (index) {
                      tabIndex.value = index;
                      _commentNotifier.value = _commentNotifier.value.copyWith(
                        selectedCommentType:
                            index == 0 ? 'Feedback' : 'Suggestion',
                      );
                    },
                  ),
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: IndexedStack(
                    index: updatedTabIndex,
                    children: [
                      _buildCommentList(
                        mainContext,
                        courseCubit,
                        _resourceComments
                            .where((comment) =>
                                comment.commentType == CommentType.FEEDBACK)
                            .toList(),
                      ),
                      _buildCommentList(
                        mainContext,
                        courseCubit,
                        _resourceComments
                            .where((comment) =>
                                comment.commentType == CommentType.SUGGESTION)
                            .toList(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCommentList(BuildContext mainContext,
      CourseListCubit courseCubit, List<ResourseComment> comments) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(comments.length, (index) {
        final comment = comments[index];
        return Column(
          children: [
            _chatItem(mainContext, courseCubit, comment),
            if (comment.children != null)
              Padding(
                padding: const EdgeInsets.only(left: 34),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(
                    comment.children!.length,
                    (childIndex) => _chatItem(mainContext, courseCubit,
                        comment.children![childIndex]),
                  ),
                ),
              ),
          ],
        );
      }),
    );
  }

  Widget _chatItem(BuildContext mainContext, CourseListCubit courseCubit,
      ResourseComment commentObj) {
    print("commentObj.profilePic------>${commentObj.profilePic}");
    return Card(
      color: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          commentObj.profilePic != null
              ? _profileIcon(commentObj.profilePic)
              : const SizedBox.shrink(),
          const SizedBox(width: 14),
          _userCommentInfo(mainContext, courseCubit, commentObj),
          const SizedBox(width: 10),
          _commentTypeDateInfo(commentObj),
          const SizedBox(width: 5),
        ],
      ),
    );
  }

  Widget _profileIcon(String avatar) {
    return Container(
      height: 40,
      width: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          width: 2,
          color: AppTheme.whiteColor,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.blackColor.withOpacity(0.1),
            blurRadius: 1,
            spreadRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: CircleAvatar(
        backgroundColor: AppTheme.transparentColor,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(5.0),
          child: avatar != null && avatar.trim().isNotEmpty
              ? CachedNetworkImage(
                  imageUrl: avatar,
                  fit: BoxFit.cover,
                  placeholder: (context, url) =>
                      const CircularProgressIndicator(),
                  errorWidget: (context, url, error) => _placeHolderWidget(),
                )
              : _placeHolderWidget(),
        ),
      ),
    );
  }

  Widget _placeHolderWidget() {
    return const ProfilePlaceholderWidget(maxWidth: 44 * 2);
  }

  Widget _userCommentInfo(BuildContext mainContext, CourseListCubit courseCubit,
      ResourseComment commentObj) {
    return Expanded(
      child: Container(
        color: Colors.transparent,
        child: Align(
          alignment: Alignment.topLeft,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // const SizedBox(height: 2),
              Text(
                commentObj.user,
                style: LMSFonts.mediumFont(
                    14, AppTheme.courseVideoPrimaryTextColor, 1.2),
              ),
              const SizedBox(height: 4),
              Text(
                commentObj.message.toString().trim(),
                style: LMSFonts.regularFontWithHeight(
                    12, AppTheme.courseVideoPrimaryTextColor, 1.2),
              ),
              const SizedBox(height: 5),
              commentObj.roleName == RoleName.ADMIN
                  ? GestureDetector(
                      onTap: () {
                        //reply action

                        _showCommentBox(mainContext, courseCubit,
                            parentId: commentObj.parentId,
                            roleName: commentObj.roleName);
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Row(
                          children: [
                            const Icon(
                              Icons.reply,
                              color: AppTheme.coursePecentageTxtGrey,
                              size: 16,
                            ),
                            Text(
                              ' Reply',
                              style: LMSFonts.regularFontWithHeight(
                                  12, AppTheme.coursePecentageTxtGrey, 1.2),
                            ),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
              SizedBox(height: commentObj.commentedTime != null ? 5 : 0),
            ],
          ),
        ),
      ),
    );
  }

  Widget _commentTypeDateInfo(ResourseComment commentObj) {
    return Container(
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const SizedBox(height: 2),
          commentStatusIcon(commentObj.commentStatus),
          const SizedBox(height: 7),
          Text(
            commentObj.commentedTime != null
                ? AppDateFormatter().formatDatedmmmmy(commentObj.commentedTime!)
                : '',
            style: LMSFonts.regularFontWithHeight(
                12, AppTheme.courseCommentDate, 1.2),
          ),
        ],
      ),
    );
  }

  Widget commentStatusIcon(CommentStatus commentStatus) {
    IconData iconData;
    Color iconColor;
    print("commentStatus.name: ${commentStatus.name}");

    switch (commentStatus.name.toLowerCase()) {
      case 'approved':
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case 'pending':
        iconData = Icons.hourglass_empty;
        iconColor = Colors.orange;
        break;
      case 'rejected':
        iconData = Icons.cancel;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.help_outline;
        iconColor = Colors.grey;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 20,
    );
  }

  Widget _commentBtn(BuildContext context, CourseListCubit courseCubit) {
    return Positioned(
      bottom: 8,
      right: 8,
      child: BlocBuilder<ConnectivityCubit, ConnectivityState>(
        builder: (context, state) {
          return IconButton(
              iconSize: 50,
              icon: Image.asset('$ASSETS_PATH/comment_icon.png'),
              onPressed: state is! InternetConnected
                  ? null
                  : () async {
                      _commentController.clear();
                      // disableClickOnPlayer();
                      _showCommentBox(context, courseCubit,
                          parentId: null, roleName: null);
                    });
        },
      ),
    );
  }

  _showCommentBox(BuildContext mainContext, CourseListCubit courseCubit,
      {required String? parentId, required RoleName? roleName}) {
    showDialog(
        context: mainContext,
        barrierDismissible: false,
        builder: (BuildContext dContext) {
          return BlocBuilder<CourseListCubit, CourseListState>(
            builder: (context, state) {
              // if (state is CommentTypeSelected) {
              //   commentType = state.commentType;
              // }
              return ValueListenableBuilder<AddCommentFunc>(
                  valueListenable: _commentNotifier,
                  builder: (context, commentNotifierObj, _) {
                    return AddCommentDialouge(
                      commentController: _commentController,
                      courseCubit: courseCubit,
                      selectedType: _commentNotifier
                              .value.selectedCommentType.isEmpty
                          ? (tabIndex.value == 0 ? 'Feedback' : 'Suggestion')
                          : commentNotifierObj.selectedCommentType,
                      enableSubmitBtn: commentNotifierObj.enableSubmitBtn,
                      enableDropdown:
                          parentId == null, // the comment is not a reply
                      onSubmit: () async {
                        if (commentNotifierObj.enableSubmitBtn) {
                          _commentNotifier.value = commentNotifierObj.copyWith(
                            enableSubmitBtn: false,
                          );

                          await _addNewComment(
                            mainContext,
                            courseCubit,
                            _commentController.text.trim(),
                            parentId: parentId,
                            roleName: roleName,
                          );
                          _commentController.clear();
                        }
                      },
                      onTypeSelected: (val) {
                        _commentNotifier.value = commentNotifierObj.copyWith(
                          selectedCommentType: val,
                        );
                        // courseCubit.handleCommentTypeSelection(val);
                        // courseCubit.emit(
                        //     CommentTypeSelected(commentType: commentNotifierObj.selectedCommentType));
                      },
                      onChanged: (val) {
                        _commentNotifier.value = commentNotifierObj.copyWith(
                            enableSubmitBtn:
                                _commentController.text.trim().isNotEmpty);
                        // enableCommentSubmitBtn.value =
                        //     _commentController.text.trim().isNotEmpty;
                      },
                      onClose: () {
                        Navigator.of(context).pop();
                      },
                    );
                  });
            },
          );
        });
  }

  _fetchComments(BuildContext context, CourseListCubit cubit) async {
    bool _hasAccess = true; // await _checkAccessToGetComments();
    String instanceId = courseFileResource.id;
    if (_hasAccess && instanceId.isNotEmpty) {
      await cubit.setLoading(true);
      await cubit.fetchCommentsForId(instanceId);
      CourseListState state = cubit.state;
      if (state is CourseCommentsFetched) {
        _resourceComments = state.comments
            .where(
                (element) => element.resActivityTpe == ResActivityType.comment)
            .toList();

        _resourceLikes = state.comments
            .where((element) => element.resActivityTpe == ResActivityType.like)
            .toList();
        int likeCount = _resourceLikes.length;
        _isLiked.value = _resourceLikes.any((like) =>
            like.user ==
            SLUser.shared.first_name + ' ' + SLUser.shared.last_name);

        _likes.value = likeCount;
      } else if (state is CommentsError) {
        _showCommentError(context, cubit, state.error);
      }
      await cubit.setLoading(false);
    } else {
      // no access to fetch comments
    }
  }

  _addNewComment(BuildContext context, CourseListCubit cubit, String comment,
      {required String? parentId, required RoleName? roleName}) async {
    /// locally update first
    /// upload to server in background
    ///
    String commentType = _commentNotifier.value.selectedCommentType;
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    try {
      ResourseComment _studentComment = ResourseComment(
          commentId: '',
          parentId: parentId ?? '',
          message: comment,
          subject: TOPIC_NAME,
          commentType: CommentType.values.firstWhere((e) =>
              e.toString().split('.').last.toLowerCase() ==
              commentType.toLowerCase()),
          user: SLUser.shared.first_name + ' ' + SLUser.shared.last_name,
          commentedTime: DateTime.now(),
          profilePic: SLUser.shared.avatar_url,
          commentStatus: CommentStatus.PENDING,
          roleName: SLUser.shared.userRole ?? RoleName.STUDENT,
          resActivityTpe: ResActivityType.comment);

      Map<String, dynamic> jsonReqBody = parentId != null
          ? {
              "instance_id": courseFileResource.id,
              "user_id": USER_ID,
              "comment_data": {
                "parent_id": parentId,
                "subject": TOPIC_ID,
                "message": comment,
                "type": commentType,
                'activity_type': ResActivityType.comment.name
              }
            }
          : {
              "instance_id": courseFileResource.id,
              "user_id": USER_ID,
              "comment_data": {
                "subject": TOPIC_ID,
                "message": comment,
                "type": commentType,
                'activity_type': ResActivityType.comment.name
              }
            };

      await cubit.setLoading(true);
      await cubit.uploadComment(jsonReqBody);
      CourseListState state = cubit.state;
      Navigator.pop(context);

      ///
      /// activity log
      ///
      final isSuccess = state is CourseCommentsUploaded;
      final logResult = isSuccess ? 'success' : 'error';
      final actionDetails = isSuccess
          ? 'comment added'
          : state is CommentsError
              ? state.error
              : 'Add comment failure';
      final actionComment = isSuccess
          ? 'Commented at ${DateTime.now()}'
          : 'Comment failed at ${DateTime.now()}';

      final reqJson = {
        'activity_type': 'Course_Resource',
        'screen_name': 'PDF View',
        'action_details': actionDetails,
        'target_id': courseFileResource.id,
        'action_comment': actionComment,
        'log_result': logResult,
      };

      await _appConfigCubit.setUserActivityLog(reqJson);

      ///
      if (state is CommentsError) {
        _showCommentError(context, cubit, state.error);
      } else {
        _commentController.clear();
        parentId = null;
        roleName = null;
        await cubit.addComment(_studentComment, _resourceComments);
        cubit.setLoading(false);
        _showSubmissionDialgoue(context);
      }
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  _showCommentError(BuildContext context, CourseListCubit cubit, String error) {
    //_isDataLoading.value = false;
    cubit.setLoading(false);
    showPGExceptionPopup(context, error);
  }

  // Function to show the dialogue
  void _showSubmissionDialgoue(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: SingleActionDialogue(
            title: '',
            message: SLStrings.getTranslatedString(KEY_COMMENT_SUBMITTED),
            iconWidget: const Icon(Icons.check_circle, size: 60),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: false,
            handleOkCallback: () {
              Navigator.of(context).pop();
            },
          ),
        );
      },
    );
  }
}
