// To parse this JSON data, do
//
//     final liveClassModel = liveClassModelFromJson(jsonString);

import 'dart:convert';

/*************  ✨ Windsurf Command 🌟  *************/
List<LiveClassModel> liveClassModelFromJson(String str) =>
    List<LiveClassModel>.from(
        json.decode(str).map((x) => LiveClassModel.fromJson(x)));
/*******  23dc2209-3148-46b6-846d-91c60aac49dc  *******/

String liveClassModelToJson(List<LiveClassModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class LiveClassModel {
  Status status;
  DateTime endDate;
  String? passcode;
  dynamic courseId;
  String? meetingId;
  DateTime startDate;
  String? meetingUrl;
  MeetingType meetingType;
  MeetingStatus meetingStatus;
  CurrentMeetingStatus currentMeetingStatus;
  String countdownText = "";

  LiveClassModel({
    required this.status,
    required this.endDate,
    required this.passcode,
    required this.courseId,
    required this.meetingId,
    required this.startDate,
    required this.meetingUrl,
    required this.meetingType,
    required this.meetingStatus,
    required this.currentMeetingStatus,
    required this.countdownText,
  });

  factory LiveClassModel.fromJson(Map<String, dynamic> json) {
    DateTime startTime = json["start_date"] != null
        ? DateTime.parse(json["start_date"]).toLocal()
        : DateTime.now();
    DateTime endTime = json["end_date"] != null
        ? DateTime.parse(json["end_date"]).toLocal()
        : DateTime.now();

// Determine the current meeting status based on the start and end times
    CurrentMeetingStatus currentMeetingStatusTemp =
        (DateTime.now().isAfter(startTime) && DateTime.now().isBefore(endTime))
            ? CurrentMeetingStatus.LIVE
            : DateTime.now().isBefore(startTime)
                ? CurrentMeetingStatus.UPCOMING
                : CurrentMeetingStatus.ENDED;

    return LiveClassModel(
      status: statusValues.map[json["status"]]!,
      endDate: endTime,
      passcode: json["passcode"],
      courseId: json["course_id"],
      meetingId: json["meeting_id"],
      startDate: startTime,
      meetingUrl: json["meeting_url"],
      meetingType: meetingTypeValues.map[json["meeting_type"]]!,
      meetingStatus: meetingStatusValues.map[json["meeting_status"]]!,
      currentMeetingStatus: currentMeetingStatusTemp,
      countdownText: "",
    );
  }

  LiveClassModel copyWith({
    String? meetingId,
    DateTime? startDate,
    DateTime? endDate,
    CurrentMeetingStatus? currentMeetingStatus,
    MeetingType? meetingType,
    String? meetingUrl,
    String? passcode,
  }) {
    return LiveClassModel(
        meetingId: meetingId ?? this.meetingId,
        startDate: startDate ?? this.startDate,
        endDate: endDate ?? this.endDate,
        currentMeetingStatus: currentMeetingStatus ?? this.currentMeetingStatus,
        meetingType: meetingType ?? this.meetingType,
        meetingUrl: meetingUrl ?? this.meetingUrl,
        passcode: passcode ?? this.passcode,
        status: status ?? this.status,
        courseId: courseId ?? this.courseId,
        meetingStatus: meetingStatus ?? this.meetingStatus,
        countdownText: countdownText ?? this.countdownText);
  }

  Map<String, dynamic> toJson() => {
        "status": statusValues.reverse[status],
        "end_date": endDate.toIso8601String(),
        "passcode": passcode,
        "course_id": courseId,
        "meeting_id": meetingId,
        "start_date": startDate.toIso8601String(),
        "meeting_url": meetingUrl,
        "meeting_type": meetingTypeValues.reverse[meetingType],
        "meeting_status": meetingStatusValues.reverse[meetingStatus],
      };
}

enum CurrentMeetingStatus { LIVE, UPCOMING, ENDED }

final currentMeetingStatusValues = EnumValues({
  "Live": CurrentMeetingStatus.LIVE,
  "Upcoming": CurrentMeetingStatus.UPCOMING,
  "Ended": CurrentMeetingStatus.ENDED
});

enum MeetingStatus { PENDING, STARTED }

final meetingStatusValues = EnumValues(
    {"Pending": MeetingStatus.PENDING, "Started": MeetingStatus.STARTED});

enum MeetingType { GMEET, ZOOM }

final meetingTypeValues =
    EnumValues({"gmeet": MeetingType.GMEET, "zoom": MeetingType.ZOOM});

enum Status { ACTIVE }

final statusValues = EnumValues({"Active": Status.ACTIVE});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
