import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

import '../../config/enums/alert_types.dart';
import '../../config/enums/resource_activity_status.dart';
import '../../domain/models/course_dashboard/course_page_resource.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/helper.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '../../utils/resources/user_activity_res.dart';
import '/src/config/themes/app_theme.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/page_content.dart';
import '../../utils/constants/nums.dart';
import '../cubits/section_details/section_details_cubit.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/button_widget.dart';
import '../widgets/empty_screen_view.dart';
import '../widgets/loading_indicator.dart';
import '/src/config/themes/lms_fonts.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../../utils/constants/strings.dart';
import '../widgets/app_bar.dart';

@RoutePage()
// ignore: must_be_immutable
class StudyMaterialViewScreen extends HookWidget {
  final int moduleLength;
  final ResourceProgress? resourceProgress;
  final CoursePageResource pageContent;

  StudyMaterialViewScreen({
    Key? key,
    required this.resourceProgress,
    required this.moduleLength,
    required this.pageContent,
  }) : super(key: key);

  bool isDataLoading = false;

  /// set to true if the page is marked as done
  ValueNotifier<bool> _isCompleted = ValueNotifier(false);

  static const double _horizontalMargin =
      9.0; // html takes padding 7 by default

  late ValueNotifier<bool> _hasAccessToSaveProgress;

  final scrollController = ScrollController();
  final _userActivityLog = UserActivityLog.instance;

  // final scrollController2 = PrimaryScrollController();
  String time = '${0} $HOURS ${0} $MINUTES ${0} $SECONDS';
  Map<String, dynamic> args = {};

  // decides whether to disable/enable save progress
  _checkPrivilegeAcccess() async {
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_UPDATE_COURSE_PROGRESS;
    _hasAccessToSaveProgress.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);
  }

  _onBackTapped(BuildContext context) async {
    args = {'progress': _isCompleted.value ? 100.0 : 0.0, 'time_spent': time};
    ResourceActivityStatus status = _isCompleted.value
        ? ResourceActivityStatus.completed
        : ResourceActivityStatus.inProgress;

    await _setResourceActivityLog(context, status: status);
    kIsWeb
        ? Beamer.of(context).beamBack(data: args)
        : appRouter.popForced(args);
  }

  @override
  Widget build(BuildContext context) {
    final _sectionDetailsCubit = BlocProvider.of<SectionDetailsCubit>(context);

    _hasAccessToSaveProgress = useState<bool>(false);
    _isCompleted = useState<bool>(false);

    useEffect(() {
      _isCompleted.value = (resourceProgress?.progress ?? 0.0) >= 100;
      _sectionDetailsCubit.initCubit();
      _isCompleted.value = resourceProgress?.markedAsDone ?? false;
      Future<void>.microtask(() async {
        await _checkPrivilegeAcccess();
        /*   TODO: Remove later
          _sectionDetailsCubit.setLoading(true);
          await _sectionDetailsCubit.fetchPageContent(instance);
          _sectionDetailsCubit.setLoading(false);*/
        await _setResourceActivityLog(context,
            status: ResourceActivityStatus.started);
      });

      return null;
    }, []);

    return BlocListener<SectionDetailsCubit, SectionDetailsState>(
        listener: (context, state) async {
      // if (state is PageContentsFetched) {
      //   pageContent = state.pageContentData;
      // } else
      if (state is SectionDetailsListLoading) {
        isDataLoading = state.isDataLoading;
      }
    }, child: BlocBuilder<SectionDetailsCubit, SectionDetailsState>(
            builder: (context, state) {
      bool hasValidContents = pageContent != null;
      return PopScope(
        canPop: false,
        onPopInvoked: (bool didPop) {
          if (didPop) {
            return;
          }
          _onBackTapped(context);
        },
        child: Scaffold(
            appBar: PreferredSize(
                preferredSize: const Size.fromHeight(70),
                child: AppBarWidget(
                  title: STUDY_MATERIAL_APPBAR_TITLE,
                  leadingIconName: BACK_ARROW,
                  trailingWidget: Container(),
                  leadingBtnAction: () {
                    _onBackTapped(context);
                  },
                )),
            body: ValueListenableBuilder(
                valueListenable: _isCompleted,
                builder: (context, _, __) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16)),
                    ),
                    child: Scrollbar(
                      controller: scrollController,
                      child: Stack(
                        children: [
                          ScrollConfiguration(
                            behavior: ScrollConfiguration.of(context).copyWith(
                              scrollbars: false,
                              dragDevices: {
                                PointerDeviceKind.touch,
                                PointerDeviceKind.mouse,
                              },
                            ),
                            child:
                                LayoutBuilder(builder: (context, constraints) {
                              return Align(
                                alignment: Alignment.topCenter,
                                child: Container(
                                    width: constraints.maxWidth < 720
                                        ? double.infinity
                                        : 800,
                                    margin: const EdgeInsets.only(
                                        left: horizontalMargin,
                                        right: horizontalMargin),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      // color: AppTheme.whiteColor,
                                    ),
                                    child: Stack(
                                      alignment: Alignment.topCenter,
                                      children: [
                                        SingleChildScrollView(
                                          // controller: scrollController2,
                                          // primary: true,
                                          child: Align(
                                            alignment: Alignment.topCenter,
                                            child: Container(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  SizedBox(
                                                      height: _isCompleted
                                                                  .value ||
                                                              (resourceProgress
                                                                      ?.markedAsDone ??
                                                                  false)
                                                          ? 15
                                                          : 70),
                                                  _pageTitle(),
                                                  const SizedBox(height: 10),
                                                  _htmlContent(
                                                      context,
                                                      constraints,
                                                      _sectionDetailsCubit),
                                                  const SizedBox(height: 20),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        _isCompleted.value == false ||
                                                resourceProgress
                                                        ?.markedAsDone ==
                                                    false
                                            ? state is SectionDetailsListLoading ||
                                                    isDataLoading
                                                ? Container()
                                                : _doneBtnAction(
                                                    context,
                                                    MARK_AS_DONE,
                                                    _sectionDetailsCubit,
                                                    state)
                                            : Container(),
                                      ],
                                    )),
                              );
                            }),
                          ),
                          isDataLoading
                              ? Positioned.fill(child: LoadingIndicatorClass())
                              : !hasValidContents
                                  ? const EmptyScreenView(title: SUBJECTS_EMPTY)
                                  : Container()
                        ],
                      ),
                    ),
                  );
                })),
      );
    }));
  }

  Widget _pageTitle() {
    return Padding(
      padding: const EdgeInsets.only(left: 7),
      child: Text(pageContent?.name ?? '',
          style: LMSFonts.semiBoldFont(16, AppTheme.commentBoxContent)),
    );
  }

  Widget _htmlContent(BuildContext context, BoxConstraints constraints,
      SectionDetailsCubit _sectionDetailsCubit) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 7),
      child: Column(
        children: [
          HtmlWidget(
            pageContent?.content ?? '',
            // style: LMSFonts.htmlStyeForCurrentAffairs(
            //     MediaQuery.sizeOf(context).width - (17 * 2)),
            onTapUrl: (url) => launchSelectedUrl(url),
          ),
          kIsWeb
              ? closeButton(constraints, _sectionDetailsCubit, context)
              : Container(),
        ],
      ),
    );
  }

  Widget _doneBtnAction(BuildContext context, String title,
      SectionDetailsCubit _sectionDetailsCubit, SectionDetailsState state) {
    return Positioned(
      top: 0,
      right: 3,
      child: Container(
        width: MediaQuery.sizeOf(context).width,
        color: Colors.white,
        child: Visibility(
          visible: _hasAccessToSaveProgress.value,
          child: Container(
            decoration: const BoxDecoration(
              borderRadius:
                  BorderRadiusDirectional.vertical(top: Radius.circular(16)),
            ),
            height: 50,
            margin: const EdgeInsets.only(top: 16),
            child: Align(
              alignment: Alignment.topRight,
              child: ButtonWidget(
                minimumSize: const Size(10, 10),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(buttonRadius),
                    side: const BorderSide(
                      color: AppTheme.examInfoTextColor,
                    )),
                color: AppTheme.whiteTextColor,
                child: Text(
                  MARK_AS_DONE,
                  style: LMSFonts.semiBoldFont(16, AppTheme.examInfoTextColor),
                ),
                onPressed: () async => _showConfirmationDialogue(
                    context, _sectionDetailsCubit, state),
              ),
            ),
          ),
        ),
      ),
    );
  }

  _showConfirmationDialogue(BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit, SectionDetailsState state) {
    showDialog(
      context: context,
      builder: (popcontext) {
        return MultiActionDialogue(
          alertType: AlertType.warning,
          title: CONFIRM,
          content: ALERT_TITLE,
          cancelBtnText: CANCEL_TEXT,
          confirmBtnText: EXAM_ALERT_OK,
          onCancelTap: () {
            kIsWeb ? Navigator.of(context).pop() : appRouter.popForced();
          },
          onContinueTap: () async {
            Navigator.of(popcontext).pop();
            _sectionDetailsCubit.setLoading(true);
            args = {
              'progress': _isCompleted.value ? 100 : 100,
              'time_spent': time
            };
            if (pageContent.courseId != null && pageContent.id != null) {
              await _sectionDetailsCubit.setCourseProgress(
                  pageContent.courseId!, pageContent.id!, args);
              state = _sectionDetailsCubit.state;

              if (state is SetProgressState) {
                _isCompleted.value = true;
                _showProgressDialog(context, args);
              } else {
                _isCompleted.value = false;
              }

              await _userActivityLog.setResProgressActivityLog(
                  context: context,
                  screen: 'Study Material View',
                  resourceType: 'Study material',
                  id: pageContent.id ?? '',
                  result: state is SetProgressState ? 'success' : 'error',
                  responseStatus: state is SetProgressState
                      ? 'Progress updated'
                      : 'Progress update failed');

              await _setResourceActivityLog(context,
                  status: ResourceActivityStatus.completed);
            }
            _sectionDetailsCubit.setLoading(false);
          },
        );
      },
    );
  }

  _showProgressDialog(BuildContext context, Map<String, dynamic> args) {
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
          alertType: AlertType.success,
          title: SUCESS,
          content: PROGRESS_SUCESS_ALERT_CONTENT,
          cancelBtnText: "",
          confirmBtnText: OK,
          onCancelTap: () {},
          onContinueTap: () {
            if (!_isCompleted.value) {
              kIsWeb
                  ? Beamer.of(context).beamBack()
                  : Navigator.of(context).pop();
            } else if (_isCompleted.value) {
              _onBackTapped(context);
            }
          },
        );
      },
    );
  }

  Widget closeButton(BoxConstraints constraints,
      SectionDetailsCubit _sectionDetailsCubit, BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Container(
        margin: constraints.maxWidth < 720
            ? const EdgeInsets.only(bottom: 10)
            : const EdgeInsets.only(bottom: 20),
        child: ButtonWidget(
            minimumSize: const Size(10, 10),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(buttonRadius),
                side: const BorderSide(
                  color: AppTheme.examInfoTextColor,
                )),
            color: AppTheme.whiteColor,
            child: Text(SLStrings.getTranslatedString(KEY_CLOSE),
                style: LMSFonts.regularFont(14, AppTheme.examIntroTextColor)),
            onPressed: () async {
              // if (moduleLength > 1) {
              //   kIsWeb
              //       ? Beamer.of(context)
              //           .beamToReplacementNamed('/section-details')
              //       : Navigator.of(context).pop();
              // } else {
              kIsWeb
                  ? Beamer.of(context).beamToReplacementNamed('/course-details')
                  : appRouter.popUntil((route) =>
                      route.settings.name == CourseDashboardRoute.name);
              // }
            }),
      ),
    );
  }

  Future<void> _setResourceActivityLog(BuildContext context,
      {required ResourceActivityStatus status}) async {
    await _userActivityLog.setResourceActivityLog(
        context: context,
        resourceType: 'Study Material',
        status: status,
        currentDuration:
            _isCompleted.value || (resourceProgress?.markedAsDone ?? false)
                ? 1.0
                : 0.0,
        id: pageContent.id ?? '',
        result: 'success');
  }
}
