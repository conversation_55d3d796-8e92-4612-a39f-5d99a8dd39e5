import 'package:equatable/equatable.dart';

class PageContent extends Equatable {
  final String id;
  final String name;
  final String content;

  PageContent({
    this.id = '',
    this.name = '',
    this.content = '',
  });

  factory PageContent.fromJson(Map<String, dynamic> json) {
    return PageContent(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      content: json['content'] ?? '',
    );
  }

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [
      id,
      name,
      content,
    ];
  }
}
