enum QuestionStatus {
  answered("answered"),
  markedForLater("markedForLater"),
  reported('reported'),
  unAnswered('unAnswered'),
  skipped('skipped');

  const QuestionStatus(this.value);

  final String value;
}

QuestionStatus stringToQuestStatus(String value) {
  return QuestionStatus.values.firstWhere(
      (status) => status.toString() == value,
      orElse: () => QuestionStatus.unAnswered);
}

enum AnswerStatus {
  right('right'),
  wrong('wrong');

  const AnswerStatus(this.value);

  final String value;
}

AnswerStatus stringToAnswerStatus(String value) {
  return AnswerStatus.values.firstWhere((status) => status.toString() == value,
      orElse: () => AnswerStatus.wrong);
}
