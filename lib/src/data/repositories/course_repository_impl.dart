import '../../config/app_config/api_config.dart';
import '../../domain/models/resource_comment.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/strings.dart';
import '/src/domain/models/course.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/models/course_details.dart';
import '../../domain/repositories/course_respository.dart';

class CourseRepositoryImpl implements CourseRepository {
  CourseRepositoryImpl();

  @override
  Future<List<Course>> getUserCourse(String topicId, String orgId) async {
    final course = await Supabase.instance.client
        .from(APIConfig.V_COURSE)
        .select()
        .eq("category_id", topicId)
        .eq('org_id', ORG_ID);

    List<dynamic> userListt = course;
    List<Map<String, dynamic>> mapList =
        userListt.map((item) => item as Map<String, dynamic>).toList();
    List<Course> courseList =
        mapList.map((json) => Course.fromJson(json)).toList();

    return courseList;
  }

  /// Course Details
  @override
  Future<List<CourseDetails>> fetchCourseDetailsInfo(String courseId) async {
    List<CourseDetails> _courseDetailsList = [];
    bool isNetworkAvailable = await checkNetworkStatus();
    if (isNetworkAvailable) {
      final response = await Supabase.instance.client
          .from(APIConfig.V_COURSE_DETAILS)
          .select()
          .eq('course_id', courseId)
          .onError((error, stackTrace) {
        return _courseDetailsList;
      });
      List<dynamic> responseData = response;
      if (responseData.isEmpty) {
        return _courseDetailsList;
      } else {
        CourseDetails? _courseDetails = CourseDetails.fromJson(responseData[0]);
        _courseDetailsList = [_courseDetails];
        return _courseDetailsList;
      }
    } else {
      return _courseDetailsList;
    }
  }

  /// Course Details- current affairs by date
  @override
  Future<List<CurrentAffairs>> fetchMoreCurrentAffairs() async {
    final response = await Supabase.instance.client
        .from(APIConfig.V_BULLETIN_BOARD)
        .select()
        .filter('type', 'eq', 'Current_Affairs')
        .eq('org_id', ORG_ID);
    List<dynamic> responseData = response;
    List<CurrentAffairs> _currentAffairs = List<CurrentAffairs>.from(
        responseData.map((x) => CurrentAffairs.fromJson(x))).toList();
    return _currentAffairs;
  }

  @override
  Future<List<ResourseComment>> fetchCommentsForId(String instanceId) async {
    List<ResourseComment> commentsFetched = [];

    final response = await Supabase.instance.client.rpc(
        APIConfig.GET_COMMENTS_FN,
        params: {'instance_id': instanceId, 'org_id': ORG_ID});

    List<dynamic> responseData = response ?? [];

    commentsFetched = List<ResourseComment>.from(
        responseData.map((x) => ResourseComment.fromJson(x))).toList();
    return commentsFetched;
  }

  @override
  Future uploadCommentsForId(Map<String, dynamic> jsonBody) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.ADD_COMMENTS_FN, params: jsonBody);
    Map<String, dynamic> responseData = response ?? [];
    return responseData;
  }

  // set course progress

  @override
  Future<dynamic> setCourseProgress(
      String course, String instance, Map<String, dynamic> duration) async {
    Map<String, dynamic> progressData = {
      "course_id": course,
      "instance_id": instance,
      "org_id": ORG_ID,
      "progress_data": duration,
      "user_id": USER_ID
    };

    final response = await Supabase.instance.client
        .rpc(APIConfig.SET_COURSE_PROGRESS_FN, params: progressData);

    if (response != null) {
      Map<String, dynamic> responseData = response;
      return responseData;
    } else {
      return false;
    }
  }
}
