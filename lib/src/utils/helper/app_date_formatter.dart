import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';

class AppDateFormatter {
  AppDateFormatter._internal();

  static AppDateFormatter instance = AppDateFormatter._internal();

  factory AppDateFormatter() => instance;

  /// 20-June-2023
  String formatDatedMy(DateTime date) {
    final DateFormat formatter = DateFormat('dd-MMM-yyyy');
    return formatter.format(date);
  }

  /// June 20
  String formatDateMd(DateTime date) {
    final DateFormat formatter = DateFormat('MMMM dd');
    return formatter.format(date);
  }

  /// June 2023
  String formatDateMMMMyyyy(DateTime date) {
    final DateFormat formatter = DateFormat('MMMM yyyy');
    return formatter.format(date);
  }

  /// 20 June 2023 12:00 AM/PM
  String formatToDateTimeString(DateTime date) {
    final DateFormat formatter = DateFormat('dd MMM yyyy hh:mm a');
    return formatter.format(date);
  }

  /// 20th June 2023
  String formatDatedmmmmy(DateTime date) {
    final DateFormat formatter = DateFormat('d MMMM y');
    return formatter.format(date);
  }

  // yyyy/MM/dd
  String formatDateyyyyMMdd(DateTime date) {
    String formattedDate = DateFormat('yyyy/MM/dd').format(date);
    return formattedDate;
  }

//
  String formatJiffydoMy(DateTime date) {
    String formattedDate =
        Jiffy.parse(date.toString()).format(pattern: 'do MMM yyyy');
    return formattedDate;
  }

  /// November 14, 2023, at 2:30 PM
  String formatForExamAvailabilityPopup(DateTime date) {
    String formattedTime = DateFormat.yMMMMd().add_jm().format(date);
    return formattedTime;
  }

  /// 24 Jun 2023
  String formatForCurrentAffairs(DateTime date) {
    String formattedDate = DateFormat('d MMM yyyy').format(date);
    return formattedDate;
  }

  Duration formatStringToDuration(String input) {
    // Split the input time string into hours, minutes, and seconds
    List<String> parts = input.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);
    int seconds = int.parse(parts[2]);

    // Create a Duration from the given time
    Duration duration =
        Duration(hours: hours, minutes: minutes, seconds: seconds);

    return duration;
  }

  Duration addDurationInto(Duration prevDuration, Duration newDuration) {
    Duration updatedDuration = prevDuration + newDuration;
    return updatedDuration;
  }

  String formatDurationToString(Duration duration) {
    // Extract new time components
    int newHours = duration.inHours;
    int remainingMinutes = duration.inMinutes % 60;
    int remainingSeconds = duration.inSeconds % 60;

    // Build the output string by excluding parts with zero value
    List<String> resultParts = [];
    if (newHours > 0) resultParts.add("$newHours hrs");
    if (remainingMinutes > 0) resultParts.add("$remainingMinutes min");
    if (remainingSeconds > 0) resultParts.add("$remainingSeconds sec");

    // Join the parts with a space separator
    String output = resultParts.join(' ');
    output = output.isEmpty ? '0' : output;

    return output;
  }
}
