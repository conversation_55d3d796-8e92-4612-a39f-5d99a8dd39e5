// ignore_for_file: must_be_immutable

import 'package:cached_network_image/cached_network_image.dart';
import 'package:confetti/confetti.dart';

import '../../config/enums/resource_activity_status.dart';
import '../../config/themes/app_dynamic_theme.dart';
import '../../config/themes/app_theme.dart';

import '../../domain/models/course_dashboard/ppt_data.dart';
import '../../domain/models/question_data.dart';
import '../../domain/models/resource_comment.dart';
import '../../domain/models/sl_user.dart';
import '../../utils/constants/lists.dart';
import '../../utils/constants/nums.dart';
import '../../utils/constants/strings.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../../utils/helper/navigation_handler.dart';
import '../../utils/resources/user_activity_res.dart';
import '../cubits/app_config/app_config_cubit.dart';
import '../cubits/connectivity/connectivity_cubit.dart';
import '../cubits/connectivity/connectivity_state.dart';
import '../cubits/course_details/course_details_cubit.dart';
import '../widgets/alert_popup/confirmation_popup.dart';
import '../widgets/button_widget.dart';
import '../widgets/profile_placeholder_widget.dart';
import '/src/config/enums/alert_types.dart';
import '/src/config/router/app_router.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/domain/models/check_point_data/check_point_data.dart';
import '/src/domain/models/course_dashboard/course_file_resource.dart';
import '/src/domain/models/milestone.dart';
import '/src/domain/services/localizations/sl_strings.dart';
import '/src/domain/services/localizations/string_keys.dart';
import '/src/presentation/cubits/course_list/course_list_cubit.dart';
import '/src/presentation/cubits/exam/exam_cubit.dart';
import '/src/presentation/widgets/alert_popup/multi_action_dialogue.dart';
import '/src/presentation/widgets/alert_popup/single_action_dialogue.dart';
import '/src/presentation/widgets/app_bar.dart';
import '/src/presentation/widgets/loading_indicator.dart';
import '/src/utils/constants/helper.dart';
import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'course_video_view.dart';

enum NavigationTo {
  course_details,
  details_dashboard,
  section_details,
  subjects_tab_view,
  exam_list
}

@RoutePage()
class PPTViewerScreen extends HookWidget with WidgetsBindingObserver {
  final CourseFileResource courseFileResource;
  final List<CheckPoint> checkPoints;
  final bool isFromExamScreen;
  final bool isExamPassed;
  final NavigationTo navigateBackTo;

  PPTViewerScreen({
    super.key,
    required this.courseFileResource,
    this.checkPoints = const [],
    this.isFromExamScreen = false,
    this.isExamPassed = false,
    required this.navigateBackTo,
  });

  final _scaffoldKey = GlobalKey<ScaffoldState>();

  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  late TabController _tabController;

  final ValueNotifier<WebViewController> webViewController =
      ValueNotifier<WebViewController>(WebViewController());
  final ValueNotifier<bool> showButton = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _isDataLoading = ValueNotifier<bool>(false);

  ValueNotifier<bool> readMore = ValueNotifier<bool>(false);
  ValueNotifier<bool> _isCommentsInFullSCreen = ValueNotifier<bool>(false);
  final ValueNotifier<AddCommentFunc> _commentNotifier = ValueNotifier(
      AddCommentFunc(enableSubmitBtn: false, selectedCommentType: 'Feedback'));
  final ValueNotifier<int> tabIndex = ValueNotifier<int>(0);

  final List<CheckPoint> _checkPoints = [];

  List<Question> _questions = [];
  List<ResourseComment> _resourceComments = [];
  List<ResourseComment> _resourceLikes = [];

  CourseListCubit? _courseCubitObj;
  QuestionData? _questionData;

  int lastWatchTime = 0;
  int _currentSlide = 0;

  bool localcheckEmpty = false;
  bool _isCompleted = false;

  final _userActivityLog = UserActivityLog.instance;

  final ValueNotifier<bool> _isLiked = ValueNotifier<bool>(false);
  final ValueNotifier<int> _likes = ValueNotifier<int>(0);
  final ValueNotifier<double> _likeScale = ValueNotifier<double>(1.0);
  final ConfettiController _confettiController =
      ConfettiController(duration: const Duration(milliseconds: 700));
  final AppDynamicTheme? _appDynamicTheme = null; // Set as per your theme logic

  _initCheckpointInfo(
      BuildContext context, CourseListCubit _courseCubit) async {
    if (_isCompleted) {
      webViewController.value.loadRequest(
        Uri.parse(
          courseFileResource.url,
        ),
      );
    } else {
      final localcheck =
          await _courseCubit.getCheckPointData(courseFileResource.id);

      if (localcheck.isNotEmpty) {
        _checkPoints.addAll(localcheck);

        _sortCheckpoints(_checkPoints);
        _sortCheckpoints(checkPoints);
      } else {
        _checkPoints.addAll(checkPoints);

        _sortCheckpoints(_checkPoints);
        _sortCheckpoints(checkPoints);
        localcheckEmpty = true;
      }

      _courseCubitObj = _courseCubit;

      _manageCheckPoints(context, _courseCubit);
    }
  }

  _manageCheckPoints(BuildContext context, CourseListCubit _courseCubit) async {
    ///load all datas from local..

    if (isExamPassed) {
      showButton.value = false;
      await _courseCubit.insertToCompletedCheckPoint(
        _checkPoints.first,
        courseFileResource.id,
      );

      _courseCubit.insertToPPTData(
        PPTData(
          pptindex: "0",
          url: _courseCubit.pptDataCoj["url"],
          checkpointId: checkPoints.first.checkpointId!,
        ),
        courseFileResource.id,
      );

      _checkPoints.removeAt(0);

      _courseCubit.insertToCheckPointData(courseFileResource.id, _checkPoints);

      webViewController.value.loadRequest(
        Uri.parse(_courseCubit.pptDataCoj["url"]),
      );
    } else {
      final completedCheckpoints =
          await _courseCubit.getCompletedCheckPointData(courseFileResource.id);

      if (completedCheckpoints != null) {
        final checkPointpptdata = await _courseCubit.getPPTData(
          courseFileResource.id,
        );

        _currentSlide =
            int.parse(_extractSlideValue(checkPointpptdata?.url ?? '') ?? "");
        webViewController.value.loadRequest(
          Uri.parse(checkPointpptdata!.url),
        );
      } else {
        if (_checkPoints.isEmpty) {
          _currentSlide = ((courseFileResource.progress / 100) *
                  courseFileResource.pageCount)
              .toInt();
          String url = _loadSlide(_currentSlide, courseFileResource.url);
          webViewController.value.loadRequest(
            Uri.parse(
              url,
            ),
          );
        } else {
          ////load from slide data
          webViewController.value.loadRequest(
            Uri.parse(
              courseFileResource.url,
            ),
          );
        }
      }
    }
  }

  String _loadSlide(int slideNum, String url) {
    String currentUrl = url;
    url.replaceFirst(RegExp(r'slide=id.p\d+'), 'slide=id.p$slideNum');

    print(url);
    return url;
  }

  String _nextSlideLoad(CourseListCubit _courseCubit, String loadUrl) {
    Uri uri;
    if (loadUrl.isEmpty) {
      String currentUrl = _courseCubit.pptDataCoj["url"];
      uri = Uri.parse(currentUrl);
    } else {
      uri = Uri.parse(loadUrl);
    }

    // Extract query parameters
    Map<String, String> queryParams = Map.from(uri.queryParameters);

    // Extract slide number and increment it
    if (queryParams.containsKey("slide")) {
      String slideId = queryParams["slide"] ?? "";
      if (slideId.startsWith("id.p")) {
        int slideNumber = int.tryParse(slideId.substring(4)) ?? 0;
        slideNumber++; // Increment slide number
        queryParams["slide"] = "id.p$slideNumber"; // Update slide parameter
      }
    }

    // Build the new URL and return it
    return uri.replace(queryParameters: queryParams).toString();
  }

  _sortCheckpoints(List<CheckPoint> checkpoints) {
    checkpoints.sort((a, b) => (a.startPage ?? 0).compareTo(b.startPage ?? 0));
  }

  @override
  Widget build(BuildContext context) {
    final navigate = useState<bool>(true);
    final url = useState<String>('');
    final _courseCubit = BlocProvider.of<CourseListCubit>(context);
    _tabController =
        useTabController(initialLength: 2, initialIndex: tabIndex.value);

    useEffect(() {
      ///checkpoint
      WidgetsBinding.instance.addObserver(this);

      _isCompleted = courseFileResource.progress >= 100;
      _courseCubit.initCourseCubit();
      _courseCubitObj?.setQuestionLoading(true);

      _initCheckpointInfo(context, _courseCubit);
      _courseCubitObj?.setQuestionLoading(false);

      webViewController.value
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onUrlChange: (change) {
              final slideIdRegex = RegExp(r"slide=id\.(p\d+)");

              if (change.url != null) {
                final currentMatch = slideIdRegex.firstMatch(url.value);
                final changeMatch = slideIdRegex.firstMatch(change.url!);

                if (currentMatch != null && changeMatch != null) {
                  final currentSlideId =
                      int.parse(currentMatch.group(1)!.substring(1));
                  final newSlideId =
                      int.parse(changeMatch.group(1)!.substring(1));

                  _courseCubit.updatepptStatus(change.url!, currentSlideId);
                  _currentSlide = newSlideId;
                  if (newSlideId < currentSlideId && !_isCompleted) {
                    webViewController.value.goBack();
                  }
                } else {
                  url.value = change.url!;
                }
                if (_checkPoints.isNotEmpty) {
                  _checkSlideAndShowPopup(
                      context,
                      change.url ?? '',
                      (_checkPoints.first.startPage ?? 0).toInt(),
                      _checkPoints.first);
                }
                if (change.url != null) {
                  url.value = change.url!;
                }
              }
            },
            onProgress: (int progress) {
              debugPrint('Loading progress: $progress%');
              if (progress == 100) {
                _isDataLoading.value =
                    false; // Hide loading indicator when done
              } else {
                _isDataLoading.value =
                    true; // Show loading indicator while loading
              }
            },
            onPageStarted: (String newUrl) async {
              if (newUrl.contains("google.com/tools/feedback")) {
                await webViewController.value.reload();
              }
            },
            onPageFinished: (String newUrl) {
              _isDataLoading.value = false;
              debugPrint('Page finished loading: $newUrl');
            },
            onWebResourceError: (WebResourceError error) {
              debugPrint('Web resource error: ${error.description}');
            },
            onNavigationRequest: (NavigationRequest request) {
              if (navigate.value) {
                navigate.value = false;
                return NavigationDecision.navigate;
              } else {
                return NavigationDecision.prevent;
              }
            },
          ),
        );
      // ..loadRequest(
      //   Uri.parse(
      //     courseFileResource.url,
      //   ),
      // );

      Future<void>.microtask(() async {
        await _fetchComments(context, _courseCubit);
        await _setResourceActivityLog(context,
            status: ResourceActivityStatus.started);
      });

      _commentController.addListener(() {
        _commentNotifier.value = _commentNotifier.value
            .copyWith(enableSubmitBtn: _commentController.text.isNotEmpty);
        // enableCommentSubmitBtn.value = _commentController.text.isNotEmpty;
      });

      return () {
        // _commentController.removeListener(() {});

        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
        _onBackTapped(context, _courseCubit);
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: 'PPT',
              trailingWidget: IconButton(
                icon: Icon(Icons.comment, color: AppTheme.whiteColor),
                tooltip: 'Show Comments',
                onPressed: () {
                  _isCommentsInFullSCreen.value =
                      !_isCommentsInFullSCreen.value;
                },
              ),
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () => _onBackTapped(context, _courseCubit),
            )),
        body: BlocBuilder<CourseListCubit, CourseListState>(
          builder: (context1, state) {
            if (state is CourseCommentsFetched) {
              _isDataLoading.value = false;
              _resourceComments = state.comments;
            } else if (state is CommentsAdded) {
              _isDataLoading.value = false;
              _resourceComments = state.comments;
            }
            return ValueListenableBuilder(
                valueListenable: _isDataLoading,
                builder: (context, valueNotifierAttributeValue, child) {
                  return Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16)),
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: Stack(
                      children: [
                        Positioned(
                          top: 0,
                          right: 0,
                          left: 0,
                          bottom: 0,
                          child: Container(
                            margin: const EdgeInsets.only(
                                left: horizontalMargin,
                                right: horizontalMargin,
                                bottom: horizontalMargin),
                            child: ValueListenableBuilder(
                                valueListenable: _isCommentsInFullSCreen,
                                builder: (context, _, __) {
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Visibility(
                                        visible: !_isCommentsInFullSCreen.value,
                                        child: AnimatedOpacity(
                                          duration: const Duration(
                                              milliseconds:
                                                  500), // Animation duration
                                          opacity:
                                              !_isCommentsInFullSCreen.value
                                                  ? 1.0
                                                  : 0.0, // Smooth fade effect(
                                          child: SizedBox(
                                            height: MediaQuery.of(context)
                                                    .size
                                                    .height /
                                                1.5,
                                            child: Stack(
                                              children: [
                                                webViewController.value != null
                                                    ? NotificationListener<
                                                        ScrollNotification>(
                                                        onNotification:
                                                            (notification) {
                                                          if (notification
                                                              is ScrollUpdateNotification) {
                                                            return notification
                                                                    .metrics
                                                                    .axis ==
                                                                Axis.vertical;
                                                          }
                                                          return false;
                                                        },
                                                        child: WebViewWidget(
                                                            controller:
                                                                webViewController
                                                                    .value!),
                                                      )
                                                    : const SizedBox.shrink(),
                                                Positioned(
                                                  top: 0,
                                                  right: 0,
                                                  left: 0,
                                                  child: Container(
                                                    color: Colors.white,
                                                    height: 60,
                                                    child: _resourceTitle(),
                                                  ),
                                                ),
                                                ValueListenableBuilder<bool>(
                                                  valueListenable: showButton,
                                                  builder:
                                                      (context, value, child) {
                                                    return value
                                                        ? Positioned(
                                                            bottom: 0,
                                                            right: 0,
                                                            left: 0,
                                                            child: Container(
                                                              color:
                                                                  Colors.white,
                                                              height: 60,
                                                              child: _beginAssessmentView(
                                                                  context,
                                                                  _checkPoints
                                                                      .first),
                                                            ),
                                                          )
                                                        : const SizedBox
                                                            .shrink(); // Use SizedBox.shrink() instead of Container() for efficiency
                                                  },
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Visibility(
                                        maintainState: true,
                                        maintainAnimation: true,
                                        visible: !_isCommentsInFullSCreen.value,
                                        child: _resourceDesc(context),
                                      ),
                                      _isCommentsInFullSCreen.value
                                          ? Container()
                                          : Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8.0),
                                              child: UserEngagementRow(
                                                isLiked: _isLiked,
                                                likes: _likes,
                                                likeScale: _likeScale,
                                                confettiController:
                                                    _confettiController,
                                                appDynamicTheme:
                                                    _appDynamicTheme,
                                                commentCount:
                                                    _resourceComments.length,
                                                resourceId:
                                                    courseFileResource.id,
                                                enableLike: !_isCompleted,
                                                onCommentTapped: () =>
                                                    _isCommentsInFullSCreen
                                                            .value =
                                                        !_isCommentsInFullSCreen
                                                            .value,
                                              ),
                                            ),
                                      SizedBox(
                                          height: _resourceComments.isEmpty &&
                                                  _isCommentsInFullSCreen.value
                                              ? 50
                                              : 5),
                                      // _resourceComments.isNotEmpty &&
                                      //         !_isCommentsInFullSCreen.value
                                      //     ? const Divider(
                                      //         thickness: 1,
                                      //         height: 1,
                                      //         color:
                                      //             AppTheme.dividerColorInReview,
                                      //       )
                                      //     : Container(),
                                      // Visibility(
                                      //   visible: _resourceComments.isNotEmpty,
                                      //   child: Center(
                                      //     child: GestureDetector(
                                      //       onTap: () {
                                      //         _isCommentsInFullSCreen.value =
                                      //             !_isCommentsInFullSCreen
                                      //                 .value;
                                      //       },
                                      //       child: Container(
                                      //           margin: const EdgeInsets.only(
                                      //               top: 5),
                                      //           padding:
                                      //               const EdgeInsets.all(5),
                                      //           decoration: BoxDecoration(
                                      //             // color: Colors.grey,
                                      //             borderRadius:
                                      //                 BorderRadius.circular(10),
                                      //           ),
                                      //           child: Text(
                                      //             _isCommentsInFullSCreen.value
                                      //                 ? 'See Less'
                                      //                 : 'See More Comments',
                                      //             style: LMSFonts.mediumFont(
                                      //                 12,
                                      //                 AppTheme
                                      //                     .primaryTextColorBlack,
                                      //                 1.0),
                                      //           )),
                                      //     ),
                                      //   ),
                                      // ),

                                      // CommentFeatureWidget(
                                      //     resourceId: courseFileResource.id,
                                      //     comments: _resourceComments,
                                      //     commentController: _commentController,
                                      //     tabIndex: tabIndex,
                                      //     commentNotifier: _commentNotifier,
                                      //     courseCubit: _courseCubit,
                                      //     isFullScreen:
                                      //         _isCommentsInFullSCreen.value,
                                      //     enableClickOnPlayer: () {}),
                                      Visibility(
                                        visible: _isCommentsInFullSCreen.value,
                                        child: _commentsHead(),
                                      ),
                                      SizedBox(
                                          height: _isCommentsInFullSCreen.value
                                              ? 10
                                              : 0),

                                      Visibility(
                                        visible: _isCommentsInFullSCreen.value,
                                        child: Expanded(
                                          child: _chatListView(
                                              context, _courseCubit),
                                        ),
                                      ),
                                      // const SizedBox(height: 60),
                                    ],
                                  );
                                }),
                          ),
                        ),
                        if (_isDataLoading.value)
                          Center(
                            child: LoadingIndicatorClass(),
                          ),
                        _isCompleted
                            ? Container()
                            : _isCommentsInFullSCreen.value
                                ? _commentBtn(context, _courseCubit)
                                : Container()
                      ],
                    ),
                  );
                });
          },
        ),
      ),
    );
  }

  Future<void> _setResourceActivityLog(BuildContext context,
      {required ResourceActivityStatus status}) async {
    await _userActivityLog.setResourceActivityLog(
        context: context,
        resourceType: 'PPT',
        status: status,
        currentDuration: _currentSlide / courseFileResource.pageCount,
        id: courseFileResource.id,
        result: 'success');
  }

  _onBackTapped(BuildContext context, CourseListCubit _courseCubit) async {
    if (!_isCompleted) {
      await _saveProgressAction(context, _courseCubit);
    }
    await _setResourceActivityLog(context,
        status: _isCompleted
            ? ResourceActivityStatus.completed
            : ResourceActivityStatus.inProgress);
    if (!isFromExamScreen) {
      appRouter.popForced(_currentSlide / courseFileResource.pageCount);
    } else {
      handleBackNavFromScreens(context, navigateBackTo);
    }
  }

  String? _extractSlideValue(String url) {
    Uri uri = Uri.parse(url);
    RegExp regExp = RegExp(r'slide=id\.p(\d+)');
    Match? match = regExp.firstMatch(uri.toString());

    if (match != null && match.groupCount >= 1) {
      return match.group(1);
    }
    return null;
  }

  _saveProgressAction(
      BuildContext context, CourseListCubit _courseCubit) async {
    // if (courseFileResource.progress > 100.0) {
    //   return;
    // }
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

    _courseCubit.setLoading(true);

    await _courseDetailsCubit.setFileProgress(
        courseFileResource.courseId, courseFileResource.id, _currentSlide);
    final state = _courseDetailsCubit.state;

    await _userActivityLog.setResProgressActivityLog(
        context: context,
        screen: 'PPT View',
        resourceType: 'PPT',
        id: courseFileResource.id,
        result: state is SetCourseResProgressState ? 'success' : 'error',
        responseStatus: state is SetCourseResProgressState
            ? 'Progress updated'
            : state is SetCourseResProgressFailureState
                ? state.error
                : 'Progress update failed');

    _courseCubit.setLoading(false);
  }

  _resourceTitle() {
    return Container(
      alignment: Alignment.centerLeft,
      child: Text(
        courseFileResource.name.trim(),
        textAlign: TextAlign.left,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
      ),
    );
  }

  _resourceDesc(BuildContext context) {
    return Container(
        alignment: Alignment.centerLeft,
        child: ValueListenableBuilder(
          valueListenable: readMore,
          builder: (context, value, child) {
            final text = courseFileResource.description;
            final textPainter = TextPainter(
              textDirection: TextDirection.ltr,
              text: TextSpan(
                text: text,
                style: LMSFonts.regularFontWithHeight(
                  14,
                  AppTheme.courseVideoPrimaryTextColor,
                  1.5,
                ),
              ),
              maxLines: 4,
            )..layout(maxWidth: MediaQuery.sizeOf(context).width);

            bool isOverflowing = textPainter.didExceedMaxLines;

            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  text,
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                  style: LMSFonts.regularFontWithHeight(
                    14,
                    AppTheme.courseVideoPrimaryTextColor,
                    1.5,
                  ),
                ),
                const SizedBox(height: 5),
                Align(
                  alignment: Alignment.topRight,
                  child: Visibility(
                      visible: isOverflowing,
                      child: InkWell(
                          onTap: () {
                            readMore.value = !readMore.value;
                          },
                          child: Text(
                            value ? "View less" : "View more",
                            style: LMSFonts.regularFontWithHeight(
                                10, AppTheme.nextBtnColor, 1),
                          ))),
                )
              ],
            );
          },
        ));
  }

  _beginAssessmentView(BuildContext context, CheckPoint item) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Complete the Quiz to proceed to the next Topic.',
            style: LMSFonts.mediumFont(14, AppTheme.reportQstnSD, 1.0),
          ),
        ),
        _beginAssessmentBtn(context, item)
      ],
    );
  }

  _beginAssessmentBtn(BuildContext context, CheckPoint item) {
    return ButtonWidget(
        child: Text(
          'Begin Assessment',
          style: LMSFonts.mediumFont(14, AppTheme.whiteTextColor, 1.0),
        ),
        minimumSize: const Size(100, 45),
        color: AppTheme.primaryOrange,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        onPressed: () {
          // show checkpoint
          _showCheckPointInfoPopup(context, item);
        });
  }

  Future<void> _checkSlideAndShowPopup(BuildContext context, String url,
      int? startSlide, CheckPoint item) async {
    final slideIdRegex = RegExp(r"slide=id\.(p\d+)");
    final match = slideIdRegex.firstMatch(url);

    if (startSlide == null || match == null) {
      return;
    }

    final groupValue = match.group(1); // Extract group value safely
    if (groupValue != null && groupValue.length > 1) {
      final slideNumber =
          int.parse(groupValue.substring(1)); // Extract substring safely

      if (courseFileResource.pageCount != slideNumber) {
        if (slideNumber == startSlide + 1) {
          //  _showPopup(context, "You have reached the check point!");
          _showCheckPointInfoPopup(context, item);
        }
      } else {
        showButton.value = startSlide == courseFileResource.pageCount &&
            item.startPage == courseFileResource.pageCount;
      }
    }
  }

  // checkpoint
  // custom popup opens depending on the type of milestone
  void _showCheckPointInfoPopup(BuildContext context, CheckPoint item) {
    _showExamCheckpointPopup(context, item);
  }

  _showExamCheckpointPopup(BuildContext context, CheckPoint item) {
    return showDialog(
        context: context,
        builder: (context1) => PopScope(
              canPop: false,
              child: SingleActionDialogue(
                title: '',
                message: SLStrings.getTranslatedString(
                    KEY_CHECKPOINT_EXAM_NAVIGATION_MSG),
                buttonText: SLStrings.getTranslatedString(KEY_OK),
                isDefaultAction: false,
                handleOkCallback: () {
                  _courseCubitObj?.setQuestionLoading(true);

                  Navigator.of(context1).pop();
                  _fetchExamQuestions(context, item.instanceId ?? "", item);
                },
              ),
            ));
  }

  _fetchExamQuestions(
      BuildContext context, String examId, CheckPoint item) async {
    var route = ModalRoute.of(_scaffoldKey.currentState?.context ?? context);
    final _examCubit = BlocProvider.of<ExamCubit>(context);
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    await _examCubit.startCheckpointQuiz(item.checkpointId ?? '');
    ExamState state = _examCubit.state;

    // User activity logging
    final isSuccess = state is ExamQuestionsFetched &&
        state.examData.isNotEmpty &&
        state.examData.first.questions.isNotEmpty &&
        state.examData.first.quizAttemptId != null;

    final logResult = isSuccess ? 'success' : 'error';

    final actionDetails = isSuccess
        ? 'Exam questions fetched'
        : state is ExamPGException
            ? (state).error
            : state is ExamsQuestionsFailureState
                ? (state).error
                : 'Exam fetch failure';

    final actionComment = isSuccess
        ? 'Questions fetched at ${DateTime.now()}'
        : 'Fetch failed at ${DateTime.now()}';

    final reqJson = {
      'activity_type': 'Checkpoint',
      'screen_name': 'PPT View',
      'action_details': actionDetails,
      'target_id': item.checkpointId ?? '',
      'action_comment': actionComment,
      'log_result': logResult,
    };

    await _appConfigCubit.setUserActivityLog(reqJson);

    // Handling different states
    if (state is ExamPGException) {
      _courseCubitObj?.setQuestionLoading(false);
      _showPGExceptionPopup(
          _scaffoldKey.currentState?.context ?? context, state.error);
    } else if (state is ExamQuestionsFetched) {
      if (state.examData.isNotEmpty) {
        _questionData = state.examData.first;
      }

      _questions = state.examData.isEmpty ? [] : state.examData.first.questions;

      if (_questions.isNotEmpty &&
          _questionData != null &&
          _questionData?.quizAttemptId != null) {
        _courseCubitObj?.setQuestionLoading(false);

        await appRouter.push(ExamViewRoute(
          examDuration: _questionData?.duration ?? 0,
          shouldShowTimer: true,
          isFromViewResult: false,
          quizAttemptId: _questionData?.quizAttemptId ?? '',
          questionData: _questionData!,
          isFromVideoView: true,
          isFromPptView: true,
          checkPoints: checkPoints,
          navigateBackTo: navigateBackTo,
        ));
      } else {
        _courseCubitObj?.setQuestionLoading(false);
        if (route != null && route.isActive) {
          _showQuestionsEmptyPopup(
              _scaffoldKey.currentState?.context ?? context);
        }
      }
    }
  }

  _showQuestionsEmptyPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        // return CustomAlertDialog(title: SORRY, message: QUESTION_NOT_AVAILABLE);
        return PopScope(
          canPop: false,
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: SLStrings.getTranslatedString(KEY_QUESTION_NOT_AVAILABLE),
            cancelBtnText: "",
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
            alertType: AlertType.error,
            onCancelTap: () {},
            onContinueTap: () {
              handleBackNavFromScreens(context, navigateBackTo);
            },
          ),
        );
      },
    );
    //.then((value) => enableClickOnPlayer());
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            alertType: AlertType.error,
            onCancelTap: () => appRouter.popForced(),
            onContinueTap: () {
              // isPlayerClosed.value = true;
              // if (kIsWeb) {
              //   Navigator.of(context).pop();
              //   Beamer.of(context).beamBack();
              // } else {
              //   appRouter.popUntil((route) =>
              //       route.settings.name == CourseDetailsDashboardRoute.name);
              // }
            },
            cancelBtnText: '',
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
          ),
        );
      },
    );
    //.then((value) => enableClickOnPlayer());
  }

  ///
  /// comments
  ///

  Widget _commentsHead() {
    return Visibility(
      visible: _resourceComments.isNotEmpty && courseFileResource.id.isNotEmpty,
      child: Container(
        child: Text(
          SLStrings.getTranslatedString(KEY_VIDEO_COMMENTS),
          style:
              LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
        ),
      ),
    );
  }

  Widget _commentsTabItem(
    String label,
    bool isActive,
  ) {
    return Container(
        height: 35,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isActive ? Colors.white : Colors.transparent,
        ),
        width: double.infinity / 1,
        margin: const EdgeInsets.symmetric(vertical: 2),
        child: Tab(text: label));
  }

  Widget _chatListView(BuildContext mainContext, CourseListCubit courseCubit) {
    int feedbackCount = _resourceComments
        .where((element) => element.commentType == CommentType.FEEDBACK)
        .toList()
        .length;

    int suggestionCount = _resourceComments
        .where((element) => element.commentType == CommentType.SUGGESTION)
        .toList()
        .length;
    return Visibility(
      visible: courseFileResource.id.isNotEmpty && _resourceComments.isNotEmpty,
      child: ValueListenableBuilder<int>(
        valueListenable: tabIndex,
        builder: (context, updatedTabIndex, __) {
          return DefaultTabController(
            length: 2,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade300,
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: AppTheme.courseVideoPrimaryTextColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: AppTheme.transparentColor,
                    padding: EdgeInsets.zero,
                    tabs: [
                      _commentsTabItem(
                          'Feedback ($feedbackCount)', tabIndex.value == 0),
                      _commentsTabItem(
                          'Suggestion ($suggestionCount)', tabIndex.value == 1),
                    ],
                    onTap: (index) {
                      tabIndex.value = index;
                      _commentNotifier.value = _commentNotifier.value.copyWith(
                        selectedCommentType:
                            index == 0 ? 'Feedback' : 'Suggestion',
                      );
                    },
                  ),
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: IndexedStack(
                    index: updatedTabIndex,
                    children: [
                      _buildCommentList(
                        mainContext,
                        courseCubit,
                        _resourceComments
                            .where((comment) =>
                                comment.commentType == CommentType.FEEDBACK)
                            .toList(),
                      ),
                      _buildCommentList(
                        mainContext,
                        courseCubit,
                        _resourceComments
                            .where((comment) =>
                                comment.commentType == CommentType.SUGGESTION)
                            .toList(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCommentList(BuildContext mainContext,
      CourseListCubit courseCubit, List<ResourseComment> comments) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(comments.length, (index) {
        final comment = comments[index];
        return Column(
          children: [
            _chatItem(mainContext, courseCubit, comment),
            if (comment.children != null)
              Padding(
                padding: const EdgeInsets.only(left: 34),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(
                    comment.children!.length,
                    (childIndex) => _chatItem(mainContext, courseCubit,
                        comment.children![childIndex]),
                  ),
                ),
              ),
          ],
        );
      }),
    );
  }

  Widget _chatItem(BuildContext mainContext, CourseListCubit courseCubit,
      ResourseComment commentObj) {
    print("commentObj.profilePic------>${commentObj.profilePic}");
    return Card(
      color: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          commentObj.profilePic != null
              ? _profileIcon(commentObj.profilePic)
              : const SizedBox.shrink(),
          const SizedBox(width: 14),
          _userCommentInfo(mainContext, courseCubit, commentObj),
          const SizedBox(width: 10),
          _commentTypeDateInfo(commentObj),
          const SizedBox(width: 5),
        ],
      ),
    );
  }

  Widget _profileIcon(String avatar) {
    return Container(
      height: 40,
      width: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          width: 2,
          color: AppTheme.whiteColor,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.blackColor.withOpacity(0.1),
            blurRadius: 1,
            spreadRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: CircleAvatar(
        backgroundColor: AppTheme.transparentColor,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(5.0),
          child: avatar != null && avatar.trim().isNotEmpty
              ? CachedNetworkImage(
                  imageUrl: avatar,
                  fit: BoxFit.cover,
                  placeholder: (context, url) =>
                      const CircularProgressIndicator(),
                  errorWidget: (context, url, error) => _placeHolderWidget(),
                )
              : _placeHolderWidget(),
        ),
      ),
    );
  }

  Widget _placeHolderWidget() {
    return const ProfilePlaceholderWidget(maxWidth: 44 * 2);
  }

  Widget _userCommentInfo(BuildContext mainContext, CourseListCubit courseCubit,
      ResourseComment commentObj) {
    return Expanded(
      child: Container(
        color: Colors.transparent,
        child: Align(
          alignment: Alignment.topLeft,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // const SizedBox(height: 2),
              Text(
                commentObj.user,
                style: LMSFonts.mediumFont(
                    14, AppTheme.courseVideoPrimaryTextColor, 1.2),
              ),
              const SizedBox(height: 4),
              Text(
                commentObj.message.toString().trim(),
                style: LMSFonts.regularFontWithHeight(
                    12, AppTheme.courseVideoPrimaryTextColor, 1.2),
              ),
              const SizedBox(height: 5),
              commentObj.roleName == RoleName.ADMIN
                  ? GestureDetector(
                      onTap: () {
                        //reply action

                        _showCommentBox(mainContext, courseCubit,
                            parentId: commentObj.parentId,
                            roleName: commentObj.roleName);
                      },
                      child: Container(
                        color: Colors.transparent,
                        child: Row(
                          children: [
                            const Icon(
                              Icons.reply,
                              color: AppTheme.coursePecentageTxtGrey,
                              size: 16,
                            ),
                            Text(
                              ' Reply',
                              style: LMSFonts.regularFontWithHeight(
                                  12, AppTheme.coursePecentageTxtGrey, 1.2),
                            ),
                          ],
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
              SizedBox(height: commentObj.commentedTime != null ? 5 : 0),
            ],
          ),
        ),
      ),
    );
  }

  Widget _commentTypeDateInfo(ResourseComment commentObj) {
    return Container(
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const SizedBox(height: 2),
          commentStatusIcon(commentObj.commentStatus),
          const SizedBox(height: 7),
          Text(
            commentObj.commentedTime != null
                ? AppDateFormatter().formatDatedmmmmy(commentObj.commentedTime!)
                : '',
            style: LMSFonts.regularFontWithHeight(
                12, AppTheme.courseCommentDate, 1.2),
          ),
        ],
      ),
    );
  }

  Widget commentStatusIcon(CommentStatus commentStatus) {
    IconData iconData;
    Color iconColor;
    print("commentStatus.name: ${commentStatus.name}");

    switch (commentStatus.name.toLowerCase()) {
      case 'approved':
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case 'pending':
        iconData = Icons.hourglass_empty;
        iconColor = Colors.orange;
        break;
      case 'rejected':
        iconData = Icons.cancel;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.help_outline;
        iconColor = Colors.grey;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 20,
    );
  }

  Widget _commentBtn(BuildContext context, CourseListCubit courseCubit) {
    return Positioned(
      bottom: 8,
      right: 8,
      child: BlocBuilder<ConnectivityCubit, ConnectivityState>(
        builder: (context, state) {
          return IconButton(
              iconSize: 50,
              icon: Image.asset('$ASSETS_PATH/comment_icon.png'),
              onPressed: state is! InternetConnected
                  ? null
                  : () async {
                      _commentController.clear();
                      // disableClickOnPlayer();
                      _showCommentBox(context, courseCubit,
                          parentId: null, roleName: null);
                    });
        },
      ),
    );
  }

  _showCommentBox(BuildContext mainContext, CourseListCubit courseCubit,
      {required String? parentId, required RoleName? roleName}) {
    showDialog(
        context: mainContext,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return BlocBuilder<CourseListCubit, CourseListState>(
            builder: (context, state) {
              // if (state is CommentTypeSelected) {
              //   commentType = state.commentType;
              // }
              return ValueListenableBuilder<AddCommentFunc>(
                  valueListenable: _commentNotifier,
                  builder: (context, commentNotifierObj, _) {
                    return AddCommentDialouge(
                      commentController: _commentController,
                      courseCubit: courseCubit,
                      selectedType: _commentNotifier
                              .value.selectedCommentType.isEmpty
                          ? (tabIndex.value == 0 ? 'Feedback' : 'Suggestion')
                          : commentNotifierObj.selectedCommentType,
                      enableSubmitBtn: commentNotifierObj.enableSubmitBtn,
                      enableDropdown:
                          parentId == null, // the comment is not a reply
                      onSubmit: () async {
                        if (commentNotifierObj.enableSubmitBtn) {
                          _commentNotifier.value = commentNotifierObj.copyWith(
                            enableSubmitBtn: false,
                          );

                          await _addNewComment(
                            context,
                            courseCubit,
                            _commentController.text.trim(),
                            parentId: parentId,
                            roleName: roleName,
                          );
                          _commentController.clear();
                        }
                      },
                      onTypeSelected: (val) {
                        _commentNotifier.value = commentNotifierObj.copyWith(
                          selectedCommentType: val,
                        );
                        // courseCubit.handleCommentTypeSelection(val);
                        // courseCubit.emit(
                        //     CommentTypeSelected(commentType: commentNotifierObj.selectedCommentType));
                      },
                      onChanged: (val) {
                        _commentNotifier.value = commentNotifierObj.copyWith(
                            enableSubmitBtn:
                                _commentController.text.trim().isNotEmpty);
                        // enableCommentSubmitBtn.value =
                        //     _commentController.text.trim().isNotEmpty;
                      },
                      onClose: () {
                        Navigator.of(context).pop();
                      },
                    );
                  });
            },
          );
        });
  }

  _fetchComments(BuildContext context, CourseListCubit cubit) async {
    bool _hasAccess = true; // await _checkAccessToGetComments();
    if (_hasAccess) {
      String instanceId = courseFileResource.id;
      await cubit.setLoading(true);
      await cubit.fetchCommentsForId(instanceId);
      CourseListState state = cubit.state;
      if (state is CourseCommentsFetched) {
        _resourceComments = state.comments
            .where(
                (element) => element.resActivityTpe == ResActivityType.comment)
            .toList();

        _resourceLikes = state.comments
            .where((element) => element.resActivityTpe == ResActivityType.like)
            .toList();
        int likeCount = _resourceLikes.length;
        _isLiked.value = _resourceLikes.any((like) =>
            like.user ==
            SLUser.shared.first_name + ' ' + SLUser.shared.last_name);

        _likes.value = likeCount;
      } else if (state is CommentsError) {
        _showCommentError(context, cubit, state.error);
      }
      await cubit.setLoading(false);
    } else {
      // no access to fetch comments
    }
  }

  _addNewComment(BuildContext context, CourseListCubit cubit, String comment,
      {required String? parentId, required RoleName? roleName}) async {
    /// locally update first
    /// upload to server in background
    ///
    final _appConfigCubit = BlocProvider.of<AppConfigCubit>(context);
    String commentType = _commentNotifier.value.selectedCommentType;

    try {
      ResourseComment _studentComment = ResourseComment(
          commentId: '',
          parentId: parentId ?? '',
          message: comment,
          subject: TOPIC_NAME,
          commentType: CommentType.values.firstWhere((e) =>
              e.toString().split('.').last.toLowerCase() ==
              commentType.toLowerCase()),
          user: SLUser.shared.first_name + ' ' + SLUser.shared.last_name,
          commentedTime: DateTime.now(),
          profilePic: SLUser.shared.avatar_url,
          commentStatus: CommentStatus.PENDING,
          roleName: SLUser.shared.userRole ?? RoleName.STUDENT,
          resActivityTpe: ResActivityType.comment);

      Map<String, dynamic> jsonReqBody = parentId != null
          ? {
              "instance_id": courseFileResource.id,
              "user_id": USER_ID,
              "comment_data": {
                "parent_id": parentId,
                "subject": TOPIC_ID,
                "message": comment,
                "type": commentType,
                'activity_type': ResActivityType.comment.name
              }
            }
          : {
              "instance_id": courseFileResource.id,
              "user_id": USER_ID,
              "comment_data": {
                "subject": TOPIC_ID,
                "message": comment,
                "type": commentType,
                'activity_type': ResActivityType.comment.name
              }
            };

      await cubit.setLoading(true);
      await cubit.uploadComment(jsonReqBody);
      CourseListState state = cubit.state;
      Navigator.pop(context);

      ///
      /// activity log
      ///
      final isSuccess = state is CourseCommentsUploaded;
      final logResult = isSuccess ? 'success' : 'error';
      final actionDetails = isSuccess
          ? 'Comment added'
          : state is CommentsError
              ? state.error
              : 'Add comment failure';
      final actionComment = isSuccess
          ? 'Commented at ${DateTime.now()}'
          : 'Comment failed at ${DateTime.now()}';

      final reqJson = {
        'activity_type': 'Course_Resource',
        'screen_name': 'PPT View',
        'action_details': actionDetails,
        'target_id': courseFileResource.id,
        'action_comment': actionComment,
        'log_result': logResult,
      };

      await _appConfigCubit.setUserActivityLog(reqJson);

      ///

      if (state is CommentsError) {
        _showCommentError(context, cubit, state.error);
      } else {
        _commentController.clear();
        parentId = null;
        roleName = null;
        await cubit.addComment(_studentComment, _resourceComments);
        cubit.setLoading(false);
        _showSubmissionDialgoue(context);
      }
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  _showCommentError(BuildContext context, CourseListCubit cubit, String error) {
    //_isDataLoading.value = false;
    cubit.setLoading(false);
    showPGExceptionPopup(context, error);
  }

  // Function to show the dialogue
  void _showSubmissionDialgoue(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: SingleActionDialogue(
            title: '',
            message: SLStrings.getTranslatedString(KEY_COMMENT_SUBMITTED),
            iconWidget: const Icon(Icons.check_circle, size: 60),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: false,
            handleOkCallback: () {
              Navigator.of(context).pop();
            },
          ),
        );
      },
    );
  }
}
