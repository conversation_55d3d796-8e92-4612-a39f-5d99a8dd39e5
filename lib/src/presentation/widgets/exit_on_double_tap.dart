import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ExitOnDoubleTap extends StatelessWidget {
  final Widget child;
  final GlobalKey<ScaffoldState>? scaffoldKey;
  const ExitOnDoubleTap({super.key, required this.child, this.scaffoldKey});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        if (didPop) {
          return;
        }
        
        final scaffoldState = scaffoldKey?.currentState;
        if (scaffoldState != null) {
          scaffoldState.closeDrawer();
        }
/*
        if (_firstBackPressTime == null ||
            currentTime.difference(_firstBackPressTime) >
                const Duration(seconds: 1)) {
          _firstBackPressTime = currentTime;

          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text(EXIT_APP_MSG),
            duration: Duration(milliseconds: 120),
          ));

          if (context.mounted) {
            Get.to(ExitOnDoubleTap(
              scaffoldKey: scaffoldKey,
              child: child,
            ));
          }

          return Future.value(true);
        }*/

        SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      },
      child: child,
    );
  }
}
