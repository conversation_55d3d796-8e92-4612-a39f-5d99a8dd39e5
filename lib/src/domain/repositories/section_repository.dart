import '../models/course_dashboard/course_file_resource.dart';
import '/src/domain/models/check_point_data/check_point_data.dart';
import '../models/course_dashboard/course_page_resource.dart';
import '../models/course_dashboard/course_video_resource.dart';
import '../models/responses/section_details.dart';

abstract class SectionRepository {
  Future<SectionDetails> fetchSectionDetailsInfo(String sectionId);

  // Study Material View
// Fetch Page Content
  Future<CoursePageResource?> fetchPageContent(Map<String, dynamic> jsonReq);

  // Fetch Video info
  Future<CourseVideoResource?> fetchResourceVideoInfo(
      Map<String, dynamic> jsonReq);
  Future<CheckPointData?> fetchVideoCheckPointInfo(String moduleId);

  Future<CourseFileResource?> fetchResourceImageInfo(
      Map<String, dynamic> jsonReq);
  Future<dynamic> getCourseProgress(String instance);
}
