import 'dart:async';

import '/src/utils/constants/strings.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '/src/domain/models/live_class_model.dart';
import '/src/domain/repositories/live_class_list_repository.dart';

part 'livelist_state.dart';

class LivelistCubit extends Cubit<LivelistState> {
  final LiveListRepository _repository;
  Timer? _liveStatusTimer;
  Timer? _countdownTimer;
  List<LiveClassModel> _liveClasses = [];

  LivelistCubit(this._repository) : super(LivelistInitial());

  @override
  Future<void> close() {
    _liveStatusTimer?.cancel();
    _countdownTimer?.cancel();
    return super.close();
  }

  Future<void> fetchLiveClasses() async {
    try {
      if (ORG_ID.isEmpty) {
        return;
      }
      emit(LivelistLoading());
      final params = {'org_id': ORG_ID};

      final classes = await _repository.fetchLiveClassList(params);
      _liveClasses = classes
        ..sort((a, b) => a.startDate.compareTo(b.startDate));

      emit(LivelistLoaded(_liveClasses));
      _startLiveClassStatusUpdater();
    } on Exception catch (e) {
      emit(LivelistError(e.toString()));
    }
  }

  void _startLiveClassStatusUpdater() {
    _liveStatusTimer?.cancel(); // Cancel previous timer if any

    _liveStatusTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      final now = DateTime.now();

      final updatedClasses = _liveClasses.map((liveClass) {
        final isLive =
            now.isAfter(liveClass.startDate) && now.isBefore(liveClass.endDate);

        final isEnded = now.isAfter(liveClass.endDate);

        print("liveclasslistupdated!!!!!!!!!!!!!!!! at $now");

        if (isLive &&
            liveClass.currentMeetingStatus != CurrentMeetingStatus.LIVE) {
          return liveClass.copyWith(
              currentMeetingStatus: CurrentMeetingStatus.LIVE);
        } else if (isEnded &&
            liveClass.currentMeetingStatus != CurrentMeetingStatus.ENDED) {
          return liveClass.copyWith(
              currentMeetingStatus: CurrentMeetingStatus.ENDED);
        } else {
          return liveClass;
        }
      }).toList();

      _liveClasses = updatedClasses;
      final filteredLiveClasses = _liveClasses
          .where((liveClass) =>
              liveClass.meetingId != null &&
              liveClass.currentMeetingStatus != CurrentMeetingStatus.ENDED)
          .toList();
      emit(LivelistLoaded(filteredLiveClasses));
    });
  }

  // void _startCountdownIfNeeded(DateTime startDate) {
  //   final now = DateTime.now();
  //   final difference = startDate.difference(now);
  //   String _countdownText = "";

  //   if (difference.inSeconds > 0 && difference.inSeconds <= 60) {
  //     _countdownText = difference.inSeconds.toString();
  //     _countdownTimer?.cancel(); // Cancel previous timer if any
  //     _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
  //       final remaining = startDate.difference(DateTime.now());
  //       if (remaining.inSeconds <= 0) {
  //         _countdownText = "Starting now!";

  //         timer.cancel();
  //       } else {
  //         _countdownText = remaining.inSeconds.toString();
  //       }
  //     });
  //   }
  // }
}
