import 'dart:ui';

import 'package:SmartLearn/src/utils/constants/nums.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/models/subscription_plan/plan_course_result.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/strings.dart';
import '../widgets/app_bar.dart';

@RoutePage()
class SubscriptionInfoViewScreen extends HookWidget
    with WidgetsBindingObserver {
  final String planName;
  final List<PlanCourse> planInfo;
  SubscriptionInfoViewScreen(
      {required this.planName, required this.planInfo, super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBarWidget(
          title: planName, // SLStrings.getTranslatedString(KEY_SUBSCRIPTION),
          trailingWidget: Container(),
          leadingIconName: BACK_ARROW,
          leadingBtnAction: () {
            kIsWeb ? Beamer.of(context).beamBack() : appRouter.popForced();
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16), topRight: Radius.circular(16)),
        ),
        child: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
          return Align(
            child: Container(
              height: constraints.maxHeight,
              width: constraints.maxWidth < 720 ? constraints.maxWidth : 800,
              padding: const EdgeInsets.only(left: 15, right: 15, top: 15),
              child: ScrollConfiguration(
                behavior: ScrollConfiguration.of(context).copyWith(
                  dragDevices: {
                    PointerDeviceKind.touch,
                    PointerDeviceKind.mouse,
                  },
                ),
                child: Column(
                  children: [
                    // Courses Included:
                    _introText(),
                    // Course Name
                    _coursesListView(),
                    // Tab view- Resources/Videos
                    // Resource Name
                    // Desc.
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _titleWidget() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        SLStrings.getTranslatedString(KEY_ABOUT_COURSE),
        style: LMSFonts.semiBoldFont(18, AppTheme.primaryTextColorBlack),
      ),
    );
  }

  Widget _courseDesc(PlanCourse courseInfo) {
    // return Align(
    //   alignment: Alignment.centerLeft,
    //   child: Text(
    //     'The courses included in this plan are:',
    //     style: LMSFonts.mediumFont(14, AppTheme.primaryTextColorBlack, 1.0),
    //   ),
    // );

    return _resourceDescWidget(courseInfo.courseDesc ?? '');
  }

  Widget _introText() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        'The courses included in this plan are:',
        style: LMSFonts.mediumFont(14, AppTheme.primaryTextColorBlack, 1.0),
      ),
    );
  }

  Widget _coursesListView() {
    return Expanded(
      child: ListView.builder(
          itemCount: planInfo.length,
          padding: const EdgeInsets.only(top: horizontalMargin),
          itemBuilder: (BuildContext context, int index) {
            return _courseInfoWidget(planInfo[index]);
          }),
    );
  }

  Widget _courseInfoWidget(PlanCourse courseInfo) {
    return Container(
      margin: const EdgeInsets.only(bottom: horizontalMargin),
      child: Column(
        children: [
          _nameWidget('course.png', (courseInfo.courseName ?? ''),
              AppTheme.sidemenuBGColor),
          const SizedBox(height: horizontalMargin),
          // About Course
          // _titleWidget(),
          // const SizedBox(height: 5),
          // Course Desc
          _courseDesc(courseInfo),
          // const SizedBox(height: 5),
          _courseResourceList(courseInfo),
          _courseVideoList(courseInfo),
        ],
      ),
    );
  }

  Widget _nameWidget(String assetName, String title, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Image.asset(
          '$ASSETS_PATH/$assetName',
          height: 25,
          width: 25,
          color: color,
        ),
        const SizedBox(width: 5),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              title,
              style: LMSFonts.mediumFont(15, color, 1.0),
            ),
          ),
        )
      ],
    );
  }

  Widget _courseResourceList(PlanCourse courseInfo) {
    List<Widget> _resourceWidgets = [];
    List<Resource> resourcses = courseInfo.resources ?? [];
    for (var resource in resourcses) {
      Widget _resourceView = _courseResourceDetailsWidget(
          resource.name ?? '', 'file_light.png', resource.description ?? '');
      _resourceWidgets.add(_resourceView);
    }
    return Column(
      children: _resourceWidgets,
    );
  }

  Widget _courseResourceDetailsWidget(
      String name, String asset, String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: horizontalMargin),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _nameWidget(asset, name, AppTheme.secondaryAppColor),
          // _resourceDescWidget(description),
        ],
      ),
    );
  }

  Widget _resourceDescWidget(String desc) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: HtmlWidget(
        desc,
        onTapUrl: (url) => launchSelectedUrl(url),
      ),
    );
  }

  Widget _courseVideoList(PlanCourse courseInfo) {
    List<Widget> _resourceWidgets = [];
    List<ResourcesIntro> resourcses = courseInfo.intro ?? [];
    for (var resource in resourcses) {
      Widget _resourceView = _courseResourceDetailsWidget(
          resource.intro_name ?? '', 'video_light.png', '');
      _resourceWidgets.add(_resourceView);
    }
    return Column(
      children: _resourceWidgets,
    );
  }
}
