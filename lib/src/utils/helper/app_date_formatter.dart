import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';

class AppDateFormatter {
  /// 20-June-2023
  String formatDatedMy(DateTime date) {
    final DateFormat formatter = DateFormat('dd-MMM-yyyy');
    return formatter.format(date);
  }

  /// June 20
  String formatDateMd(DateTime date) {
    final DateFormat formatter = DateFormat('MMMM dd');
    return formatter.format(date);
  }

  /// June 2023
  String formatDateMMMMyyyy(DateTime date) {
    final DateFormat formatter = DateFormat('MMMM yyyy');
    return formatter.format(date);
  }

  /// 20 June 2023 12:00 AM/PM
  String formatToDateTimeString(DateTime date) {
    final DateFormat formatter = DateFormat('dd MMM yyyy hh:mm a');
    return formatter.format(date);
  }

  /// 20th June 2023
  String formatDatedmmmmy(DateTime date) {
    final DateFormat formatter = DateFormat('d MMMM y');
    return formatter.format(date);
  }

  // yyyy/MM/dd
  String formatDateyyyyMMdd(DateTime date) {
    String formattedDate = DateFormat('yyyy/MM/dd').format(date);
    return formattedDate;
  }

//
  String formatJiffydoMy(DateTime date) {
    String formattedDate =
        Jiffy.parse(date.toString()).format(pattern: 'do MMM yyyy');
    return formattedDate;
  }

  /// November 14, 2023, at 2:30 PM
  String formatForExamAvailabilityPopup(DateTime date) {
    String formattedTime = DateFormat.yMMMMd().add_jm().format(date);
    return formattedTime;
  }

  /// 24 Jun 2023
  String formatForCurrentAffairs(DateTime date) {
    String formattedDate = DateFormat('d MMM yyyy').format(date);
    return formattedDate;
  }
}
