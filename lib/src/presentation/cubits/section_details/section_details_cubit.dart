import 'dart:io';
import 'package:get/utils.dart';

import '../../../config/app_config/api_constants.dart';
import '../../../domain/models/course_dashboard/course_file_resource.dart';
import '../../../domain/models/course_dashboard/course_page_resource.dart';
import '../../../domain/models/course_dashboard/course_video_resource.dart';
import '../../../utils/constants/helper.dart';
import '../../../utils/constants/strings.dart';
import '../course_list/course_list_cubit.dart';
import '/src/domain/repositories/user_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../domain/models/check_point_data/check_point_data.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../../../domain/models/course_progress.dart';
import '../../../domain/models/responses/section_details.dart';
import '../../../domain/repositories/course_respository.dart';
import '../../../domain/repositories/section_repository.dart';

part 'section_details_state.dart';

class SectionDetailsCubit extends Cubit<SectionDetailsState> {
  final SectionRepository _sectionRepository;
  final CourseRepository _courseRepository;
  final UserRepository _userRepository;
  SectionDetailsCubit(
      this._sectionRepository, this._courseRepository, this._userRepository)
      : super(const SectionDetailsInitial());
  bool loading = false;

  late SectionDetails sectionDetails;
  CourseVideoResource? courseVideoObj;
  CourseFileResource? courseFileobj;
  CheckPointData? checkPointData;

  bool setValue = false;

  /// to set the state to initial state
  /// during section details to study material navigation,
  /// last state change was to SectionDetailsLoading
  /// so in order to reset the state to initial
  ///
  initCubit() {
    emit(const SectionDetailsInitial());
  }

  fetchSectionDetails(String sectionId) async {
    try {
      sectionDetails =
          await _sectionRepository.fetchSectionDetailsInfo(sectionId);
      List<Modules> modules = sectionDetails.modules;
      List<Folder> folders = sectionDetails.folders;

      modules = _updateModules(sectionDetails.modules);

      for (var i = 0; i < sectionDetails.folders.length; i++) {
        List<Modules> folderModules =
            _updateModules(sectionDetails.folders[i].folderModules);
        folders[i].folderModules = folderModules;
      }

      sectionDetails.modules = modules;
      sectionDetails.folders = folders;
      emit(SectionDetailsFetched(sectionDetailsData: sectionDetails));
      // return;
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][fetchSectionDetails] :${e.toString()}");
      if (e.code == TOKEN_EXPIRED_STATUS_CODE) {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          fetchSectionDetails(sectionId);
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      } else {
        String errorMsg = e.details;
        emit(SectionDetailsError(error: errorMsg));
      }
    } on Exception catch (e) {
      emit(SectionDetailsError(error: e.toString()));
    }
  }

  List<Modules> _updateModules(List<Modules> modules) {
    List<Modules> modulesUpdated = modules;
    final hasInProgressItems = modules.any(
      (e) => (e.isEnabled && e.progress < 100) && !e.isSkipped,
    );

    final firstInProgressItem = modules.firstWhereOrNull(
      (e) => !e.isEnabled && e.progress < 100 && !e.isSkipped,
    );
    final firstInProgressIndex =
        firstInProgressItem != null ? modules.indexOf(firstInProgressItem) : -1;

    if (firstInProgressIndex != -1) {
      modulesUpdated[firstInProgressIndex].isEnabled = !hasInProgressItems;
    }
    return modulesUpdated;
  }

  setLoading(bool loadStatus) {
    if (loadStatus) {
      emit(SectionDetailsListLoading(isDataLoading: loadStatus));
    } else {
      emit(const SectionDetailsListLoading(isDataLoading: false));
    }
  }

  Future<void> changeStatus(int index) async {
    emit(SectionDetailschangeStatus(index: index));
  }

  Future<void> expanding(bool value, int index) async {
    if (value == true) {
      emit(SectionDetailsExapanding(index: index, expand: true));
    } else {
      emit(SectionDetailsExapanding(index: index, expand: false));
    }
  }

//
// Section Details UI Enhancement
//
  Future<void> stepItemTapped(int currentResourceStep, int currentFolderStep,
      int currentFolderResourceStep) async {
    emit(StepItemTapState(
      currentResourceStep: currentResourceStep,
      currentFolderStep: currentFolderStep,
      currentFolderResourceStep: currentFolderResourceStep,
    ));
  }

// Study Material View
// Fetch Page Content

  fetchPageContent(String instanceId, String moduleId) async {
    try {
      if (ORG_ID.isEmpty ||
          USER_ID.isEmpty ||
          instanceId.isEmpty ||
          moduleId.isEmpty) {
        return;
      }

      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "page_id": instanceId,
        "course_module_id": moduleId,
      };
      CoursePageResource? content =
          await _sectionRepository.fetchPageContent(jsonReq);
      if (content != null) {
        emit(PageContentsFetched(pageContentData: content));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][fetchPageContent] :${e.toString()}");
      String errorMsg = e.details;
      emit(PageDetailsError(error: errorMsg));
    } on Exception catch (e) {
      emit(PageDetailsError(error: e.toString()));
    }
  }

// PDF View
  Future<String> getFileFromUrl(String url) async {
    try {
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);
      var dir = await getApplicationDocumentsDirectory();

      File file = File("${dir.path}/mypdfonline.pdf");

      File urlFile = await file.writeAsBytes(bytes, flush: true);
      String path = urlFile.path;

      emit(PdfViewer(path: path));
      return path;
    } on Exception catch (e) {
      throw Exception("Error opening url file");
    }
  }

  fetchVideoContent(String instanceId, String moduleId) async {
    try {
      if (ORG_ID.isEmpty ||
          USER_ID.isEmpty ||
          instanceId.isEmpty ||
          moduleId.isEmpty) {
        return;
      }

      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "url_id": instanceId,
        "course_module_id": moduleId,
        "user_id": USER_ID
      };

      courseVideoObj = await _sectionRepository.fetchResourceVideoInfo(jsonReq);
      if (courseVideoObj != null) {
        if (courseVideoObj!.isCheckpointEnabled == true) {
          checkPointData = await _sectionRepository
              .fetchVideoCheckPointInfo(courseVideoObj!.courseModuleId!);
          courseVideoObj!.checkpoints = checkPointData?.checkPoints ?? [];
          emit(VideoResourceFetched(
              courseVideo: courseVideoObj!,
              checkPoints: checkPointData?.checkPoints ?? []));
        } else {
          emit(VideoResourceFetched(
              courseVideo: courseVideoObj!, checkPoints: []));
        }
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][fetchVideoContent] :${e.toString()}");
      emit(VideoResourceError(error: e.details));
    } on Exception catch (e) {
      emit(VideoResourceError(error: e.toString()));
    }
  }

  fetchFileContent(String instanceId, String moduleId) async {
    try {
      if (ORG_ID.isEmpty ||
          USER_ID.isEmpty ||
          instanceId.isEmpty ||
          moduleId.isEmpty) {
        return;
      }

      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "file_id": instanceId,
        "course_module_id": moduleId,
      };
      CourseFileResource? content =
          await _sectionRepository.fetchResourceImageInfo(jsonReq);
      if (content != null) {
        emit(FileResourceFetched(resourseFile: content));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][fetchFileContent] :${e.toString()}");
      String errorMsg = e.details;
      emit(FileResourceError(error: errorMsg));
    } on Exception catch (e) {
      emit(FileResourceError(error: e.toString()));
    }
  }

  Future<dynamic> changeDoneStatus(bool doneStatus) async {
    emit(SectionDetailschangeDoneStatus(doneStatus: doneStatus));
  }

  // get course progress
  getCourseprogress(String instance) async {
    try {
      ResourceProgress responseData =
          await _sectionRepository.getCourseProgress(instance);

      emit(GetCourseProgressState(progress: responseData));
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][getCourseprogress] :${e.toString()}");
      String errorMsg = e.details;
      emit(GetCourseProgressFailureState(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][SectionDetailsCubit][getCourseprogress] :${e.toString()}");
      emit(GetCourseProgressFailureState(error: e.toString()));
    }
  }

  refreshScreen() {
    emit(RefreshState());
  }

  setCourseProgress(
      String courseId, String instance, Map<String, dynamic> duration) async {
    try {
      final responseData = await _courseRepository.setCourseProgress(
          courseId, instance, duration);

      if (responseData["status"] == 'success') {
        emit(SetProgressState());
      } else {
        emit(ResourceFailureState(error: responseData.toString()));
      }
    } on Exception catch (e) {
      emit(ResourceFailureState(error: e.toString()));
    }
  }

  updateSetValue(bool status) {
    setValue = status;
  }

/* TODO: Remove later  
Future<void> fetchCheckPointData(String moduleId) async {
    try {
      await _sectionRepository.fetchVideoCheckPointInfo(moduleId);
    } on Exception catch (error) {
      debugPrint("fetchCheckPointData => ${error.toString()}");
    }
  }*/

  setSkippedResource(Modules courseResource) async {
    try {
      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "course_id": courseResource.courseId,
        "resource_id": courseResource.instanceId,
        "user_id": USER_ID,
        "type": courseResource.moduleType.name.capitalizeFirst,
        "action": "skipped",
        "progress": courseResource.progress,
        "course_module_id": courseResource.courseModuleId,
      };

      final responseData = await _courseRepository.setSkippedResource(jsonReq);

      if (responseData["status"] == 1) {
        emit(SkipModuleResourceSuccess());
      } else {
        emit(SkipModuleResourceFailure(error: responseData.toString()));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][SkipResourceFailure] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(SkipModuleResourceFailure(error: errorMsg));
    } on Exception catch (e) {
      emit(SkipModuleResourceFailure(error: e.toString()));
    }
  }
}
