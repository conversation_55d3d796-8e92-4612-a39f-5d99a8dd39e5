// ignore_for_file: non_constant_identifier_names

import '../../utils/constants/strings.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

///
/// API Config URLs
///
///
// ignore_for_file: constant_identifier_names

class APIConfig {
  // DEV ENV
  final String SUPABASE_URL_DEV = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: dotenv.env['SUPABASE_URL_DEV'] ?? '',
  );

  final String SUPABASE_ANNON_KEY_DEV = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: dotenv.env['SUPABASE_ANNON_KEY_DEV'] ?? '',
  );

  // STAGING ENV
  final String SUPABASE_URL_STAGE_QA = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: dotenv.env['SUPABASE_URL_STAGE_QA'] ?? '',
  );
  final String SUPABASE_ANNON_KEY_STAGE_QA = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: dotenv.env['SUPABASE_ANNON_KEY_STAGE_QA'] ?? '',
  );

  // Competitor Demo ENV
  final String SUPABASE_URL_COMPETITOR = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: dotenv.env['SUPABASE_URL_COMPETITOR_DEMO'] ?? '',
  );

  final String SUPABASE_ANNON_KEY_COMPETITOR = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: dotenv.env['SUPABASE_ANNON_KEY_COMPETITOR_DEMO'] ?? '',
  );

  // PROD env
  final String SUPABASE_URL_PROD = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: dotenv.env['SUPABASE_URL_PROD'] ?? '',
  );

  final String SUPABASE_ANNON_KEY_PROD = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: dotenv.env['SUPABASE_ANNON_KEY_PROD'] ?? '',
  );

  //Competitor Live Class
  final String SUPABASE_URL_LIVE_CLASS_NEW = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: dotenv.env['SUPABASE_URL_LIVE_CLASS_NEW'] ?? '',
  );

  final String SUPABASE_ANNON_KEY_LIVE_CLASS_NEW = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: dotenv.env['SUPABASE_ANNON_KEY_LIVE_CLASS_NEW'] ?? '',
  );

  ///NEW TEST
  
    final String SUPABASE_URL_COMPETITOR_NEW_TEST = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: dotenv.env['NEXT_PUBLIC_SUPABASE_URL'] ?? '',
  );

  final String SUPABASE_ANNON_KEY_COMPETITOR_NEW_TEST = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: dotenv.env['NEXT_PUBLIC_SUPABASE_ANON_KEY'] ?? '',
  );

 //COMPETITOR LIVE WITH DATA ENVIRONMENT KEYS

   final String SUPABASE_URL_COMPETITOR_LIVE = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: dotenv.env['NEXT_PUBLIC_SUPABASE_URL_LIVE'] ?? '',
  );

  final String SUPABASE_ANNON_KEY_COMPETITOR_LIVE = String.fromEnvironment(
    'SUPABASE_ANNON_KEY',
    defaultValue: dotenv.env['NEXT_PUBLIC_SUPABASE_ANON_KEY_LIVE'] ?? '',
  );

  // Assign the keys as per Env
  String SUPABASE_URL = '';
  String SUPABASE_ANNON_KEY = '';

  APIConfig() {
    SUPABASE_URL = SUPABASE_URL_COMPETITOR_LIVE;
    SUPABASE_ANNON_KEY = SUPABASE_ANNON_KEY_COMPETITOR_LIVE;
  }

  // env
  static const BRANCH_ENV_DEV = 'dev';
  static const BRANCH_ENV_STAGE = 'stage';

  // email redirect url
  static const ROUTER_DOMAIN = 'http://10.10.12.237/app/smartlearn-admin';

  static const EMAIL_REDIRECT_URL =
      '$ROUTER_DOMAIN/welcome-64e7264ae8fade72537b4696/edit?branch=$BRANCH_ENV_DEV';

  // default org values for QA env
  // static const String defaultOrgId = "1bd52b79-8da9-43a6-b566-59400207d5fb";
  // static const String defaultRollId = "b695f581-ef80-441e-9aa1-4ff291f3faf1";
  // static const String defaultOrgName = "Citrus Informatics Pvt Ltd";

  // default org values for Competitor env
  static const String defaultOrgId = "87e018ab-149b-43e4-ab75-3633951015d1";
  static const String defaultRollId = "5628008b-ca03-4751-92f7-062bf5cfbceb";
  static const String defaultOrgName = "Competitor";

  // reset password email redirect url
  // TO DO

  ///
  /// User repository
  ///

  static const String V_PERSON = 'v_person';
  static String PROFILE_STORAGE = '$ORG_ID/avatars/$USER_ID';
  static const String UPDATE_PROFILE_FN = 'update_profile';
  static const String ON_USER_LOGOUT = 'on_user_logout';

  ///
  /// Org repository
  ///

  static const String V_USER_ORGS = 'v_user_orgs';
  static const String SET_DEAFULT_ORG_FN = 'add_org_claim_role_to_users';
  static const String EG_FN_SEND_MAIL_NOTIFICATION = "sendUsersignupEmailAlert";

  ///
  /// Topic repository
  ///

  static const String V_CATEGORY = 'v_category';
  static const String V_CATEGORY_BY_HIERARCHY =
      'get_category_hierarchy_enrolled';

  ///
  /// Course repository
  ///

  static const String V_COURSE = 'v_course_enrolled';
  static const String GET_COURSE_DETAILS = 'fn_get_course_details';
  static const String V_BULLETIN_BOARD =
      'v_view_bulletin_board'; // to fetch more current affairs
  static const String GET_COMMENTS_FN = 'fn_fetch_comments_of_instance';
  static const String ADD_COMMENTS_FN = 'fn_post_comment';
  static const String SET_COURSE_PROGRESS_FN = 'set_course_progress';
  static const String SET_LIKE = 'fn_post_comment';

  ///
  /// Section repository
  ///

  static const String GET_SECTION_DETAILS_FN = 'fn_fetch_section_details';

  static const String VIEW_RESOURCE_PAGE =
      'fn_get_view_resource_page'; // 'view_resource_page';
  static const String VIEW_RESOURCE_URL =
      'fn_get_course_resource_url'; //'view_resource_url';
  static const String VIEW_RESOURCE_FILE =
      'fn_get_view_resource_file'; // 'view_resource_file';
  static const String GET_COURSE_PROGRESS_FN = 'get_course_progress';
  static const String GET_RESOURCE_CHECKPOINT_DATA =
      'get_check_point_by_course_module_id';

  ///
  /// Exam repository
  ///

  static const String GET_QUIZ_LIST_FN = 'get_quizes_of_course';
  static const String GET_ATTEMPTED_QUIZ_LIST_FN =
      'get_attempted_quizes_of_user';
  static const String GET_QUESTIONS_OF_QUIZ_FN = 'get_questions_of_quiz';
  static const String START_QUIZ_FN = 'start_quiz';
  static const String SUBMIT_QUIZ_FN = 'submit_quiz_answers';
  static const String V_FETCH_REVIEW_LIST = 'v_submit_answers';
  static const String CALCULATE_RESULT_FN = 'fn_evaluate_quiz';
  static const String FETCH_RESULTS_FN = 'fn_get_quiz_right_answers';
  static const String FETCH_CHECKPOINT_QUIZ = 'start_checkpoint_quiz';
  static const String END_CHECKPOINT_QUIZ = 'fn_submit_checkpoint_quiz';

  ///
  /// Rank repository
  ///

  static const String FETCH_RANK_LIST_FOR_COURSE = 'fetch_rank_list_for_course';
  static const String FETCH_RANK_LIST_FOR_EXAM = 'fetch_rank_list_for_quiz';

  ///
  /// Push Notification
  ///
  static const String INSERT_FCM_TOKEN = 'insert_push_notification';

  ///
  /// Device info
  ///
  static const String UPLOAD_DEVICE_INFO = 'insert_device_info';

  ///
  /// Privilege access list
  ///
  static const String GET_PRIVILEGE_ACCESS_LIST = 'get_user_role_privilege';

  ///
  /// Subscription
  ///
  static const String V_SUBSCRIPTION_PLAN_LIST =
      'v_subscription_plan'; // fetch all plans available to an org
  static const String GET_SUBSCRIPTION_PLAN_LIST =
      'get_subscription_plans_for_user'; // fetch plans specific to org and user
  static const String SUBMIT_SUBSCRIPTION_PLAN =
      'add_subscription_plan_for_user';
  static const String GET_PLAN_STATUS = 'get_user_purchase_plan_status';
  static const String GET_COURSE_FOR_PLAN = 'get_course_resource_list_by_plan';

  static const String GET_SUBSCRIPTION_HISTORY =
      'get_subscription_purchase_report';

  ///
  /// Notification
  ///

  static const String HAS_USER_WATCHED_VIDEOS = 'has_user_watched_videos';

  //
  /// Login
  /////
  static const String ON_USER_LOGIN = 'on_user_login';

  ///
  /// New Dashboard
  ///
  static const String GET_ALL_COURSE_STATS = 'fn_get_user_course_all_stats';

  static const String GET_LIVE_LIST = 'fn_get_live_class_details';

  static const String GET_ALL_ANALYTICS_DATA =
      'fn_get_category_summary_all_courses';

  static const String GET_COURSE_ASSIGNMENTS =
      'fn_get_course_resources_details';

  static const String GET_COURSEWISE_STATS =
      'fn_get_coursewise_user_statistics';

  static const String GET_USER_COURSE_PROGRESS =
      'fn_get_user_course_progress'; // for graph data

  static const String GET_EXAM_PERFORMANCE_CATWISE =
      'fn_get_exam_performance_category_wise';

  static const String GET_VIDEO_RESOURCE_INFO = 'fn_get_view_resource_url';
  static const String GET_FILE_RESOURCE_INFO = 'fn_get_view_resource_file';
  static const String GET_PAGE_RESOURCE_INFO = 'fn_get_view_resource_page';

  static const String SET_FILE_RES_PROGRESS =
      'fn_update_file_view_progress'; //file resource
  // static const String GET_FILE_RES_PROGRESS =
  //     'fn_get_course_video_progress_resource_list'; //file resource

  static const String SET_SKIPPED_RESOURCE = 'fn_skip_resource';
  static const String GET_DASHBOARD_CONFIG = 'fn_get_app_dashboard_config';
  static const String GET_BRANDING_CONFIG = 'fn_get_custom_branding_details';
  static const String USER_ACTIVITY_LOG = 'fn_record_user_activity';
}
