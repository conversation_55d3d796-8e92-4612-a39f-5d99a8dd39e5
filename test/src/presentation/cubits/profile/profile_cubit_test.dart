import 'dart:io';

import 'package:SmartLearn/src/domain/models/organization.dart';
import 'package:SmartLearn/src/domain/models/profile.dart';

import 'package:SmartLearn/src/domain/repositories/user_repository.dart';
import 'package:SmartLearn/src/presentation/cubits/profile/profile_cubit.dart';
import 'package:SmartLearn/src/presentation/cubits/profile/profile_state.dart';
import 'package:SmartLearn/src/presentation/cubits/splash_screen/splash_screen_cubit.dart';
import 'package:SmartLearn/src/utils/constants/api_response.dart';
import 'package:bloc_test/bloc_test.dart';

import 'package:flutter_test/flutter_test.dart';
import 'package:gotrue/src/types/user.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../course_list/course_list_cubit_test.dart';

void main() {
  UserRepository _userRepository;
  late MockCourseRepository _mockCourseRepo;
  group('UserCubit', () {
    late UserCubit userCubit;
    late SplashScreenCubit splashScreenCubit;

    setUp(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      SharedPreferences.setMockInitialValues({});
      _userRepository = MockUserRepository();
      _mockCourseRepo = MockCourseRepository();
      userCubit = UserCubit(_userRepository);
      splashScreenCubit = SplashScreenCubit(_mockCourseRepo, _userRepository);
    });

    tearDown(() {
      userCubit.close();
    });

    // Get user info
    blocTest<UserCubit, UserState>(
        'emits [ UserLoadingState,UserLoadingState] when userinfo event is added',
        build: () => userCubit,
        act: (cubit) => cubit.getUserInfo(MOCK_USER_ID),
        expect: () => [
              UserLoadedState(
                  profile: MOCK_USER_PROFILE_INFO,
                  orgList: ORGANIZATIONLIST_APIRESPONSE)
            ]);
    File? file;

    // Update user info
    blocTest<UserCubit, UserState>(
        'emits [ UserLoadingState] when Update User Info event is added',
        build: () => userCubit,
        act: (cubit) => cubit.updateUserInfo(
            MOCK_USER_ID, MOCK_FIRSTNAME, MOCK_LASTNAME, MOCK_AVATARURL, file),
        setUp: () => UserLoadingState(
            isLoading: true,
            profile: MOCK_USER_PROFILE_INFO,
            orgList: MOCK_ORGANIZATION_LIST),
        wait: const Duration(seconds: 1),
        expect: () => [
              const UserLoadingState(isLoading: true),
              const UserUpdatedState()
            ]);

    blocTest<SplashScreenCubit, SplashScreenState>('checkTokenAndRefresh',
        build: () => splashScreenCubit,
        act: (cubit) => cubit.checkTokenAndRefresh(),
        expect: () =>
            [const UserAuthenticated(isAuthenticated: true, userId: "")]);

    blocTest<UserCubit, UserState>('userSignout',
        build: () => userCubit,
        act: (cubit) => cubit.userSignout(),
        expect: () => [UserSignoutSuccess()]);
  });

  //TODO Fetch image from gallery
}

class MockUserRepository implements UserRepository {
  @override
  Future<Profile> getUserInfo(String userId) async {
    await Future.delayed(const Duration(seconds: 1));
    return MOCK_USER_PROFILE_INFO;
  }

  @override
  Future<List<Organization>> getUserOrganizations(String userId) async {
    await Future.delayed(const Duration(seconds: 1));
    return ORGANIZATIONLIST_APIRESPONSE;
  }

  File? file;

  @override
  updateUserInfo(
      MOCK_USER_ID, MOCK_FIRSTNAME, MOCK_LASTNAME, MOCK_AVATARURL, file) async {
    return Future.value(false);
  }

  @override
  Future<User?> checkTokenAndRefresh() async {
    return Future.value(MOCK_USER_RESPONSE);
  }

  @override
  Future userSignout() {
    return Future(() => true);
  }
}
