part of 'service_cubit.dart';

class ServiceState extends Equatable {
  const ServiceState();

  @override
  List<Object> get props => [];
}

class ServiceInitial extends ServiceState {}

class ServiceFCMUploaded extends ServiceState {
  final PushNotificationModel pushNotificationModel;

  const ServiceFCMUploaded({required this.pushNotificationModel});

  @override
  List<Object> get props => [PushNotificationModel];
}

class ServiceDeviceInfoUploaded extends ServiceState {}
