part of 'subscription_cubit.dart';

class SubscriptionState extends Equatable {
  const SubscriptionState();

  @override
  List<Object> get props => [];
}

class SubscriptionInitial extends SubscriptionState {}

class SubscriptionsFetched extends SubscriptionState {
  final List<SubscriptionPlan> plansList;

  const SubscriptionsFetched({required this.plansList});

  @override
  List<Object> get props => [plansList];
}

class SubscriptionSubmitted extends SubscriptionState {
  final bool submissionStatus;

  const SubscriptionSubmitted({required this.submissionStatus});

  @override
  List<Object> get props => [submissionStatus];
}

class SubscriptionApproved extends SubscriptionState {
  final bool isApproved;
  final String selectedPlanId;
  const SubscriptionApproved(
      {required this.isApproved, required this.selectedPlanId});

  @override
  List<Object> get props => [isApproved, selectedPlanId];
}

class SubscriptionsFailure extends SubscriptionState {
  final String error;

  const SubscriptionsFailure({required this.error});

  @override
  List<Object> get props => [error];
}

class OnSubscriptionIndexChange extends SubscriptionState {
  final int selectedIndex;

  const OnSubscriptionIndexChange({required this.selectedIndex});
  @override
  List<Object> get props => [selectedIndex];
}

class IsLoading extends SubscriptionState {
  final bool isLoading;

  const IsLoading({required this.isLoading});
  @override
  List<Object> get props => [isLoading];
}

class PurchaseHistoryFetched extends SubscriptionState {
  final List<PurchasedPlanListsByUser> purchasedPlans;
  final List<PurchasedPlanListsByUser> activePlans;
  final List<PendingPlanListsByUser> pendingPlans;

  const PurchaseHistoryFetched(
      {required this.purchasedPlans,
      required this.activePlans,
      required this.pendingPlans});

  @override
  List<Object> get props => [purchasedPlans, activePlans, pendingPlans];
}

class PurchaseHistoryFailure extends SubscriptionState {
  final String error;

  const PurchaseHistoryFailure({required this.error});

  @override
  List<Object> get props => [error];
}
