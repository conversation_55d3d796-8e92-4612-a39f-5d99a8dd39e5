// ignore_for_file: lines_longer_than_80_chars, constant_identifier_names

class SLEnglishStrings {
  static const Map<String, String?> ENGLISH_STRINGS = {
    'SUBMIT': 'Submit',
    'SUCCESS': 'Success',
    'YES': 'Yes',
    'OK': 'Ok',
    'DONE': 'Done',
    'CONTINUE': 'Continue',
    'FINISH': 'Finish',
    'CLOSE': 'Close',
    'NO': 'No',
    'LOADING': 'Loading',
    'WARNING': 'Warning',
    'ERROR': 'Error',
    'START': 'Start',
    'SORRY': 'Sorry!',
    'CANCEL': 'Cancel',
    'SEND': 'Send',
    'DELETE': 'Delete',

    // login
    'TITLE': 'Login',
    'EMAIL': 'Email',
    'PASSWORD': 'Password',
    'FORGOT_PASSWORD': 'Forgot Password',
    'BUTTON_TEXT': 'Sign In',
    'OR_CONTINUE_WITH': 'Or continue with',
    'NO_ACCOUNT': 'Don\'t have account?',
    'SIGNUP': 'Sign Up',
    'RE_LOGIN': 'Back to Login',
    'INVALID_EMAIL': 'Invalid email address',
    'EMAIL_FIELD_REQUIRED': 'Email field required',
    'VALID_PASSWORD': 'Please enter a valid password',
    'SIGNUP_TITLE': 'Create An Account',
    'FIRST_NAME': 'First name',
    'LAST_NAME': 'Last name',
    'CONFIRM_PASSWORD': 'Confirm Password',
    'PHONE_NUMBER': 'Phone Number',
    'SIGNUP_BUTTON_TEXT': 'Submit',
    'ACCOUNT': 'Already have an Account?',
    'LOGIN': 'Log In',
    'REGISTER_SUCCESS_MSG':
        'Account created successfully, please check your mail and activate the account.',
    'EMAIL_EXIST':
        'Email already exists!\n Try to sign up with different email id.',
    'EMPTY_VIEW_TEXT': 'No Data Found!',

    // course details
    'CURRENT_AFFAIRS': 'Current Affairs',
    'SEE_ALL': 'See All',
    'SEE_LESS': 'See Less',
    'CURRENT_AFFAIRS_EMPTY':
        'Current affairs are not available for the selected month.\nPlease select another month.',
    'SUBJECTS': 'Subjects',
    'MODULE1': 'module1',
    'MODULE2': 'module2',
    'EXAM': 'Exam',
    'EXAMS': 'Exams',
    'VIDEOS': 'Videos',
    'VIDEO': 'Video',
    'COMMENT_BOX_TITLE': 'Select option',
    'COMMENT_BOX_SUB_TITLE_FEEDBACK':
        'Kindly share your feedback about the course.',
    'COMMENT_BOX_SUB_TITLE_SUGGESTION':
        'Feel free to leave any suggestions about the course.',
    'COMMENT_BOX_PLACEHOLDER': 'Type here...',
    'ADD_COMMENT': 'Add ',
    'COMMENT_SUBMITTED': 'Comment Added!',
    'SUBJECT_DETAILS_NOT_FOUND':
        'Subject details not found!\nPlease try again later.',
    'MODULE_DETAILS_NOT_FOUND':
        'Exam details not found!\nPlease try again later.',
    'AFFAIRS_DETAILS_NOT_FOUND': 'Details not found!\nPlease try again later.',
    'EXIT_APP_MSG': 'Press Back Button Again to Exit',
    'TOPICS_EMPTY': 'Sorry, no topics found!\nPlease try again later.',
    'COURSE_EMPTY':
        'Sorry, no courses found for this topic!\nPlease try again later.',
    'ORGANIZATION_EMPTY':
        'Sorry, no organizations found for this user!\nPlease try again later.',
    'COURSE_DETAILS_EMPTY':
        'Sorry, no details found for this course!\nPlease try again later.',
    'SUBJECTS_EMPTY':
        'Sorry, no details found for this subject!\nPlease try again later.',
    'EXAMS_EMPTY':
        'Sorry, no exams found for this course!\nPlease try again later.',
    'RANK_LIST_EMPTY': 'Sorry, Empty Rank List!\nCheck back later for updates!',
    'CURRENT_AFFAIRS_CONTENT_EMPTY': 'Content not available!',
    'ABOUT_COURSE': 'About Course',
    'READ_MORE': 'Read More',
    'DESCRIPTION': 'Description',

    // validation cubit
    'EMAIL_REQUIRED': 'Email is required',
    'INVALID_EMAIL_ADDRESS': 'Invalid email address',

    'PHONE_NUM_REQUIRED': 'Phone number is required',
    'INVALID_PHONE_NUM': 'Invalid phone number',
    'PHONE_NUM_LENGTH_ERROR': 'Please enter a valid 10-digit phone number.',

    'PASSWORD_REQUIRED': 'Password Required',
    'PASSWORD_LENGTH_MISMATCH': 'Password should be at least 6 characters',
    'PASSWORDS_MISMATCH': 'Passwords doesn\'t match',
    'NO_NETWORK_TRY_LATER':
        'Network Disconnected!\nPlease try again when the network is available.',
    'LOGIN_FAILURE': 'Failed to login!\nPlease try again later',

    // exam list
    'EXAM_LIST_APPBAR_TITLE': 'Exams List',
    'NEW_EXAMS_TAB': 'New Exams',
    'ATTEMPTED_TAB': 'Attempted',
    'PASSED_TAB': 'Passed',
    'QUESTIONS': 'Questions',
    'PASSMARK': 'Passmark',
    'MARKS': 'Marks',
    'ATTEND_BTN': 'Attend',
    'VIEW_RESULTS_BTN': 'Results',
    'REVIEW_BTN': 'Review',
    'RANK_LIST_BTN': 'Rank List',
    'RE_ATTEND_BTN': 'Re-Attend',
    'EXAM_AVAILABLE_POPUP': 'The examination will be available on',

    'MIN': 'Min',

    // exam intro
    'EXAM_INTRO_APPBAR_TITLE': 'Exam',
    'DURATION': 'Duration',
    'DATE': 'Date',
    'INTRODUCTION': 'Introduction',
    'EXAM_AVAILABLE': 'Exam Available',
    'EXAM_TIPS': 'Exam Tips',
    'QUESTION_WITHIN': 'question within',
    'EXAM_MINUTES': 'minutes',
    'CHOOSE_EXAM': 'Choose another exam',
    'REQUEST_REJECTED': 'Your request has been rejected by admin!',
    'EXAM_TOTAL_MARK': 'is the total mark and',
    'EXAM_PASS_MARK': 'is the pass mark',
    'FROM': 'From',
    'TO': 'to',
    'MARK': 'Mark',

    // errors
    'VIEW_RESULTS_FAILURE': 'Failed to fetch results!\nPlease try again later.',
    'VIEW_RESULTS_QUIZ_ATTEMPT_ID_EMPTY':
        'Server error!\nPlease try again later.', // from backend the quiz attempt id is empty
    'RANK_LIST_FAILURE':
        'Failed to fetch rank list for this exam!\nPlease try again later.',

    'EXAM_EXPIRED': 'Exam expired!',
    'QUESTION_NOT_AVAILABLE':
        'Questions are not available for this exam right now.\n Please try again later.',
    'RANK_NOT_AVAILABLE':
        'Rank list is not available for this exam right now.\n Please try another exam.',
    'EXAM_AVAILABILITY': 'The exam will be accessible starting from',

    //
    'FAILED_HOST_LOOKUP': 'Failed host lookup:',

    //
    'LANGUAGE_CHANGE_ALERT':
        'Language change detected. Please login again to continue.',

    'LANGUAGE_CHANGE_ALERT_EN': 'Language change detected',

    // exam result analysis

    'EXAM_RESULT_APPBAR_TITLE': 'Exam Result Analysis',
    'EXAM_FINISH': 'Finish',
    'REATTEND_EXAM':
        "Don't worry you can re-attend the \n exam from the home screen.",
    'EXAM_ANALYSIS': "You can have a look at your exam result here.",

    'SCORE_SHEET': "Score Sheet",
    'TOTAL_MARK_SCORED': "Total Marks Scored",
    'STATISTICS': "Topic-wise Statistics",
    'CORRECT': "Correct",
    'WRONG': "Wrong",
    'SKIPPED': "Skipped",
    'VIEW_SOLUTION': "View Solutions",
    'FAILED_EXAMINATION': "You failed in the examination",
    'PASSED_EXAMINATION': "You passed the examination.",
    'ATTENDED': "Attended",
    'X_AXIS': 'Question No.',
    'Y_AXIS': 'Score',
    'YOUR_ANSWER': 'Your answer:',
    'CORRECT_ANSWER': 'Correct answer:',
    'SKIPPED_QUESTION_STATUS': 'You skipped this question',
    'WRONG_QUESTION_STATUS': 'You answered wrong',
    'CORRECT_QUESTION_STATUS': 'You answered correctly',
    'REVIEW_ANSWER':
        "Here is your score sheet.You can click on the \n graph and review your answers and the score \n you've obtained.",

    // RankList

    'RANK_APPBAR_TITLE': "Rank List",
    'RANK_TITLE': 'Top 10 performers',
    'YOUR_RANK': 'Your Rank',
    'TOP_RANK_HOLDERS': 'Top rank holders',
    'YOUR_RANK_WITH_COMPETITORS': 'Your rank with your competitors',
    'RANK_EXAM_ATTENDED': 'Exam Attended',
    'LEVEL': 'Level',

    // exam view
    'EXAM_APPBAR_TITLE': 'Exam View',
    'EXAM_APPBAR_ALERT_MESSAGE':
        "Are you sure you want to go back?\nYou will lose your exam Progress!",
    'EXAM_SUBMIT_ALERT_MESSAGE': "Are you sure you want to submit the exam?",
    'EXAM_LABEL_TEXT': "Please type the issue here",
    'EXAM_FLAG_FOR_REVIEW_ICON': "Flag for review",
    'EXAM_PROGRESS': "Exam Progress",
    'EXAM_QUICK_INFO': "Quick info",
    'EXAM_SKIPPED': "Skipped",
    'EXAM_ATTENDED': "Attended",
    'EXAM_FLAG_REVIEW': "Flagged for review",
    'EXAM_UNANSWERED': "Unanswered",
    'EXAM_SELECTED_QSTN': "Selected question",
    'COPYRIGHT': "@Smart Learn",
    'EXAM_REPORT_QUESTION': "Report Questions",
    'EXAM_ISSUE_TYPE': "Please select issue type",
    'EXAM_TIP': 'Exam Tip',

    'EXAM_ALREADY_ATTEND':
        'You have already attended this exam.\n Please choose another exam.',
    'EXAM_COMPLETE_TITLE': 'Exam Completed',
    'EXAM_COMPLETE_MSG': 'You have completed the exam.',
    'EXAM_PENALITY': "Penalty, if applicable:",
    'EXAM_MARK': "Mark:",
    'EXAM_NETWORK':
        'Network Disconnected!\n Don\'t worry, your exam will be submitted when the network is availble.',
    'EXAM_TIME_UP': 'Times up!',
    'EXAM_SUBMISSION_STATUS': 'Exam submission in progress, please wait.',

    'PREVIOUS': 'Previous',
    'NEXT': 'Next',
    'EXAM_RESULT': 'Exam Result',
    'CORRECT_ANSWER_EXAM_VIEW': 'Correct answer',
    'WRONG_ANSWER_EXAM_VIEW': 'Wrong answer',
    'ANSWERED': 'Answered',

    'TRY_AGAIN': 'Try Again',

    ///
    /// Profile
    ///
    'PROFILE_APPBAR_TITLE': 'Profile',
    'PROFILE_FIRST_NAME': 'First Name',
    'PROFILE_LAST_NAME': 'Last Name',
    'DELETE_ACCOUNT': 'Delete Account',
    'SELECT_FROM_CAMERA': 'From\ncamera',
    'SELECT_FROM_GALLERY': 'From\ngallery',
    'REMOVE_PROFILE_PIC': 'Remove profile pic',

    // alerts- profile
    'ORGANIZATION_EMPTY_ALERT':
        "No organisation linked in your profile. Please contact admin",
    'FILE_SIZE_WARNING':
        "Please submit a valid file with a size not exceeding 1 MB",
    'FILE_TYPE_WARNING': "Please upload a valid image",
    'STORAGE_PERMISSION_WARNING': "You have disabled storage permission",
    'CAMERA_PERMISSION_WARNING': "You have disabled camera permission",
    'GALLERY_PERMISSION_REQ':
        "Please enable permissions for using gallery access",
    'CAMERA_PERMISSION_REQ':
        "Please enable permissions for using camera services",
    'ENABLE_NOW': "Enable Now",
    'PROFILE_UPDATION_SUCCESS': 'Profile details updated successfully!',
    'PROFILE_UPDATION_FAILED': 'Sorry!\nFailed to update profile details.',
    'FAILED_TO_OPEN_CAMERA':
        "Sorry!\n Unable to open camera now.\n Please try again later",
    'FAILED_TO_OPEN_GALLERY':
        "Sorry!\n Unable to open gallery now.\n Please try again later",
    'ACCOUNT_DELETE_CONFIRM': 'Are you sure you want to delete this account?',
    'FIELD_REQUIRED': 'Field cannot be empty! ',

    //Side drawer screen
    'PROFILE': "Profile",
    'SETTINGS': "Settings",
    'RANKING': "Ranking",
    'NEWS': "News",
    'NOTIFICATIONS': "Notifications",
    'ABOUT': "About",
    'SHARE': "Share",
    'FEEDBACK': "Feedback",
    'SUGGESTION': "Suggestion",
    'SIGNOUT': "Signout",
    'SIGNOUT_MSG': "Are you sure to want to logout?",
    'LIVE_CLASS': "Live Class",

    //exam review
    'EXAM_REVIEW_APPBAR_TITLE': "Exam Review",
    'EXAM_ATTENDED_ON': 'Exam Attended on',
    'QUESTION_ANSWER': 'Questions and selected answers:',
    'ANSWER_OPTED': 'Answer opted:',
    'HOUR': 'hour',
    'HOURS': 'hours',
    'MINUTES': 'minutes',
    'MINUTE': 'minute',
    'SECOND': 'second',
    'SECONDS': 'seconds',
    'DIALOGUE_CONTENT': "Unable to load exam results!\nplease try again later",
    'DIALOGUE_CONTENT_REVIEW_FAILURE':
        "Unable to load exam review!\nplease try again later",
    'DIALOGUE_CONTENT_REVIEW_FAILURE_MESSAGE':
        "Unable to load previous data",

    // course video
    'CHECKPOINT_EXAM_NAVIGATION_MSG':
        'Check point reached. Now there will be a Quiz to check your understanding so far. You will be redirected now to the quiz associated with the checkpoint.',
    'SECTION_TITLE': "Subject Details",
    'SAVE_PROGRESS_BTN': "Save Progress",
    'PROGRESS_SUCESS_ALERT_CONTENT': "Your Progress Successfully saved",
    'PROGRESS_ALERT_CONTENT': "Do you want to save Progress",
    'VIDEO_COMMENTS': "Comments",
    'POPUP_TITLE': "Custom Popup",
    'POPUP_MSG':
        "This is a custom popup that appears from 10 seconds to every 10 seconds.",
    'PROMPT_TEXT': "What is Natural Science",
    'PROMPT_VALIDATION_MSG': "Please Enter answer",
    'VIDEO_PLAYER_ERROR':
        'There is a problem while playing the video.\nPlease try again later.',
    'SAVE_PROGRESS_FAILURE':
        'Failed to save progress!\nPlease try again later.',
    'INVALID_FILE_FORMAT': 'Invalid file format!',
    'CHECKPOINT_COMPLETE_MSG':
        'Great job! You\'ve successfully completed\nthis video session.\nKeep up the good work!',

    // onBoarding Screen
    'SKIP': 'Skip',
    'LEARN': "Learn to Excel",
    'LEARN_PROFESSIONAL':
        "Learn for professional as well as competitive exam for expert faculty's question.",
    'PRACTICE': 'Practice to Succeed',
    'PRACTICE_SESSION':
        "Try out practice session created by expert trainer and track your progress!",
    'STUDY': 'Study to Win',
    'STUDY_MATERIALS':
        'Learn from experts recommended study materials for professional & competitive exams.',
    'WATCH': 'Watch to Learn',
    'WATCH_FEATURES':
        'Watch our featured educational videos and learn like never before.',
    'CONQUER': 'Conquer your Dream',
    'UNLOCK_LEVEL':
        'You need to unlock this level to get the exams in this level.',

    // organization view
    'ORGANIZATION_APPBAR_TITLE': 'Organization',
    'EMPTY_TOPIC_FOR_ORGANIZATION':
        "Please enroll in this organization to proceed.",

    // Subscription
    'SUBSCRIPTION': 'Subscription',
    'SUBSCRIPTION_ADDED':
        'Subscription added. Waiting for approval from admin!',
    'PENDING': 'Pending',
    'SELECT': 'Select',
    'APPROVED': 'Approved',
    'UPGRADE_PREMIUM': "Upgrade your membership",
    'SUBSCRIPTION_DESCRIPTION':
        "We offer pricing plans tailored to your needs. Find the perfect fit for your budget and goals.",
    'PURCHASE_HISTORY': "Purchase History",
    'PURCHASED_PLANS': "Purchased plans",
    'ACTIVE_PLANS': 'Active plans',
    'PLANS_WAITING_FOR_APPROVAL': "Plans waiting for approval",
    'PLAN_AVAILABILITY_FROM': 'Plan Availability:\n',
    'PLAN_VALIDITY': 'Plan Validity:\n',
    'PLAN_AVAILABILITY_TO': ' up to ',
    'PLAN_CONFIRMATION_TITLE': 'Confirm Subscription',
    'PLAN_CONFIRMATION_CONTENT':
        'Would you like to subscribe to the selected plan?',
    'PLAN_EMPTY': 'No Plans found!',
    'SEARCH_HINT_FOR_PLAN': 'Search by plan name',
    'RESOURCES': 'Resources',
    'RESOURCE_EMPTY': "No courses are linked with the selected plan.",

    // Notification
    'LOCAL_NOTIFICATION_TITLE': 'Session Notification',
    'LOCAL_NOTIFICATION_CONTENT':
        'You have missed the video session. Kindly attend the session.',
  };
}
