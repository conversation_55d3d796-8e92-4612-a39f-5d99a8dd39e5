// To parse this JSON data, do
//
//     final courseDashboard = courseDashboardFromJson(jsonString);

import 'dart:convert';

List<CoursesInfo> courseDashboardFromJson(String str) => List<CoursesInfo>.from(
    json.decode(str).map((x) => CoursesInfo.fromJson(x)));

String courseDashboardToJson(List<CoursesInfo> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CoursesInfo {
  final String courseId;
  final String courseName;
  final String timeSpent;
  final double progress;
  final double percentage;
  final double totalMarks;
  final int achievements;
  bool showCourseDetails = false;

  CoursesInfo({
    required this.progress,
    required this.courseId,
    required this.percentage,
    required this.timeSpent,
    required this.totalMarks,
    required this.courseName,
    required this.achievements,
    showCourseDetails,
  });

  factory CoursesInfo.fromJson(Map<String, dynamic> json) {
    return CoursesInfo(
      progress: (json["progress"] ?? 0).toDouble(),
      courseId: json["course_id"] ?? '',
      percentage: (json["percentage"] ?? 0).toDouble(),
      timeSpent: json["time_spent"] ?? '00:00:00',
      totalMarks: (json["totalMarks"] ?? 0).toDouble(),
      courseName: json["course_name"] ?? '',
      achievements: json["achievements"] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        "progress": progress,
        "course_id": courseId,
        "percentage": percentage,
        "time_spent": timeSpent,
        "totalMarks": totalMarks,
        "course_name": courseName,
        "achievements": achievements,
      };
}
