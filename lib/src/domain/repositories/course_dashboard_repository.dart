import '../models/course_dashboard/course_dashboard_config.dart';
import '../models/course_dashboard/courses_info.dart';
import '../models/course_dashboard/course_analytics.dart';
import '../models/course_details.dart';

abstract class CourseDashboardRepository {
  Future<List<CoursesInfo>> fetchAllCourseStats(Map<String, dynamic> jsonReq);
  Future<CourseAnalyticsModel> fetchAllAnalyticsData(
      Map<String, dynamic> jsonReq);
  Future<List<CurrentAffairs>> fetchAllCurrentAffairs();
  Future<DashboardConfig> fetchDashboardConfig(Map<String, dynamic> jsonReq);
}
