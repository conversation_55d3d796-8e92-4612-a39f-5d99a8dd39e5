// ignore_for_file: avoid_catches_without_on_clauses

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/app_config/preference_config.dart';

class SharedPreferencesUtils {
  SharedPreferencesUtils._internal();
  static SharedPreferencesUtils instance = SharedPreferencesUtils._internal();
  factory SharedPreferencesUtils() => instance;

  late SharedPreferences preferences;

  Future<void> initPreference() async {
    preferences = await SharedPreferences.getInstance();
  }

  Future<bool> setRouteData(String key, Map<String, dynamic> data) async {
    try {
      final value = jsonEncode(data);
      return await preferences.setString(key, value);
    } catch (e) {
      debugPrint("setRouteData => ${e.toString()}");
      return false;
    }
  }

  Map<String, dynamic> getRouteData(String key) {
    try {
      final value = preferences.getString(key) ?? '';
      final Map<String, dynamic> data = jsonDecode(value);
      return data;
    } catch (e) {
      debugPrint("getRouteData =>${e.toString()}");
      return {};
    }
  }

  Future<String> getUserAccessToken() async {
    try {
      return await preferences.getString(PREF_KEY_ACCESS_TOKEN) ?? '';
    } catch (e) {
      debugPrint("getUserToken => ${e.toString()}");
      return '';
    }
  }
}
