import 'dart:io';

import 'package:beamer/beamer.dart';

import '../../config/app_config/preference_config.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '/src/presentation/views/profile_review_view.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:auto_size_text/auto_size_text.dart';

import '../../config/app_config/sl_config.dart';
import '../../config/themes/theme_colors.dart';
import '../../domain/models/rank.dart';
import '/src/presentation/views/feedback_rating_view.dart';
import '/src/utils/constants/helper.dart';

import '../../config/themes/lms_fonts.dart';
import '../cubits/course_feedback/course_feedback_cubit.dart';
import '/src/config/themes/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/router/app_router.dart';
import '../../utils/constants/strings.dart';
import '../cubits/profile/profile_cubit.dart';
import '../cubits/profile/profile_state.dart';
import 'alert_popup/multi_action_dialogue.dart';
import 'alert_popup/confirmation_popup.dart';

class DrawerWidget extends StatelessWidget {
  final String name;
  final String email;
  final String avatarUrl;
  final String? courseName;
  final bool hasCourseAccess;
  final bool hasProfileAccess;
  final bool hasSubscriptionAccess;

  const DrawerWidget({
    Key? key,
    required this.name,
    required this.email,
    required this.avatarUrl,
    this.courseName,
    required this.hasCourseAccess,
    required this.hasProfileAccess,
    required this.hasSubscriptionAccess,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _userCubit = BlocProvider.of<UserCubit>(context);

    return Scaffold(
      backgroundColor: AppTheme.sidemenuBGColor,
      body: BlocBuilder<UserCubit, UserState>(
        bloc: _userCubit,
        builder: (context, state) {
          return LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
            double webHeight = constraints.maxHeight;
            Widget gapConstraintWidget =
                webHeight <= 440 ? Spacer() : const SizedBox(height: 34);
            return SizedBox(
              child: Column(
                children: <Widget>[
                  _topContainer(context, constraints.maxHeight,
                      _userCubit.state, constraints.maxWidth),
                  _divider(),
                  const SizedBox(height: 34),

                  hasProfileAccess ? _profileTile(context) : Container(),
                  // const Spacer(),
                  // const SizedBox(height: 34),
                  hasProfileAccess ? gapConstraintWidget : Container(),
                  _rankTile(context),
                  //   const SizedBox(height: 34),
                  // const Spacer(),
                  gapConstraintWidget,
                  /* _newsTile(context),
                  const Spacer(),
                  _settingsTile(context),
                  const Spacer(),
                  _notificationsTile(context),
                  const Spacer(),
                  _feebacktile(context),
                  const Spacer(),*/
                  _aboutTile(context),
                  // const SizedBox(height: 34),
                  //  const Spacer(),
                  // _shareTile(context),
                  //   const SizedBox(height: 34),
                  //  const Spacer(),
                  gapConstraintWidget,
                  _subscriptionTile(context),
                  hasSubscriptionAccess ? gapConstraintWidget : Container(),
                  _signoutTile(context, _userCubit, state),
                  //  const SizedBox(height: 34),
                  const Spacer(),
                  _divider(),
                  webHeight <= 440
                      ? const Spacer()
                      : const SizedBox(height: 22),
                  _appVersion(),
                  webHeight <= 440
                      ? const Spacer()
                      : const SizedBox(height: 22),
                ],
              ),
            );
          });
        },
      ),
    );
  }

  Widget _topContainer(context, maxHeight, state, maxWidth) {
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 10, top: 10),
      decoration: const BoxDecoration(),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const SizedBox(height: 10),
            Row(
              children: [
                _profileIconWidget(state, maxWidth),
                const SizedBox(width: 15),
                _userInfoWidget(state, maxWidth),
                const SizedBox(width: 5),
                _closeBtn(context),
              ],
            ),
            SizedBox(height: hasCourseAccess ? 20 : 0),
            hasCourseAccess ? _topicWidget(context, maxHeight) : Container(),
            kIsWeb
                ? const SizedBox(height: 20)
                : SizedBox(height: Platform.isAndroid ? 20 : 0),
          ],
        ),
      ),
    );
  }

  Widget _profileIconWidget(state, maxWidth) {
    return ProfileImageWidget(
      avatarUrl: state.profile?.avatarUrl,
      showCamera: false,
      radius: 50,
      onEditTapped: () {},
    );
  }

  Widget _closeBtn(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          color: Colors.transparent,
          child: Align(
            alignment: Alignment.centerRight,
            child: Image.asset('$ASSETS_PATH/close.png', height: 30, width: 30),
          ),
        ),
      ),
    );
  }

  Widget _userInfoWidget(UserState state, double maxWidth) {
    String firstName =
        convertToCamelCase(state.profile?.firstName ?? "").trim();
    String lastName = convertToCamelCase(state.profile?.lastName ?? "").trim();
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AutoSizeText(
            firstName + ' ' + lastName,
            style: LMSFonts.semiBoldFont(16, LmsColors.white),
          ),
          _emailWidget(state),
        ],
      ),
    );
  }

  Widget _emailWidget(UserState state) {
    return state.profile != null && state.profile!.phoneNum.isNotEmpty
        ? Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image.asset(
              //   '$ASSETS_PATH/phone.png',
              //   height: 14,
              //   width: 14,
              // ),
              const Padding(
                padding: EdgeInsets.only(top: 3),
                child: Icon(
                  Icons.mail,
                  color: AppTheme.whiteColor,
                  size: 14,
                ),
              ),
              const SizedBox(width: 5),
              Expanded(
                child: AutoSizeText(
                  state.profile != null ? (state.profile?.email ?? '') : '',
                  // maxLines: 2,
                  style: LMSFonts.regularFontWithHeight(14, LmsColors.white, 0),
                ),
              )
            ],
          )
        : Container();
  }

  Widget _topicWidget(BuildContext context, double maxHeight) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () => showWarning(context),
        child: Container(
          margin: const EdgeInsets.only(right: 5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.0),
            border: Border.all(color: AppTheme.whiteColor.withOpacity(0.8)),
          ),
          padding: const EdgeInsets.all(3),
          // alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset('$ASSETS_PATH/topic_sidemenu.png',
                  height: 24, width: 24),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  TOPIC_NAME,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: LMSFonts.regularFontWithHeight(
                      14, AppTheme.whiteTextColor, 0),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _divider() {
    return Divider(
      thickness: 0.7,
      height: 1,
      color: AppTheme.whiteColor.withOpacity(0.5),
    );
  }

  Widget _profileTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_PROFILE),
        icon: 'profile_sidemenu',
        iconSize: 18,
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/profile-review')
              : appRouter.push(ProfileReviewRoute());
        });
  }

  Widget _rankTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_RANKING),
        icon: 'ranking',
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/rank-list', data: {
                  'isCourseRankList': true,
                  'rankListForExam': const <Rank>[],
                })
              : appRouter.push(RankListViewRoute(
                  isCourseRankList: true, rankListForExam: const <Rank>[]));
        });
  }

  Widget _newsTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_NEWS),
        icon: 'news',
        onTapCallback: () => Navigator.of(context).pop());
  }

  Widget _settingsTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_SETTINGS),
        icon: 'settings',
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/settings')
              : appRouter.push(SettingsViewRoute());
        });
  }

  Widget _notificationsTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_NOTIFICATIONS),
        icon: 'notification_sidemenu',
        onTapCallback: () => Navigator.of(context).pop());
  }

  Widget _aboutTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_ABOUT),
        icon: 'about',
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/about')
              : appRouter.push(const AboutViewRoute());
        });
  }

  Widget _feebacktile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_FEEDBACK),
        icon: 'feedback_sidemenu',
        onTapCallback: () {
          Navigator.of(context).pop();
          showDialog(
              context: context,
              barrierDismissible: true,
              builder: (BuildContext context) {
                TextEditingController xx = TextEditingController();
                int selectedUptoIndex = 0;
                return BlocBuilder<CourseFeedbackCubit, CourseFeedbackState>(
                  builder: (context, state) {
                    if (state is CourseFeedbackUpdate) {
                      selectedUptoIndex = state.uptoIndex;
                    }
                    return FeedbackDialouge(
                      feedbackController: xx,
                      selectedUptoIndex: selectedUptoIndex,
                      updateStars: (val) {
                        context
                            .read<CourseFeedbackCubit>()
                            .updateStars(true, val);
                      },
                      onSubmit: () => kIsWeb
                          ? Beamer.of(context).beamBack()
                          : appRouter.pop(),
                    );
                  },
                );
              });
        });
  }

  Widget _shareTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_SHARE),
        icon: 'share',
        onTapCallback: () {
          Navigator.of(context).pop();
          shareAppInfo();
        });
  }

  Widget _signoutTile(
      BuildContext context, UserCubit _userCubit, UserState state) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_SIGNOUT),
        icon: 'signout',
        onTapCallback: () async {
          Navigator.of(context).pop();
          _showSignoutConfirmationPopup(context, _userCubit, state);
        });
  }

  Widget _subscriptionTile(BuildContext context) {
    return Visibility(
      visible: hasSubscriptionAccess,
      child: DrawerListTile(
          title: SLStrings.getTranslatedString(KEY_SUBSCRIPTION),
          icon: 'subscription',
          onTapCallback: () {
            Navigator.of(context).pop();
            kIsWeb
                ? Beamer.of(context).beamToNamed('/subscription-view')
                : appRouter.push(SubscriptionViewRoute());
          }),
    );
  }

  _showSignoutConfirmationPopup(
      BuildContext context, UserCubit _userCubit, UserState state) {
    showDialog(
        context: context,
        builder: (context1) {
          return MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_WARNING),
            content: SLStrings.getTranslatedString(KEY_SIGNOUT_MSG),
            alertType: AlertType.warning,
            cancelBtnText: SLStrings.getTranslatedString(KEY_NO),
            confirmBtnText: SLStrings.getTranslatedString(KEY_YES),
            onContinueTap: () async {
              SharedPreferences _prefs = await SharedPreferences.getInstance();
              _prefs.setBool(PREF_KEY_SIGNOUT, true);
              // set to false if the user goes to login screen without manualy signing out
              // for localisation
              _prefs.setBool(PREF_KEY_NAVIGATING_AFTER_LOGOUT, true);
              await _userCubit.userSignout();
              await clearCurrentUserInfo();
            },
            onCancelTap: () {
              kIsWeb ? Navigator.of(context1).pop() : appRouter.pop();
            },
          );
        });
  }

  Widget _appVersion() {
    return Container(
      alignment: Alignment.bottomCenter,
      margin: const EdgeInsets.only(right: 10),
      child: Text(
        'v${SLConfig.APP_VERSION}',
        style: LMSFonts.regularFont(14, AppTheme.whiteColor),
      ),
    );
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            cancelBtnText: SLStrings.getTranslatedString(KEY_OK),
            confirmBtnText: '',
            alertType: AlertType.error,
            onContinueTap: () =>
                kIsWeb ? Navigator.of(context).pop() : appRouter.pop(),
            onCancelTap: () {
              kIsWeb
                  ? Beamer.of(context).beamToReplacementNamed('/login-view')
                  : appRouter.popUntil((route) =>
                      route.settings.name == LoginEmailViewRoute.name);
            });
      },
    );
  }
}

class DrawerListTile extends StatelessWidget {
  final String title;
  final String icon;
  final double iconSize;
  final VoidCallback onTapCallback;

  const DrawerListTile(
      {super.key,
      required this.title,
      required this.icon,
      this.iconSize = 24,
      required this.onTapCallback});

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () => onTapCallback(),
        child: Container(
          color: Colors.transparent,
          padding: const EdgeInsets.only(left: 16),
          child: Row(
            children: [
              SizedBox(
                height: 24,
                width: 24,
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: (24 - iconSize) / 2),
                  child: Image.asset(
                    '$ASSETS_PATH/$icon.png',
                    color: AppTheme.whiteColor,
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Text(
                title,
                style: LMSFonts.tabItem(),
              ),
            ],
          ),
          // onTap: () => onTapCallback(),
        ),
      ),
    );
  }
}
