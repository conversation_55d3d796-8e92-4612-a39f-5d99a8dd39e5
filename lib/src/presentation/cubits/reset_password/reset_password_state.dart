part of 'reset_password_cubit.dart';

class ResetPasswordState extends Equatable {
  final bool isLoading;
  final String error;
  const ResetPasswordState({
    this.isLoading = false,
    this.error=''
  });

  @override
  List<Object> get props => [];
}

class ResetPasswordInitial extends ResetPasswordState {}

class ResetPasswordSuccess extends ResetPasswordState {
  ResetPasswordSuccess();

  @override
  List<Object> get props => [];
}

class ResetPasswordLoading extends ResetPasswordState {
  const ResetPasswordLoading({super.isLoading});
}

class ResetPasswordError extends ResetPasswordState {
  final String error;
  ResetPasswordError({required this.error});

  @override
  List<Object> get props => [error];
}