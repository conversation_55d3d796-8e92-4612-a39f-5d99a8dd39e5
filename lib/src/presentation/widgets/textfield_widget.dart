
import '../../utils/constants/strings.dart';

import '/src/config/themes/app_theme.dart';

import '../../utils/helper/regex_strings.dart';
import '/src/config/themes/lms_fonts.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';


typedef StringCallback = Function(String val);

class SLTextFieldWidget extends StatelessWidget {
  @required
  final TextEditingController controller;
  final FocusNode focusNode;
  final String hint;
  final String? errorMsg;
  final bool isEnabled;
  final bool isNameField;
  final bool obscureText;
  final bool isPassword;
  final bool showSuffixIcon;
  final VoidCallback onSufixIconTapped;
  final VoidCallback? onTap;
  final StringCallback? onChange;
  final StringCallback onSubmitted;

  final TextInputType textInputType;

  const SLTextFieldWidget(
      {super.key,
      required this.controller,
      required this.focusNode,
      this.errorMsg,
      this.isEnabled = true,
      this.isNameField = false,
      this.isPassword = false,
      required this.showSuffixIcon,
      this.textInputType = TextInputType.text,
      required this.hint,
      required this.onTap,
      required this.onSufixIconTapped,
      required this.obscureText,
      required this.onChange,
      required this.onSubmitted});

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      obscureText: obscureText,
      keyboardType: textInputType,
      enabled: isEnabled,
      textAlignVertical: TextAlignVertical.center,
      textAlign: TextAlign.left,
      obscuringCharacter: '*',
      textCapitalization: textInputType == TextInputType.text
          ? TextCapitalization.sentences
          : TextCapitalization.none,
      inputFormatters: (textInputType == TextInputType.phone)
          ? [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(
                  textInputType == TextInputType.phone ? 10 : null)
            ]
          : (textInputType == TextInputType.text)
              ? [
                  FilteringTextInputFormatter.allow(RegExp(inputNameRegex)),
                  LengthLimitingTextInputFormatter(isNameField ? 25 : null)
                ]
              : obscureText
                  ? [
                      FilteringTextInputFormatter.deny(RegExp(' ')),
                    ]
                  : [],
      style: LMSFonts.textFieldValueTextStyle(),
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        suffixIcon: showSuffixIcon
            ? errorMsg != null &&
                    errorMsg!.isNotEmpty &&
                    controller.text.isNotEmpty
                ? GestureDetector(
                    onTap: () => onSufixIconTapped(), child: _suffixIcon())
                : null
            : null,
        contentPadding: const EdgeInsets.symmetric(horizontal: 17),
        focusedBorder: _focusedTextFieldBorder(),
        enabledBorder: _textFieldBorder(),
        disabledBorder: _textFieldBorder(),
        filled: isEnabled,
        fillColor: Colors.white,
        hintText: hint,
        hintStyle: LMSFonts.textFieldPlaceHolderTextStyle(),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(44.0),
        ),
        errorText: errorMsg,
        errorStyle: LMSFonts.textFieldErrorTextStyle(),
        errorBorder: _errorTextFieldBorder(),
      ),
      onSubmitted: (value) => {onSubmitted(value)},
      onChanged: onChange,
      onTap: onTap,
    );
  }

  OutlineInputBorder _focusedTextFieldBorder() {
    return OutlineInputBorder(
      borderSide:
          const BorderSide(color: AppTheme.transparentColor, width: 1.2),
      borderRadius: BorderRadius.circular(44),
    );
  }

  OutlineInputBorder _textFieldBorder() {
    return OutlineInputBorder(
      borderSide:
          const BorderSide(color: AppTheme.transparentColor, width: 1.2),
      borderRadius: BorderRadius.circular(44),
    );
  }

  OutlineInputBorder _errorTextFieldBorder() {
    return OutlineInputBorder(
      borderSide: BorderSide(
          color: errorMsg != null && errorMsg != ''
              ? AppTheme.textFieldErrorBorder
              : AppTheme.transparentColor,
          width: 1.2),
      borderRadius: BorderRadius.circular(44),
    );
  }

  Widget _suffixIcon() {
    return Container(
      height: 22,
      width: 22,
      padding: const EdgeInsets.only(left: 5, right: 16),
      child: Image.asset('$ASSETS_PATH/error.png'),
    );
  }
}

///
/// new textfield
///

class TextFieldWidget extends StatelessWidget {
  @required
  final TextEditingController controller;
  final FocusNode focusNode;
  final String hint;
  final String? errorMsg;
  final bool isEnabled;
  final bool isNameField;
  final VoidCallback? onTap;
  final StringCallback? onChange;
  final StringCallback onSubmitted;

  final TextInputType textInputType;

  final bool obscureText;
  final bool enableMultipleLines;

  const TextFieldWidget({
    super.key,
    this.errorMsg,
    this.isEnabled = true,
    this.isNameField = false,
    this.enableMultipleLines = false,
    this.textInputType = TextInputType.text,
    required this.controller,
    required this.focusNode,
    required this.hint,
    required this.onTap,
    required this.obscureText,
    required this.onChange,
    required this.onSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      obscureText: obscureText,
      keyboardType: textInputType,
      enabled: isEnabled,
      textAlignVertical: TextAlignVertical.center,
      obscuringCharacter: '*',
      textCapitalization: textInputType == TextInputType.text
          ? TextCapitalization.sentences
          : TextCapitalization.none,
      inputFormatters: (textInputType == TextInputType.phone)
          ? [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(
                  textInputType == TextInputType.phone ? 10 : null)
            ]
          : (textInputType == TextInputType.text)
              ? [
                  FilteringTextInputFormatter.allow(RegExp(inputNameRegex)),
                  LengthLimitingTextInputFormatter(isNameField ? 25 : null)
                ]
              : obscureText
                  ? [
                      FilteringTextInputFormatter.deny(RegExp(' ')),
                    ]
                  : [],
      textAlign: TextAlign.left,
      style: LMSFonts.textFieldValueTextStyle(),
      textInputAction: TextInputAction.next,
      maxLines: enableMultipleLines ? null : 1,
      decoration: InputDecoration(
        filled: !isEnabled,
        fillColor: AppTheme.borderColor.withOpacity(0.4),
        focusedBorder: _focusedTextFieldBorder(),
        enabledBorder: _textFieldBorder(),
        disabledBorder: _textFieldBorder(),
        contentPadding: const EdgeInsets.symmetric(vertical: 9, horizontal: 12),
        errorStyle: LMSFonts.textFieldErrorTextStyle(),
        errorBorder: _errorTextFieldBorder(),
        errorText: errorMsg,
        border: _textFieldBorder(),
      ),
      onSubmitted: (value) => {onSubmitted(value)},
      onChanged: onChange,
      onTap: onTap,
    );
  }

  OutlineInputBorder _focusedTextFieldBorder() {
    return OutlineInputBorder(
      borderSide: const BorderSide(color: AppTheme.primaryBlue),
      borderRadius: BorderRadius.circular(10),
    );
  }

  OutlineInputBorder _textFieldBorder() {
    return OutlineInputBorder(
      borderSide: const BorderSide(color: AppTheme.textFieldBorder),
      borderRadius: BorderRadius.circular(10),
    );
  }

  OutlineInputBorder _errorTextFieldBorder() {
    return OutlineInputBorder(
      borderSide: BorderSide(
        color: errorMsg != null && errorMsg != ''
            ? AppTheme.textFieldErrorBorder
            : AppTheme.transparentColor,
      ),
      borderRadius: BorderRadius.circular(10),
    );
  }
}
