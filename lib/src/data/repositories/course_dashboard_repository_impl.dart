import '../../domain/models/course_dashboard/course_dashboard_config.dart';
import '../../../src/domain/models/course_dashboard/courses_info.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../config/app_config/api_config.dart';
import '../../domain/models/course_dashboard/course_analytics.dart';
import '../../domain/models/course_details.dart';
import '../../domain/repositories/course_dashboard_repository.dart';
import '../../utils/constants/strings.dart';

class CourseDashboardRepositoryImpl implements CourseDashboardRepository {
  @override
  Future<List<CoursesInfo>> fetchAllCourseStats(
      Map<String, dynamic> jsonReq) async {
    List<CoursesInfo> allCourseInfo = [];

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_ALL_COURSE_STATS, params: jsonReq);

    List<dynamic> responseData = response ?? [];

    allCourseInfo =
        List<CoursesInfo>.from(responseData.map((x) => CoursesInfo.fromJson(x)))
            .toList();
    return allCourseInfo;
  }

  @override
  Future<CourseAnalyticsModel> fetchAllAnalyticsData(
      Map<String, dynamic> jsonReq) async {
    CourseAnalyticsModel allAnalyticsInfo;

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_ALL_ANALYTICS_DATA, params: jsonReq);

    dynamic responseData = response ?? [];

    allAnalyticsInfo = CourseAnalyticsModel.fromJson(responseData);
    return allAnalyticsInfo;
  }

  @override
  Future<List<CurrentAffairs>> fetchAllCurrentAffairs() async {
    final response = await Supabase.instance.client
        .from(APIConfig.V_BULLETIN_BOARD)
        .select()
        .filter('type', 'eq', 'Current_Affairs')
        .eq('org_id', ORG_ID);
    List<dynamic> responseData = response;
    List<CurrentAffairs> _currentAffairs = List<CurrentAffairs>.from(
        responseData.map((x) => CurrentAffairs.fromJson(x))).toList();
    return _currentAffairs;
  }

  @override
  Future<DashboardConfig> fetchDashboardConfig(
      Map<String, dynamic> jsonReq) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_DASHBOARD_CONFIG, params: jsonReq);
    final dashboardConfig = DashboardConfig.fromJson(response);
    return dashboardConfig;
  }
}
