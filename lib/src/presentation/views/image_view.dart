import '../../config/enums/resource_activity_status.dart';
import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../utils/resources/user_activity_res.dart';
import '/src/domain/services/localizations/string_keys.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '/src/config/themes/theme_colors.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/themes/lms_fonts.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/resource_file.dart';
import '../../domain/models/responses/section_details.dart';
import '../../utils/constants/nums.dart';
import '../cubits/section_details/section_details_cubit.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/button_widget.dart';
import '/src/config/themes/app_theme.dart';

import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';

import '../widgets/app_bar.dart';
import 'package:photo_view/photo_view.dart';

@RoutePage()
// ignore: must_be_immutable
class ImageViewScreen extends HookWidget {
  final CourseFileResource resourceFile;
  final int moduleLength;
  ResourceProgress? resourceProgress;

  ImageViewScreen({
    Key? key,
    required this.resourceFile,
    required this.resourceProgress,
    required this.moduleLength,
  }) : super(key: key);

  SectionDetails? sectionDetails;

  late ValueNotifier<bool> _hasAccessToSaveProgress;
  ValueNotifier<bool> _isCompleted = ValueNotifier(false);

  Map<String, dynamic> args = {};

  String time = '0 $HOURS 0 $MINUTES 0 $SECONDS';

  final double _horizontalMargin = 16.0;

  final _userActivityLog = UserActivityLog.instance;

  // decides whether to disable/enable save progress
  _checkPrivilegeAcccess() async {
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_UPDATE_COURSE_PROGRESS;
    _hasAccessToSaveProgress.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);
  }

  _onBackTapped(BuildContext context) async {
    args = {
      'progress':
          (_isCompleted.value || (resourceProgress?.progress ?? 0.0) >= 100)
              ? 100
              : 0,
      'time_spent': time
    };
    await _setResourceActivityLog(context,
        status: ResourceActivityStatus.inProgress);
    kIsWeb
        ? Beamer.of(context).beamBack(data: args)
        : appRouter.popForced(args);
  }

  @override
  Widget build(BuildContext context) {
    final _sectionDetailsCubit = BlocProvider.of<SectionDetailsCubit>(context);
    _hasAccessToSaveProgress = useState<bool>(false);
    _isCompleted = useState<bool>(false);

    useEffect(() {
      _isCompleted.value = (resourceProgress?.progress ?? 0.0) >= 100;
      Future<void>.microtask(() async {
        await _checkPrivilegeAcccess();
        args = {'progress': 0, 'time_spent': time};
        if (resourceProgress?.markedAsDone == true) {
          context.read<SectionDetailsCubit>().changeDoneStatus(true);
          _isCompleted.value = true;
        }
        await _setResourceActivityLog(context,
            status: ResourceActivityStatus.started);
      });
      return null;
    }, [_isCompleted]);

    return BlocListener<SectionDetailsCubit, SectionDetailsState>(
        listener: (context, state) async {
      if (state is SectionDetailschangeDoneStatus) {
        _isCompleted.value = state.doneStatus;
      }
    }, child: BlocBuilder<SectionDetailsCubit, SectionDetailsState>(
            builder: (context, state) {
      return PopScope(
        canPop: false,
        onPopInvoked: (bool didPop) {
          if (didPop) {
            return;
          }

          _onBackTapped(context);
        },
        child: Scaffold(
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(70),
              child: AppBarWidget(
                title: resourceFile.name,
                trailingWidget: Container(),
                leadingIconName: BACK_ARROW,
                leadingBtnAction: () {
                  _onBackTapped(context);
                },
              )),
          body: ValueListenableBuilder(
              valueListenable: _isCompleted,
              builder: (context, _, __) {
                return Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16)),
                    ),
                    child: LayoutBuilder(builder:
                        (BuildContext context, BoxConstraints constraints) {
                      return Stack(children: [
                        Align(
                          child: Container(
                              width: constraints.maxWidth < 720
                                  ? double.infinity
                                  : 720,
                              margin: EdgeInsets.symmetric(
                                  horizontal: _horizontalMargin),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  _isCompleted.value ||
                                          (resourceProgress?.markedAsDone ??
                                              false)
                                      ? Container()
                                      : _doneBtnAction(context, MARK_AS_DONE,
                                          _sectionDetailsCubit, state, args),
                                  SizedBox(
                                      height: _isCompleted.value ||
                                              (resourceProgress?.markedAsDone ??
                                                  false)
                                          ? 20
                                          : 6),
                                  Expanded(
                                    child: SizedBox(
                                      height:
                                          MediaQuery.sizeOf(context).height -
                                              300,
                                      // width: MediaQuery.sizeOf(context).width - 70,

                                      child: Align(
                                        alignment: Alignment.topCenter,
                                        child: ClipRRect(
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(10)),
                                          child: PhotoView(
                                            tightMode: true,
                                            loadingBuilder:
                                                (BuildContext context,
                                                    ImageChunkEvent?
                                                        loadingProgress) {
                                              return const Center(
                                                child:
                                                    CircularProgressIndicator(),
                                              );
                                            },
                                            minScale: PhotoViewComputedScale
                                                    .contained *
                                                0.8,
                                            maxScale: PhotoViewComputedScale
                                                    .contained *
                                                2.0,
                                            backgroundDecoration:
                                                const BoxDecoration(
                                              color: LmsColors.white,
                                            ),
                                            imageProvider:
                                                NetworkImage(resourceFile.url),
                                            errorBuilder: (BuildContext context,
                                                Object exception,
                                                StackTrace? stackTrace) {
                                              return const Text(
                                                  IMAGE_VIEW_ERROR_TEXT);
                                            },
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  kIsWeb
                                      ? closeButton(constraints,
                                          _sectionDetailsCubit, context)
                                      : Container(),
                                ],
                              )),
                        )
                      ]);
                    }));
              }),
        ),
      );
    }));
  }

  Widget _doneBtnAction(
      BuildContext context,
      String title,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      Map<String, dynamic> args) {
    return Visibility(
      visible: _hasAccessToSaveProgress.value,
      child: Container(
        decoration: const BoxDecoration(
          borderRadius:
              BorderRadiusDirectional.vertical(top: Radius.circular(16)),
        ),
        height: 60,
        margin: const EdgeInsets.only(top: 10),
        child: Align(
          alignment: Alignment.topRight,
          child: ButtonWidget(
            minimumSize: const Size(10, 10),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(buttonRadius),
                side: const BorderSide(
                  color: AppTheme.examInfoTextColor,
                )),
            color: AppTheme.whiteTextColor,
            child: Text(
              MARK_AS_DONE,
              style: LMSFonts.semiBoldFont(16, AppTheme.examInfoTextColor),
            ),
            onPressed: () async => (resourceProgress?.progress ?? 0.0) >= 100
                ? {}
                : _showConfirmationDialogue(
                    context, _sectionDetailsCubit, state, args),
          ),
        ),
      ),
    );
  }

  _showConfirmationDialogue(
      BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      Map<String, dynamic> args) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (popcontext) {
        return MultiActionDialogue(
          alertType: AlertType.warning,
          title: CONFIRM,
          content: ALERT_TITLE,
          cancelBtnText: CANCEL_TEXT,
          confirmBtnText: EXAM_ALERT_OK,
          onCancelTap: () {
            kIsWeb ? Navigator.of(context).pop() : appRouter.popForced();
          },
          onContinueTap: () async {
            Navigator.of(popcontext).pop();
            _sectionDetailsCubit.setLoading(true);
            args = {'progress': 100, 'time_spent': time};
            await _sectionDetailsCubit.setCourseProgress(
                resourceFile.courseId, resourceFile.id, args);
            state = _sectionDetailsCubit.state;

            if (state is SetProgressState) {
              _isCompleted.value = true;
              _showProgressDialog(context, args);
            } else {
              _isCompleted.value = false;
              args = {'progress': 0, 'time_spent': time};
            }

            await _userActivityLog.setResProgressActivityLog(
                context: context,
                screen: 'Image View',
                resourceType: 'Image',
                id: resourceFile.id,
                result: state is SetProgressState ? 'success' : 'error',
                responseStatus: state is SetProgressState
                    ? 'Progress updated'
                    : 'Progress update failed');

            await _setResourceActivityLog(context,
                status: ResourceActivityStatus.completed);
            _sectionDetailsCubit.setLoading(false);
          },
        );
      },
    );
  }

  _showProgressDialog(BuildContext context, Map<String, dynamic> args) {
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
          alertType: AlertType.success,
          title: SUCESS,
          content: PROGRESS_SAVED,
          cancelBtnText: "",
          confirmBtnText: OK,
          onCancelTap: () {},
          onContinueTap: () {
            if (!_isCompleted.value) {
              kIsWeb
                  ? Beamer.of(context).beamBack()
                  : Navigator.of(context).pop();
            } else if (_isCompleted.value) {
              _onBackTapped(context);
            }
          },
        );
      },
    );
  }

  Widget closeButton(BoxConstraints constraints,
      SectionDetailsCubit _sectionDetailsCubit, BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Container(
        margin: constraints.maxWidth < 720
            ? const EdgeInsets.only(bottom: 10)
            : const EdgeInsets.only(bottom: 20),
        child: ButtonWidget(
            minimumSize: const Size(10, 10),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(buttonRadius),
                side: const BorderSide(
                  color: AppTheme.examInfoTextColor,
                )),
            color: AppTheme.whiteColor,
            child: Text(SLStrings.getTranslatedString(KEY_CLOSE),
                style: LMSFonts.regularFont(14, AppTheme.examIntroTextColor)),
            onPressed: () async {
              // if (moduleLength > 1) {
              //   kIsWeb
              //       ? Beamer.of(context)
              //           .beamToReplacementNamed('/section-details')
              //       : Navigator.of(context).pop();
              // } else {
              kIsWeb
                  ? Beamer.of(context).beamToReplacementNamed('/course-details')
                  : appRouter.popUntil((route) =>
                      route.settings.name == CourseDashboardRoute.name);
              // }
            }),
      ),
    );
  }

  Future<void> _setResourceActivityLog(BuildContext context,
      {required ResourceActivityStatus status}) async {
    await _userActivityLog.setResourceActivityLog(
        context: context,
        resourceType: 'Image',
        status: status,
        currentDuration:
            _isCompleted.value || (resourceProgress?.markedAsDone ?? false)
                ? 1.0
                : 0.0,
        id: resourceFile.id,
        result: 'success');
  }
}
