// ignore_for_file: must_be_immutable

part of 'course_list_cubit.dart';

abstract class CourseListState extends Equatable {
  final buttonColor;
  final List<Course> course;
  double videoProgress;
  final CourseDetails? courseDetailsData;
  final List<CurrentAffairs> currentAffairs;
  final List<DateTime> monthList;

  CourseListState({
    this.buttonColor = false,
    this.course = const [],
    this.videoProgress = 0.0,
    this.courseDetailsData,
    this.currentAffairs = const [],
    this.monthList = const [],
  });

  @override
  List<Object> get props => [buttonColor, course];
}

class CourseListInitial extends CourseListState {
  CourseListInitial({super.buttonColor});
}

class CourseListError extends CourseListState {
  final String error;
  CourseListError({required this.error});
}

class CourseListSuccess extends CourseListState {
  final List<Course> course;
  final buttonColor;

  CourseListSuccess({required this.course, required this.buttonColor
      // required this.topicList
      });
  @override
  List<Object> get props => [course, buttonColor];
}

class UpdateSelectedCourseColor extends CourseListState {
  final bool shouldChangeColor;
  UpdateSelectedCourseColor({required this.shouldChangeColor});
  @override
  List<Object> get props => [shouldChangeColor];
}

///
/// Course Details
///
class CourseDetailsInitial extends CourseListState {}

class CourseDetailsLoading extends CourseListState {
  final bool isDataLoading;
  CourseDetailsLoading({required this.isDataLoading, super.course});

  @override
  List<Object> get props => [isDataLoading];
}

class CourseDetailsFetched extends CourseListState {
  CourseDetailsFetched({super.courseDetailsData, super.course});

  @override
  List<Object> get props => [];
}

class CurrentAffairsFetched extends CourseListState {
  CurrentAffairsFetched({super.currentAffairs, super.course});

  @override
  List<Object> get props => [currentAffairs];
}

class CurrentAffairsFailure extends CourseListState {
  final String error;
  CurrentAffairsFailure({required this.error});

  @override
  List<Object> get props => [error];
}

class CourseDetailsEmpty extends CourseListState {
  CourseDetailsEmpty({super.course});
}

class CourseDetailsError extends CourseListState {
  final String error;
  CourseDetailsError({required this.error});

  @override
  List<Object> get props => [error];
}

///
/// Course Videos View
///
class CourseVideoStatus extends CourseListState {
  final bool isFullSCreen;
  CourseVideoStatus({required this.isFullSCreen});

  @override
  List<Object> get props => [isFullSCreen];
}

class VideoPlayerStatus extends CourseListState {
  VideoPlayerStatus();

  @override
  List<Object> get props => [];
}

class VideoPlayerError extends CourseListState {
  VideoPlayerError();

  @override
  List<Object> get props => [];
}

class CourseCommentsFetched extends CourseListState {
  final List<ResourseComment> comments;
  CourseCommentsFetched({required this.comments});

  @override
  List<Object> get props => [comments];
}

class CourseCommentsUploaded extends CourseListState {
  final List<ResourseComment> comments;
  CourseCommentsUploaded({required this.comments});

  @override
  List<Object> get props => [comments];
}

class CommentsAdded extends CourseListState {
  final List<ResourseComment> comments;
  CommentsAdded({required this.comments});

  @override
  List<Object> get props => [comments];
}

class CommentsError extends CourseListState {
  final String error;
  CommentsError({required this.error});

  @override
  List<Object> get props => [error];
}

class CommentTypeSelected extends CourseListState {
  final String commentType;
  CommentTypeSelected({required this.commentType});

  @override
  List<Object> get props => [commentType];
}

class RefreshCommentWindow extends CourseListState {
  RefreshCommentWindow();
}

///
/// Course News View
///
class CourseNewsSelection extends CourseListState {
  final DateTime selectedDate;
  final List<CurrentAffairs> filteredList;
  CourseNewsSelection({required this.selectedDate, required this.filteredList});

  @override
  List<Object> get props => [selectedDate, filteredList];
}

class CourseNewsScroll extends CourseListState {
  final bool canScroll;
  CourseNewsScroll({required this.canScroll});

  @override
  List<Object> get props => [canScroll];
}

class CourseNewsMonthGeneration extends CourseListState {
  // final DateTime selectedDate;

  CourseNewsMonthGeneration({super.monthList});

  @override
  List<Object> get props => [monthList];
}

// set course progress state
class SetResourceProgressState extends CourseListState {
  SetResourceProgressState();

  @override
  List<Object> get props => [];
}

class ResourceProgressFailureState extends CourseListState {
  final String error;
  ResourceProgressFailureState({required this.error});

  @override
  List<Object> get props => [error];
}

class CourseListButtonEnable extends CourseListState {
  final bool enable;

  CourseListButtonEnable({required this.enable});

  @override
  List<Object> get props => [enable];
}

///
/// UI Enchancements
///

class CarouselChangeIndex extends CourseListState {
  final int carouselIndex;

  CarouselChangeIndex({required this.carouselIndex});

  @override
  List<Object> get props => [carouselIndex];
}

class SetQuestionLoading extends CourseListState {
  final bool status;
  SetQuestionLoading({required this.status});

  @override
  List<Object> get props => [status];
}

// PWA - youtube loader

class YoutubePlayerLoaded extends CourseListState {
  final bool status;
  YoutubePlayerLoaded({this.status = false});

  @override
  List<Object> get props => [status];
}

// set resourcse as skipped
class SkipResourceSuccess extends CourseListState {
  SkipResourceSuccess();

  @override
  List<Object> get props => [];
}

class SkipResourceFailure extends CourseListState {
  final String error;
  SkipResourceFailure({required this.error});

  @override
  List<Object> get props => [error];
}


class ResourceLikeSuccess extends CourseListState {
  
  ResourceLikeSuccess();

  @override
  List<Object> get props => [];
}

class ResourceLikeError extends CourseListState {
  final String error;
  ResourceLikeError({required this.error});

  @override
  List<Object> get props => [error];
}