import 'package:auto_route/auto_route.dart';

import '../../config/themes/app_theme.dart';
import '../../utils/constants/strings.dart';
import '/src/presentation/views/subjects_detailed_tab_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';
import '../../utils/constants/daily_course_list_json.dart';
import '../widgets/app_bar.dart';

@RoutePage()
class DailyCourseListViewScreen extends HookWidget {
  final String title;
  const DailyCourseListViewScreen({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    List<Map<String, dynamic>> dataList = DailyCourseData.getDataList();

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: AppBarWidget(
          title: title,
          leadingIconName: BACK_ARROW,
          trailingWidget: Container(),
          leadingBtnAction: () {
            appRouter.popForced();
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16), topRight: Radius.circular(16)),
        ),
        child: ListView.builder(
          itemCount: dataList.length,
          padding: const EdgeInsets.only(top: 16.0, bottom: 16.0),
          itemBuilder: (context, index) {
            String week = dataList[index]["weeks"];
            List<String> modules =
                List<String>.from(dataList[index]["modules"]);

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: modules
                  .map((module) => Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: CardView(
                          title: '$week $module',
                          leadingWidget: const Icon(
                            Icons.article_outlined,
                          ),
                          trailingWidget: const Icon(Icons.lock),
                          index: index,
                          onTapCallback: () =>
                              {}, // appRouter.push(SubjectsDetailedTabViewRoute(title: '$week $module'))
                        ),
                      ))
                  .toList(),
            );
          },
        ),
      ),
    );
  }
}
