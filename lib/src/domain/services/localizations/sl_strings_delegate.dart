import '/src/utils/constants/lists.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'sl_strings.dart';

class SLStringsDelegate extends LocalizationsDelegate<SLStrings> {
  const SLStringsDelegate();

  @override
  bool isSupported(Locale locale) =>
      localizationLangauges.contains(locale.languageCode);

  @override
  Future<SLStrings> load(Locale locale) {
    SLStrings.currentLanguage = locale.languageCode;
    SLStrings.currentLocale = locale;
    return SynchronousFuture<SLStrings>(SLStrings());
  }

  @override
  bool shouldReload(LocalizationsDelegate<SLStrings> old) => true;
}
