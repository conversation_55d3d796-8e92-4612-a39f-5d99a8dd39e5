import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:pinput/pinput.dart';

import '../../config/router/app_router.dart';
import '../../config/themes/theme_colors.dart';
import '../../utils/constants/nums.dart';
import '../../utils/constants/strings.dart';
import '../cubits/otp/otp_cubit.dart';
import '../widgets/button_widget.dart';

class OtpView extends HookWidget {
  const OtpView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: () {
      FocusScopeNode currentFocus = FocusScope.of(context);
      if (!currentFocus.hasPrimaryFocus) {
        currentFocus.unfocus();
      }
    }, child: Scaffold(
      body: Bloc<PERSON><PERSON>er<OtpCubit, OtpState>(builder: (context, state) {
        return SafeArea(
          child: Container(
              child: Column(
            children: [
              const SizedBox(height: 20),
              _otpTitle(),
              otpTextField(context, state),
              _otpButton(context, state),
            ],
          )),
        );
      }),
    ));
  }

  Widget _otpTitle() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            loginTitle,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w900,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            loginSubTitle,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w900,
            ),
          ),
          const Text(
            loginDescription,
            style: const TextStyle(
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            verification + ' ' + '+919745825327',
            style: const TextStyle(
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 20),
          GestureDetector(
              onTap: () {
                // appRouter.push(const LoginEmailViewRoute());
              },
              child: const Text(
                editPhone,
                style: const TextStyle(
                  fontSize: 15,
                ),
              )),
          const SizedBox(height: 20),
          RichText(
            text: TextSpan(
                text: recievedCode,
                style: const TextStyle(color: Colors.black, fontSize: 18),
                children: <TextSpan>[
                  TextSpan(
                      text: resendCode,
                      style: const TextStyle(color: Colors.black, fontSize: 18),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          // appRouter.push(const LoginViewRoute());
                        })
                ]),
          ),
        ],
      ),
    );
  }

  Widget otpTextField(BuildContext context, OtpState state) {
    final defaultPinTheme = PinTheme(
      width: 50,
      height: 50,
      textStyle: const TextStyle(fontSize: 22, color: Colors.black),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Color.fromRGBO(239, 239, 243, 2)),
        boxShadow: [
          BoxShadow(
            color: Colors.white10.withOpacity(0.8),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3), // changes position of shadow
          ),
        ],
      ),
    );

    return Pinput(
      length: 6,
      keyboardType: TextInputType.number,
      defaultPinTheme: defaultPinTheme,
      textInputAction: TextInputAction.next,
      showCursor: true,
      validator: (s) {
        debugPrint('validating code: $s');
      },
      onCompleted: (val) {},
      onChanged: (val) {
        context.read<OtpCubit>().updateColor(val);
      },
    );
  }

  Widget _otpButton(BuildContext context, OtpState state) {
    return Expanded(
      child: Align(
        alignment: Alignment.bottomCenter,
        child: ButtonWidget(
          color: state.buttonColor ? LmsColors.secondaryGreen : LmsColors.grey,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonRadius),
          ),
          child: const Text(otpButtonText,
              style: TextStyle(
                fontSize: 20,
              )),
          onPressed: () {},
          //   textColor: Colors.white,
        ),
      ),
    );
  }
}
