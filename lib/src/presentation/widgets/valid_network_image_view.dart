import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:cached_network_image/cached_network_image.dart';

class ValidNetworkImage extends StatefulWidget {
  final String url;
  final String fallbackAssetPath;

  const ValidNetworkImage({
    Key? key,
    required this.url,
    required this.fallbackAssetPath,
  }) : super(key: key);

  @override
  State<ValidNetworkImage> createState() => _ValidNetworkImageState();
}

class _ValidNetworkImageState extends State<ValidNetworkImage> {
  final _isValidImage = ValueNotifier<bool?>(null);

  Future<void> _validateImageUrl() async {
    try {
      final response = await http.head(Uri.parse(widget.url));
      final contentType = response.headers['content-type'];
      if (mounted) {
        _isValidImage.value =
            contentType != null && contentType.startsWith('image/');
      }
    } catch (e) {
      if (mounted) {
        _isValidImage.value = false;
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _validateImageUrl();
  }

  @override
  void dispose() {
    _isValidImage.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool?>(
      valueListenable: _isValidImage,
      builder: (context, isValid, _) {
        if (isValid == null) {
          return const Center(child: CircularProgressIndicator());
        }

        if (isValid) {
          return CachedNetworkImage(
            imageUrl: widget.url,
            placeholder: (context, url) => const CircularProgressIndicator(),
            errorWidget: (context, url, error) =>
                Image.asset(widget.fallbackAssetPath, fit: BoxFit.cover),
            fit: BoxFit.cover,
          );
        } else {
          return Image.asset(widget.fallbackAssetPath, fit: BoxFit.cover);
        }
      },
    );
  }
}
