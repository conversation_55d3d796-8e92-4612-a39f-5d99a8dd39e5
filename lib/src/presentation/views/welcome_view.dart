import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '/src/config/themes/lms_fonts.dart';
import '/src/config/themes/theme_colors.dart';

import '../../config/app_config/sl_config.dart';
import '../../config/themes/app_theme.dart';
import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';

import '../widgets/app_bar.dart';

@RoutePage()
class WelcomeViewScreen extends HookWidget {
  const WelcomeViewScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvoked: (bool didPop) {
          if (didPop) {
            return;
          }
        },
        child: Scaffold(
          // appBar: PreferredSize(
          //     preferredSize: const Size.fromHeight(70),
          //     child: AppBarWidget(
          //       isFromLogin: true,
          //       title: SLConfig.APP_TITLE,
          //       trailingWidget: Container(),
          //       leadingIcon: BACK_ARROW,
          //       leadingBtnAction: () {},
          //     )),
          body: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            decoration: const BoxDecoration(
              color: AppTheme.onboardingBackground,
              image: DecorationImage(
                image: AssetImage('$ASSETS_PATH/background.png'),
                fit: BoxFit.cover,
              ),
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _logoWidget(),
                    _titleWidget(SLConfig.APP_TITLE),
                    //const SizedBox(height: 3),
                    //  _subtTitleWidget(SCHOOL_BANKING),
                    const SizedBox(height: 16),
                    _contentTitleWidget(WELCOME_SMART_LEARN),
                    const SizedBox(height: 10),
                    Text(
                      SMART_LEARN_ACCOUNT,
                      textAlign: TextAlign.center,
                      style: LMSFonts.regularFontWithHeight(
                          14, AppTheme.blackColor, 1.5),
                    ),
                    const SizedBox(height: 20),
                    _contentWidget(context),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        ));
  }

  _logoWidget() {
    return Image.asset(
      '$ASSETS_PATH/logo.png',
    );
  }

  _titleWidget(String title) {
    return Text(
      title,
      style: LMSFonts.semiBoldFont(30, AppTheme.whiteColor),
    );
  }

  _subtTitleWidget(String title) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: LMSFonts.semiBoldFont(18, AppTheme.whiteColor),
    );
  }

  _contentTitleWidget(String title) {
    return Text(
      title,
      style: LMSFonts.semiBoldFont(20, AppTheme.blackColor),
    );
  }

  _contentWidget(BuildContext context) {
    return GestureDetector(
      onTap: () => kIsWeb
          ? Beamer.of(context).beamToReplacementNamed('/login-view',
              data: {'isTokenExpired': false})
          : appRouter.push(LoginEmailViewRoute(isTokenExpired: false)),
      child: Text(
        RE_LOGIN,
        style: LMSFonts.regularFontUndelined(14, AppTheme.whiteColor),
      ),
    );
  }
}
