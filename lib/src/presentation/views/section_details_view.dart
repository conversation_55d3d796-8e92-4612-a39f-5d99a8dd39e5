import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/models/page_content.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../../utils/constants/nums.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '../widgets/alert_popup/confirmation_popup.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/empty_screen_view.dart';
import '/src/presentation/widgets/connectivity_widget.dart';

import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/resource_file.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../widgets/debounced_button.dart';
import '../widgets/stepper_widget.dart' as stepper;
import '/src/config/enums/section_type.dart';
import '/src/config/themes/lms_fonts.dart';
import '../../config/enums/section_status.dart';
import '../../domain/models/question_data.dart';
import '../../utils/constants/strings.dart';
import '../cubits/exam/exam_cubit.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/domain/models/course_details.dart';
import '/src/presentation/cubits/section_details/section_details_cubit.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../domain/models/responses/section_details.dart';
import '../widgets/app_bar.dart';
import '../widgets/loading_indicator.dart';

@RoutePage()
// ignore: must_be_immutable
class SectionDetailsViewScreen extends HookWidget with WidgetsBindingObserver {
  final String sectionId;
  final String title;

  SectionDetailsViewScreen(
      {Key? key, required this.sectionId, required this.title})
      : super(key: key);

  SectionDetails? sectionDetails;
  QuestionData? _questionData;

  List<Question> _questions = [];

  String? pathName;

  int _currentStep = 0;
  int remainingAttempts = 0;

  ValueNotifier<bool> isDataLoading = ValueNotifier(false);
  ValueNotifier<bool> hasValidContents = ValueNotifier(true);
  BuildContext? currentContext;

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    // TO DO: context should not be equal to null
    didChangeAppLocale(locales, currentContext);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final _sectionDetailsCubit = BlocProvider.of<SectionDetailsCubit>(context);
    final _examCubit = BlocProvider.of<ExamCubit>(context);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);

      Future<void>.microtask(() async {
        isDataLoading.value = true;

        await _sectionDetailsCubit.fetchSectionDetails(sectionId);
        SectionDetailsState state = _sectionDetailsCubit.state;
        if (state is SectionDetailsError) {
          showPGExceptionPopup(context, state.error);
        }
        isDataLoading.value = false;
      });
      return () {
        currentContext = null;
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: title,
              trailingWidget: Container(),
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {
                kIsWeb
                    ? Beamer.of(context)
                        .beamToReplacementNamed('/course-details')
                    : Navigator.of(context).pop();
              },
            )),
        body: BlocListener<SectionDetailsCubit, SectionDetailsState>(
            listener: (context, state) async {
          if (state is SectionDetailsExapanding) {
            sectionDetails?.modules[state.index].expansionStatus = state.expand;
          } else if (state is SectionDetailschangeStatus) {
            sectionDetails?.modules[state.index].status =
                SectionStatus.done.value;
          } else if (state is SectionDetailsFetched) {
            sectionDetails = SectionDetails(
                resources: state.sectionDetailsData?.resources ?? [],
                modules: state.sectionDetailsData?.modules ?? []);
            sectionDetails = state.sectionDetailsData;
            hasValidContents.value = (sectionDetails != null &&
                    sectionDetails?.modules != null &&
                    sectionDetails!.modules.isNotEmpty) &&
                state is! SectionDetailsInitial;
          } else if (state is SectionDetailsListLoading) {
            // isDataLoading.value = state.isDataLoading;
          } else if (state is StepItemTapState) {
            _currentStep = state.step;
          }
        }, child: BlocBuilder<SectionDetailsCubit, SectionDetailsState>(
                builder: (context, state) {
          if (state is SectionDetailsFetched) {
            sectionDetails = SectionDetails(
                resources: state.sectionDetailsData?.resources ?? [],
                modules: state.sectionDetailsData?.modules ?? []);
            sectionDetails = state.sectionDetailsData;
            hasValidContents.value = (sectionDetails != null &&
                    sectionDetails?.modules != null &&
                    sectionDetails!.modules.isNotEmpty) &&
                state is! SectionDetailsInitial;
          }
          return ValueListenableBuilder(
              valueListenable: isDataLoading,
              builder: (context, _, __) {
                return Container(
                  decoration: const BoxDecoration(
                    color: AppTheme.whiteColor,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16)),
                  ),
                  child: LayoutBuilder(builder: (context, constraints) {
                    return Stack(children: [
                      Align(
                        child: Container(
                          width: constraints.maxWidth < 720
                              ? constraints.maxWidth
                              : 800,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 20),
                          child: Card(
                              color: AppTheme.sectionCardBgColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.0),
                              ),
                              child: ScrollConfiguration(
                                behavior:
                                    ScrollConfiguration.of(context).copyWith(
                                  dragDevices: {
                                    PointerDeviceKind.touch,
                                    PointerDeviceKind.mouse,
                                  },
                                ),
                                child: Column(children: [
                                  Container(
                                    decoration: const BoxDecoration(
                                      color: AppTheme.stepperColor,
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(10),
                                        topRight: Radius.circular(10),
                                      ),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 25, vertical: 15),
                                    width: MediaQuery.of(context).size.width,
                                    height: 50,
                                    child: Text(
                                      SECTION_TITLE,
                                      style: LMSFonts.sectionProgressTextStyle(
                                          16, AppTheme.whiteColor),
                                    ),
                                  ),
                                  Expanded(
                                      child: Theme(
                                          data: ThemeData(
                                            colorScheme: Theme.of(context)
                                                .colorScheme
                                                .copyWith(
                                                  primary:
                                                      AppTheme.stepperColor,
                                                ),
                                          ),
                                          child: sectionDetails != null &&
                                                  sectionDetails!
                                                      .modules!.isNotEmpty
                                              ? _stepperWidget(
                                                  _currentStep,
                                                  context,
                                                  sectionDetails?.modules,
                                                  _examCubit,
                                                  _sectionDetailsCubit,
                                                  state)
                                              : Container()))
                                ]),
                              )),
                        ),
                      ),
                      ConnectivityStatusWidget(),
                      isDataLoading.value == true
                          ? Positioned.fill(child: LoadingIndicatorClass())
                          : !hasValidContents.value
                              ? EmptyScreenView(
                                  title: SLStrings.getTranslatedString(
                                      KEY_SUBJECTS_EMPTY),
                                  showRetryButton: Supabase.instance.client.auth
                                              .currentSession?.isExpired ==
                                          true
                                      ? true
                                      : false,
                                  retryAction: () async {
                                    isDataLoading.value = true;
                                    await _sectionDetailsCubit
                                        .fetchSectionDetails(sectionId);
                                    isDataLoading.value = false;
                                  },
                                )
                              : Container()
                    ]);
                  }),
                );
              });
        })));
  }

  String _getProgressval(int index) {
    double progressPercentage =
        sectionDetails?.modules[index].progress.toDouble() ?? 0.0;
    progressPercentage /= 100;
    String progressvalue = progressPercentage != null
        ? progressPercentage == 0.0
            ? '0'
            : (progressPercentage * 100).toStringAsFixed(0)
        : '0';
    progressvalue = progressvalue == 'NaN' ? '0' : progressvalue;
    return progressvalue;
  }

  Widget _stepperWidget(
      int _currentStep,
      BuildContext context,
      List<Modules>? module,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state) {
    int length = module?.length ?? 0;
    List<stepper.Step> stepList = [];

    for (int index = 0; index < length; index++) {
      remainingAttempts = sectionDetails?.modules[index].remainingAttempts ?? 0;

      String progressvalue = _getProgressval(index);
      String resoureType =
          sectionDetails?.modules[index].moduleType.toLowerCase() ?? '';
      bool _isTypeQuiz = resoureType == SectionType.quiz.value;
      bool _isTypePage = resoureType == SectionType.page.value;

      bool _isTypeFileImage = resoureType == SectionType.image.value ||
          resoureType == SectionType.file.value;

      stepList.add(
        stepper.Step(
          title: Row(children: [
            _isTypeQuiz
                ? _imageWidget('question_light.png', 24.0, enableIcon: true)
                : _isTypePage
                    ? _imageWidget('file_light.png', 24.0, enableIcon: true)
                    : _isTypeFileImage
                        ? _imageWidget('file_light.png', 24.0, enableIcon: true)
                        : _imageWidget('video_light.png', 24.0,
                            enableIcon: true),
            _moduleNameWidget(index),
            sectionDetails?.modules[index].didMarkedAsDone ?? false
                ? _imageWidget('done_button.png', 24.0, enableIcon: true)
                : Container()
          ]),
          content: _stepperContentWidget(context, _sectionDetailsCubit, state,
              _examCubit, index, progressvalue),
          isActive: _currentStep >= index,
        ),
      );
    }
    return stepper.Stepper(
      key: ValueKey(getRandomValueForKey()),
      currentStep: _currentStep,
      controlsBuilder: (context, controller) {
        return Container();
      },
      stepIconBuilder: (stepIndex, stepState) {
        return Container(
          decoration: const BoxDecoration(
            color: AppTheme.stepperColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              (stepIndex + 1).toString(),
              style: LMSFonts.sectionProgressTextStyle(14, AppTheme.whiteColor),
            ),
          ),
        );
      },
      onStepTapped: (int index) {
        _sectionDetailsCubit.stepItemTapped(index);
      },
      steps: stepList,
    );
  }

  Widget _moduleNameWidget(int index) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(left: 8),
        child: _moduleNameText(index),
      ),
    );
  }

  Widget _moduleNameText(int index) {
    return Text(
      sectionDetails?.modules[index].moduleName.trim() ?? '',
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: LMSFonts.mediumFont(14, AppTheme.progressContainerColor, 1.0),
    );
  }

  Widget _stepperContentWidget(
      BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      ExamCubit _examCubit,
      int index,
      String progressvalue) {
    Modules? moduleObj = sectionDetails?.modules[index];

    String resoureType = moduleObj?.moduleType.toLowerCase() ?? '';

    bool _isTypeQuiz = resoureType == SectionType.quiz.value;
    bool _isTypeVideo = resoureType == SectionType.video.value ||
        resoureType == SectionType.url.value;
    bool _isTypePage = resoureType == SectionType.page.value;
    bool _isTypeFileImage = resoureType == SectionType.image.value ||
        resoureType == SectionType.file.value;

    if (moduleObj == null) {
      return Container();
    } else {
      return Column(
        children: <Widget>[
          DebouncedButton(
            disableButton: false,
            onPressed: () {
              _onTapNavigationInfo(context, _examCubit, _sectionDetailsCubit,
                  state, index, moduleObj);
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AppTheme.sectionDetailsBgColor,
              ),
              padding: const EdgeInsets.symmetric(
                  horizontal: horizontalMargin, vertical: 10),
              child: Row(
                children: [
                  (_isTypeVideo && moduleObj.progress != 0)
                      ? _videoProgressWidget(progressvalue)
                      : Container(),
                  Expanded(
                    child: _moduleNameText(index),
                  ),
                  _isTypeQuiz
                      ? _examAttendBtn(
                          context, _sectionDetailsCubit, _examCubit, moduleObj)
                      : _isTypePage || _isTypeFileImage
                          ? _imageWidget('view.png', 24.0, enableIcon: true)
                          : _imageWidget('arrow.png', 16.0, enableIcon: true)
                ],
              ),
            ),
          ),
        ],
      );
    }
  }

  Widget _videoProgressWidget(String progressvalue) {
    return Container(
        width: 45,
        height: 30,
        margin: const EdgeInsets.only(right: 15),
        decoration: BoxDecoration(
          color: AppTheme.progressContainerColor,
          borderRadius: BorderRadius.circular(5.0),
        ),
        child: Center(
          child: Text(
            progressvalue + '%',
            style: LMSFonts.sectionProgressTextStyle(12, AppTheme.whiteColor),
          ),
        ));
  }

  Widget _examAttendBtn(
      BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit,
      ExamCubit _examCubit,
      Modules moduleObj) {
    return GestureDetector(
      onTap: remainingAttempts > 0
          ? () async {
              await _fetchQuestionsForExam(
                _examCubit,
                context,
                _sectionDetailsCubit,
                moduleObj.instanceId ?? '',
              );
            }
          : null,
      child:
          _imageWidget('quiz_add.png', 24.0, enableIcon: remainingAttempts > 0),
    );
  }

  Widget _imageWidget(String path, double height, {required bool enableIcon}) {
    return Image.asset(
      '$ASSETS_PATH/$path',
      height: height,
      color:
          enableIcon ? AppTheme.iconEnabledColor : AppTheme.iconDisableddColor,
    );
  }

  _navigationInfo(BuildContext context) async {
    try {
      kIsWeb
          ? Beamer.of(context).beamToNamed('/exam-intro', data: {
              'questionData': _questionData!,
              'isFromSectionDetails': true
            })
          : appRouter.push(ExamIntroductionViewRoute(
              questionData: _questionData!, isFromSectionDetails: true));
    } catch (e) {
      print("exception => _navigationInfo => $e");
    }
  }

  _onTapNavigationInfo(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      int index,
      Modules moduleObj) async {
    String resoureType = moduleObj.moduleType.toLowerCase();

    bool _isTypeQuiz = resoureType == SectionType.quiz.value;

    bool _isTypeVideo = resoureType == SectionType.video.value ||
        resoureType == SectionType.url.value;
    bool _isTypePage = resoureType == SectionType.page.value;
    bool _isTypeFileImage = resoureType == SectionType.image.value ||
        resoureType == SectionType.file.value;

    String instanceId = moduleObj.instanceId;
    CourseProgress? _courseProgress =
        await _fetchCourseProgress(context, _sectionDetailsCubit, instanceId);

    bool _hasAccess = await _checkAccessForFetchingResource();

    if (_isTypeQuiz) {
      if (moduleObj.remainingAttempts > 0) {
        await _fetchQuestionsForExam(
          _examCubit,
          context,
          _sectionDetailsCubit,
          moduleObj.instanceId ?? '',
        );
      }
    } else if (_courseProgress != null && _hasAccess) {
      if (_isTypeVideo) {
        await _handleVideoTypeNavigation(context, _examCubit,
            _sectionDetailsCubit, state, index, _courseProgress);
      } else if (_isTypePage) {
        await _handlePageTypeNavigation(context, _examCubit,
            _sectionDetailsCubit, state, index, _courseProgress);
      } else if (_isTypeFileImage) {
        await _handleFileImageTypeNavigation(context, _examCubit,
            _sectionDetailsCubit, state, index, _courseProgress);
      }
    } else {
      showNoAccessPopup(context, '');
    }
  }

  Future<bool> _checkAccessForFetchingResource() async {
    // decides whether to fetch course resource- url, video, image, file
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_RESOURCE;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _handleVideoTypeNavigation(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      int index,
      CourseProgress courseProgress) async {
    CourseVideo? _courseVideoObj;
    List<CheckPoint> _checkPoints = [];
    String? id;
    String instanceId = sectionDetails!.modules[index].instanceId;

    isDataLoading.value = true;

    await _sectionDetailsCubit.fetchVideoContent(instanceId);
    // await _sectionDetailsCubit
    //     .fetchCheckPointData("9bf89423-3b9b-4b83-a654-b4a6f6c05b2a");
    state = _sectionDetailsCubit.state;

    if (state is VideoResourceError) {
      // await _sectionDetailsCubit.setLoading(false);
      isDataLoading.value = false;

      showPGExceptionPopup(context, state.error);
    } else if (state is VideoResourceFetched) {
      _courseVideoObj = state.courseVideo;
      _courseVideoObj.instanceId = instanceId;
      _checkPoints.addAll(state.checkPoints);
      state = _sectionDetailsCubit.state;

      isDataLoading.value = false;

      // await _sectionDetailsCubit.setLoading(false);

      id = sectionDetails?.courseId;
      await _sectionDetailsCubit.getCourseprogress(_courseVideoObj.instanceId);
      state = _sectionDetailsCubit.state;
      if (state is GetCourseProgressState) {
        CourseProgress courseProgress = state.progress;

        kIsWeb
            ? Beamer.of(context).beamToNamed(
                "/video-view",
                data: {
                  "courseVideo": _courseVideoObj,
                  "courseId": id!,
                  "progess": courseProgress,
                  "isFromSectionDetails": true,
                  "checkPoints": _checkPoints
                },
              )
            : appRouter
                .push(CourseVideoViewRoute(
                    courseVideo: _courseVideoObj,
                    courseId: id!,
                    progess: courseProgress,
                    isFromSectionDetails: true,
                    checkPoints: _checkPoints))
                .then((value) async {
                if (value != null) {
                  Map<String, dynamic> args = value as Map<String, dynamic>;

                  double progress = double.parse(args['progress'].toString());
                  sectionDetails?.modules[index].progress = progress.toInt();
                  if (progress == 100) {
                    sectionDetails?.modules[index].didMarkedAsDone = true;
                  }
                  await _sectionDetailsCubit.refreshScreen();
                }
              });
      }
    }
  }

  _handlePageTypeNavigation(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      int index,
      CourseProgress courseProgress) async {
    String instanceId = sectionDetails!.modules[index].instanceId;
    isDataLoading.value = true;
    // await _sectionDetailsCubit.setLoading(true);
    await _sectionDetailsCubit.fetchPageContent(instanceId);
    state = _sectionDetailsCubit.state;
    if (state is PageContentsFetched) {
      // await _sectionDetailsCubit.getCourseprogress(instanceId);
      // state = _sectionDetailsCubit.state;
      // if (state is GetCourseProgressState) {
      //   CourseProgress courseProgress = state.progress;
      PageContent pageContent = state.pageContentData;
      isDataLoading.value = false;
      // await _sectionDetailsCubit.setLoading(false);
      kIsWeb
          ? Beamer.of(context).beamToNamed('/study-materials', data: {
              'pageContent': pageContent,
              'instance': instanceId,
              'courseId': sectionDetails!.modules[index].courseId,
              'progess': courseProgress
            })
          : appRouter
              .push(StudyMaterialViewRoute(
                  pageContent: pageContent,
                  instance: instanceId,
                  courseId: sectionDetails!.modules[index].courseId,
                  progess: courseProgress))
              .then((value) async {
              if (value != null && value == true) {
                // Map<String, dynamic> args = value as Map<String, dynamic>;

                double studyProgress = 100.0;
                sectionDetails?.modules[index]?.progress =
                    studyProgress.toInt();
                // if (studyProgress == 100) {
                sectionDetails?.modules[index].didMarkedAsDone = true;
                // }
                await _sectionDetailsCubit.refreshScreen();
              }
            });
      // } else if (state is GetCourseProgressFailureState) {
      //   showPGExceptionPopup(context, state.error);
      // }
    } else if (state is PageDetailsError) {
      isDataLoading.value = false;

      // await _sectionDetailsCubit.setLoading(false);
      showPGExceptionPopup(context, state.error);
    }
  }

  _handleFileImageTypeNavigation(
      BuildContext context,
      ExamCubit _examCubit,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      int index,
      CourseProgress courseProgress) async {
    try {
      ResourseFile? _resourceFile;
      String instance = sectionDetails!.modules[index].instanceId;
      isDataLoading.value = true;

      //  await _sectionDetailsCubit.setLoading(true);
      await _sectionDetailsCubit.fetchFileContent(instance);
      // For Image View

      state = _sectionDetailsCubit.state;

      if (state is FileResourceFetched) {
        _resourceFile = state.resourseFile;
        _resourceFile.instanceId = instance;

        // await _sectionDetailsCubit
        //     .getCourseprogress(sectionDetails!.modules[index].instanceId);

        // state = _sectionDetailsCubit.state;

        // if (state is GetCourseProgressState) {
        //   CourseProgress courseProgress = state.progress;
        bool isNetworkUrl = await validateImage(_resourceFile.url ?? '');
        if (_resourceFile != null && !_resourceFile.url.endsWith(HTML)) {
          if (_resourceFile.url.endsWith(IMAGE_EXTENSIONJPG) ||
              _resourceFile.url.endsWith(IMAGE_EXTENSIONJPEG) ||
              _resourceFile.url.endsWith(IMAGE_EXTENSIONPNG) ||
              (isNetworkUrl)) {
            _handleNavigationToImageView(_sectionDetailsCubit, index,
                courseProgress, _resourceFile, context);
          } else {
            isDataLoading.value = false;
            //   await _sectionDetailsCubit.setLoading(false);
            _showAlertDialog(context,
                SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
          }
        } else {
          isDataLoading.value = false;
          //   await _sectionDetailsCubit.setLoading(false);
          _showAlertDialog(
              context, SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
        }
        // } else if (state is GetCourseProgressFailureState) {
        //   showPGExceptionPopup(context, state.error);
        // }
      } else if (state is FileResourceError) {
        isDataLoading.value = false;
        // await _sectionDetailsCubit.setLoading(false);
        showPGExceptionPopup(context, state.error);
      } else {
        isDataLoading.value = false;
        // await _sectionDetailsCubit.setLoading(false);
        _showAlertDialog(
            context, SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
      }
    } on Exception catch (_) {
      // TODO
    }
  }

  _handleNavigationToImageView(
    SectionDetailsCubit _sectionDetailsCubit,
    int index,
    CourseProgress courseProgress,
    ResourseFile _resourceFile,
    BuildContext context,
  ) async {
    isDataLoading.value = false;
    // await _sectionDetailsCubit.setLoading(false);
    kIsWeb
        ? Beamer.of(context).beamToNamed('/image-view', data: {
            'imagePath': _resourceFile.url,
            'resourceFile': _resourceFile,
            'instance': sectionDetails!.modules[index].instanceId,
            'courseId': sectionDetails!.modules[index].courseId,
            'progess': courseProgress,
          })
        : appRouter
            .push(ImageViewRoute(
                imagePath: _resourceFile.url,
                resourceFile: _resourceFile,
                instance: sectionDetails!.modules[index].instanceId,
                courseId: sectionDetails!.modules[index].courseId,
                progess: courseProgress))
            .then((value) async {
            if (value != null) {
              Map<String, dynamic> args = value as Map<String, dynamic>;

              double imageProgress = double.parse(args['progress'].toString());
              sectionDetails?.modules[index].progress = imageProgress.toInt();
              if (imageProgress == 100) {
                sectionDetails?.modules[index].didMarkedAsDone = true;
              }
              await _sectionDetailsCubit.refreshScreen();
            }
          });
  }

  Future<bool> _checkAccessToFetchQuestions() async {
    // decides whether to disable/enable attend  button
    String screen = PrivilegeAccessConsts.SCREEN_EXAM;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_GET_QUIZ_QUESTIONS;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  Future<bool> _checkAccessToFetchCourseProgress() async {
    // decides whether to fetch course progress
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_GET_COURSE_PROGRESS;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  ///fetch the progress value for any type resource
  Future<CourseProgress?> _fetchCourseProgress(BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit, String instanceId) async {
    var route = ModalRoute.of(context);
    bool _hasAccess = await _checkAccessToFetchCourseProgress();
    if (_hasAccess) {
      SectionDetailsState state = _sectionDetailsCubit.state;
      await _sectionDetailsCubit.getCourseprogress(instanceId);
      state = _sectionDetailsCubit.state;
      if (state is GetCourseProgressState) {
        CourseProgress courseProgress = state.progress;
        return courseProgress;
      } else if (state is GetCourseProgressFailureState) {
        showPGExceptionPopup(context, state.error);
      }
    } else {
      if (route != null && route.isActive) {
        showNoAccessPopup(context, '');
      }
    }
    return null;
  }

  void _showAlertDialog(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: msg,
          iconWidget: const Icon(Icons.error, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  pdfScreenNavigation(
      SectionDetailsCubit _sectionDetailsCubit, BuildContext context) async {
    try {
      isDataLoading.value = true;
      // _sectionDetailsCubit.setLoading(true);
      String path = await _sectionDetailsCubit.getFileFromUrl(SAMPLE_PDF_PATH);

      if (path != null) {
        kIsWeb
            ? Beamer.of(context)
                .beamToNamed('/pdf-view', data: {'urlPDFPath': path})
            : await appRouter.push(PDFViewRoute(urlPDFPath: path));
      }
      isDataLoading.value = false;

      // _sectionDetailsCubit.setLoading(false);
    } on Exception catch (e) {
      debugPrint('[section_details_view.dart][pdfScreenNavigation]: $e');
    }
  }

  _fetchQuestionsForExam(ExamCubit _examCubit, BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit, String examId) async {
    try {
      var route = ModalRoute.of(context);
      bool _hasAccess = await _checkAccessToFetchQuestions();

      if (_hasAccess) {
        isDataLoading.value = true;
        //    _sectionDetailsCubit.setLoading(true);
        await _examCubit.fetchQuestions(examId);
        isDataLoading.value = false;
        // _sectionDetailsCubit.setLoading(false);

        ExamState state = _examCubit.state;

        if (state is ExamsQuestionsFailureState) {
          if (route != null && route.isActive) {
            showPGExceptionPopup(context, state.error);
          }
        } else {
          if (_examCubit.state.examData.isNotEmpty) {
            _questionData = _examCubit.state.examData.first;
          }
          _questions = _examCubit.state.examData.isEmpty
              ? []
              : _examCubit.state.examData.first.questions;

          DateTime currentTime = DateTime.now();
          DateTime startTime = _questionData?.startTime ?? currentTime;
          DateTime endTime = _questionData?.endTime ?? currentTime;

          if ((currentTime.isAfter(startTime) &&
                  currentTime.isBefore(endTime)) ||
              (currentTime.isAtSameMomentAs(startTime))) {
            // check if the exam is valid
            if (_questions.isNotEmpty) {
              _navigationInfo(context);
            } else {
              // no questions available for the exam
              _showQuestionsEmptyPopup(context);
            }
          } else {
            // not valid
            // exam expired
            // TO DO: remove after- modify list tile UI and show as invalid
            if (currentTime.isBefore(startTime)) {
              String startTimeFormatted =
                  AppDateFormatter().formatForExamAvailabilityPopup(startTime);
              _showInvalidExamPopup(context,
                  '${SLStrings.getTranslatedString(KEY_EXAM_AVAILABLE_POPUP)} $startTimeFormatted');
            } else {
              _showInvalidExamPopup(
                  context, SLStrings.getTranslatedString(KEY_EXAM_EXPIRED));
            }
          }
        }
      } else {
        if (route != null && route.isActive) {
          showNoAccessPopup(context, '');
        }
      }
    } on Exception catch (e) {
      debugPrint('[section_details_view][_fetchQuestionsForExam]: $e');
    }
  }

  _showInvalidExamPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: msg,
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showQuestionsEmptyPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        // ignore: lines_longer_than_80_chars
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: SLStrings.getTranslatedString(KEY_QUESTION_NOT_AVAILABLE),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              return;
            }
          },
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            alertType: AlertType.error,
            onContinueTap: () =>
                kIsWeb ? Navigator.of(context).pop() : appRouter.pop(),
            onCancelTap: () {
              kIsWeb
                  ? Beamer.of(context).beamToNamed('/section-details')
                  : appRouter.popUntil((route) =>
                      route.settings.name == SectionDetailsViewRoute.name);
            },
            cancelBtnText: SLStrings.getTranslatedString(KEY_OK),
            confirmBtnText: '',
          ),
        );
      },
    );
  }
}
