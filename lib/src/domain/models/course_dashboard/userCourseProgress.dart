// To parse this JSON data, do
//
//     final userCourseProgress = userCourseProgressFromJson(jsonString);

import 'dart:convert';

UserCourseProgress userCourseProgressFromJson(String str) =>
    UserCourseProgress.fromJson(json.decode(str));

String userCourseProgressToJson(UserCourseProgress data) =>
    json.encode(data.toJson());

class UserCourseProgress {
  List<UserCourseProgressResult> result;
  String status;

  UserCourseProgress({
    required this.result,
    required this.status,
  });

  factory UserCourseProgress.fromJson(Map<String, dynamic> json) =>
      UserCourseProgress(
        result:
            List<UserCourseProgressResult>.from(json["result"].map((x) => UserCourseProgressResult.fromJson(x))),
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "result": List<dynamic>.from(result.map((x) => x.toJson())),
        "status": status,
      };
}

class UserCourseProgressResult {
  String timeSpent;
  String courseName;
  int currentPage;
  String currentTime;
  String totalLength;
  String resourceName;
  double totalProgress;
  double progressPercent;

  UserCourseProgressResult({
    required this.timeSpent,
    required this.courseName,
    required this.currentPage,
    required this.currentTime,
    required this.totalLength,
    required this.resourceName,
    required this.totalProgress,
    required this.progressPercent,
  });

  factory UserCourseProgressResult.fromJson(Map<String, dynamic> json) => UserCourseProgressResult(
        timeSpent: json["time_spent"] ?? "",
        courseName: json["course_name"] ?? "",
        currentPage: json["current_page"] ?? 0,
        currentTime: json["current_time"] ?? "",
        totalLength: json["total_length"] ?? "",
        resourceName: json["resource_name"] ?? "",
        totalProgress: (json["total_progress"] ?? 0) * 1.0,
        progressPercent: (json["progress_percent"] ?? 0) * 1.0,
      );

  Map<String, dynamic> toJson() => {
        "time_spent": timeSpent,
        "course_name": courseName,
        "current_page": currentPage,
        "current_time": currentTime,
        "total_length": totalLength,
        "resource_name": resourceName,
        "total_progress": totalProgress,
        "progress_percent": progressPercent,
      };
}
