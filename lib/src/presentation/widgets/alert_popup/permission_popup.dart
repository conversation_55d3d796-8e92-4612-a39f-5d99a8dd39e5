// ignore_for_file: unnecessary_null_comparison

import 'package:auto_route/auto_route.dart';

import '../../../config/enums/alert_types.dart';
import '../../../domain/services/localizations/sl_strings.dart';
import '../../../domain/services/localizations/string_keys.dart';
import '/src/utils/constants/strings.dart';

import '/src/config/router/app_router.dart';
import 'multi_action_dialogue.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

showWarning(BuildContext context, String access, String msg) {
  bool isThereCurrentDialogShowing = ModalRoute.of(context)?.isCurrent != true;
  var currentpage = appRouter.current.route.name;
  if (isThereCurrentDialogShowing && currentpage != ExamViewRoute.name) {
    ///
    /// exam gets submitted when goes to the  background
    /// so no need to show lang change alert in exam view
    ///
    appRouter.popUntil((route) => route.settings.name == currentpage);
  }
  Future.delayed(const Duration(seconds: 1), () {
    if (context != null &&
        context.routeData != null &&
        context.routeData.isActive) {
      showDialog(
          context: context,
          builder: (context1) {
            String title = access == GALLERY
                ? SLStrings.getTranslatedString(KEY_STORAGE_PERMISSION_WARNING)
                : SLStrings.getTranslatedString(KEY_CAMERA_PERMISSION_WARNING);

            String content = msg.isNotEmpty
                ? msg
                : access == GALLERY
                    ? SLStrings.getTranslatedString(KEY_GALLERY_PERMISSION_REQ)
                    : SLStrings.getTranslatedString(KEY_CAMERA_PERMISSION_REQ);
            return MultiActionDialogue(
              title: title,
              alertType: AlertType.warning,
              content: content,
              cancelBtnText: SLStrings.getTranslatedString(KEY_CANCEL),
              confirmBtnText: SLStrings.getTranslatedString(KEY_ENABLE_NOW),
              onContinueTap: () {
                appRouter.popForced();
                openAppSettings();
              },
              onCancelTap: () {
                appRouter.popForced();
              },
            );
          });
    }
  });
}
