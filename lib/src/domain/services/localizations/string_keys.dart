///
/// Stores the keys for localization for each string used in langauge files
///
///

// ignore_for_file: constant_identifier_names

///
///
const String KEY_SUCCESS = 'SUCCESS';
const String KEY_SUBMIT = 'SUBMIT';
const String KEY_YES = 'YES';
const String KEY_OK = 'OK';
const String KEY_DONE = 'DONE';
const String KEY_CONTINUE = 'CONTINUE';
const String KEY_FINISH = 'FINISH';
const String KEY_CLOSE = 'CLOSE';
const String KEY_NO = 'NO';
const String KEY_LOADING = 'LOADING';
const String KEY_WARNING = 'WARNING';
const String KEY_ERROR = 'ERROR';

const String KEY_SORRY = 'SORRY';
const String KEY_START = 'START';

const String KEY_OOPS_ALERT = 'OOPS';
const String KEY_CONFIRM = 'CONFIRM';
const String KEY_CANCEL = 'CANCEL';
const String KEY_SEND = 'SEND';
const String KEY_DELETE = 'DELETE';

///
/// login
///

const String KEY_TITLE = 'TITLE';
const String KEY_EMAIL = 'EMAIL';
const String KEY_PASSWORD = 'PASSWORD';
const String KEY_BUTTON_TEXT = 'BUTTON_TEXT';
const String KEY_FORGOT_PASSWORD = 'FORGOT_PASSWORD';
const String KEY_OR_CONTINUE_WITH = 'OR_CONTINUE_WITH';
const String KEY_NO_ACCOUNT = 'NO_ACCOUNT';
const String KEY_SIGNUP = 'SIGNUP';
const String KEY_RE_LOGIN = 'RE_LOGIN';
const String KEY_INVALID_EMAIL = 'INVALID_EMAIL';
const String KEY_EMAIL_FIELD_REQUIRED = 'EMAIL_FIELD_REQUIRED';
const String KEY_VALID_PASSWORD = 'VALID_PASSWORD';

///
/// sign up view
///

const String KEY_SIGNUP_TITLE = 'SIGNUP_TITLE';
const String KEY_FIRST_NAME = 'FIRST_NAME';
const String KEY_LAST_NAME = 'LAST_NAME';
const String KEY_CONFIRM_PASSWORD = 'CONFIRM_PASSWORD';
const String KEY_PHONE_NUMBER = 'PHONE_NUMBER';
const String KEY_ACCOUNT = 'ACCOUNT';
const String KEY_LOGIN = 'LOGIN';
const String KEY_REGISTER_SUCCESS_MSG = 'REGISTER_SUCCESS_MSG';
const String KEY_EMAIL_EXIST = 'EMAIL_EXIST';

///
/// course details view
///

const String KEY_EMPTY_VIEW_TEXT = 'EMPTY_VIEW_TEXT';
const String KEY_CURRENT_AFFAIRS = 'CURRENT_AFFAIRS';
const String KEY_SEE_ALL = 'SEE_ALL';
const String KEY_SEE_LESS = "SEE_LESS";
const String KEY_CURRENT_AFFAIRS_EMPTY = 'CURRENT_AFFAIRS_EMPTY';
const String KEY_SUBJECTS = 'SUBJECTS';
const String KEY_MODULE1 = 'MODULE1';
const String KEY_MODULE2 = 'MODULE2';
const String KEY_EXAM = 'EXAM';
const String KEY_EXAMS = 'EXAMS';
const String KEY_VIDEOS = 'VIDEOS';
const String KEY_COMMENT_BOX_TITLE = 'COMMENT_BOX_TITLE';
const String KEY_COMMENT_BOX_SUB_TITLE_FEEDBACK =
    'COMMENT_BOX_SUB_TITLE_FEEDBACK';
const String KEY_COMMENT_BOX_SUB_TITLE_SUGGESTION =
    'COMMENT_BOX_SUB_TITLE_SUGGESTION';
const String KEY_COMMENT_BOX_PLACEHOLDER = 'COMMENT_BOX_PLACEHOLDER';
const String KEY_ADD_COMMENT = 'ADD_COMMENT';
const String KEY_COMMENT_SUBMITTED = 'COMMENT_SUBMITTED';
const String KEY_SUBJECT_DETAILS_NOT_FOUND = 'SUBJECT_DETAILS_NOT_FOUND';
const String KEY_MODULE_DETAILS_NOT_FOUND = 'MODULE_DETAILS_NOT_FOUND';
const String KEY_AFFAIRS_DETAILS_NOT_FOUND = 'AFFAIRS_DETAILS_NOT_FOUND';
const String KEY_EXIT_APP_MSG = 'EXIT_APP_MSG';
const String KEY_ABOUT_COURSE = 'ABOUT_COURSE';
const String KEY_READ_MORE = 'READ_MORE';
const String KEY_DESCRIPTION = 'DESCRIPTION';

///Empty Screens
const String KEY_TOPICS_EMPTY = 'TOPICS_EMPTY';
const String KEY_COURSE_EMPTY = 'COURSE_EMPTY';
const String KEY_ORGANIZATION_EMPTY = 'ORGANIZATION_EMPTY';
const String KEY_COURSE_DETAILS_EMPTY = 'COURSE_DETAILS_EMPTY';
const String KEY_SUBJECTS_EMPTY = 'SUBJECTS_EMPTY';
const String KEY_EXAMS_EMPTY = 'EXAMS_EMPTY';
const String KEY_RANK_LIST_EMPTY = 'RANK_LIST_EMPTY';
const String KEY_CURRENT_AFFAIRS_CONTENT_EMPTY =
    'CURRENT_AFFAIRS_CONTENT_EMPTY';

///
/// validation cubit
///

const String KEY_EMAIL_REQUIRED = 'EMAIL_REQUIRED';
const String KEY_INVALID_EMAIL_ADDRESS = 'INVALID_EMAIL_ADDRESS';

const String KEY_PHONE_NUM_REQUIRED = 'PHONE_NUM_REQUIRED';
const String KEY_INVALID_PHONE_NUM = 'INVALID_PHONE_NUM';
const String KEY_PHONE_NUM_LENGTH_ERROR = 'PHONE_NUM_LENGTH_ERROR';

const String KEY_PASSWORD_REQUIRED = 'PASSWORD_REQUIRED';
const String KEY_PASSWORD_LENGTH_MISMATCH = 'PASSWORD_LENGTH_MISMATCH';
const String KEY_PASSWORDS_MISMATCH = 'PASSWORDS_MISMATCH';
const String KEY_NO_NETWORK_TRY_LATER = 'NO_NETWORK_TRY_LATER';
const String KEY_LOGIN_FAILURE = 'LOGIN_FAILURE';

///
/// Exam list View
///

const String KEY_EXAM_LIST_APPBAR_TITLE = 'EXAM_LIST_APPBAR_TITLE';
const String KEY_NEW_EXAMS_TAB = 'NEW_EXAMS_TAB';
const String KEY_ATTEMPTED_TAB = 'ATTEMPTED_TAB';
const String KEY_PASSED_TAB = 'PASSED_TAB';
const String KEY_QUESTIONS = 'QUESTIONS';
const String KEY_PASSMARK = 'PASSMARK';
const String KEY_MARKS = 'MARKS';
const String KEY_MIN = 'MIN';
const String KEY_ATTEND_BTN = 'ATTEND_BTN';
const String KEY_VIEW_RESULTS_BTN = 'VIEW_RESULTS_BTN';
const String KEY_REVIEW_BTN = 'REVIEW_BTN';
const String KEY_RANK_LIST_BTN = 'RANK_LIST_BTN';
const String KEY_RE_ATTEND_BTN = 'RE_ATTEND_BTN';
const String KEY_EXAM_AVAILABLE_POPUP = 'EXAM_AVAILABLE_POPUP';

///
/// Exam Introduction Screen
///

const String KEY_EXAM_INTRO_APPBAR_TITLE = 'EXAM_INTRO_APPBAR_TITLE';
const String KEY_DURATION = 'DURATION';
const String KEY_DATE = 'DATE';
const String KEY_INTRODUCTION = 'INTRODUCTION';
const String KEY_EXAM_AVAILABLE = 'EXAM_AVAILABLE';
const String KEY_EXAM_TIPS = 'EXAM_TIPS';
const String KEY_EXAM_ATTEND = 'ATTEND_BTN';
const String KEY_QUESTION_WITHIN = 'QUESTION_WITHIN';
const String KEY_EXAM_MINUTES = 'EXAM_MINUTES';
const String KEY_CHOOSE_EXAM = 'CHOOSE_EXAM';
const String KEY_REQUEST_REJECTED = 'REQUEST_REJECTED';
const String KEY_EXAM_TOTAL_MARK = 'EXAM_TOTAL_MARK';
const String KEY_EXAM_PASS_MARK = 'EXAM_PASS_MARK';
const String KEY_FROM = 'FROM';
const String KEY_TO = 'TO';
const String KEY_MARK = 'MARK';

///
/// Errors
///

const String KEY_VIEW_RESULTS_FAILURE = 'VIEW_RESULTS_FAILURE';
const String KEY_VIEW_RESULTS_QUIZ_ATTEMPT_ID_EMPTY =
    'VIEW_RESULTS_QUIZ_ATTEMPT_ID_EMPTY';
const String KEY_RANK_LIST_FAILURE = 'RANK_LIST_FAILURE';

const String KEY_EXAM_EXPIRED = 'EXAM_EXPIRED';
const String KEY_QUESTION_NOT_AVAILABLE = 'QUESTION_NOT_AVAILABLE';
const String KEY_RANK_NOT_AVAILABLE = 'RANK_NOT_AVAILABLE';
const String KEY_EXAM_AVAILABILITY = 'EXAM_AVAILABILITY';

const String KEY_LANGUAGE_CHANGE_ALERT = 'LANGUAGE_CHANGE_ALERT';

const String KEY_LANGUAGE_CHANGE_ALERT_EN = 'LANGUAGE_CHANGE_ALERT_EN';

const String KEY_SECTION_TITLE = 'SECTION_TITLE';
const String KEY_INVALID_FILE_FORMAT = 'INVALID_FILE_FORMAT';

///
/// Exam Result Analyis
///
const String KEY_EXAM_RESULT_APPBAR_TITLE = 'EXAM_RESULT_APPBAR_TITLE';
const String KEY_EXAM_FINISH = 'EXAM_FINISH';
const String KEY_REATTEND_EXAM = 'REATTEND_EXAM';
const String KEY_EXAM_ANALYSIS = 'EXAM_ANALYSIS';
const String KEY_SCORE_SHEET = 'SCORE_SHEET';
const String KEY_TOTAL_MARK_SCORED = 'TOTAL_MARK_SCORED';
const String KEY_STATISTICS = 'STATISTICS';
const String KEY_CORRECT = 'CORRECT';
const String KEY_WRONG = 'WRONG';
const String KEY_SKIPPED = 'SKIPPED';
const String KEY_VIEW_SOLUTION = 'VIEW_SOLUTION';
const String KEY_FAILED_EXAMINATION = 'FAILED_EXAMINATION';
const String KEY_PASSED_EXAMINATION = 'PASSED_EXAMINATION';
const String KEY_ATTENDED = 'ATTENDED';
const String KEY_X_AXIS = 'X_AXIS';
const String KEY_Y_AXIS = 'Y_AXIS';
const String KEY_YOUR_ANSWER = 'YOUR_ANSWER';
const String KEY_CORRECT_ANSWER = 'CORRECT_ANSWER';
const String KEY_SKIPPED_QUESTION_STATUS = 'SKIPPED_QUESTION_STATUS';
const String KEY_WRONG_QUESTION_STATUS = 'WRONG_QUESTION_STATUS';
const String KEY_CORRECT_QUESTION_STATUS = 'CORRECT_QUESTION_STATUS';
const String KEY_REVIEW_ANSWER = 'REVIEW_ANSWER';

// RanlList

const String KEY_RANK_APPBAR_TITLE = 'RANK_APPBAR_TITLE';
const String KEY_RANK_TITLE = 'RANK_TITLE';
const String KEY_YOUR_RANK = 'YOUR_RANK';
const String KEY_TOP_RANK_HOLDERS = 'TOP_RANK_HOLDERS';
const String KEY_YOUR_RANK_WITH_COMPETITORS = 'YOUR_RANK_WITH_COMPETITORS';
const String KEY_RANK_EXAM_ATTENDED = 'RANK_EXAM_ATTENDED';
const String KEY_LEVEL = 'LEVEL';

///
/// Exam View
///

const String KEY_EXAM_APPBAR_TITLE = 'EXAM_APPBAR_TITLE';
const String KEY_EXAM_APPBAR_ALERT_MESSAGE = "EXAM_APPBAR_ALERT_MESSAGE";
const String KEY_EXAM_SUBMIT_ALERT_MESSAGE = "EXAM_SUBMIT_ALERT_MESSAGE";
const String KEY_EXAM_LABEL_TEXT = "EXAM_LABEL_TEXT";
const String KEY_EXAM_FLAG_FOR_REVIEW_ICON = "EXAM_FLAG_FOR_REVIEW_ICON";
const String KEY_EXAM_PROGRESS = "EXAM_PROGRESS";
const String KEY_EXAM_QUICK_INFO = "EXAM_QUICK_INFO";
const String KEY_EXAM_SKIPPED = "EXAM_SKIPPED";
const String KEY_EXAM_ATTENDED = "EXAM_ATTENDED";
const String KEY_EXAM_FLAG_REVIEW = "EXAM_FLAG_REVIEW";
const String KEY_EXAM_UNANSWERED = "EXAM_UNANSWERED";
const String KEY_EXAM_SELECTED_QSTN = "EXAM_SELECTED_QSTN";
const String KEY_COPYRIGHT = "COPYRIGHT";
const String KEY_EXAM_REPORT_QUESTION = "EXAM_REPORT_QUESTION";
const String KEY_EXAM_ISSUE_TYPE = "EXAM_ISSUE_TYPE";
const String KEY_EXAM_TIP = 'EXAM_TIP';
const String KEY_EXAM_ALREADY_ATTEND = 'EXAM_ALREADY_ATTEND';
const String KEY_EXAM_COMPLETE_TITLE = 'EXAM_COMPLETE_TITLE';
const String KEY_EXAM_COMPLETE_MSG = 'EXAM_COMPLETE_MSG';
const String KEY_EXAM_PENALITY = "_EXAM_PENALITY";
const String KEY_EXAM_MARK = "EXAM_MARK";
const String KEY_EXAM_TIME_UP = 'EXAM_TIME_UP';
const String KEY_EXAM_NETWORK = 'EXAM_NETWORK';
const String KEY_FAILED_HOST_LOOKUP = 'AILED_HOST_LOOKUP';
const String KEY_EXAM_SUBMISSION_STATUS = 'EXAM_SUBMISSION_STATUS';

const String KEY_PREVIOUS = 'PREVIOUS';
const String KEY_NEXT = 'NEXT';
const String KEY_EXAM_RESULT = 'EXAM_RESULT';
const String KEY_CORRECT_ANSWER_EXAM_VIEW = 'CORRECT_ANSWER_EXAM_VIEW';
const String KEY_WRONG_ANSWER_EXAM_VIEW = 'WRONG_ANSWER_EXAM_VIEW';
const String KEY_ANSWERED = 'ANSWERED';

const String KEY_TRY_AGAIN = 'TRY_AGAIN';

///
/// Profile
///

const String KEY_PROFILE_APPBAR_TITLE = 'PROFILE_APPBAR_TITLE';
const String KEY_PROFILE_FIRST_NAME = 'PROFILE_FIRST_NAME';
const String KEY_PROFILE_LAST_NAME = 'PROFILE_LAST_NAME';
const String KEY_DELETE_ACCOUNT = 'DELETE_ACCOUNT';

const String KEY_SELECT_FROM_CAMERA = 'SELECT_FROM_CAMERA';
const String KEY_SELECT_FROM_GALLERY = 'SELECT_FROM_GALLERY';
const String KEY_REMOVE_PROFILE_PIC = 'REMOVE_PROFILE_PIC';

// alerts- profile
const String KEY_ORGANIZATION_EMPTY_ALERT = 'ORGANIZATION_EMPTY_ALERT';

const String KEY_FILE_SIZE_WARNING = 'FILE_SIZE_WARNING';
const String KEY_FILE_TYPE_WARNING = 'FILE_TYPE_WARNING';

const String KEY_STORAGE_PERMISSION_WARNING =
    "You have disabled storage permission";

const String KEY_CAMERA_PERMISSION_WARNING = 'CAMERA_PERMISSION_WARNING';
const String KEY_GALLERY_PERMISSION_REQ = 'GALLERY_PERMISSION_REQ';
const String KEY_CAMERA_PERMISSION_REQ = 'CAMERA_PERMISSION_REQ';
const String KEY_ENABLE_NOW = 'ENABLE_NOW';

const String KEY_PROFILE_UPDATION_SUCCESS = 'PROFILE_UPDATION_SUCCESS';
const String KEY_PROFILE_UPDATION_FAILED = 'PROFILE_UPDATION_FAILED';
const String KEY_FAILED_TO_OPEN_CAMERA = 'FAILED_TO_OPEN_CAMERA';
const String KEY_FAILED_TO_OPEN_GALLERY = 'FAILED_TO_OPEN_GALLERY';

const String KEY_ACCOUNT_DELETE_CONFIRM = 'ACCOUNT_DELETE_CONFIRM';

const String KEY_FIELD_REQUIRED = 'FIELD_REQUIRED';

// Side drawer screen

const String KEY_PROFILE = "PROFILE";
const String KEY_SETTINGS = "SETTINGS";
const String KEY_RANKING = "RANKING";
const String KEY_LIVE_CLASS = "LIVE_CLASS";
const String KEY_NEWS = "NEWS";
const String KEY_NOTIFICATIONS = "NOTIFICATIONS";
const String KEY_ABOUT = "ABOUT";
const String KEY_SHARE = "SHARE";
const String KEY_FEEDBACK = "FEEDBACK";
const String KEY_SUGGESTION = "SUGGESTION";
const String KEY_SIGNOUT = "SIGNOUT";
const String KEY_SIGNOUT_MSG = "SIGNOUT_MSG";

// Exam Review Screen
const String KEY_EXAM_REVIEW_APPBAR_TITLE = 'EXAM_REVIEW_APPBAR_TITLE';
const String KEY_EXAM_ATTENDED_ON = 'EXAM_ATTENDED_ON';
const String KEY_QUESTION_ANSWER = 'QUESTION_ANSWER';
const String KEY_ANSWER_OPTED = 'ANSWER_OPTED';
const String KEY_HOUR = 'HOUR';
const String KEY_HOURS = 'HOURS';
const String KEY_MINUTES = 'MINUTES';
const String KEY_MINUTE = 'MINUTES';
const String KEY_SECOND = 'SECOND';
const String KEY_SECONDS = 'SECONDS';

const String KEY_DIALOGUE_CONTENT = 'DIALOGUE_CONTENT';
const String KEY_DIALOGUE_CONTENT_REVIEW_FAILURE =
    'DIALOGUE_CONTENT_REVIEW_FAILURE';
const String KEY_DIALOGUE_CONTENT_REVIEW_FAILURE_MESSAGE =
    'DIALOGUE_CONTENT_REVIEW_FAILURE_MESSAGE';

// course video
const String KEY_VIDEO = 'VIDEO';
const String KEY_SAVE_PROGRESS_BTN = 'SAVE_PROGRESS_BTN';
const String KEY_PROGRESS_SUCESS_ALERT_CONTENT =
    "PROGRESS_SUCESS_ALERT_CONTENT";
const String KEY_PROGRESS_ALERT_CONTENT = "PROGRESS_ALERT_CONTENT";
const String KEY_VIDEO_COMMENTS = "VIDEO_COMMENTS";
const String KEY_VIDEO_PLAYER_ERROR = "VIDEO_PLAYER_ERROR";
const String KEY_SAVE_PROGRESS_FAILURE = 'SAVE_PROGRESS_FAILURE';

// Course Video Screen- checkpoint

const String KEY_POPUP_TITLE = "POPUP_TITLE";
const String KEY_POPUP_MSG = 'POPUP_MSG';
const String KEY_PROMPT_TEXT = "PROMPT_TEXT";
const String KEY_PROMPT_VALIDATION_MSG = 'PROMPT_VALIDATION_MSG';
const String KEY_CHECKPOINT_EXAM_NAVIGATION_MSG =
    'CHECKPOINT_EXAM_NAVIGATION_MSG';
const String KEY_CHECKPOINT_COMPLETE_MSG = 'CHECKPOINT_COMPLETE_MSG';
// onBoarding Screen

const String KEY_SKIP = 'SKIP';
const String KEY_LEARN = 'LEARN';
const String KEY_LEARN_PROFESSIONAL = 'LEARN_PROFESSIONAL';
const String KEY_PRACTICE = 'PRACTICE';
const String KEY_PRACTICE_SESSION = 'PRACTICE_SESSION';
const String KEY_STUDY = 'STUDY';
const String KEY_STUDY_MATERIALS = 'STUDY_MATERIALS';
const String KEY_WATCH = 'WATCH';
const String KEY_WATCH_FEATURES = 'WATCH_FEATURES';
const String KEY_CONQUER = 'CONQUER';
const String KEY_UNLOCK_LEVEL = 'UNLOCK_LEVEL';

// organization view

const String KEY_ORGANIZATION_APPBAR_TITLE = 'ORGANIZATION_APPBAR_TITLE';
const String KEY_EMPTY_TOPIC_FOR_ORGANIZATION = 'EMPTY_TOPIC_FOR_ORGANIZATION';

// subscription

const String KEY_SUBSCRIPTION = 'SUBSCRIPTION';
const String KEY_SUBSCRIPTION_ADDED = 'SUBSCRIPTION_ADDED';
const String KEY_PENDING = 'PENDING';
const String KEY_SELECT = 'SELECT';
const String KEY_APPROVED = 'APPROVED';
const String KEY_UPGRADE_PREMIUM = "UPGRADE_PREMIUM";
const String KEY_SUBSCRIPTION_DESCRIPTION = 'SUBSCRIPTION_DESCRIPTION';
const String KEY_PURCHASE_HISTORY = "PURCHASE_HISTORY";
const String KEY_PURCHASED_PLANS = "PURCHASED_PLANS";
const String KEY_ACTIVE_PLANS = 'ACTIVE_PLANS';
const String KEY_PLANS_WAITING_FOR_APPROVAL = "PLANS_WAITING_FOR_APPROVAL";
const String KEY_PLAN_AVAILABILITY_FROM = 'PLAN_AVAILABILITY_FROM';
const String KEY_PLAN_VALIDITY = 'PLAN_VALIDITY';
const String KEY_PLAN_AVAILABILITY_TO = 'PLAN_AVAILABILITY_TO';
const String KEY_PLAN_CONFIRMATION_TITLE = 'PLAN_CONFIRMATION_TITLE';
const String KEY_PLAN_CONFIRMATION_CONTENT = 'PLAN_CONFIRMATION_CONTENT';
const String KEY_PLAN_EMPTY = 'PLAN_EMPTY';
const String KEY_SERACH_HINT_FOR_PLAN = 'SEARCH_HINT_FOR_PLAN';
const String KEY_RESOURCES = 'RESOURCES';
const String KEY_RESOURCE_EMPTY = 'RESOURCE_EMPTY';

// Notification
const String KEY_LOCAL_NOTIFICATION_TITLE = 'LOCAL_NOTIFICATION_TITLE';
const String KEY_LOCAL_NOTIFICATION_CONTENT = 'LOCAL_NOTIFICATION_CONTENT';
