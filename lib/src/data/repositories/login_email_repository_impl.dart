import 'package:SmartLearn/src/utils/constants/helper.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/repositories/login_email_repository.dart';
import '../../domain/services/network_services.dart';

class LoginEmailRepositoryImpl implements LoginEmailRepository {
  LoginEmailRepositoryImpl();

  @override
  Future<String> loginEmail(String email, String password) async {
    try {
      Supabase.instance;
      // ignore: unused_catch_clause
      // ignore: avoid_catching_errors
    } on AssertionError catch (_) {
      /// if the network wasn't available during
      /// supabase init, then this api call will throw assertion error
      /// in such case, re-init supabase again when the network is available
      ///
      bool isNetworkAvailable = await checkNetworkStatus();
      if (isNetworkAvailable) {
        await NetworkServices().initSupabase();
      }
      return '';
    }

    final response = await Supabase.instance.client.auth
        .signInWithPassword(email: email, password: password);
    String userId = response.user?.id ?? "";

    return userId;
  }

  @override
  Future<dynamic> resetPassword(String email) async {
    final response =
        await Supabase.instance.client.auth.resetPasswordForEmail(email);

    return true;
  }
}
