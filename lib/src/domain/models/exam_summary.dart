// To parse this JSON data, do
//
//     final examSummary = examSummaryFromJson(jsonString);

import 'dart:convert';

import '/src/domain/models/question_data.dart';

List<ExamSummary> examSummaryFromJson(String str) => List<ExamSummary>.from(
    json.decode(str).map((x) => ExamSummary.fromJson(x)));

String examSummaryToJson(List<ExamSummary> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ExamSummary {
  final String examName;
  final String mainTopic;
  final int duration;
  final double passMark;
  final double totalMark;
  final double scoredMark;
  final bool didPassExam;

  final DateTime startTime;
  final DateTime endTime;
  final int numOfQuestions;
  final int skippedAnsCount;
  final int wrongAnswerCount;
  final int correctAnswerCount;
  final String description;
  final List<Question> questions;
  final List<CategoeyWiseSummary> categoeyWiseSummary;

  ExamSummary({
    required this.examName,
    required this.mainTopic,
    required this.description,
    required this.duration,
    required this.passMark,
    required this.totalMark,
    required this.scoredMark,
    required this.didPassExam,
    required this.startTime,
    required this.endTime,
    required this.numOfQuestions,
    required this.skippedAnsCount,
    required this.wrongAnswerCount,
    required this.correctAnswerCount,
    required this.questions,
    required this.categoeyWiseSummary,
  });

  factory ExamSummary.fromJson(Map<String, dynamic> json) {
    List<Question> questionList = json["quest_answers"] != null
        ? List<Question>.from(
            json["quest_answers"].map((x) => Question.fromJson(x)))
        : [];
    questionList.sort((a, b) => a.id.compareTo(b.id));

    dynamic score = double.parse((json["scored_mark"] ?? '0.0').toString());
    //json["scored_mark"] ?? '10';
    dynamic passMark =
        double.parse((json["pass_mark"] * 1.0 ?? 0.0).toString());
    dynamic totalMark =
        double.parse((json["total_mark"] * 1.0 ?? 0.0).toString());
    bool examStatus = score >= passMark;

    return ExamSummary(
      examName: json["name"] ?? '',
      mainTopic: json["main_topic"] ?? '',
      description: json["description"] ?? '',
      duration: json["duration"] ?? 0,
      passMark: passMark,
      scoredMark: score,
      totalMark: totalMark,
      didPassExam: examStatus,
      startTime:
          DateTime.parse(json["start_time"] ?? DateTime.now().toString()),
      endTime: DateTime.parse(json["end_time"] ?? DateTime.now().toString()),
      numOfQuestions: json["num_of_questions"] ?? 0,
      skippedAnsCount: json["skipped_ans_count"] ?? 0,
      wrongAnswerCount: json["wrong_answer_count"] ?? 0,
      correctAnswerCount: json["correct_answer_count"] ?? 0,
      questions: questionList,
      categoeyWiseSummary: json["categoey_wise_summary"] != null
          ? List<CategoeyWiseSummary>.from(json["categoey_wise_summary"]
              .map((x) => CategoeyWiseSummary.fromJson(x)))
          : [],
    );
  }

  Map<String, dynamic> toJson() => {
        "name": examName,
        "duration": duration,
        "end_time": endTime.toIso8601String(),
        "pass_mark": passMark,
        "main_topic": mainTopic,
        "start_time": startTime.toIso8601String(),
        "total_mark": totalMark,
        "num_of_questions": numOfQuestions,
        "skipped_ans_count": skippedAnsCount,
        "wrong_answer_count": wrongAnswerCount,
        "correct_answer_count": correctAnswerCount,
        "scored_mark": scoredMark,
        "description": description,
        "quest_answers": List<dynamic>.from(questions.map((x) => x.toJson())),
        "categoey_wise_summary":
            List<dynamic>.from(categoeyWiseSummary.map((x) => x.toJson())),
      };
}

class CategoeyWiseSummary {
  final int skippedAnsCount;
  final int wrongAnswerCount;
  final int correctAnswerCount;
  final String questionCategoryId;
  final String questionCategoryName;

  CategoeyWiseSummary({
    required this.skippedAnsCount,
    required this.wrongAnswerCount,
    required this.correctAnswerCount,
    required this.questionCategoryId,
    required this.questionCategoryName,
  });

  factory CategoeyWiseSummary.fromJson(Map<String, dynamic> json) =>
      CategoeyWiseSummary(
        skippedAnsCount: json["skipped_ans_count"],
        wrongAnswerCount: json["wrong_answer_count"],
        correctAnswerCount: json["correct_answer_count"],
        questionCategoryId: json["question_category_id"],
        questionCategoryName: json["question_category_name"],
      );

  Map<String, dynamic> toJson() => {
        "skipped_ans_count": skippedAnsCount,
        "wrong_answer_count": wrongAnswerCount,
        "correct_answer_count": correctAnswerCount,
        "question_category_id": questionCategoryId,
        "question_category_name": questionCategoryName,
      };
}
