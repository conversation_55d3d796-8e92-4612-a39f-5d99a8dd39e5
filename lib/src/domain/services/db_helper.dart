// ignore_for_file: lines_longer_than_80_chars, avoid_catches_without_on_clauses

import 'package:SmartLearn/src/domain/models/check_point_data/check_point_data.dart';

import '../../../src/domain/models/milestone_data.dart';
import '../../../src/domain/models/videoplayer_data.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../models/course_dashboard/ppt_data.dart';
import '/src/config/app_config/db_config.dart';

class DbHelper {
  DbHelper._internal();

  static DbHelper instance = DbHelper._internal();

  factory DbHelper() => instance;

  Database? db;

  static const _databaseName = DBConfig.DATABASE_NAME;
  static const _databaseVersion = DBConfig.DATABASE_VERSION;

  static const examTable = DBConfig.EXAM_TABLE_NAME;
  static const videoPlayerData = DBConfig.VIDEO_PLAYER_DATA;
  static const milestoneData = DBConfig.MILESTONE_DATA;

  //VideoPlayer Date
  static const id = 'id';
  static const videoId = 'video_id';
  static const startTime = 'start_time';
  static const endTime = 'end_time';
  static const milestonesCompleted = 'milestones_completed';
  static const lastWatchTime = 'last_watch_time';
  static const milestoneDurations = 'milestone_durations';

  //Milestone Data
  static const milestoneId = 'milestone_id';
  static const sessionStart = 'session_start';
  static const sessionEnd = 'session_end';
  static const sessionResponse = 'session_response';
  static const sessionStatus = 'session_status';

  static const columnId = 'id';
  // static const columnOrgId = 'org_id';
  // static const columnUserId = 'user_id';
  // static const columnExamId = 'quiz_id';

  // QuestionData
  static const columnExamId = 'exam_id';
  static const columnQuizAttemptId = 'quiz_attempt_id';
  static const columnExamName = 'exam_name';
  static const columnExamIntro = 'description';
  static const columnExamDuration = 'duration';
  static const columnExamEndTime = 'end_time';
  static const columnExamStartTime = 'start_time';
  static const columnExamPassMark = 'pass_mark';
  static const columnMainTopic = 'main_topic';
  static const columnTotalMark = 'total_mark';
  static const columnQuestionAnswers = 'quest_answers';
  static const columnQuestionCount = 'num_of_questions';
  static const columnNetworkStatus = 'network_status';
  static const columnExamUploadStatus =
      'exam_upload_status'; // 1=> uploaded to server, 0=> not uploaded
  static const columnExamCompletedStatus =
      'exam_completed_status'; // 1=> completed exam, 0=> not completed

  // Question
  static const columnQuestionId = 'selected_answer_ids';
  static const columnQuestionName = 'question_name';
  static const columnAnswersList = 'answers';
  // static const columnExamId = 'quiz_id';
  static const columnQuestionText = 'question_text';
  static const columnSelectedAnswerIndexes = 'selected_answer_indexes';
  static const columnSelectedAnswerIds = 'selected_answer_ids';
  static const columnQuestionsStatus = 'questions_status';

  // Answer
  static const columnAnswerId = 'answer_id';
  static const columnAnswerValue = 'answer';
  static const columnAnswerFraction = 'fraction';
  static const columnAnswerStatus = 'answer_status';

//completecheckpoint Table Name
  static const tableCompletedCheckPoint = 'completed_check_point';

// Column Names
  static const columnCheckpointId = 'checkpoint_id';
  static const columnResid = 'res_id';
  static const columnOrgId = 'org_id';
  static const columnSequence = 'sequence';
  static const columnCreatedAt = 'created_at';
  static const columnCreatedBy = 'created_by';
  static const columnStartPage = 'start_page';
  static const columnStartTime = 'start_time';
  static const columnUpdatedAt = 'updated_at';
  static const columnUpdatedBy = 'updated_by';
  static const columnInstanceId = 'instance_id';
  static const columnModuleName = 'module_name';
  static const columnIsMandatory = 'is_mandatory';
  static const columnModuleTypeId = 'module_type_id';
  static const columnCheckpointName = 'checkpoint_name';
  static const columnCheckpointType = 'checkpoint_type';
  static const columnCourseModuleId = 'course_module_id';
  static const columnInstanceEndTime = 'instance_end_time';

/////for pptdata saving
  static const tablePPTData = 'ppt_data';

  static const pcolumnId = 'id';
  static const columnIndex = 'pptindex';
  static const columnUrl = 'url';
  static const pcolumnCheckpointId = 'checkpoint_id';

  // checkpoint Table Name
// Table Name
  static const tableCheckPoints = 'check_points';

// Column Names
  static const colCheckpointId = 'id'; // Primary Key
  static const colResid =
      'resid'; // Unique ID for the collection of checkpoints
  static const colOrgId = 'org_id';
  static const colSequence = 'sequence';
  static const colCreatedAt = 'created_at';
  static const colCreatedBy = 'created_by';
  static const colStartPage = 'start_page';
  static const colStartTime = 'start_time';
  static const colUpdatedAt = 'updated_at';
  static const colUpdatedBy = 'updated_by';
  static const colInstanceId = 'instance_id';
  static const colModuleName = 'module_name';
  static const colIsMandatory = 'is_mandatory';
  static const colModuleTypeId = 'module_type_id';
  static const colCheckpointName = 'checkpoint_name';
  static const colCheckpointType = 'checkpoint_type';
  static const colCourseModuleId = 'course_module_id';
  static const colInstanceEndTime = 'instance_end_time';

  // this opens the database (and creates it if it doesn't exist)
  Future<void> initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _databaseName);
    db = await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: onCreate,
    );
  }

  // SQL code to create the database table
  Future onCreate(Database db, int version) async {
    await db.execute('''
          CREATE TABLE  $examTable(
            $columnId INTEGER,
            $columnExamId TEXT,
            $columnQuizAttemptId TEXT,
            $columnExamName TEXT NOT NULL,
            $columnExamIntro TEXT NOT NULL,
            $columnExamDuration TEXT NOT NULL,
            $columnExamEndTime TEXT NOT NULL,
            $columnExamStartTime TEXT NOT NULL,
            $columnExamPassMark TEXT NOT NULL,
            $columnMainTopic TEXT NOT NULL,
            $columnTotalMark TEXT NOT NULL,
            $columnQuestionCount TEXT NOT NULL,
            $columnQuestionAnswers TEXT NOT NULL,
            $columnNetworkStatus TEXT NOT NULL,
            $columnExamUploadStatus INTEGER NOT NULL,
            $columnExamCompletedStatus INTEGER NOT NULL,
            PRIMARY KEY ($columnId, $columnExamId)
                  )''');
    await db.execute('''
      CREATE TABLE $videoPlayerData(
            $id INTEGER PRIMARY KEY AUTOINCREMENT,
            $videoId TEXT NOT NULL,
            $startTime INTEGER NOT NULL,
            $endTime INTEGER NOT NULL,
            $milestonesCompleted INTEGER NOT NULL,
            $lastWatchTime INTEGER NOT NULL,
            $milestoneDurations TEXT NOT NULL
      )
 ''');
    await db.execute('''
      CREATE TABLE $milestoneData(
            $id INTEGER PRIMARY KEY AUTOINCREMENT,
            $videoId TEXT NOT NULL,
            $milestoneId TEXT NOT NULL,
            $sessionStart INTEGER NOT NULL,
            $sessionEnd INTEGER NOT NULL,
            $sessionResponse TEXT NOT NULL,
            $sessionStatus INTEGER NOT NULL
      )
 ''');
    await db.execute('''
  CREATE TABLE $tableCompletedCheckPoint (
    $columnResid TEXT PRIMARY KEY,  -- Using resid as the primary key
    $columnCheckpointId TEXT,       -- Kept as a normal column
    $columnOrgId TEXT,
    $columnSequence INTEGER,
    $columnCreatedAt TEXT, 
    $columnCreatedBy TEXT,
    $columnStartPage INTEGER,
    $columnStartTime TEXT,
    $columnUpdatedAt TEXT,
    $columnUpdatedBy TEXT,
    $columnInstanceId TEXT,
    $columnModuleName TEXT,
    $columnModuleTypeId TEXT,
    $columnCheckpointName TEXT,
    $columnCheckpointType TEXT,
    $columnCourseModuleId TEXT,
    $columnInstanceEndTime TEXT
  )
''');
    await db.execute('''
  CREATE TABLE $tablePPTData (
    $columnResid TEXT PRIMARY KEY,  -- resid as the primary key
    $pcolumnId TEXT,
    $columnIndex TEXT,
    $columnUrl TEXT,
    $pcolumnCheckpointId TEXT
  )
''');
    await db.execute('''
  CREATE TABLE $tableCheckPoints (
    $columnCheckpointId TEXT PRIMARY KEY, 
    $colResid TEXT NOT NULL,          
    $colOrgId TEXT,                 
    $colSequence INTEGER,            
    $colCreatedAt TEXT,             
    $colCreatedBy TEXT,             
    $colStartPage INTEGER,          
    $colStartTime TEXT,             
    $colUpdatedAt TEXT,             
    $colUpdatedBy TEXT,             
    $colInstanceId TEXT,            
    $colModuleName TEXT,            

    $colModuleTypeId TEXT,         
    $colCheckpointName TEXT,        
    $colCheckpointType TEXT,        
    $colCourseModuleId TEXT,       
    $colInstanceEndTime TEXT      
  )
''');
  }

  // check if table exists before deleting entries
  Future<bool> tableExists(String tableName) async {
    if (db != null) {
      final result = await db!.query(tableName);
      return result.isNotEmpty;
    }
    return false;
  }

  // Delete entries
  Future clearTable() async {
    if (await tableExists(examTable)) {
      await db!.rawDelete("DELETE FROM $examTable");
    }
  }

  Future clearVideoData() async {
    if (await tableExists(videoPlayerData)) {
      await db!.rawDelete("DELETE FROM $videoPlayerData");
    }
  }

  // close db
  Future closeDataBase() async {
    await db?.close();
  }

  // drop table
  Future dropTable() async {
    await db?.execute('DROP TABLE IF EXISTS $examTable');
  }

  Future dropVideoDataTable() async {
    await db?.execute('DROP TABLE IF EXISTS $videoPlayerData');
  }

  // Helper methods

  // Inserts a row in the database where each key in the Map is a column name
  // and the value is the column value. The return value is the id of the
  // inserted row.
  Future<int> insert(Map<String, dynamic> row) async {
    int insertedId = await db?.insert(examTable, row) ?? 0;
    return insertedId;
  }

  // Updates a row in the database where examid matches the field
  Future<int> update(Map<String, dynamic> row, String examId) async {
    int updatedId = await db?.update(examTable, row,
            where: '$columnExamId = ?', whereArgs: [examId]) ??
        0;
    return updatedId;
    // .update('$examTable WHERE $columnExamId=?', row, whereArgs: [examId]);
  }

  Future<int> insertOrUpdate(Map<String, dynamic> row, String examId) async {
    List<Map<String, dynamic>> rows = await queryForExam(examId);

    if (rows.isNotEmpty) {
      // If the row exists, update it.
      return await update(row, examId);
    } else {
      // If the row does not exist, insert a new one.
      return await insert(row);
    }
  }

  // All of the rows are returned as a list of maps, where each map is
  // a key-value list of columns.
  Future<List<Map<String, dynamic>>> queryAllRows() async {
    List<Map<String, dynamic>> queryData = await db?.query(examTable) ?? [];
    return queryData;
  }

  // specified row will be returned as a list of maps, where each map is
  // a key-value list of columns.
  Future<List<Map<String, dynamic>>> queryForExam(String examId) async {
    List<Map<String, dynamic>> queryData = await db?.query(
            '$examTable WHERE $columnExamId = ?',
            whereArgs: [examId],
            distinct: true) ??
        [];
    return queryData;
  }

  // specified row will be returned as a list of maps, where each map is
  // a key-value list of columns.
  // fetch the exams that were not uploaded to server
  // columnExamUploadStatus will be '0' for such exams
  Future<List<Map<String, dynamic>>> queryForUnuploadedExams() async {
    List<Map<String, dynamic>> queryData = await db?.query(
            '$examTable WHERE $columnExamUploadStatus = ? AND $columnExamCompletedStatus = ?',
            whereArgs: [0, 1],
            distinct: true) ??
        [];
    return queryData;
  }

  Future<void> insertToVideoData(VideoPlayerData data) async {
    try {
      final response = await db?.query(videoPlayerData,
          where: '$videoId = ?', whereArgs: [data.videoId]);
      if (response != null && response.isNotEmpty) {
        await db?.rawUpdate(
            'UPDATE $videoPlayerData SET $endTime = ?, $milestonesCompleted = ?, $lastWatchTime = ?, $milestoneDurations = ?  WHERE $videoId = ?',
            [
              data.endTime,
              data.milestonesCompleted,
              data.lastWatchTime,
              data.milestoneDurations,
              data.videoId,
            ]);
      } else {
        final status = await db?.insert(videoPlayerData, data.toJson());
      }
    } catch (e) {
      debugPrint("error =>insertToVideoData => $e");
    }
  }

  Future<List<VideoPlayerData>> getVideoPlayerData(String id) async {
    List<VideoPlayerData> playerData = [];
    try {
      final data = await db?.query(videoPlayerData,
              where: '$videoId = ?', whereArgs: [id]) ??
          [];
      playerData = data.map((e) => VideoPlayerData.fromJson(e)).toList();
      debugPrint("items => ${playerData[0].milestonesCompleted}");
      return playerData;
    } catch (e) {
      debugPrint("error =>getVideoPlayerData => $e");
      return playerData;
    }
  }

  Future<void> insertMilestoneData(MilestoneData data) async {
    try {
      final status = await db?.insert(milestoneData, data.toJson());
    } catch (e) {
      debugPrint("error => insertMilestoneData => $e");
    }
  }

  Future<List<MilestoneData>> getMilestoneData(String id) async {
    List<MilestoneData> result = [];
    try {
      final data = await db
          ?.query(milestoneData, where: '$videoId = ?', whereArgs: [id]);
      debugPrint("items => $data");
      result = data?.map((e) => MilestoneData.fromJson(e)).toList() ?? [];
      return result;
    } catch (e) {
      debugPrint("error => getMilestoneData => $e");
      return result;
    }
  }

  // // All of the methods (insert, query, update, delete) can also be done using
  // // raw SQL commands. This method uses a raw query to give the row count.
  // Future<int> queryRowCount() async {
  //   final results = await _db?.rawQuery('SELECT COUNT(*) FROM $table');
  //   return Sqflite.firstIntValue(results) ?? 0;
  // }

  // // We are assuming here that the id column in the map is set. The other
  // // column values will be used to update the row.
  // Future<int> update(Map<String, dynamic> row) async {
  //   int id = row[columnId];
  //   return await _db?.update(
  //     table,
  //     row,
  //     where: '$columnId = ?',
  //     whereArgs: [id],
  //   );
  // }

  // // Deletes the row specified by the id. The number of affected rows is
  // // returned. This should be 1 as long as the row exists.
  // Future<int> delete(int id) async {
  //   return await _db?.delete(
  //     table,
  //     where: '$columnId = ?',
  //     whereArgs: [id],
  //   );
  // }

  Future<void> insertCheckPoints(
      String resid, List<CheckPoint> checkPoints) async {
    try {
      final batch = db?.batch();

      // Step 1: Remove existing checkpoints for the resid
      batch?.delete(
        tableCheckPoints,
        where: '$colResid = ?',
        whereArgs: [resid],
      );

      // Step 2: Insert new checkpoints
      for (var checkPoint in checkPoints) {
        final data = checkPoint.toJson();
        data[colResid] = resid; // Assign resid to each checkpoint

        // Remove 'colIsMandatory' before inserting
        data.remove(colIsMandatory);

        batch?.insert(
          tableCheckPoints,
          data,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      // Step 3: Commit batch
      await batch?.commit(noResult: true);
    } catch (e) {
      debugPrint("error => insertCheckPoints => $e");
    }
  }

  Future<List<CheckPoint>> getCheckPoints(String resid) async {
    try {
      final data = await db?.query(
        'check_points',
        where: 'resid = ?',
        whereArgs: [resid],
      );

      if (data != null && data.isNotEmpty) {
        return data.map((e) => CheckPoint.fromJson(e)).toList();
      }
      return [];
    } catch (e) {
      debugPrint("error => getCheckPoints => $e");
      return [];
    }
  }

  Future<void> insertCompletedCheckPoint(
      CheckPoint checkPoint, String resid) async {
    try {
      final existingData = await db?.query(
        'completed_check_point',
        where: 'res_id = ?',
        whereArgs: [resid], // Now checking only resid
        limit: 1,
      );

      // Convert CheckPoint object to JSON and remove 'is_mandatory'
      final checkPointData = checkPoint.toJson();
      checkPointData.remove('is_mandatory'); // Exclude this field

      // Add 'resid' to the map before inserting/updating
      checkPointData['res_id'] = resid;

      if (existingData != null && existingData.isNotEmpty) {
        debugPrint("Updating checkpoint for resid: $resid");
        await db?.update(
          'completed_check_point',
          checkPointData,
          where: 'res_id = ?',
          whereArgs: [resid], // Ensure resid is included
        );
      } else {
        debugPrint("Inserting new checkpoint for resid: $resid");
        await db?.insert('completed_check_point', checkPointData);
      }
    } catch (e) {
      debugPrint("Error in insertCompletedCheckPoint: $e");
    }
  }

  Future<CheckPoint?> getCompletedCheckPoint(String resid) async {
    try {
      final data = await db?.query(
        'completed_check_point',
        where: 'res_id = ?',
        whereArgs: [resid], // Now retrieving based only on resid
        limit: 1,
      );

      if (data != null && data.isNotEmpty) {
        debugPrint("Checkpoint found for resid: $resid");
        return CheckPoint.fromJson(data.first);
      } else {
        debugPrint("No checkpoint found for resid: $resid");
      }
    } catch (e) {
      debugPrint("Error in getCompletedCheckPoint: $e");
    }
    return null;
  }

  Future<void> insertPPTData(PPTData data, String resid) async {
    try {
      // Check if a record with the same resid already exists
      final existingData = await db?.query(
        'ppt_data',
        where: '$columnResid = ?', // Use correct column name
        whereArgs: [resid],
        limit: 1,
      );

      // Convert PPTData to JSON and add resid
      final pptDataMap = data.toJson();
      pptDataMap[columnResid] = resid; // Store resid in the database

      if (existingData != null && existingData.isNotEmpty) {
        // Update the existing record
        await db?.update(
          'ppt_data',
          pptDataMap,
          where: '$columnResid = ?', // Use correct column name
          whereArgs: [resid],
        );
      } else {
        // Insert a new record if no matching resid exists
        await db?.insert('ppt_data', pptDataMap);
      }
    } catch (e) {
      debugPrint("error => insertPPTData => $e");
    }
  }

  Future<PPTData?> getPPTData(String resid) async {
    try {
      final data = await db?.query(
        'ppt_data',
        where: '$columnResid = ?', // Use correct column name
        whereArgs: [resid],
        limit: 1,
      );

      if (data != null && data.isNotEmpty) {
        return PPTData.fromJson(data.first);
      }
      return null;
    } catch (e) {
      debugPrint("error => getPPTData => $e");
      return null;
    }
  }

  Future<void> insertPPTSlideData(PPTData data) async {
    try {
      final status = await db?.insert('ppt_slide_data', data.toJson());
    } catch (e) {
      debugPrint("error => insertPPTData => $e");
    }
  }

  Future<PPTData?> getPPTSlideData(String checkpointId) async {
    try {
      final data = await db?.query('ppt_slide_data',
          where: 'checkpoint_id = ?', whereArgs: [checkpointId], limit: 1);

      if (data != null && data.isNotEmpty) {
        return PPTData.fromJson(data.first);
      }
      return null;
    } catch (e) {
      debugPrint("error => getPPTData => $e");
      return null;
    }
  }
}
