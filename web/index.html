<!DOCTYPE html><html><head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="SmartLearn">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png">

  <title>SmartLearn</title>
  <link rel="manifest" href="manifest.json">

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer=""></script>
  <style id="splash-screen-style">
    .flutter-loader {
      z-index: 999999;
    }
    
    #splash {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0px;
        left: 0px;
        z-index: 999998;
        opacity: 1;
        /* animation type and duration */
        transition: opacity 0.25s;
        background-color: #00AFBB;
              -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
    }
    
    #splash.remove {
      /* enable click through when run animation */
      pointer-events: none;
      /* start animation */
      opacity: 0;
    }

    #splash .center {
      margin: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      -ms-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
    }

    #splash .contain {
      display:block;
      width:100%; height:100%;
      object-fit: contain;
    }

    #splash .stretch {
      display:block;
      width:100%; height:100%;
    }

    #splash .cover {
      display:block;
      width:100%; height:100%;
      object-fit: cover;
    }

    #splash .bottom {
      position: absolute;
      bottom: 0;
      left: 50%;
      -ms-transform: translate(-50%, 0);
      transform: translate(-50%, 0);
    }

    #splash .bottomLeft {
      position: absolute;
      bottom: 0;
      left: 0;
    }

    #splash .bottomRight {
      position: absolute;
      bottom: 0;
      right: 0;
    }
  </style>
  <script id="splash-screen-script">
    function removeSplashFromWeb() {
      const splashElement = document.getElementById("splash");
      splashElement.classList.add("remove");
      setTimeout(function () {
        splashElement.remove();
        document.getElementById("splash-screen-script")?.remove();
      }, 750 /* animation time + wait rendering and others(500ms) */);
    }
  </script>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
  <script src="assets/packages/flutter_tex/js/flutter_tex.js"></script>
  <script type="text/javascript">window.flutterWebRenderer = "canvaskit";</script>
</head>
<body>
  <div id="splash">
    <picture>
        <source srcset="splash/img/light-1x.png 1x, splash/img/light-2x.png 2x, splash/img/light-3x.png 3x, splash/img/light-4x.png 4x" media="(prefers-color-scheme: light)">
        <source srcset="splash/img/dark-1x.png 1x, splash/img/dark-2x.png 2x, splash/img/dark-3x.png 3x, splash/img/dark-4x.png 4x" media="(prefers-color-scheme: dark)">
        <img class="center" aria-hidden="true" src="splash/img/light-1x.png" alt="">
    </picture>
  </div>
  <script>
    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>


</body></html>