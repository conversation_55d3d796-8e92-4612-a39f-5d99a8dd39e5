// To parse this JSON data, do
//
//     final comment = commentFromJson(jsonString);

import 'dart:convert';

import 'package:equatable/equatable.dart';

List<ResourseComment> commentFromJson(String str) => List<ResourseComment>.from(
    json.decode(str).map((x) => ResourseComment.fromJson(x)));

String commentToJson(List<ResourseComment> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ResourseComment extends Equatable {
  final String commentId;
  final String message;
  final String subject;
  final String user;
  final String profilePic;
  final String commentType;
  final DateTime? commentedTime;
  final List<ResourseComment>? children;

  const ResourseComment({
    required this.commentId,
    required this.message,
    required this.subject,
    required this.user,
    required this.profilePic,
    required this.commentType,
    required this.commentedTime,
    this.children,
  });

  factory ResourseComment.fromJson(Map<String, dynamic> json) =>
      ResourseComment(
        commentId: json["id"] ?? '',
        message: json["message"] ?? '',
        subject: json["subject"] ?? '',
        user: json["name"] ?? '',
        profilePic: json["avatar_url"] ?? '',
        commentedTime: DateTime.tryParse(json["created_at"]),
        commentType: json['type'] ?? '',
        children: json["children"] == null
            ? []
            : List<ResourseComment>.from(
                json["children"]!.map((x) => ResourseComment.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": commentId,
        "message": message,
        "subject": subject,
        "user": user,
        "profile_pic": profilePic,
        "children": children == null
            ? []
            : List<dynamic>.from(children!.map((x) => x.toJson())),
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [commentId, message, subject, user, profilePic, children];
  }
}
