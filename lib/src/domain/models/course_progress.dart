import 'package:equatable/equatable.dart';

class CourseProgress extends Equatable {
  double progress;
  final String timeSpent;
  final String instanceId;
  final bool markedAsDone;

  CourseProgress({
    required this.progress,
    required this.timeSpent,
    required this.instanceId,
    required this.markedAsDone,
  });

  factory CourseProgress.fromJson(Map<String, dynamic> json) {
    return CourseProgress(
      progress: (json['progress'] ?? 0) * 1.0,
      timeSpent: json['time_spent'] ?? '',
      instanceId: json['instance_id'] ?? '',
      markedAsDone: json['marked_as_done'] ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
        'progress': progress,
        'time_spent': timeSpent,
        'instance_id': instanceId,
        'marked_as_done': markedAsDone,
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [];
  }
}
