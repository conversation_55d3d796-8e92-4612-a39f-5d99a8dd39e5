import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:path_provider/path_provider.dart';

import '../../config/themes/app_dynamic_theme.dart';
import '../../utils/resources/user_activity_res.dart';
import '/src/domain/models/course_dashboard/course_dashboard_config.dart';
import '/src/domain/models/course_dashboard/course_video_resource.dart';
import '/src/presentation/cubits/course_dashboard/course_dashboard_cubit.dart';

import '../../../src/presentation/widgets/valid_network_image_view.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_dashboard/course_assignments.dart';
import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../domain/models/course_dashboard/course_page_resource.dart';
import '../../domain/models/responses/section_details.dart';
import '../../utils/constants/nums.dart';
import '../cubits/course_details/course_details_cubit.dart';
import '../cubits/section_details/section_details_cubit.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/linear_progress_indicator_widget.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../utils/helper/privilege_access_mapper.dart';
import '/src/domain/services/network_services.dart';
import 'package:carousel_slider/carousel_slider.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';

import '../../utils/constants/helper.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../../domain/models/course_progress.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '../widgets/connectivity_widget.dart';
import '/src/presentation/widgets/empty_screen_view.dart';
import '/src/presentation/widgets/vedio_thumbnail_widget.dart';
import '/src/config/enums/course_grid_item.dart';
import '../../config/themes/app_theme.dart';
import '/src/config/router/app_router.dart';
import '/src/presentation/cubits/course_list/course_list_cubit.dart';
import '/src/config/enums/scroll_direction.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../src/config/themes/lms_fonts.dart';
import '../../../src/presentation/widgets/app_bar.dart';
import '../../../src/utils/constants/strings.dart';
import '../../../src/utils/helper/app_date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../domain/models/course_details.dart';
import '../cubits/profile/profile_cubit.dart';
import '../widgets/drawer_widget.dart';
import '../widgets/loading_indicator.dart';
import 'ppt_viewer.dart';
import 'package:provider/provider.dart' as provider;

@RoutePage()
class CourseDetailsViewScreen extends HookWidget with WidgetsBindingObserver {
  final Map<String, dynamic> sampleJson = {
    'current_affairs_size': 5,
    'videos_scroll_direction': GridScrollDirection.horizontal,
    'videos_crossaxis_count': 20,
    'subjects_scroll_direction': GridScrollDirection.horizontal,
    'subjects_crossaxis_count': 20,
    'exams_scroll_direction': GridScrollDirection.vertical,
    'exams_crossaxis_count': 20,
  };

  final String courseId;
  final String courseName;
  final bool isFromLogin;

  // CourseDetailsViewScreen(requi this.courseId, this.courseName, {Key? key, this.isFromLogin = false})
  //     // : courseId = COURSE_ID,
  //     //   courseName = COURSE_NAME;

  CourseDetailsViewScreen(
      {Key? key,
      required this.courseId,
      required this.courseName,
      this.isFromLogin = false})
      : super(key: key);

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController _affairsScrollController = ScrollController();
  final CarouselController _controller = CarouselController();
  final ValueNotifier<bool> _isDataLoading = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _seeAllResources = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _seeAllSubjects = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _seeAllGeneralResources =
      ValueNotifier<bool>(false);

  static const double _affairsHeight = 150;
  static const double _videoItemHeight = 151;
  static const double _subjectItemHeight = 130;
  static const double _horizontalMargin = 16;
  static const double _verticalGapBwSections = 30;
  static const double _verticalSpaceAfterTitle = 15;

  List<Resource> _resources = [];

  CourseDetails? courseDetails;

  /// set to true when the
  /// horizontal scroll for affairs list reaches the last item.
  /// used to set the visibility of the arrow.
  bool _didReachEndofScroll = false;
  bool didUpdateScreen = false;

  /// decides whether to show the course in sidemenu
  late ValueNotifier<bool> isCourseAccessGiven;
  late ValueNotifier<bool> hasSubscriptionAccess;
  late ValueNotifier<bool> shouldFetchCourseDetails;
  late ValueNotifier<bool> shouldFetchCurrentAffairs;
  late ValueNotifier<bool> shouldFetchSectionDetails;
  late ValueNotifier<bool> shouldFetchNewExams;
  late ValueNotifier<bool> shouldFetchAttemptedExams;

  int _currentCarouselItemIndex = 0;
  int assignmentListCount = 0;

  BuildContext? currentContext;
  AppDynamicTheme? appDynamicTheme;
  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    // TO DO: context should not be equal to null
    didChangeAppLocale(locales, currentContext);
  }

  _reloadData(CourseListCubit _courseCubit, UserCubit _userCubit) async {
    if (await checkNetworkStatus()) {
      _courseCubit.setLoading(true);
      await _userCubit.getUserInfo(USER_ID);
      await _courseCubit.fetchCourseDetails(COURSE_ID);
    }
  }

  _initAccessDetails() async {
    // decides whether to show course in sidemenu
    String screen = PrivilegeAccessConsts.SCREEN_COURSE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_COURSE_LIST;
    isCourseAccessGiven.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show course details
    screen = PrivilegeAccessConsts.SCREEN_COURSE;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_COURSE_DETAILS;
    shouldFetchCourseDetails.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show current affairs
    screen = PrivilegeAccessConsts.SCREEN_CURRENT_AFFAIRS;
    accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_GET_CURRENT_AFFAIR_lIST;
    shouldFetchCurrentAffairs.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show section details
    screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_SECTION_DETAILS;
    shouldFetchSectionDetails.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show new exams
    screen = PrivilegeAccessConsts.SCREEN_EXAM;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_EXAM_LIST;
    shouldFetchNewExams.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show attempted exams
    screen = PrivilegeAccessConsts.SCREEN_EVALUATION;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_ATTENDED_EXAM_LIST;
    shouldFetchAttemptedExams.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to show subscription tab
    screen = PrivilegeAccessConsts.SCREEN_SUBSCRIPTION;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_USER_SUBSCRIPTION_LIST;
    bool subAccessCheck1 = await PrivilegeAccessMapper.checkPrivilegeAccessFor(
        screen,
        accessRequiredFeature: accessRequiredFeature);
    hasSubscriptionAccess.value = subAccessCheck1;
  }

  _initCourseDetailsInfo(
      BuildContext context, CourseListCubit _courseCubit) async {
    courseDetails = CourseDetails(
        summary: '',
        fullName: '',
        shortName: '',
        courseModule: [],
        courseVideos: [],
        currentAffairs: [],
        exams: []);
    if (shouldFetchCourseDetails.value) {
      _courseCubit.setLoading(true);
      await _courseCubit.fetchCourseDetails(courseId);
    }
    // Future.delayed(const Duration(seconds: 1), () {
    //   _courseCubit.setLoading(false);
    // });
    _affairsScrollController.addListener(() {
      if (_affairsScrollController.offset ==
          _affairsScrollController.position.maxScrollExtent) {
        //reached end of scroll
        _didReachEndofScroll = true;
      } else {
        _didReachEndofScroll = false;
      }

      _courseCubit.removeArrow(!_didReachEndofScroll);
    });

    CourseListState state = _courseCubit.state;
    if (state is CourseDetailsError) {
      debugPrint(state.error);
      showPGExceptionPopup(context, state.error);
    }
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    isCourseAccessGiven = useState<bool>(false);
    hasSubscriptionAccess = useState<bool>(false);
    shouldFetchCourseDetails = useState<bool>(false);
    shouldFetchCurrentAffairs = useState<bool>(false);
    shouldFetchNewExams = useState<bool>(false);
    shouldFetchAttemptedExams = useState<bool>(false);
    shouldFetchSectionDetails = useState<bool>(false);

    final _courseCubit = BlocProvider.of<CourseListCubit>(context);
    final _userCubit = BlocProvider.of<UserCubit>(context);

    String? firstName = _userCubit.userFirstName ?? "";
    String? lastName = _userCubit.userLastName ?? "";
    String? fullName = firstName + ' ' + lastName;
    String? email = _userCubit.state.profile?.email ?? '';
    String? avatarUrl = _userCubit.state.profile?.avatarUrl ?? '';
    String _courseId = '';
    String _courseName = '';

    Future<void> _fetchRequiredData() async {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      _courseId = _prefs.getString('courseID') ?? '';
      COURSE_ID = _courseId;
      _courseName = _prefs.getString('courseName') ?? '';
      COURSE_NAME = _courseName;
      USER_ID = _prefs.getString("userId") ?? '';
    }

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      assignmentListCount =
          BlocProvider.of<CourseDashboardCubit>(context).assignmentsCount;
      _resources =
          BlocProvider.of<CourseDashboardCubit>(context).generalResources;
      Future<void>.microtask(() async {
        await _initAccessDetails();
        await _initCourseDetailsInfo(context, _courseCubit);
        final _deviceInfoObj = await fetchDeviceInfo();
      });

      return () {
        currentContext = null;
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return Scaffold(
      drawerEnableOpenDragGesture: false,
      key: _scaffoldKey,
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(75),
          child: AppBarWidget(
            title: COURSE_NAME,
            toolbarHeight: 75,
            isHomeScreen: false,
            // trailingWidget: Image.asset(
            //   '$ASSETS_PATH/notifications.png',
            //   width: 36,
            //   height: 31,
            // ),
            trailingWidget: Container(),
            leadingIconName: BACK_ARROW,
            leadingBtnAction: () => kIsWeb
                ? Beamer.of(context).beamBack()
                : appRouter.popForced(didUpdateScreen),
            // final result = await InternetConnectionCheckerPlus().hasConnection;
            // final scaffoldState = _scaffoldKey.currentState;
            // if (scaffoldState != null) {
            //   scaffoldState.openDrawer();
            // }
          )),
      drawer: Drawer(
          width: kIsWeb ? 350 : MediaQuery.of(context).size.width * 0.8,
          child: DrawerWidget(
            name: fullName,
            email: email,
            avatarUrl: avatarUrl,
            courseName: courseName,
            hasCourseAccess: isCourseAccessGiven.value,
            hasProfileAccess: true,
            hasSubscriptionAccess: hasSubscriptionAccess.value,
          )),
      body: Container(
        // padding: const EdgeInsets.symmetric(horizontal: 5),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16), topRight: Radius.circular(16)),
        ),
        child: SafeArea(
          child: BlocBuilder<CourseListCubit, CourseListState>(
            builder: (context, state) {
              CourseDetails _courseDetails = CourseDetails(
                summary: state.courseDetailsData?.summary ?? '',
                fullName: state.courseDetailsData?.fullName ?? '',
                shortName: state.courseDetailsData?.shortName ?? '',
                courseModule: state.courseDetailsData?.courseModule ?? [],
                courseVideos: state.courseDetailsData?.courseVideos ?? [],
                currentAffairs: state.courseDetailsData?.currentAffairs ?? [],
                exams: state.courseDetailsData?.exams ?? [],
                courseAssignments:
                    state.courseDetailsData?.courseAssignments ?? [],
              );

              if (state is CourseDetailsFetched) {
                _courseDetails = CourseDetails(
                  summary: state.courseDetailsData?.summary ?? '',
                  fullName: state.courseDetailsData?.fullName ?? '',
                  shortName: state.courseDetailsData?.shortName ?? '',
                  courseModule: state.courseDetailsData?.courseModule ?? [],
                  courseVideos: state.courseDetailsData?.courseVideos ?? [],
                  currentAffairs: state.courseDetailsData?.currentAffairs ?? [],
                  exams: state.courseDetailsData?.exams ?? [],
                  courseAssignments:
                      state.courseDetailsData?.courseAssignments ?? [],
                );
                courseDetails = _courseDetails;
                _currentCarouselItemIndex = kIsWeb
                    ? (courseDetails?.currentAffairs.length ?? 0) > 1
                        ? 1
                        : 0
                    : 0;
              } else if (state is CarouselChangeIndex) {
                _currentCarouselItemIndex = state.carouselIndex;
              }
              bool isEmptyData = courseDetails != null
                  ? courseDetails!.courseModule.isEmpty &&
                      courseDetails!.courseVideos.isEmpty &&
                      courseDetails!.currentAffairs.isEmpty &&
                      courseDetails!.exams.isEmpty &&
                      courseDetails!.courseAssignments.isEmpty
                  : _courseDetails.courseModule.isEmpty &&
                      _courseDetails.courseVideos.isEmpty &&
                      _courseDetails.currentAffairs.isEmpty &&
                      _courseDetails.exams.isEmpty &&
                      _courseDetails.courseAssignments.isEmpty;
              return LayoutBuilder(
                  builder: (BuildContext context, BoxConstraints constraints) {
                return ValueListenableBuilder(
                    valueListenable: _isDataLoading,
                    builder: (context, _, __) {
                      return Stack(
                        children: [
                          (isEmptyData && state is CourseDetailsEmpty) ||
                                  state is CourseDetailsError
                              ? EmptyScreenView(
                                  title: SLStrings.getTranslatedString(
                                      KEY_COURSE_DETAILS_EMPTY),
                                  retryAction: () {
                                    _reloadData(_courseCubit, _userCubit);
                                  },
                                  buttonText: SLStrings.getTranslatedString(
                                      KEY_TRY_AGAIN),
                                  showRetryButton: NetworkServices
                                                  .client
                                                  ?.client
                                                  .auth
                                                  .currentSession ==
                                              null ||
                                          NetworkServices.client?.client.auth
                                                  .currentSession?.isExpired ==
                                              true
                                      ? true
                                      : false,
                                )
                              : Align(
                                  child: Container(
                                    height: constraints.maxHeight,
                                    width: constraints.maxWidth < 720
                                        ? double.infinity
                                        : 800,
                                    decoration: const BoxDecoration(
                                      // color: Colors.white,
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(16),
                                          topRight: Radius.circular(16)),
                                    ),
                                    child: ScrollConfiguration(
                                      behavior: ScrollConfiguration.of(context)
                                          .copyWith(
                                              scrollbars: false,
                                              dragDevices: {
                                            PointerDeviceKind.touch,
                                            PointerDeviceKind.mouse,
                                          }),
                                      child: SingleChildScrollView(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // _currentAffairsView(
                                            //     constraints,
                                            //     context,
                                            //     courseDetails ?? _courseDetails,
                                            //     _courseCubit),
                                            _courseDescriptionWidget(
                                                constraints,
                                                context,
                                                courseDetails ?? _courseDetails,
                                                _courseCubit),
                                            // : Container(),
                                            _videosView(
                                                context,
                                                constraints,
                                                courseDetails ?? _courseDetails,
                                                state,
                                                _courseCubit),

                                            courseDetails != null &&
                                                    _resources.isNotEmpty &&
                                                    courseDetails!
                                                        .courseAssignments
                                                        .isNotEmpty
                                                ? _resourceContainer(
                                                    constraints,
                                                    context,
                                                    courseDetails!
                                                        .courseAssignments
                                                        .length,
                                                    isAssignment: false)
                                                : const SizedBox.shrink(),
                                            _assignmentsContainer(
                                                context,
                                                _courseCubit,
                                                constraints,
                                                courseDetails != null
                                                    ? courseDetails!
                                                        .courseAssignments
                                                    : [],
                                                isAssignments: false),

                                            courseDetails != null &&
                                                    courseDetails!
                                                        .courseAssignments
                                                        .isNotEmpty
                                                ? _resourceContainer(
                                                    constraints,
                                                    context,
                                                    courseDetails!
                                                        .courseAssignments
                                                        .length,
                                                    isAssignment: true)
                                                : const SizedBox.shrink(),

                                            _assignmentsContainer(
                                                context,
                                                _courseCubit,
                                                constraints,
                                                courseDetails != null
                                                    ? courseDetails!
                                                        .courseAssignments
                                                    : [],
                                                isAssignments: true),
                                                
                                            // shouldFetchSectionDetails.value
                                            //     ?
                                            _subjectsView(
                                                context,
                                                constraints,
                                                courseDetails ?? _courseDetails,
                                                state,
                                                _courseCubit),
                                            // : Container(),
                                            shouldFetchNewExams.value ||
                                                    shouldFetchAttemptedExams
                                                        .value
                                                ? _examsView(
                                                    context,
                                                    constraints,
                                                    courseDetails ??
                                                        _courseDetails,
                                                    state,
                                                    _courseCubit)
                                                : Container(),
                                            const SizedBox(height: 16),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                          ConnectivityStatusWidget(),
                          state is CourseDetailsLoading &&
                                      state.isDataLoading ||
                                  _isDataLoading.value
                              ? Positioned.fill(child: LoadingIndicatorClass())
                              : Container(),
                        ],
                      );
                    });
              });
            },
          ),
        ),
      ),
    );
  }

  Widget _titleWidget(BoxConstraints constraints, String title) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: _horizontalMargin),
      child: Text(
        title,
        style: LMSFonts.semiBoldFont(18, AppTheme.headerTextColor),
      ),
    );
  }

  Widget _courseDescriptionWidget(
      BoxConstraints constraints,
      BuildContext context,
      CourseDetails courseDetails,
      CourseListCubit courseCubit) {
    return Visibility(
        visible: (courseDetails.fullName.isNotEmpty &&
                courseDetails.fullName != null ||
            courseDetails.shortName.isNotEmpty &&
                courseDetails.shortName != null),
        child: Container(
            margin: const EdgeInsets.only(top: _verticalGapBwSections),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _titleWidget(constraints,
                    SLStrings.getTranslatedString(KEY_ABOUT_COURSE)),
                const SizedBox(height: _verticalSpaceAfterTitle),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: _horizontalMargin),
                  child: (courseDetails.fullName.isNotEmpty &&
                          courseDetails.fullName != null)
                      ? Text(courseDetails.fullName)
                      : Text(courseDetails.shortName),
                ),
                if (courseDetails.summary != '')
                  Container(
                    alignment: Alignment.bottomRight,
                    padding: EdgeInsets.all(6),
                    child: GestureDetector(
                        child: Padding(
                            padding: EdgeInsets.only(right: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  SLStrings.getTranslatedString(KEY_READ_MORE),
                                  style: LMSFonts.semiBoldFont(
                                      12, AppTheme.primaryAppColor),
                                ),
                                Image.asset(
                                  '$ASSETS_PATH/see_all_btn.png',
                                  width: 20,
                                  height: 10,
                                ),
                              ],
                            )),
                        onTap: () {
                          kIsWeb
                              ? Beamer.of(context).beamToNamed(
                                  '/course-description-view',
                                  data: {
                                      'summary': courseDetails.summary ?? ''
                                    })
                              : appRouter.push(CourseDescriptionViewRoute(
                                  summary: courseDetails.summary ?? ''));
                        }),
                  ),
              ],
            )));
  }

  Widget _currentAffairsView(BoxConstraints constraints, BuildContext context,
      CourseDetails courseDetails, CourseListCubit courseCubit) {
    bool showMore = courseDetails.currentAffairs.length >
        sampleJson['current_affairs_size'];
    int listLength = showMore
        ? sampleJson['current_affairs_size']
        : courseDetails.currentAffairs.length;
    return Visibility(
      visible: listLength > 0,
      child: Container(
        margin: const EdgeInsets.only(top: _verticalGapBwSections),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: _titleWidget(constraints,
                      SLStrings.getTranslatedString(KEY_CURRENT_AFFAIRS)),
                ),
                _seeAllWidget(context, courseCubit),
              ],
            ),
            const SizedBox(height: _verticalSpaceAfterTitle),
            _affairsCarouselWidget(context, courseCubit,
                courseDetails.currentAffairs, constraints),
          ],
        ),
      ),
    );
  }

  Widget _seeAllWidget(BuildContext context, CourseListCubit courseCubit) {
    return Visibility(
      visible: true, // shouldFetchCurrentAffairs.value,
      child: InkWell(
        onTap: () async {
          courseCubit.setLoading(true);
          await courseCubit.fetchMoreCurrentAffairs();
          CourseListState state = courseCubit.state;
          if (state is CurrentAffairsFetched) {
            kIsWeb
                ? Beamer.of(context).beamToNamed('/news-more',
                    data: {'affairsList': state.currentAffairs})
                : appRouter.push(
                    NewsViewMoreViewRoute(affairsList: state.currentAffairs));
            courseCubit.setLoading(false);
          }
        },
        child: Container(
          margin: const EdgeInsets.only(right: _horizontalMargin),
          color: Colors.white,
          child: Row(
            children: [
              Text(
                SLStrings.getTranslatedString(KEY_SEE_ALL),
                style: LMSFonts.semiBoldFont(
                    12,
                    appDynamicTheme?.navbarTextColor ??
                        AppTheme.primaryAppColor),
              ),
              Image.asset(
                '$ASSETS_PATH/see_all_btn.png',
                width: 20,
                height: 10,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _affairsCarouselWidget(
      BuildContext context,
      CourseListCubit courseCubit,
      List<CurrentAffairs?> affairsList,
      BoxConstraints constraints) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: kIsWeb ? 16 : 0),
      child: Column(children: [
        _carouselSliderWidget(context, courseCubit, affairsList, constraints),
        const SizedBox(height: 12),
        _carouselDotsWidget(affairsList, courseCubit),
      ]),
    );
  }

  _carouselSliderWidget(BuildContext context, CourseListCubit courseCubit,
      List<CurrentAffairs?> affairsList, BoxConstraints constraints) {
    double _affairsWidth = MediaQuery.sizeOf(context).width;
    final List<Widget> imageSliders = [];
    for (var i = 0; i < affairsList.length; i++) {
      CurrentAffairs? item = affairsList[i];
      Widget newsWidget = item != null
          ? InkWell(
              onTap: () {
                int index = i;
                if (courseDetails!.currentAffairs[index] != null &&
                    courseDetails!
                        .currentAffairs[index]!.newsContent.isNotEmpty) {
                  kIsWeb
                      ? Beamer.of(context).beamToNamed('/course-news', data: {
                          'currentAffairs':
                              courseDetails!.currentAffairs[index]!
                        })
                      : appRouter.push(CourseNewsViewRoute(
                          currentAffairs: courseDetails!.currentAffairs[index]!,
                        ));
                } else {
                  _showAlertDialog(
                      context,
                      SLStrings.getTranslatedString(
                          KEY_AFFAIRS_DETAILS_NOT_FOUND));
                }
              },
              child: Container(
                height: constraints.maxWidth < 720 ? 150 : 300,
                margin:
                    const EdgeInsets.symmetric(horizontal: _horizontalMargin),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    image: DecorationImage(
                        fit: BoxFit.cover,
                        image: AssetImage(item.backgroundImage))),
                child: Stack(
                  children: <Widget>[
                    _carouselContentWidget(item, i),
                  ],
                ),
              ),
            )
          : Container();

      imageSliders.add(newsWidget);
    }

    return Container(
      color: Colors.transparent,
      child: CarouselSlider(
        items: imageSliders,
        carouselController: _controller,
        options: CarouselOptions(
            initialPage: _currentCarouselItemIndex,
            enlargeCenterPage: kIsWeb ? true : false,
            aspectRatio: constraints.maxWidth < 720
                ? _affairsWidth / _affairsHeight
                : 25 / 4,
            viewportFraction: constraints.maxWidth < 720 ? 1 : 0.6,
            enableInfiniteScroll: false,
            onPageChanged: (index, reason) =>
                courseCubit.changeCarouselIndex(index)),
      ),
    );
  }

  _carouselContentWidget(CurrentAffairs item, int index) {
    return Positioned(
      bottom: 0.0,
      left: 0.0,
      right: 0.0,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: const LinearGradient(
            colors: [Color.fromRGBO(0, 0, 0, 1), Color.fromRGBO(0, 0, 0, 0)],
            begin: Alignment.bottomCenter,
            end: Alignment.topCenter,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _affairsTitleWidget(item.newsTitle),
            _dateItemWidget(index),
          ],
        ),
      ),
    );
  }

  _affairsTitleWidget(String newsTitle) {
    return Text(
      newsTitle,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: LMSFonts.mediumFont(16, AppTheme.whiteTextColor, 1.0),
    );
  }

  _dateItemWidget(int index) {
    String date = courseDetails != null &&
            courseDetails!.currentAffairs[index] != null
        ? AppDateFormatter()
            .formatDatedMy(courseDetails!.currentAffairs[index]!.publishedDate)
        : AppDateFormatter().formatDatedMy(DateTime.now());
    return Text(
      date,
      textAlign: TextAlign.center,
      style: LMSFonts.mediumFont(12, AppTheme.whiteTextColor, 1),
    );
  }

  _carouselDotsWidget(
      List<CurrentAffairs?> affairsList, CourseListCubit courseCubit) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: affairsList.asMap().entries.map((entry) {
        return GestureDetector(
          onTap: () => _updateCarousalIndex(entry, courseCubit),
          child: Container(
            width: 6.0,
            height: 6.0,
            margin: const EdgeInsets.symmetric(horizontal: 6.0),
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _currentCarouselItemIndex == entry.key
                    ? AppTheme.carousalSelectedDotColor
                    : AppTheme.carousalUnSelectedDotColor),
          ),
        );
      }).toList(),
    );
  }

  _updateCarousalIndex(
      MapEntry<int, CurrentAffairs?> entry, CourseListCubit courseCubit) {
    _controller.jumpToPage(entry.key);
    courseCubit.changeCarouselIndex(entry.key);
  }

  Widget _videosView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails _courseDetails,
      CourseListState state,
      CourseListCubit _courseCubit) {
    int itemCount = _courseDetails.courseVideos.length;

    return Visibility(
      visible: itemCount > 0,
      child: Container(
        margin: const EdgeInsets.only(top: _verticalGapBwSections),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _titleWidget(
                constraints, SLStrings.getTranslatedString(KEY_VIDEOS)),
            const SizedBox(height: _verticalSpaceAfterTitle),
            sampleJson['videos_scroll_direction'] ==
                    GridScrollDirection.vertical
                ? _verticalGridView(context, constraints, _courseDetails,
                    itemCount, CourseGridItem.videos, state, _courseCubit)
                : _horizontalVideoView(
                    context,
                    constraints,
                    _courseDetails,
                    _courseCubit,
                    itemCount,
                    CourseGridItem.videos,
                  ),
          ],
        ),
      ),
    );
  }

  Widget _horizontalVideoView(
    BuildContext context,
    BoxConstraints constraints,
    CourseDetails? _courseDetails,
    CourseListCubit _courseCubit,
    int itemCount,
    CourseGridItem gridItem,
  ) {
    List<CourseVideo?> _courseVideos = _courseDetails?.courseVideos ?? [];
    final List<Widget> videoSliders = _courseVideos
        .map((item) => item != null
            ? _videoItem(context, constraints, item, _courseCubit,
                _courseVideos.indexOf(item), _courseVideos.length)
            : Container())
        .toList();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: _horizontalMargin),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: videoSliders,
        ),
      ),
    );
    // List<CourseVideo?> _courseVideos = _courseDetails?.courseVideos ?? [];
    // final List<Widget> videoSliders = _courseVideos
    //     .map((item) => item != null
    //         ? _videoItem(context, item, _courseVideos.indexOf(item),
    //             constraints, _courseCubit, _courseVideos.length)
    //         : Container())
    //     .toList();
    // return SingleChildScrollView(
    //   scrollDirection: Axis.horizontal,
    //   child: Row(
    //     crossAxisAlignment: CrossAxisAlignment.start,
    //     children: videoSliders,
    //   ),
    // );
  }

  Widget _verticalGridView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails? _courseDetails,
      int itemCount,
      CourseGridItem gridItem,
      CourseListState state,
      CourseListCubit _courseCubit) {
    switch (gridItem) {
      case CourseGridItem.videos:
        itemCount = itemCount > sampleJson['videos_crossaxis_count']
            ? sampleJson['videos_crossaxis_count']
            : itemCount;
        break;
      case CourseGridItem.subjects:
        itemCount = itemCount > sampleJson['subjects_crossaxis_count']
            ? sampleJson['subjects_crossaxis_count']
            : itemCount;
        break;
      case CourseGridItem.exams:
        itemCount = itemCount > sampleJson['exams_crossaxis_count']
            ? sampleJson['exams_crossaxis_count']
            : itemCount;
        break;
      default:
    }

    return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        primary: false,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // Number of columns in one view
          childAspectRatio: gridItem == CourseGridItem.videos ? 2 / 1.4 : 2 / 1,
          //  (constraints.maxWidth / 2)/  (constraints.maxWidth / 2.88),
          mainAxisSpacing: 20,
          crossAxisSpacing: 20,
        ),
        itemCount: itemCount,
        itemBuilder: (BuildContext ctx, index) {
          String moduleName = gridItem == CourseGridItem.subjects
              ? _courseDetails != null &&
                      _courseDetails.courseModule[index] != null
                  ? _courseDetails.courseModule[index]!.moduleName
                  : ''
              : gridItem == CourseGridItem.exams
                  ? _courseDetails != null &&
                          _courseDetails.exams[index] != null
                      ? _courseDetails.exams[index]!.moduleName
                      : ''
                  : '';
          return gridItem == CourseGridItem.videos
              ? _videoItem(
                  context,
                  constraints,
                  _courseDetails?.courseVideos[index],
                  _courseCubit,
                  index,
                  _courseDetails?.courseVideos.length ?? 0)
              : gridItem == CourseGridItem.subjects
                  ? _subjectNames(
                      context,
                      moduleName,
                      constraints,
                      _courseDetails?.courseModule[index],
                      index,
                      _courseDetails?.courseModule.length ?? 0)
                  : gridItem == CourseGridItem.exams
                      ? _examContainer(context, _courseDetails?.exams ?? [],
                          constraints) //_examTitles(context, constraints,_courseDetails?.exams[index], moduleName, index)
                      : Container();
        });
  }

  Widget _videoItem(
      BuildContext context,
      BoxConstraints constraints,
      CourseVideo? courseVideo,
      CourseListCubit _courseCubit,
      int index,
      int totalCount) {
    /// _horizontalMargin * 3 -> left, right padding gap fo the view,
    /// and the gap in between each item
    /// -10 -> left and right padding of the entire view
    double screenWidth = MediaQuery.sizeOf(context).width;
    double _videoItemWidth = totalCount == 1 || totalCount > 2
        ? (screenWidth / 2) - (_horizontalMargin * 3)
        : (screenWidth - (_horizontalMargin * 3)) / 2;
    CourseVideo courseVideoArg = courseVideo ??
        CourseVideo(
            videoId: '',
            videoName: '',
            videoURL: '',
            progress: 0,
            description: '',
            videoThumbnail: '',
            instanceId: '');
    bool _isYouTubeVideo = courseVideoArg.videoURL != '' &&
        courseVideoArg.videoURL.startsWith(YOUTUBE_URL);

    ResourceProgress? progress = ResourceProgress(
        progress: 0, timeSpent: "", instanceId: "", markedAsDone: false);
    return InkWell(
      // for showing hand icon for web while hovering
      onTap: () => debugPrint("object"),
      child: Container(
        margin: const EdgeInsets.only(right: _horizontalMargin),
        width: constraints.maxWidth < 720 ? _videoItemWidth : 200,
        alignment: Alignment.topLeft,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: VideoThumbnailWidget(
                videoUrl: courseVideoArg.videoURL,
                isYoutubeVideo: _isYouTubeVideo,
                removeShadow: true,
                videoTitle: courseVideoArg.videoName,
                progress: courseDetails?.courseVideos[index]?.progress ?? 0.0,
                constraints: constraints,
                shouldShowVideoProgress: true,
                thumbnail: courseVideoArg.videoThumbnail,
                // futureInst: _isYouTubeVideo
                //     ? generateYouTubeThumbnail(courseVideoArg.videoURL)
                //     : generateVideoThumbnail(courseVideoArg.videoURL),
                onTapCallback: () {
                  /*  kIsWeb
                      ? Beamer.of(context).beamToNamed('/video-view', data: {
                          'courseVideo': courseVideoArg,
                          'progess': progress
                        })
                      : appRouter
                          .push(CourseVideoViewRoute(
                              courseVideo: courseVideoArg, progess: progress))
                          .then((value) {
                          if (value != null) {
                            Map<String, dynamic> args =
                                value as Map<String, dynamic>;
                            String progressVal = args['progress'] ?? '0.0';
                            double progressDouble = double.parse(progressVal);
                            String durationStr = args['total_duration'] ?? '0';
                            int durationInt = int.parse(durationStr);
                            if (courseDetails?.courseVideos[index] != null) {
                              courseDetails?.courseVideos[index]!.progress =
                                  progressDouble;
                              courseDetails?.courseVideos[index]!
                                  .totalDuration = durationInt;
                            }
                          }

                          _courseCubit
                              .emit(CourseVideoStatus(isFullSCreen: true));
                        });*/
                },
              ),
            ),
            courseVideo != null
                ? _videoTitleRow(courseVideo.videoName)
                : Container(),
          ],
        ),
      ),
    );
  }

  Widget _videoTitleRow(String videoName) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        height: 50,
        // color: Colors.amber,
        margin: const EdgeInsets.only(top: 5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          // crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              '$ASSETS_PATH/video_play.png',
              height: 35,
              width: 35,
            ),
            const SizedBox(width: 5),
            Expanded(
              child: Container(
                // color: Colors.red,
                alignment: Alignment.centerLeft,
                child: Text(
                  videoName,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: LMSFonts.mediumFont(14, AppTheme.headerTextColor, 21),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _resourceContainer(
      BoxConstraints constraints, BuildContext context, int assignmentTotal,
      {required bool isAssignment}) {
    final assignmentCount =
        BlocProvider.of<CourseDashboardCubit>(context).assignmentsCount;
    final generalResourceCount =
        BlocProvider.of<CourseDashboardCubit>(context).generalResourcesCount;
    return Padding(
      padding: EdgeInsets.only(top: isAssignment ? 0 : 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _titleWidget(
              constraints, isAssignment ? 'Assignments' : "General Resources"),
          Visibility(
            visible: isAssignment
                ? assignmentTotal > assignmentCount
                : _resources.length > generalResourceCount,
            child: InkWell(
              onTap: () {
                if (isAssignment) {
                  _seeAllResources.value = !_seeAllResources.value;
                } else {
                  _seeAllGeneralResources.value =
                      !_seeAllGeneralResources.value;
                }
              },
              child: Container(
                padding: const EdgeInsets.only(right: 16),
                color: Colors.white,
                child: Row(
                  children: [
                    ValueListenableBuilder(
                        valueListenable: isAssignment
                            ? _seeAllResources
                            : _seeAllGeneralResources,
                        builder: (context, value, _) {
                          return value
                              ? Text(
                                  SLStrings.getTranslatedString(KEY_SEE_LESS),
                                  style: LMSFonts.semiBoldFont(
                                      12,
                                      appDynamicTheme?.navbarTextColor ??
                                          AppTheme.primaryAppColor),
                                )
                              : Text(
                                  SLStrings.getTranslatedString(KEY_SEE_ALL),
                                  style: LMSFonts.semiBoldFont(
                                      12,
                                      appDynamicTheme?.navbarTextColor ??
                                          AppTheme.primaryAppColor),
                                );
                        }),
                    Image.asset(
                      '$ASSETS_PATH/see_all_btn.png',
                      width: 20,
                      height: 10,
                    ),
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _assignmentsContainer(
      BuildContext context,
      CourseListCubit _courseCubit,
      BoxConstraints constraints,
      List<CourseAssignments> courseAssignments,
      {required bool isAssignments}) {
    final assignmentConfigCount =
        BlocProvider.of<CourseDashboardCubit>(context).assignmentsCount;
    final assignmentCount = courseAssignments.length > assignmentConfigCount
        ? assignmentConfigCount
        : courseAssignments.length;
    final generalResourceConfigCount =
        BlocProvider.of<CourseDashboardCubit>(context).generalResourcesCount;
    final generalResourceCount = _resources.length > generalResourceConfigCount
        ? generalResourceConfigCount
        : _resources.length;
    if (courseAssignments.isNotEmpty) {
      courseAssignments.retainWhere((element) =>
          element.resourceType == ResourceType.FILE ||
          element.resourceType == ResourceType.PAGE ||
          element.resourceType == ResourceType.URL);
    }
    if (courseAssignments.isEmpty) {
      return Container();
    }
    return ValueListenableBuilder(
        valueListenable:
            isAssignments ? _seeAllResources : _seeAllGeneralResources,
        builder: (context, seeAll, _) {
          return Container(
            margin: const EdgeInsets.all(horizontalMargin),
            child: LayoutBuilder(
              builder: (layoutContext, constraints) {
                final totalWidth = constraints.maxWidth;
                const crossAxisCount = 2;
                const spacing = 8.0;

                // Tile width and height
                final tileWidth =
                    (totalWidth - ((crossAxisCount - 1) * spacing)) /
                        crossAxisCount;
                return Wrap(
                  spacing: spacing,
                  runSpacing: spacing,
                  children: List.generate(
                    isAssignments
                        ? !seeAll
                            ? assignmentCount
                            : courseAssignments.length
                        : !seeAll
                            ? generalResourceCount
                            : _resources.length,
                    (index) {
                      CourseAssignments courseAssignment =
                          courseAssignments[index];
                      if (_resources.isEmpty) {
                        return Container();
                      }
                      Resource generalResource =
                          _resources[index % _resources.length];

                      return Container(
                        color: Colors.transparent,
                        width: tileWidth,
                        child: Stack(
                          children: [
                            isAssignments
                                ? _assignmentItem(context, _courseCubit,
                                    courseAssignment, tileWidth)
                                : _generalResourceItem(context, _courseCubit,
                                    generalResource, tileWidth),
                            !courseAssignment.isEnabled
                                ? _overlayWidget(tileWidth)
                                : Container(),
                          ],
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          );
        });
  }

  Widget _overlayWidget(double tileWidth) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(width: 0.5, color: AppTheme.primaryTextColorBlack),
        ),
      ),
    );
  }

  Widget _assignmentItem(BuildContext context, CourseListCubit _courseCubit,
      CourseAssignments courseAssignment, double tileWidth) {
    ResourceType _resourceType = courseAssignment.resourceType;
    if (_resourceType == ResourceType.FILE &&
        courseAssignment.resourceUrl != null) {
      if (courseAssignment.resourceUrl!.endsWith(IMAGE_EXTENSIONJPG) ||
          courseAssignment.resourceUrl!.endsWith(IMAGE_EXTENSIONJPEG) ||
          courseAssignment.resourceUrl!.endsWith(IMAGE_EXTENSIONPNG)) {
        _resourceType = ResourceType.IMAGE;
      }
    }
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(width: 0.5, color: AppTheme.primaryTextColorBlack),
      ),
      child: GestureDetector(
        onTap: () async {
          ResourceProgress? _courseProgress =
              await _fetchCourseProgress(context, courseAssignment.resourceId);

          switch (courseAssignment.resourceType) {
            case ResourceType.FILE:
              _handleFileImageTypeNavigation(
                  context, courseAssignment, _courseProgress);
              break;
            case ResourceType.URL:
              _handleVideoTypeNavigation(
                  context, courseAssignment, _courseProgress);
              break;
            case ResourceType.PAGE:
              _handlePageTypeNavigation(
                  context, courseAssignment, _courseProgress);
              break;
            default:
          }
        },
        child: Container(
          color: Colors.transparent,
          child: Stack(
            children: [
              Column(
                children: [
                  const SizedBox(height: 8),
                  _thumbnailWidget(
                    context,
                    courseAssignment,
                    _courseCubit,
                  ),
                  const Divider(
                      indent: 10, endIndent: 10, color: AppTheme.infoBlue),
                  _assignmentTitleWidget(courseAssignment),
                  const SizedBox(height: 5),
                  _progressIndicator(courseAssignment),
                  const SizedBox(height: 8),
                ],
              ),
              courseAssignment.isCheckpointEnabled
                  ? Card(
                      elevation: 4,
                      color: AppTheme.primaryOrange,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 5, vertical: 2),
                        child: Text(
                          'Checkpoint',
                          style: LMSFonts.mediumFont(
                              12, AppTheme.whiteTextColor, 1.0),
                        ),
                      ),
                    )
                  : Container(),
              Visibility(
                visible: (courseAssignment.progress ?? 0) >= 100 ? false : true,
                child: Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 3, top: 3),
                    child: Tooltip(
                      message:
                          'You cannot access the next resource until progress reaches 100%..',
                      showDuration: const Duration(seconds: 3),
                      padding: const EdgeInsets.all(8.0),
                      margin: const EdgeInsets.all(16),
                      triggerMode: TooltipTriggerMode.tap,
                      textStyle:
                          LMSFonts.mediumFont(12, AppTheme.whiteTextColor, 1.0),
                      decoration: BoxDecoration(
                        color: AppTheme.wrongANswerRed,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _generalResourceItem(BuildContext context,
      CourseListCubit _courseCubit, Resource resource, double tileWidth) {
    ResourceType _resourceType = resource.resourceType;
    if (_resourceType == ResourceType.FILE) {
      if (resource.externalUrl.endsWith(IMAGE_EXTENSIONJPG) ||
          resource.externalUrl.endsWith(IMAGE_EXTENSIONJPEG) ||
          resource.externalUrl.endsWith(IMAGE_EXTENSIONPNG)) {
        _resourceType = ResourceType.IMAGE;
      }
    }
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(width: 0.5, color: AppTheme.primaryTextColorBlack),
      ),
      child: GestureDetector(
        onTap: () async {
          switch (resource.resourceType) {
            case ResourceType.FILE:
              _grHandleFileImageTypeNavigation(context, resource);
              break;
            case ResourceType.URL:
              _grHandleVideoTypeNavigation(context, resource);
              break;
            case ResourceType.PAGE:
              _grHandlePageTypeNavigation(context, resource);
              break;
            default:
              break;
          }
        },
        child: Container(
          color: Colors.transparent,
          child: Stack(
            children: [
              Column(
                children: [
                  const SizedBox(height: 8),
                  _generalResouceThumbnailWidget(
                      context, resource, _courseCubit),
                  const Divider(
                      indent: 10, endIndent: 10, color: AppTheme.infoBlue),
                  _generalResourceTitleWidget(resource),
                  const SizedBox(height: 5),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _generalResourceTitleWidget(Resource resource) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      alignment: Alignment.center,
      height: 40,
      child: Text(
        resource.resourceName,
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: LMSFonts.mediumFont(12, AppTheme.primaryTextColorBlack, 1),
      ),
    );
  }

  Widget _assignmentTitleWidget(CourseAssignments courseAssignment) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      alignment: Alignment.center,
      height: 40,
      // color: AppTheme.reportQstnSD,
      child: Text(
        courseAssignment.resourceName,
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: LMSFonts.mediumFont(12, AppTheme.primaryTextColorBlack, 1),
      ),
    );
  }

  Widget _thumbnailWidget(
    BuildContext context,
    CourseAssignments courseAssignment,
    CourseListCubit _courseCubit,
  ) {
    ResourceType resourceType = courseAssignment.resourceType;
    bool isSkipped = courseAssignment.isSkipped;
    bool showSkipBtn = courseAssignment.isEnabled &&
        (courseAssignment.progress ?? 0.0) >= 0 &&
        (courseAssignment.progress ?? 0.0) < 100;
    String? url = courseAssignment.thumbnailUrl;
    String path = 'image.png';
    String skipIcon = 'fast-forward.png';

    debugPrint(resourceType.name);
    switch (resourceType) {
      case ResourceType.FILE:
        path = 'thumbnail_docs';
        break;
      case ResourceType.URL:
        path = 'thumbnail_video';
        break;
      case ResourceType.PAGE:
        path = 'thumbnail_docs';
        break;
      case ResourceType.IMAGE:
        path = 'thumbnail_image';
        break;

      default:
    }
    print("url----$url");
    bool isPDF = courseAssignment.fileExtension == PDF_EXTENSION;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
      // color: Colors.amber,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            color: Colors.transparent,
            height: 85,
            width: double.infinity,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: url != null && url.isNotEmpty
                  ? ValidNetworkImage(
                      url: url,
                      fallbackAssetPath: '$ASSETS_PATH/$path.png',
                    )
                  : Container(
                      color: Colors.red,
                      child: Image.asset(
                        '$ASSETS_PATH/$path.png',
                        fit: BoxFit.cover,
                      ),
                    ),
            ),
          ),
          Positioned(
              bottom: 0,
              left: 0,
              child: _shadowWidgetForIcon(
                  _imageWidget(resourceType, isPDF ? 20 : 25.0, isPDF),
                  30,
                  30)),
          showSkipBtn || isSkipped
              ? Positioned(
                  bottom: 0,
                  right: 0,
                  child: isSkipped
                      ? _skipWidget()
                      : GestureDetector(
                          onTap: () async {
                            _showSkipResourceAlert(
                                context, _courseCubit, courseAssignment);
                          },
                          child: _shadowWidgetForIcon(
                              Image.asset(
                                '$ASSETS_PATH/$skipIcon',
                                height: 16,
                                color: AppTheme.iconEnabledColor,
                              ),
                              30,
                              30),
                        ),
                )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _generalResouceThumbnailWidget(
    BuildContext context,
    Resource resource,
    CourseListCubit _courseCubit,
  ) {
    ResourceType resourceType = resource.resourceType;
    String? url = resource.thumbnailUrl;
    String path = 'image.png';

    debugPrint(resourceType.name);
    switch (resourceType) {
      case ResourceType.FILE:
        path = 'thumbnail_docs';
        break;
      case ResourceType.URL:
        path = 'thumbnail_video';
        break;
      case ResourceType.PAGE:
        path = 'thumbnail_docs';
        break;
      case ResourceType.IMAGE:
        path = 'thumbnail_image';
        break;

      default:
    }
    bool isPDF = resource.fileExtension == PDF_EXTENSION;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
      // color: Colors.amber,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            color: Colors.transparent,
            height: 85,
            width: double.infinity,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: url != null && url.isNotEmpty
                  ? ValidNetworkImage(
                      url: url,
                      fallbackAssetPath: '$ASSETS_PATH/$path.png',
                    )
                  : Container(
                      color: Colors.red,
                      child: Image.asset(
                        '$ASSETS_PATH/$path.png',
                        fit: BoxFit.cover,
                      ),
                    ),
            ),
          ),
          Positioned(
              bottom: 0,
              left: 0,
              child: _shadowWidgetForIcon(
                  _imageWidget(resourceType, isPDF ? 20 : 25.0, isPDF),
                  30,
                  30)),
        ],
      ),
    );
  }

  Widget _skipWidget() {
    return Material(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30),
      ),
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          color: Colors.grey.shade300,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
        alignment: Alignment.center,
        child: Text(
          'Skipped',
          textAlign: TextAlign.center,
          style: LMSFonts.mediumItalicFont(12, AppTheme.primaryTextColorBlack),
        ),
      ),
    );
  }

  Widget _shadowWidgetForIcon(Widget childWidget, double height, double width) {
    return Material(
      elevation: 2,
      shape: const CircleBorder(),
      color: Colors.transparent,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white.withOpacity(0.9),
        ),
        padding: const EdgeInsets.all(2.5),
        child: Align(child: childWidget),
      ),
    );
  }

  Widget _imageWidget(ResourceType resourceType, double height, bool isPDF) {
    String path = 'file_dock.png';
    debugPrint(resourceType.name);
    switch (resourceType) {
      case ResourceType.FILE:
        path = isPDF ? 'pdf.png' : 'file_dock.png';
        break;
      case ResourceType.URL:
        path = 'video_light.png';
        break;
      case ResourceType.PAGE:
        path = 'file_light.png';
        break;
      case ResourceType.IMAGE:
        path = 'gallery.png';
        break;

      default:
    }

    return Image.asset(
      '$ASSETS_PATH/$path',
      height: height,
      color: AppTheme.iconEnabledColor,
    );
  }

  Widget _progressIndicator(CourseAssignments courseAssignment) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top Row: Text and Warning Icon
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Linear Progress Indicator
              Expanded(
                child: LinearProgressIndicatorWidget(
                    progress: (courseAssignment.progress ?? 0) / 100,
                    progressColor: Colors.green,
                    backgroundColor: Colors.grey[300]),
              ),
              const SizedBox(width: 5),
              Text(
                (courseAssignment.progress ?? 0) <= 0 ||
                        (courseAssignment.progress ?? 0).isInfinite ||
                        (courseAssignment.progress ?? 0).isNaN
                    ? '0%'
                    : (courseAssignment.progress ?? 0) >= 100
                        ? '100%'
                        : '${(courseAssignment.progress ?? 0).toStringAsFixed(2)}%',
                style: LMSFonts.mediumFont(
                    12, AppTheme.primaryTextColorBlack.withOpacity(0.7), 1.0),
              ),
            ],
          ),
        ],
      ),
    );
  }

  ///fetch the progress value for any type resource
  Future<ResourceProgress?> _fetchCourseProgress(
      BuildContext context, String instanceId) async {
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

    await _courseDetailsCubit.getVideoResProgress(instanceId);

    CourseDetailsState state = _courseDetailsCubit.state;

    if (state is GetCourseResProgressState) {
      ResourceProgress courseProgress = state.progress;
      return courseProgress;
    } else if (state is GetCourseResProgressFailureState) {
      ResourceProgress courseProgress = ResourceProgress(
          progress: 0.0,
          timeSpent: '',
          instanceId: instanceId,
          markedAsDone: false);

      bool isActiveRouteInWeb = false;

      if (kIsWeb) {
        var beamerRouter = Beamer.of(context);
        isActiveRouteInWeb = beamerRouter.active;
      }

      bool isThereCurrentDialogShowing = kIsWeb
          ? isActiveRouteInWeb
          : ModalRoute.of(context)?.isCurrent != true;
      if (!isThereCurrentDialogShowing) {
        showPGExceptionPopup(context, state.error);
      }
      return courseProgress;
    }
    return null;
  }

  _handleFileImageTypeNavigation(
      BuildContext context,
      CourseAssignments courseAssignment,
      ResourceProgress? courseProgress) async {
    try {
      final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

      _isDataLoading.value = true;

      await _courseDetailsCubit.fetchFileResource(
          courseAssignment.resourceId, courseAssignment.courseModuleId);
      // For Image View

      CourseDetailsState state = _courseDetailsCubit.state;

      if (state is CourseDetailsFileResFetched) {
        if (state.courseFileResource.isNotEmpty) {
          final CourseFileResource _resourceFile =
              state.courseFileResource.first;

          final CheckPointData checkPointData = state.checkPointData;

          // bool isNetworkUrl = await validateImage(_resourceFile.url ?? '');
          // debugPrint(
          //     '----courseAssignment.url= ${courseAssignment.resourceUrl}');

          if (courseAssignment.fileExtension == PPT_EXTENSION ||
              courseAssignment.fileExtension == PPTX_EXTENSION) {
            // PPT
            _resourceFile.progress = courseProgress?.progress ?? 0.0;

            await _handleNavigationToPPTReader(
                context, _resourceFile, checkPointData);
          } else if (courseAssignment.fileExtension == PDF_EXTENSION) {
            print("courseFileResource.url------>${_resourceFile.url}");
            final file = await createFileOfPdfUrl(_resourceFile.url);
            _resourceFile.url = file.path;
            appRouter
                .push(PDFViewRoute(
                    courseFileResource: _resourceFile,
                    checkPoints: [],
                    isFromExamScreen: false,
                    isExamPassed: false,
                    navigateBackTo: NavigationTo.course_details))
                .then((value) => _isDataLoading.value = false);
          } else if (_resourceFile != null &&
              !_resourceFile.url.endsWith(HTML)) {
            if (_resourceFile.url.endsWith(IMAGE_EXTENSIONJPG) ||
                _resourceFile.url.endsWith(IMAGE_EXTENSIONJPEG) ||
                _resourceFile.url.endsWith(IMAGE_EXTENSIONPNG)) {
              // IMAGE
              await _handleNavigationToImageView(
                  context, courseAssignment, courseProgress, _resourceFile);
            } else {
              _handleInvalidFile(context, courseAssignment.resourceId);
            }
          } else {
            _handleInvalidFile(context, courseAssignment.resourceId);
          }
        }
      } else if (state is CourseResInfoFetchError) {
        _isDataLoading.value = false;
        showPGExceptionPopup(context, state.error);
      } else {
        _handleInvalidFile(context, courseAssignment.resourceId);
      }
    } on Exception catch (_) {
      _isDataLoading.value = false;
    }
  }

  _grHandleFileImageTypeNavigation(
    BuildContext context,
    Resource resource,
  ) async {
    try {
      _isDataLoading.value = true;
      final CourseFileResource _resourceFile = CourseFileResource(
          id: resource.resourceId,
          url: resource.externalUrl,
          name: resource.resourceName,
          orgId: "",
          status: "",
          comments: "",
          courseId: courseId,
          createdAt: DateTime.now(),
          pageCount: 0,
          updatedAt: DateTime.now(),
          approvedBy: "",
          description: "",
          approvedDate: "",
          moduleSource: "",
          courseModuleId: "",
          numOfCheckpoints: 0,
          isRandomCheckpoint: false,
          isCheckpointEnabled: false,
          progress: 0.0);

      if (resource.fileExtension == PPT_EXTENSION ||
          resource.fileExtension == PPTX_EXTENSION) {
        // PPT
        _resourceFile.progress = 0.0;

        await _handleNavigationToPPTReader(
            context, _resourceFile, const CheckPointData(checkPoints: []));
      } else if (resource.fileExtension == PDF_EXTENSION) {
        final file = await createFileOfPdfUrl(resource.externalUrl);
        _resourceFile.url = file.path;
        appRouter
            .push(PDFViewRoute(
                courseFileResource: _resourceFile,
                checkPoints: [],
                isFromExamScreen: false,
                isExamPassed: false,
                navigateBackTo: NavigationTo.course_details))
            .then((value) => _isDataLoading.value = false);
      } else if (_resourceFile != null && !_resourceFile.url.endsWith(HTML)) {
        if (_resourceFile.url.endsWith(IMAGE_EXTENSIONJPG) ||
            _resourceFile.url.endsWith(IMAGE_EXTENSIONJPEG) ||
            _resourceFile.url.endsWith(IMAGE_EXTENSIONPNG)) {
          // IMAGE
          await _handleNavigationToImageView(
              context, null, null, _resourceFile);
        } else {
          _handleInvalidFile(context, resource.resourceId);
        }
      } else {
        _handleInvalidFile(context, resource.resourceId);
      }
    } on Exception catch (_) {
      _isDataLoading.value = false;
    }
  }

  _handleInvalidFile(BuildContext context, String resourceId) {
    _isDataLoading.value = false;
    _showAlertDialog(
        context, SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
  }

  _handleNavigationToPPTReader(BuildContext context,
      CourseFileResource _resourceFile, CheckPointData checkPointData) async {
    Future.delayed(const Duration(milliseconds: 500), () {
      _isDataLoading.value = false;
    });
    appRouter
        .push(PPTViewerRoute(
      courseFileResource: _resourceFile,
      checkPoints: checkPointData.checkPoints ?? [],
      isFromExamScreen: false,
      navigateBackTo: NavigationTo.course_details,
    ))
        .then((value) async {
      _isDataLoading.value = true;
      if (value != null) {
        double progress = value as double;
        await _updateDashboardUI(
            context, _resourceFile.id, progress.toString());
      }

      _isDataLoading.value = false;
    });
  }

  _handleNavigationToImageView(
    BuildContext context,
    CourseAssignments? courseAssignment,
    ResourceProgress? courseProgress,
    CourseFileResource _resourceFile,
  ) async {
    String instanceId = courseAssignment?.resourceId ?? "";
    String courseId = courseAssignment?.courseId ?? "";
    int folderResourceLength = courseDetails?.courseAssignments.length ?? 0;
    Future.delayed(const Duration(milliseconds: 500), () {
      _isDataLoading.value = false;
    });

    kIsWeb
        ? Beamer.of(context).beamToNamed('/image-view', data: {
            'imagePath': _resourceFile.url,
            'resourceFile': _resourceFile,
            'instance': instanceId,
            'courseId': courseId,
            'progess': courseProgress,
            'moduleLength': folderResourceLength,
          })
        : appRouter
            .push(ImageViewRoute(
            resourceFile: _resourceFile,
            moduleLength: folderResourceLength,
            resourceProgress: courseProgress,
          ))
            .then((value) async {
            if (value != null) {
              _isDataLoading.value = true;
              Map<String, dynamic> args = value as Map<String, dynamic>;
              _isDataLoading.value = true;

              if (args.containsKey('progress')) {
                await _updateDashboardUI(
                    context, _resourceFile.id, args['progress'].toString());
              }
              _isDataLoading.value = false;
            }
          });
  }

  _handleVideoTypeNavigation(
      BuildContext context,
      CourseAssignments courseAssignment,
      ResourceProgress? courseProgress) async {
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

    await _courseDetailsCubit.fetchVideoResource(
        courseAssignment.resourceId, courseAssignment.courseModuleId);

    CourseDetailsState state = _courseDetailsCubit.state;

    List<CheckPoint> _checkPoints = [];
    if (state is CourseResInfoFetchError) {
      _isDataLoading.value = false;

      showPGExceptionPopup(context, state.error);
    } else if (state is CourseDetailsVideoResFetched) {
      if (state.courseVideoResource.isNotEmpty) {
        final _courseVideoObj = state.courseVideoResource.first;

        _checkPoints.addAll(_courseVideoObj.checkpoints);

        _isDataLoading.value = false;

        kIsWeb
            ? Beamer.of(context).beamToNamed(
                "/video-view",
                data: {
                  "courseVideo": _courseVideoObj,
                  "courseId": courseAssignment.courseId,
                  "progess": courseProgress,
                  "isFromSectionDetails": true,
                  "checkPoints": _checkPoints
                },
              )
            : appRouter
                .push(CourseVideoViewRoute(
                courseVideo: _courseVideoObj,
                courseProgress: courseProgress,
                isFromSectionDetails: true,
                checkPoints: _checkPoints,
                navigateBackTo: NavigationTo.course_details,
              ))
                .then((value) async {
                if (value != null) {
                  Map<String, dynamic> progressMap =
                      value as Map<String, dynamic>;
                  if (progressMap.containsKey('progress')) {
                    await _updateDashboardUI(
                        context,
                        courseAssignment.resourceId,
                        progressMap['progress'].toString());
                  }
                }
              });
      }
    }

    _isDataLoading.value = false;
  }

  _grHandleVideoTypeNavigation(
    BuildContext context,
    Resource resource,
  ) async {
    _isDataLoading.value = false;

    kIsWeb
        ? Beamer.of(context).beamToNamed(
            "/video-view",
            data: {
              "courseVideo": CourseVideoResource(
                  id: "",
                  instanceId: resource.resourceId,
                  name: resource.resourceName,
                  length: "",
                  orgId: "",
                  courseId: "",
                  checkpoints: [],
                  description: "",
                  externalUrl: resource.externalUrl,
                  currentPoint: "",
                  moduleSource: "",
                  courseModuleId: "",
                  numOfCheckpoints: 0,
                  isRandomCheckpoint: false,
                  isCheckpointEnabled: false,
                  alwaysShowCheckpoints: false),
              "courseId": resource.resourceId,
              "progess": 0,
              "isFromSectionDetails": true,
              "checkPoints": []
            },
          )
        : appRouter
            .push(CourseVideoViewRoute(
              courseVideo: CourseVideoResource(
                  id: "",
                  instanceId: resource.resourceId,
                  name: resource.resourceName,
                  length: "",
                  orgId: "",
                  courseId: "",
                  checkpoints: [],
                  description: "",
                  externalUrl: resource.externalUrl,
                  currentPoint: "",
                  moduleSource: "",
                  courseModuleId: "",
                  numOfCheckpoints: 0,
                  isRandomCheckpoint: false,
                  isCheckpointEnabled: false,
                  alwaysShowCheckpoints: false),
              courseProgress: null,
              isFromSectionDetails: true,
              navigateBackTo: NavigationTo.course_details,
            ))
            .then((value) async {});
    // }
    // }

    _isDataLoading.value = false;
  }

  _handlePageTypeNavigation(
      BuildContext context,
      CourseAssignments courseAssignment,
      ResourceProgress? courseProgress) async {
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

    String instanceId = courseAssignment.resourceId;
    String courseId = courseAssignment.courseId;
    int folderResourceLength = courseDetails!.courseAssignments.length;
    Future.delayed(const Duration(milliseconds: 500), () {
      _isDataLoading.value = false;
    });

    await _courseDetailsCubit.fetchPageResource(
        instanceId, courseAssignment.courseModuleId);
    CourseDetailsState state = _courseDetailsCubit.state;

    if (state is PageResourceFetched) {
      CoursePageResource pageContent = state.pageContentData;
      _isDataLoading.value = false;
      kIsWeb
          ? Beamer.of(context).beamToNamed('/study-materials', data: {
              'pageContent': pageContent,
              'instance': instanceId,
              'courseId': courseId,
              'progess': courseProgress,
              'moduleLength': folderResourceLength,
            })
          : appRouter
              .push(StudyMaterialViewRoute(
                  pageContent: pageContent,
                  moduleLength: folderResourceLength,
                  resourceProgress: courseProgress))
              .then((value) async {
              if (value != null) {
                _isDataLoading.value = true;
                Map<String, dynamic> args = value as Map<String, dynamic>;
                if (args.containsKey('progress')) {
                  await _updateDashboardUI(context, courseAssignment.resourceId,
                      args['progress'].toString());
                }
                _isDataLoading.value = false;
              }
            });
    } else if (state is PageResourceError) {
      _isDataLoading.value = false;
      showPGExceptionPopup(context, state.error);
    }
    _isDataLoading.value = false;
  }

  _grHandlePageTypeNavigation(
    BuildContext context,
    Resource resource,
  ) async {
    String instanceId = resource.resourceId;
    String courseId = "";
    int folderResourceLength = _resources.length;
    Future.delayed(const Duration(milliseconds: 500), () {
      _isDataLoading.value = false;
    });

    CoursePageResource pageContent = CoursePageResource(
      id: "",
      name: resource.resourceName,
    );
    _isDataLoading.value = false;
    kIsWeb
        ? Beamer.of(context).beamToNamed('/study-materials', data: {
            'pageContent': pageContent,
            'instance': instanceId,
            'courseId': courseId,
            'progess': 0,
            'moduleLength': folderResourceLength,
          })
        : appRouter.push(StudyMaterialViewRoute(
            pageContent: pageContent,
            moduleLength: folderResourceLength,
            resourceProgress: null));
  }

  _updateDashboardUI(
      BuildContext context, String resourceId, String progress) async {
    _isDataLoading.value = true;
    try {
      if (courseDetails != null) {
        int itemIndex = courseDetails!.courseAssignments
            .indexWhere((element) => element.resourceId == resourceId);
        courseDetails!.courseAssignments[itemIndex].progress =
            double.parse(progress);
        bool isCompleted = double.parse(progress) >= 100.0;
        if (isCompleted) {
          _updateResourceStatus(itemIndex, isCompleted);
        }
        _isDataLoading.value = false;
      }
      didUpdateScreen = true;
    } on Exception catch (e) {
      _isDataLoading.value = false;
    }
    _isDataLoading.value = false;
  }

  _updateResourceStatus(int itemIndex, bool enableResource) {
    if (courseDetails == null) {
      return;
    }
    courseDetails!.courseAssignments[itemIndex].progress = 100.0;

    final nextInprogressItem = courseDetails!.courseAssignments.indexWhere(
        (element) =>
            element.resourceType != ResourceType.QUIZ &&
            !element.isEnabled &&
            !element.isSkipped);
    final hasInProgressItems = courseDetails!.courseAssignments.any(
      (e) =>
          (e.isEnabled && e.progress != null && e.progress! < 100) &&
          !e.isSkipped,
    );
    if (nextInprogressItem != -1) {
      courseDetails!.courseAssignments[nextInprogressItem].isEnabled =
          !hasInProgressItems;
    }
    didUpdateScreen = true;
  }

  _updateResourceStatusAfterSkip(int itemIndex) {
    if (courseDetails == null) {
      return;
    }
    courseDetails!.courseAssignments[itemIndex].isSkipped = true;

    final nextInprogressItem = courseDetails!.courseAssignments.indexWhere(
        (element) =>
            element.resourceType != ResourceType.QUIZ &&
            !element.isEnabled &&
            !element.isSkipped);
    final hasInProgressItems = courseDetails!.courseAssignments.any(
      (e) =>
          (e.isEnabled && e.progress != null && e.progress! < 100) &&
          !e.isSkipped,
    );
    if (nextInprogressItem != -1) {
      courseDetails!.courseAssignments[nextInprogressItem].isEnabled =
          !hasInProgressItems;
    }
    didUpdateScreen = true;
  }

  _showSkipResourceAlert(BuildContext context, CourseListCubit _courseCubit,
      CourseAssignments courseAssignment) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return MultiActionDialogue(
          title: 'Skip',
          content: 'Do you want to skip this resource?',
          alertType: AlertType.confirm,
          onContinueTap: () async {
            try {
              final _userActivityLog = UserActivityLog.instance;
              if (Navigator.canPop(dialogContext)) {
                kIsWeb
                    ? Beamer.of(dialogContext).beamBack()
                    : Navigator.pop(dialogContext);
              }

              _isDataLoading.value = true;
              await _courseCubit.setSkippedResource(courseAssignment);

              CourseListState state = _courseCubit.state;
              if (state is SkipResourceSuccess) {
                // skipped resource

                int index = courseDetails!.courseAssignments.indexWhere(
                    (element) =>
                        element.resourceId == courseAssignment.resourceId);
                _updateResourceStatusAfterSkip(index);
              }

              ///
              /// activity log update
              ///
              bool isSuccess = state is SkipResourceSuccess;
              String error = state is SkipResourceFailure ? state.error : '';
              await _userActivityLog.setResSkipActivityLog(
                  context: context,
                  screen: 'Course Details View',
                  resourceType: courseAssignment.resourceType.name,
                  id: courseAssignment.resourceId,
                  result: isSuccess ? 'success' : 'error',
                  responseStatus:
                      isSuccess ? 'skipped' : 'failed with error: $error');
            } on Exception catch (e) {
              _isDataLoading.value = false;
            }

            ///
            _isDataLoading.value = false;
          },
          onCancelTap: () =>
              kIsWeb ? Beamer.of(context).beamBack() : Navigator.pop(context),
          cancelBtnText: 'Cancel',
          confirmBtnText: 'Skip',
        );
      },
    );
  }

  void _showAlertDialog(BuildContext context, String msg) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: msg,
          iconWidget: const Icon(Icons.error, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  showPGExceptionPopup(BuildContext context, String msg) {
    bool isThereCurrentDialogShowing = false;
    String routeNameInApp = '';
    String routeNameInWeb = '';

    if (kIsWeb) {
      final beamerRouter = Beamer.of(context);
      isThereCurrentDialogShowing = !beamerRouter.active;
      routeNameInWeb = beamerRouter.configuration.uri.path;
    } else {
      isThereCurrentDialogShowing = ModalRoute.of(context)?.isCurrent != true;
      routeNameInApp = appRouter.current.route.name;
    }
    if (isThereCurrentDialogShowing) {
      appRouter.popUntil((route) => route.settings.name == routeNameInApp);
    }
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              return;
            }
          },
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            alertType: AlertType.error,
            onContinueTap: () =>
                kIsWeb ? Beamer.of(context).beamBack() : Navigator.pop(context),
            onCancelTap: () {},
            cancelBtnText: '',
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
          ),
        );
      },
    );
  }

  Widget _subjectsView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails _courseDetails,
      CourseListState state,
      CourseListCubit _courseCubit) {
    int courseModulesLength = _courseDetails.courseModule.length;
    final subjectCount =
        BlocProvider.of<CourseDashboardCubit>(context).subjectsCount;

    return courseModulesLength > 0
        ? ValueListenableBuilder(
            valueListenable: _seeAllSubjects,
            builder: (context, value, _) {
              return Container(
                // margin: const EdgeInsets.only(top: _verticalGapBwSections - 5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _titleWidget(constraints,
                            SLStrings.getTranslatedString(KEY_SUBJECTS)),
                        Visibility(
                          visible: courseModulesLength > subjectCount,
                          child: InkWell(
                            onTap: () {
                              _seeAllSubjects.value = !_seeAllSubjects.value;
                            },
                            child: Container(
                              padding: const EdgeInsets.only(right: 16),
                              color: Colors.white,
                              child: Row(
                                children: [
                                  value
                                      ? Text(
                                          SLStrings.getTranslatedString(
                                              KEY_SEE_LESS),
                                          style: LMSFonts.semiBoldFont(
                                              12,
                                              appDynamicTheme
                                                      ?.navbarTextColor ??
                                                  AppTheme.primaryAppColor),
                                        )
                                      : Text(
                                          SLStrings.getTranslatedString(
                                              KEY_SEE_ALL),
                                          style: LMSFonts.semiBoldFont(
                                              12,
                                              appDynamicTheme
                                                      ?.navbarTextColor ??
                                                  AppTheme.primaryAppColor)),
                                  Image.asset(
                                    '$ASSETS_PATH/see_all_btn.png',
                                    width: 20,
                                    height: 10,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: _verticalSpaceAfterTitle),
                    sampleJson['subjects_scroll_direction'] ==
                            GridScrollDirection.vertical
                        ? _verticalGridView(
                            context,
                            constraints,
                            _courseDetails,
                            courseModulesLength,
                            CourseGridItem.subjects,
                            state,
                            _courseCubit)
                        : _horizontalSubjectsView(
                            context,
                            constraints,
                            _courseDetails,
                            _courseCubit,
                            courseModulesLength,
                            CourseGridItem.subjects,
                            _seeAllSubjects),
                  ],
                ),
              );
            })
        : Container();
  }

  Widget _horizontalSubjectsView(
    BuildContext context,
    BoxConstraints constraints,
    CourseDetails? _courseDetails,
    CourseListCubit _courseCubit,
    int itemCount,
    CourseGridItem gridItem,
    ValueNotifier<bool> _seeAllSubjects,
  ) {
    List<CourseModule?> _subjects = _courseDetails?.courseModule ?? [];
    final subjectCount =
        BlocProvider.of<CourseDashboardCubit>(context).subjectsCount;

    return ValueListenableBuilder<bool>(
      valueListenable: _seeAllSubjects,
      builder: (context, showAll, _) {
        final displayedSubjects =
            showAll ? _subjects : _subjects.take(subjectCount).toList();

        final List<Widget> subjectSliders = displayedSubjects
            .map((item) => item != null
                ? _subjectNames(
                    context,
                    item.moduleName,
                    constraints,
                    item,
                    _subjects.indexOf(item),
                    _subjects.length,
                  )
                : Container())
            .toList();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: _horizontalMargin),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: subjectSliders,
            ),
          ),
        );
      },
    );
  }

  Widget _horizontalGridView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails? _courseDetails,
      int courseModulesLength,
      CourseGridItem gridItem,
      CourseListCubit _courseCubit) {
    switch (gridItem) {
      case CourseGridItem.videos:
        courseModulesLength =
            courseModulesLength > sampleJson['videos_crossaxis_count']
                ? sampleJson['videos_crossaxis_count']
                : courseModulesLength;
        break;
      case CourseGridItem.subjects:
        courseModulesLength =
            courseModulesLength > sampleJson['subjects_crossaxis_count']
                ? sampleJson['subjects_crossaxis_count']
                : courseModulesLength;
        break;
      case CourseGridItem.exams:
        courseModulesLength =
            courseModulesLength > sampleJson['exams_crossaxis_count']
                ? sampleJson['exams_crossaxis_count']
                : courseModulesLength;
        break;
      default:
    }

    final gridViewHeight = constraints.maxHeight * 0.16;
    return Container(
      height: courseModulesLength > 2 ? gridViewHeight * 2 : gridViewHeight,
      color: Colors.transparent,
      child: GridView.builder(
          scrollDirection: Axis.horizontal,
          gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: 200,
            childAspectRatio: 0.969 / 1.4,
            mainAxisSpacing: 20,
            crossAxisSpacing: 20,
          ),
          itemCount: courseModulesLength,
          itemBuilder: (BuildContext ctx, index) {
            String moduleName = gridItem == CourseGridItem.subjects
                ? _courseDetails != null &&
                        _courseDetails.courseModule[index] != null
                    ? _courseDetails.courseModule[index]!.moduleName
                    : ''
                : gridItem == CourseGridItem.exams
                    ? _courseDetails != null &&
                            _courseDetails.exams[index] != null
                        ? _courseDetails.exams[index]!.moduleName
                        : ''
                    : '';
            return
                /*gridItem == CourseGridItem.videos
                ? _videoItem(
                    context,
                    constraints,
                    _courseDetails?.courseVideos[index],
                    _courseCubit,
                    index,
                    _courseDetails?.courseVideos.length ?? 0)
                : gridItem == CourseGridItem.subjects
                    ? _subjectNames(
                        context,
                        moduleName,
                        constraints,
                        _courseDetails?.courseModule[index],
                        index,
                        _courseDetails?.courseModule.length ?? 0)
                    :*/
                gridItem == CourseGridItem.exams
                    ? _examContainer(context, _courseDetails?.exams ?? [],
                        constraints) //_examTitles(context, constraints,_courseDetails?.exams[index], moduleName, index)
                    : Container();
          }),
    );
  }

  Widget _subjectNames(
      BuildContext context,
      String text,
      BoxConstraints constraints,
      CourseModule? courseModule,
      int index,
      int totalCount) {
    double screenWidth = MediaQuery.sizeOf(context).width;
    double _subjectItemWidth = totalCount == 1 || totalCount > 2
        ? (screenWidth / 2) - (_horizontalMargin * 3)
        : (screenWidth - (_horizontalMargin * 3)) / 2;

    return InkWell(
      onTap: () async {
        if (courseModule != null) {
          await _fetchSelectedSubjectDetails(
              context, courseModule.moduleId, courseModule.moduleName);
        } else {
          _showAlertDialog(context,
              SLStrings.getTranslatedString(KEY_SUBJECT_DETAILS_NOT_FOUND));
        }
      },
      child: Container(
        height: _subjectItemHeight,
        margin: EdgeInsets.only(
            right: index == totalCount - 1 ? 0 : _horizontalMargin),
        width: constraints.maxWidth < 720 ? _subjectItemWidth : 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          gradient: const LinearGradient(
            colors: [
              AppTheme.subjectsLinearGradientBottom,
              AppTheme.subjectsLinearGradientTop
            ],
            begin: Alignment.bottomRight,
            end: Alignment.topLeft,
          ),
        ),
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              '$ASSETS_PATH/subjects_general.png',
              height: 40,
            ),
            const SizedBox(height: 11),
            Flexible(
              child: Text(
                text,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: LMSFonts.mediumFont(14, AppTheme.whiteTextColor, 1.0),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _examsView(
      BuildContext context,
      BoxConstraints constraints,
      CourseDetails _courseDetails,
      CourseListState state,
      CourseListCubit _courseCubit) {
    int examsLength = _courseDetails != null ? _courseDetails.exams.length : 0;
    return examsLength > 0
        ? Container(
            margin: const EdgeInsets.only(top: _verticalGapBwSections),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _titleWidget(
                    constraints, SLStrings.getTranslatedString(KEY_EXAMS)),
                const SizedBox(height: _verticalSpaceAfterTitle),
                sampleJson['exams_scroll_direction'] ==
                        GridScrollDirection.vertical
                    ? _verticalExamsView(
                        context,
                        constraints,
                        _courseDetails,
                        _courseCubit,
                        examsLength,
                      )
                    : _horizontalGridView(context, constraints, _courseDetails,
                        examsLength, CourseGridItem.exams, _courseCubit),
              ],
            ),
          )
        : Container();
  }

  _verticalExamsView(
    BuildContext context,
    BoxConstraints constraints,
    CourseDetails? _courseDetails,
    CourseListCubit _courseCubit,
    int _examsLength,
  ) {
    List<CourseExam?> _exams = _courseDetails?.exams ?? [];
    // final List<Widget> examsSliders = _exams
    //     .map((item) => item != null
    //         ? _examTitles(context, constraints, item, item.moduleName,
    //             _exams.indexOf(item))
    //         : Container())
    //     .toList();

    return SingleChildScrollView(
      child: Container(
          margin: const EdgeInsets.symmetric(
            horizontal: _horizontalMargin,
          ),
          // child: Column(
          //   children: examsSliders,
          // ),
          child: _examContainer(context, _exams, constraints)),
    );
  }

  Widget _examContainer(BuildContext context, List<CourseExam?> _exams,
      BoxConstraints constraints) {
    double _itemWidth =
        (MediaQuery.sizeOf(context).width / 2) - _horizontalMargin * 3;
    return Container(
      width: constraints.maxWidth < 720 ? _itemWidth : 200,
      margin: const EdgeInsets.only(bottom: _verticalSpaceAfterTitle * 2),
      decoration: BoxDecoration(
        // color: AppTheme.secondaryAppColor,
        borderRadius: BorderRadius.circular(10),
      ),
      alignment: Alignment.center,
      child: TextButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          backgroundColor: AppTheme.secondaryAppColor,
          foregroundColor: AppTheme.secondaryAppColor,
        ),
        onPressed: () {
          if (_exams.isNotEmpty) {
            kIsWeb
                ? Beamer.of(context)
                    .beamToNamed('/exam-list-tab', data: {'tabIndex': 0})
                : appRouter.push(ExamListViewRoute(tabIndex: 0));
          } else {
            _showAlertDialog(context,
                SLStrings.getTranslatedString(KEY_MODULE_DETAILS_NOT_FOUND));
          }
        },
        child: Container(
          height: _subjectItemHeight - 16,
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                '$ASSETS_PATH/question_light.png',
                color: AppTheme.whiteTextColor,
                height: 40,
              ),
              const SizedBox(height: 5),
              Flexible(
                child: Text(
                  SLStrings.getTranslatedString(KEY_NEW_EXAMS_TAB),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: LMSFonts.semiBoldFont(14, AppTheme.whiteTextColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // fetch selected subject details
  // to check if the user has permssion to call api
  // to prevent showing nor permission popup in empty subject details screen
  // to prevent unwanted navigation to the screen if can't fetch data
  _fetchSelectedSubjectDetails(
      BuildContext context, String sectionId, String moduleName) async {
    final _sectionDetailsCubit = BlocProvider.of<SectionDetailsCubit>(context);
    final _courseCubit = BlocProvider.of<CourseListCubit>(context);
    SectionDetails? sectionDetailsData;

    _courseCubit.setLoading(true);

    await _sectionDetailsCubit.fetchSectionDetails(sectionId);
    SectionDetailsState state = _sectionDetailsCubit.state;
    if (state is SectionDetailsError) {
      _courseCubit.setLoading(false);
      showPGExceptionPopup(context, state.error);
    } else {
      if (state is SectionDetailsFetched) {
        sectionDetailsData = SectionDetails(
          resources: state.sectionDetailsData?.resources ?? [],
          modules: state.sectionDetailsData?.modules ?? [],
          folders: state.sectionDetailsData?.folders ?? [],
        );
        sectionDetailsData = state.sectionDetailsData;
      }
      _courseCubit.setLoading(false);

      // appRouter.push(
      //     SubjectsDetailedTabViewRoute(sectionDetailsData: sectionDetailsData));

      appRouter.push(SectionDetailsViewRoute(
          sectionId: sectionId,
          title: moduleName,
          showFolderResources: false,
          folderIndex: 0,
          sectionDetailsData: sectionDetailsData,
        ));
      // kIsWeb
      //     ? Beamer.of(context).beamToNamed(
      //         '/section-details',
      //         data: {
      //           "sectionId": sectionId,    
      //           'title': moduleName,
      //           'showFolders': false,
      //           'folderIndex': 0,
      //         },
      //         beamBackOnPop: true,
      //       )
      //     : appRouter.push(SectionDetailsViewRoute(
      //         sectionId: sectionId,
      //         title: moduleName,
      //         showFolderResources: false,
      //         folderIndex: 0,
      //         sectionDetailsData: sectionDetailsData,
      //       ));
    }
  }
}
