import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';

part 'settings_state.dart';

class SettingsCubit extends Cubit<SettingsState> {
  SettingsCubit() : super(SettingsInitial());

void toggleNotification(bool value) {
  if(value==true){
    emit(SettingstoggleNotification(toggleVlaue: true));}else{
      emit(SettingstoggleNotification(toggleVlaue: false));
    }
  }


}
