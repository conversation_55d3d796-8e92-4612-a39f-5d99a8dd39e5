import 'package:flutter/material.dart';

class ButtonWidget extends StatelessWidget {
  @required
  final Color? color;
  final Color? textColor;
  final double elevation;
  final OutlinedBorder? shape;
  @required
  final Widget child;
  @required
  final VoidCallback? onPressed;
  final Size? minimumSize;
  final double? horizontalPadding;
  final double? verticalPadding;

  const ButtonWidget(
      {super.key,
      this.color,
      this.textColor,
      this.elevation = 0.0,
      this.shape,
      this.minimumSize,
      required this.child,
      required this.onPressed,
      this.horizontalPadding = 12.0,
      this.verticalPadding = 5.0});

  @override
  Widget build(BuildContext context) {
    Size screenSize = MediaQuery.sizeOf(context);
    return ElevatedButton(
      child: child,
      style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding ?? 12.0,
              vertical: verticalPadding ?? 5),
          alignment: Alignment.center,
          elevation: elevation,
          backgroundColor: color,
          foregroundColor: textColor,
          minimumSize: minimumSize ?? Size(screenSize.width, 50),
          shape: shape),
      onPressed: onPressed,
    );
  }
}
