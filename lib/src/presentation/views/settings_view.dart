import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/themes/app_theme.dart';
import '/src/presentation/cubits/settings/settings_cubit.dart';
import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';

import '../../config/themes/theme_colors.dart';
import '../widgets/app_bar.dart';

@RoutePage()
// ignore: must_be_immutable
class SettingsViewScreen extends HookWidget {
  SettingsViewScreen({Key? key}) : super(key: key);
  bool toggle = false;
  @override
  Widget build(BuildContext context) {
    final settingsCubit = BlocProvider.of<SettingsCubit>(context);

    return Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: SETTINGS_APPBAR_TITLE,
              trailingWidget: Container(),
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {
                kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop();
              },
            )),
        body: BlocListener<SettingsCubit, SettingsState>(
            listener: (context, state) async {
          if (state is SettingstoggleNotification) {
            toggle = state.toggleVlaue;
          }
        }, child: BlocBuilder<SettingsCubit, SettingsState>(
                builder: (context, state) {
          return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              padding: const EdgeInsets.only(left: 15, right: 15),
              child: ListView(
                children: <Widget>[
                  InkWell(
                      onTap: () {},
                      child: notificationWidget(
                          Icons.notifications, NOTIFICATION, settingsCubit)),
                  _divider(),
                  InkWell(
                      onTap: () {},
                      child: listItemWidget(
                          Icons.privacy_tip_sharp, PRIVACY_POLICY)),
                  _divider(),
                  InkWell(
                      onTap: () {},
                      child: listItemWidget(Icons.person, TERMS_CONDITION)),
                  _divider(),
                  InkWell(
                      onTap: () {},
                      child: listItemWidget(Icons.share, SHARE_FRIENDS)),
                  _divider(),
                  InkWell(
                      onTap: () {},
                      child: listItemWidget(Icons.star, RATE_APP)),
                  _divider(),
                ],
              ));
        })));
  }

  Widget listItemWidget(IconData? icon, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      child: Row(
        children: <Widget>[
          Icon(icon),
          const SizedBox(width: 16.0),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 18),
            ),
          ),
          const Icon(Icons.arrow_forward_ios),
        ],
      ),
    );
  }

  Widget _divider() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, bottom: 20),
      color: LmsColors.black.withOpacity(0.1),
      height: 0.8,
      //margin: const EdgeInsets.only(bottom: 20),
    );
  }

  Widget notificationWidget(
    IconData? icon,
    String title,
    SettingsCubit settingsCubit,
  ) {
    toggle = settingsCubit.state.toggleVlaue;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      child: Row(
        children: <Widget>[
          Icon(icon),
          const SizedBox(width: 16.0),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 18),
            ),
          ),
          Switch(
            onChanged: (value) {
              settingsCubit.toggleNotification(value);
            },
            value: toggle,
            activeColor: AppTheme.primaryBlue,
            inactiveTrackColor: AppTheme.iconColor,
          )
        ],
      ),
    );
  }
}
