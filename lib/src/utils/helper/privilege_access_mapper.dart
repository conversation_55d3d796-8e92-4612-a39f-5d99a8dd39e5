// ignore_for_file: constant_identifier_names

import 'dart:convert';

import 'package:flutter/material.dart';

import '/src/domain/models/privilege_access/privilege_access.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../config/app_config/preference_config.dart';

class PrivilegeAccessMapper {
  static Future<bool> checkPrivilegeAccessFor(String screen,
      {required String accessRequiredFeature}) async {
    try {
      SharedPreferences _pref = await SharedPreferences.getInstance();

      String _prefObj = _pref.getString(PREF_KEY_PREVILEGE_ACCESS_LIST) ?? '';
      Map<String, dynamic> _prefMap =
          _prefObj.isNotEmpty ? jsonDecode(_prefObj) : {};

      PrivilegeAccess _privilegeAccessList = PrivilegeAccess.fromJson(_prefMap);
      List<RolePrivilege?> _rolePrivilegeList =
          _privilegeAccessList.rolePrivileges;

      for (var rolePrivilege in _rolePrivilegeList) {
        if (rolePrivilege != null &&
            rolePrivilege.screen != null &&
            rolePrivilege.screen!
                .toLowerCase()
                .contains(screen.toLowerCase())) {
          // same screen
          // need to check available actions
          List<String> actionKeys = rolePrivilege.actions.keys.toList();
          for (var element in actionKeys) {
            if (element.contains(accessRequiredFeature) &&
                rolePrivilege.actions[element] == true) {
              return true;
            }
          }
        }
      }
    } on Exception catch (e) {
      debugPrint('[PrivilegeAccessMapper][Exception]: ${e.toString()}');
    }
    return false;
  }
}

class PrivilegeAccessConsts {
  // screens
  static const SCREEN_ACTIVITY_LIST = 'Activity_List';
  static const SCREEN_CHECKPOINT = 'Checkpoint';
  static const SCREEN_COURSE = 'Course';
  static const SCREEN_COURSE_RESOURCE = 'Course_Resource';
  static const SCREEN_CURRENT_AFFAIRS = 'Current_Affairs';
  static const SCREEN_DASHBOARD = 'Dashboard';
  static const SCREEN_ENROLLMENTS = 'Enrollments';
  static const SCREEN_EVALUATION = 'Evaluation';
  static const SCREEN_EXAM = 'Exam';
  static const SCREEN_GROUP = 'Group';
  static const SCREEN_LOGIN = 'Login';
  static const SCREEN_NOTIFICATION = 'Notification';
  static const SCREEN_QUESTION_BANK = 'Question_Bank';
  static const SCREEN_ROLE = 'Role';
  static const SCREEN_SETTINGS = 'Settings';
  static const SCREEN_TOPIC = 'Topic';
  static const SCREEN_USER = 'User';
  static const SCREEN_SUBSCRIPTION = 'Subscription_Plans';

  // actions
  static const ACTION_GET_ACTIVITY_LIST = 'GET_ACTIVITY_LIST';
  static const ACTION_GET_CHECK_POINT = 'GET_CHECKPOINT';
  static const ACTION_CREATE_CHECK_POINT = 'CREATE_CHECKPOINT';
  static const ACTION_DELETE_CHECK_POINT = 'DELETE_CHECKPOINT';
  static const ACTION_CREATE_CHECK_POINT_SESSION = 'CREATE_CHECKPOINT_SESSION';
  static const ACTION_GET_COURSE_DETAILS = 'GET_COURSE';
  static const ACTION_GET_COURSE_LIST = 'GET_COURSE_LIST';
  static const ACTION_ADD_COMMENT = 'ADD_COMMENT';
  static const ACTION_GET_COMMENT = 'GET_COMMENT';
  static const ACTION_GET_RESOURCE = 'GET_RESOURCE';
  static const ACTION_GET_SECTION_DETAILS = 'GET_RESOURCE_LIST';
  static const ACTION_GET_COURSE_PROGRESS = 'GET_COURSE_PROGRESS';
  static const ACTION_UPDATE_COURSE_PROGRESS = 'UPDATE_COURSE_PROGRESS';
  static const ACTION_GET_CURRENT_AFFAIR_lIST = 'GET_CURRENT_AFFAIR_lIST';
  static const ACTION_GET_ENROLLMENT_LIST = 'GET_ENROLLMENT_LIST';
  static const ACTION_GET_ATTENDED_EXAM_LIST = 'GET_ATTENDED_EXAM_LIST';
  static const ACTION_ADD_EXAM = 'ADD_EXAM';
  static const ACTION_UPDATE_EXAM = 'UPDATE_EXAM';
  static const ACTION_GET_EXAM_LIST = 'GET_EXAM_LIST';
  static const ACTION_ADD_QUESTION_TO_EXAM = 'ADD_QUESTION_TO_EXAM';
  static const ACTION_GET_QUIZ_QUESTIONS = 'GET_QUIZ_QUESTIONS';
  static const ACTION_DEVICE_INFO = 'DEVICE_INFO';
  static const ACTION_PUSH_NOTIFICATION = 'PUSH_NOTIFICATION';
  static const ACTION_PUBLISH_NOTIFICATION = 'PUBLISH_NOTIFICATION';
  static const ACTION_GET_NOTIFICATION_LIST = 'GET_NOTIFICATION_LIST';
  static const ACTION_GET_PRIVILEGE = 'GET_PRIVILEGE';
  static const ACTION_GET_SETTINGS_LIST = 'GET_SETTINGS_LIST';
  static const ACTION_ADD_SETTINGS = 'ADD_SETTINGS';
  static const ACTION_GET_CATEGORY_LIST = 'GET_CATEGORY_LIST';
  static const ACTION_GET_USER_LIST = 'GET_USER_LIST';
  static const ACTION_UPDATE_USER_PROFILE = 'UPDATE_USER_PROFILE';
  static const ACTION_FETCH_QUIZ_RANK_LIST = 'PUBLISH_RESULT';
  // static const ACTION_GET_SUBSCRIPTION_LIST = 'GET_SUBSCRIPTION_LIST';
  static const ACTION_USER_SUBSCRIPTION_LIST = 'USER_SUBSCRIPTION_LIST';
  static const ACTION_USER_SUBSCRIPTION_PURCHASE_STATUS =
      'USER_SUBSCRIPTION_PURCHASE_STATUS';
  static const ACTION_UPDATE_SUBSCRIPTION = 'UPDATE_SUBSCRIPTION';
}
