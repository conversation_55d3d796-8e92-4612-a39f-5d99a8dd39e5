import 'package:get/utils.dart';
import 'package:supabase/supabase.dart';

import '../../../domain/models/course_dashboard/course_assignments.dart';
import '../../../domain/models/course_dashboard/course_page_resource.dart';
import '../../../domain/models/course_dashboard/course_video_resource.dart';
import '../../../domain/models/course_dashboard/userCourseProgress.dart';
import '../../../domain/models/course_progress.dart';
import '../../../domain/repositories/course_details_repository.dart';
import 'package:flutter/material.dart';

import '../../../domain/models/check_point_data/check_point_data.dart';
import '../../../domain/models/course_dashboard/course_details_dashboard_obj.dart';
import '../../../domain/models/course_dashboard/course_file_resource.dart';
import '../../../utils/constants/strings.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../utils/helper/app_date_formatter.dart';

part 'course_details_state.dart';

class CourseDetailsCubit extends Cubit<CourseDetailsState> {
  CourseDetailsCubit(this._courseDetailsRepository)
      : super(CourseDetailsDashboardInfoInitial());

  final CourseDetailsRepository _courseDetailsRepository;
  final AppDateFormatter _appDateFormatter = AppDateFormatter.instance;

  CheckPointData? checkPointData;
  CourseVideoResource? courseVideoObj;
  CourseFileResource? courseFileobj;

  fetchCourseResourcesInfo(String courseId) async {
    if (ORG_ID.isEmpty || USER_ID.isEmpty || courseId.isEmpty) {
      return;
    }
    try {
      int _totalAchievements = 0;
      int _completedCourseCount = 0;
      int _resourceCount = 0;
      double _totalProgress = 0.0;
      double _totalMarks = 0.0;

      String _totalTimeSpent = '0 hrs 0 min 0 sec';
      Duration _timeSpentDuration = const Duration();

      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "user_id": USER_ID,
        "course_id": courseId
      };

      print(jsonReq);
      final courseAssignments =
          await _courseDetailsRepository.fetchCourseAssignments(jsonReq);

      final hasInProgressItems = courseAssignments.any(
        (e) => e.isEnabled && e.progress != null && e.progress! < 100,
      );

      final firstInProgressItem = courseAssignments.firstWhereOrNull(
        (e) => e.resourceType != ResourceType.QUIZ && !e.isEnabled,
      );
      final firstInProgressIndex = firstInProgressItem != null
          ? courseAssignments.indexOf(firstInProgressItem)
          : -1;

      if (firstInProgressIndex != -1) {
        courseAssignments[firstInProgressIndex].isEnabled = !hasInProgressItems;
      }

      final courseWiseUserStats =
          await _courseDetailsRepository.fetchCoursewiseStats(jsonReq);
      courseWiseUserStats
          .sort((a, b) => a.resourceName.compareTo(b.resourceName));

      final userCourseProgress =
          await _courseDetailsRepository.fetchGraphicalProgress(jsonReq);

      final examPerformanceCatWise =
          await _courseDetailsRepository.fetchExamPerformancecatWise(jsonReq);

      _resourceCount = courseWiseUserStats.length;

      for (var item in courseWiseUserStats) {
        _totalProgress += item.progress;
        _totalMarks += item.totalMarks;
        _totalAchievements += item.achievements;
        if (item.progress == 100.00) {
          _completedCourseCount++;
        }

        Duration duration =
            _appDateFormatter.formatStringToDuration(item.timeSpent);
        _timeSpentDuration =
            _appDateFormatter.addDurationInto(_timeSpentDuration, duration);
      }
      _totalProgress = (_totalProgress / _resourceCount);
      _totalProgress = _totalProgress.isNaN ? 0.0 : _totalProgress;

      _totalTimeSpent =
          _appDateFormatter.formatDurationToString(_timeSpentDuration);

      CourseDetailsDashboardObj courseDetailsDashboard =
          CourseDetailsDashboardObj(
              courseAssignments: courseAssignments,
              courseWiseUserStats: courseWiseUserStats,
              userCourseProgress: userCourseProgress,
              examPerformanceCatwise: examPerformanceCatWise,
              totalProgress: _totalProgress,
              totalTimeSpent: _totalTimeSpent,
              totalMarks: _totalMarks,
              totalAchievements: _totalAchievements);

      emit(CourseDetailsDashboardInfoFetched(
          courseDetailsDashboardData: courseDetailsDashboard));

      emit(CourseDetailsDashboardInfoFetched(
          courseDetailsDashboardData: courseDetailsDashboard));
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseDetailsCubit][fetchCourseResourcesInfo] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(CourseDetailsDashboardInfoError(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(e.toString());
      emit(CourseDetailsDashboardInfoError(error: e.toString()));
    }
  }

  /// Fetch all resources's progress as a list.
  /// No API to fetch file type resource's progress seperatly
  // fetchResourcesProgress(String courseId) async {
  //   try {
  //     Map<String, dynamic> jsonReq = {
  //       "org_id": ORG_ID,
  //       "user_id": USER_ID,
  //       "course_id": courseId
  //     };

  //     final userCourseProgress =
  //         await _courseDetailsRepository.fetchGraphicalProgress(jsonReq);
  //     final _courseAssignments =
  //         await _courseDetailsRepository.fetchCourseAssignments(jsonReq);

  //     final firstInprogressItem = _courseAssignments.indexWhere((element) =>
  //         element.resourceType != ResourceType.QUIZ &&
  //         element.progress == 100.0);
  //     if (firstInprogressItem != -1) {
  //       _courseAssignments[firstInprogressItem].isEnabled = true;
  //     }

  //     emit(CourseDetailsDashboardProgressFetched(
  //         userCourseProgress: userCourseProgress,
  //         courseAssignments: _courseAssignments));
  //   } on PostgrestException catch (e) {
  //     debugPrint(
  //         "[PostgrestException][CourseDetailsCubit][fetchResourcesProgress] :${e.toString()}");
  //     String errorMsg = e.details.toString();
  //     emit(CourseDetailsDashboardInfoError(error: errorMsg));
  //   } on Exception catch (e) {
  //     debugPrint(e.toString());
  //     emit(CourseDetailsDashboardInfoError(error: e.toString()));
  //   }
  // }

  fetchVideoResource(String resId, String moduleId) async {
    if (ORG_ID.isEmpty || resId.isEmpty || moduleId.isEmpty) {
      return;
    }
    try {
      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "url_id": resId,
        "course_module_id": moduleId
      };
      final courseVideoResInfo =
          await _courseDetailsRepository.fetchCourseVideoResource(jsonReq);
      if (courseVideoResInfo.isNotEmpty) {
        if (courseVideoResInfo.first.isCheckpointEnabled) {
          Map<String, dynamic> jsonReqBody = {
            "course_module_id": moduleId,
            "org_id": ORG_ID
          };
          checkPointData = await _courseDetailsRepository
              .fetchVideoCheckPointInfo(jsonReqBody);
          courseVideoResInfo.first.checkpoints =
              checkPointData?.checkPoints ?? [];
          print(courseVideoResInfo);
        }
        if (courseVideoResInfo.isNotEmpty) {
          courseVideoObj = courseVideoResInfo.first;
        }
        emit(CourseDetailsVideoResFetched(courseVideoResInfo,
            courseDetailsDashboardData: null));
      }
    } on Exception catch (e) {
      debugPrint(
          "[PostgrestException][CourseDetailsCubit][fetchCourseVideoRes] :$e");
      emit(CourseResInfoFetchError(e.toString(),
          courseDetailsDashboardData: null));
    }
  }

  fetchFileResource(String resId, String moduleId) async {
    if (ORG_ID.isEmpty || resId.isEmpty || moduleId.isEmpty) {
      return;
    }
    try {
      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "file_id": resId,
        "course_module_id": moduleId
      };
      final courseFileResInfo =
          await _courseDetailsRepository.fetchCourseFileResource(jsonReq);

      if (courseFileResInfo.isNotEmpty) {
        courseFileobj = courseFileResInfo.first;
      }

      Map<String, dynamic> jsonReqBody = {
        "course_module_id": moduleId,
        "org_id": ORG_ID
      };
      checkPointData =
          await _courseDetailsRepository.fetchVideoCheckPointInfo(jsonReqBody);

      print(courseFileResInfo);

      emit(CourseDetailsFileResFetched(courseFileResInfo, checkPointData!,
          courseDetailsDashboardData: null));
    } on Exception catch (e) {
      debugPrint(
          "[PostgrestException][CourseDetailsCubit][fetchFileResource] :$e");
      emit(CourseResInfoFetchError(e.toString(),
          courseDetailsDashboardData: null));
    }
  }

  fetchPageResource(String resId, String moduleId) async {
    if (ORG_ID.isEmpty || resId.isEmpty || moduleId.isEmpty) {
      return;
    }
    try {
      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "page_id": resId,
        "course_module_id": moduleId
      };
      CoursePageResource? content =
          await _courseDetailsRepository.fetchCoursePageResource(jsonReq);
      if (content != null) {
        emit(PageResourceFetched(pageContentData: content));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseDetailsCubit][fetchPageResource] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(PageResourceError(error: errorMsg));
    } on Exception catch (e) {
      emit(PageResourceError(error: e.toString()));
    }
  }

  getVideoResProgress(String instanceId) async {
    if (ORG_ID.isEmpty || COURSE_ID.isEmpty || USER_ID.isEmpty) {
      return;
    }
    try {
      final jsonReq = {
        "instance_ids": [instanceId],
        "user_id": USER_ID,
        "course_id": COURSE_ID,
      };

      ResourceProgress responseData =
          await _courseDetailsRepository.getVideoResProgress(jsonReq) ??
              ResourceProgress(
                  progress: 0.0,
                  timeSpent: 'timeSpent',
                  instanceId: instanceId,
                  markedAsDone: false);

      emit(GetCourseResProgressState(progress: responseData));
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseDetailsCubit][getCourseprogress] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(GetCourseResProgressFailureState(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseDetailsCubit][getCourseprogress] :${e.toString()}");
      emit(GetCourseResProgressFailureState(error: e.toString()));
    }
  }

  setLoading(bool loadStatus) {
    if (loadStatus) {
      emit(CourseDetailsDashboardInfoLoading(isDataLoading: loadStatus));
    }
  }

  // set course progress
  setFileProgress(String courseId, String instanceId, int pageNum) async {
    if (courseId == '' || instanceId == '' || ORG_ID == '' || USER_ID == '') {
      return;
    }
    Map<String, dynamic> jsonReq = {
      "course_id": courseId,
      "instance_id": instanceId,
      "org_id": ORG_ID,
      "progress_data": {"current_page": pageNum},
      "user_id": USER_ID
    };

    print(jsonReq);

    try {
      final responseData =
          await _courseDetailsRepository.setFileProgress(jsonReq);

      if (responseData["status"] == 'success') {
        emit(const SetCourseResProgressState());
      } else {
        emit(SetCourseResProgressFailureState(error: responseData.toString()));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseDetailsCubit][getCourseprogress] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(SetCourseResProgressFailureState(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseDetailsCubit][getCourseprogress] :${e.toString()}");
      emit(SetCourseResProgressFailureState(error: e.toString()));
    }
  }
}
