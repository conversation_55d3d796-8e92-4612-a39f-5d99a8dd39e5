import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/app_config/preference_config.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../widgets/empty_screen_view.dart';
import '/src/presentation/widgets/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/router/app_router.dart';
import '../../config/themes/theme_colors.dart';
import '../../domain/models/course.dart';
import '../../utils/constants/strings.dart';
import '../cubits/course_list/course_list_cubit.dart';
import '../widgets/app_bar.dart';
import '../widgets/button_widget.dart';

@RoutePage()
// ignore: must_be_immutable
class CourseListViewScreen extends HookWidget {
  final String topic;
  final bool isFromLogin;
  CourseListViewScreen(
      {Key? key, required this.topic, this.isFromLogin = false})
      : super(key: key);

  bool isEmptyData = false;
  List<Course> courseList = [];

  _resetCourseSelection() async {
    COURSE_ID = '';
    COURSE_NAME = '';
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    _prefs.remove(PREF_KEY_COURSE_ID);
    _prefs.remove(PREF_KEY_COURSE_NAME);
  }

  @override
  Widget build(BuildContext context) {
    ValueNotifier<Course?> selectedTopic = useState<Course?>(null);

    final courseCubit = BlocProvider.of<CourseListCubit>(context);
    final screenWidth = MediaQuery.of(context).size.width;

    useEffect(() {
      Future<void>.microtask(() async {
        courseCubit.setLoading(true);
        await courseCubit.getUserCourse();
        // courseCubit.setLoading(false);
      });
      return null;
    }, const []);

    return PopScope(
        canPop: isFromLogin,
        onPopInvoked: (bool didPop) async {
          if (didPop && !isFromLogin) {
            return;
          }
          if (isFromLogin) {
            await _resetCourseSelection();
          }
        },
        child: Scaffold(
          backgroundColor: AppTheme.bgColor,
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(70),
              child: AppBarWidget(
                title: topic,
                leadingIconName: BACK_ARROW,
                isFromLogin: !isFromLogin,
                trailingWidget: Container(),
                leadingBtnAction: () async {
                  await _resetCourseSelection();
                  kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop();
                },
              )),
          body: BlocBuilder<CourseListCubit, CourseListState>(
              builder: (context, state) {
            if (state is CourseListSuccess || state is CourseDetailsLoading) {
              courseList = state.course;
            }
            return Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16)),
              ),
              child: (state is CourseDetailsLoading && state.isDataLoading)
                  ? LoadingIndicatorClass()
                  : (courseList.isNotEmpty)
                      ? LayoutBuilder(builder: (context, constraints) {
                          return Align(
                            child: SizedBox(
                              width: constraints.maxWidth < 720
                                  ? constraints.maxWidth
                                  : 800,
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                        padding: const EdgeInsets.only(
                                            left: 16, top: 20),
                                        child: Text(SELECTCOURSE,
                                            style: LMSFonts.semiBoldFont(
                                                18,
                                                AppTheme
                                                    .primaryTextColorBlack))),
                                    const Divider(
                                      indent: 16,
                                      endIndent: 16,
                                    ),
                                    Expanded(
                                        child: ListView.builder(
                                      itemCount: courseList.length,
                                      itemBuilder:
                                          (BuildContext context, int index) {
                                        return Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: <Widget>[
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 15),
                                              child: ListTile(
                                                textColor: LmsColors.white,
                                                dense: true,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            5)),
                                                title: selectedTopic.value !=
                                                            null &&
                                                        selectedTopic.value!
                                                                .full_name ==
                                                            courseList[index]
                                                                .full_name
                                                    ? Text(
                                                        courseList[index]
                                                            .full_name!,
                                                        style: LMSFonts.mediumFont(
                                                            16.0,
                                                            AppTheme
                                                                .primaryAppColor,
                                                            0))
                                                    : Text(
                                                        courseList[index]
                                                            .full_name!,
                                                        style: LMSFonts
                                                            .regularFont(
                                                          16,
                                                          AppTheme
                                                              .primaryTextColorBlack,
                                                        )),
                                                trailing: selectedTopic.value !=
                                                            null &&
                                                        selectedTopic.value!
                                                                .full_name ==
                                                            courseList[index]
                                                                .full_name
                                                    ? const Icon(
                                                        size: 30,
                                                        Icons.check,
                                                        color: AppTheme
                                                            .primaryBlue,
                                                      )
                                                    : null,
                                                onTap: () async {
                                                  selectedTopic.value =
                                                      courseList[index];

                                                  context
                                                      .read<CourseListCubit>()
                                                      .handleCourseSelection(
                                                          courseList[index]
                                                              .full_name!);
                                                },
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    )),
                                    _buttonClick(
                                        screenWidth,
                                        courseCubit.state,
                                        selectedTopic.value,
                                        selectedTopic.value?.course_id ?? '',
                                        selectedTopic.value?.full_name ?? '',
                                        context)
                                  ]),
                            ),
                          );
                        })
                      : const EmptyScreenView(
                          title: COURSE_EMPTY,
                        ),
            );
          }),
        ));
  }

  Widget _buttonClick(double screenWidth, CourseListState state, Course? course,
      String courseId, String courseName, BuildContext context) {
    return Align(
        alignment: Alignment.bottomCenter,
        child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: ButtonWidget(
              child: SizedBox(
                width: double.infinity,
                child: Text(
                  COMMON_BUTTON_TEXT,
                  style: LMSFonts.buttonStyle(16.0),
                  textAlign: TextAlign.center,
                ),
              ),
              textColor: LmsColors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              onPressed: course != null
                  ? () async {
                      COURSE_ID = courseId;
                      COURSE_NAME = courseName;
                      SharedPreferences _prefs =
                          await SharedPreferences.getInstance();

                      _prefs.setString(PREF_KEY_COURSE_ID, COURSE_ID);
                      _prefs.setString(PREF_KEY_COURSE_NAME, COURSE_NAME);
                      kIsWeb
                          ? Beamer.of(context)
                              .beamToReplacementNamed('/course-details', data: {
                              'courseId': course.course_id,
                              'courseName': course.full_name,
                              'isFromLogin': true,
                            })
                          : await appRouter.push(CourseDetailsViewRoute(
                              courseId: course.course_id ?? '',
                              courseName: course.full_name ?? '',
                              isFromLogin: true));
                    }
                  : null,
            )));
  }
}
