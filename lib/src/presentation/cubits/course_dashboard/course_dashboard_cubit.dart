import '../../../domain/models/course_dashboard/course_dashboard_config.dart';
import '../../../domain/models/course_details.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../../domain/models/course_dashboard/course_dashboard_info.dart';
import '../../../domain/models/course_dashboard/courses_info.dart';
import '../../../domain/models/course_dashboard/user_course.dart';
import '../../../domain/repositories/course_dashboard_repository.dart';
import '../../../utils/constants/strings.dart';
import '../../../utils/helper/app_date_formatter.dart';

part 'course_dashboard_state.dart';

class CourseDashboardCubit extends Cubit<CourseDashboardState> {
  CourseDashboardCubit(this._courseDashboardRepository)
      : super(const CourseDashboardInitial(courseDashboardInfo: []));

  final CourseDashboardRepository _courseDashboardRepository;

  final AppDateFormatter _appDateFormatter = AppDateFormatter.instance;

  int subjectsCount = 2;
  int assignmentsCount = 2;
  int currentAffairsCount = 2;
  int generalResourcesCount = 2;
  List<Resource> generalResources = [];

  initCourseDashboard() {
    emit(const CourseDashboardInitial(courseDashboardInfo: []));
  }

  fetchAllCourseInfo() async {
    if (ORG_ID.isEmpty || USER_ID.isEmpty) {
      return;
    }
    try {
      Map<String, dynamic> jsonReq = {"org_id": ORG_ID, "user_id": USER_ID};
      final _coursesInfo =
          await _courseDashboardRepository.fetchAllCourseStats(jsonReq);

      List<CoursesInfo> _courseInfoList = _coursesInfo;

      List<UserCourse> _userCourses = [];
      int _courseCount = _courseInfoList.length;
      double _totalProgress = 0.0;
      double _totalPercentage = 0.0;
      double _totalMarks = 0.0;
      int _totalAchievements = 0;
      int _completedCourseCount = 0;
      String _totalTimeSpent = '0 hrs 0 min 0 sec';
      Duration _timeSpentDuration = const Duration();

      for (var item in _courseInfoList) {
        UserCourse _userCourse =
            UserCourse(courseId: item.courseId, courseName: item.courseName);
        _userCourses.add(_userCourse);
        _totalProgress += item.progress;
        _totalPercentage += item.percentage;
        _totalMarks += item.totalMarks;
        _totalAchievements += item.achievements;
        if (item.progress == 100.00) {
          _completedCourseCount++;
        }

        Duration duration =
            _appDateFormatter.formatStringToDuration(item.timeSpent);
        _timeSpentDuration =
            _appDateFormatter.addDurationInto(_timeSpentDuration, duration);
      }
      _totalProgress = _totalProgress / _courseCount;
      _totalProgress = _totalProgress.isNaN ? 0.0 : _totalProgress;
      _totalPercentage = (_totalPercentage / _courseCount).roundToDouble();
      _totalPercentage = _totalPercentage.isNaN ? 0.0 : _totalPercentage;
      _totalTimeSpent =
          _appDateFormatter.formatDurationToString(_timeSpentDuration);

      ///
      /// Analytics API
      ///
      final _analyticsInfo =
          await _courseDashboardRepository.fetchAllAnalyticsData(jsonReq);

      CourseDashboardInfo _courseDashboardInfo = CourseDashboardInfo(
          userCourses: _userCourses,
          courseCount: _courseCount,
          totalProgress: _totalProgress,
          totalPercentage: _totalPercentage,
          totalMarks: _totalMarks,
          totalTimeSpent: _totalTimeSpent,
          totalAchievements: _totalAchievements,
          completedCourseCount: _completedCourseCount,
          coursesInfo: _coursesInfo,
          analyticsInfo: _analyticsInfo);

      emit(CourseDashboardDetailsFetched(
        courseDashboardInfo: [_courseDashboardInfo],
      ));
    } on Exception catch (e) {
      debugPrint(
          "[PostgrestException][CourseDashboardCubit][fetchAllCourseInfo] :$e");
      emit(CourseDashboardDetailsError(
          error: e.toString(), courseDashboardInfo: const []));
    }
  }

  fetchCurrentAffairs() async {
    try {
      final currentAffairs =
          await _courseDashboardRepository.fetchAllCurrentAffairs();
      if (currentAffairs == null || currentAffairs.isEmpty) {
        emit(const DashboardCurrentAffairsFailure(
            error: 'Empty list', courseDashboardInfo: []));
      } else {
        currentAffairs
            .sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
        emit(DashboardCurrentAffairsFetched(
            currentAffairs: currentAffairs, courseDashboardInfo: []));
      }
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseDashboardCubit][fetchCurrentAffairs] :${e.toString()}");
      emit(DashboardCurrentAffairsFailure(
          error: e.toString(), courseDashboardInfo: []));
    }
  }

  fetchDashboardConfig() async {
    Map<String, dynamic> jsonReq = {"org_id": ORG_ID, "course_id": null};
    try {
      final dashboardConfig =
          await _courseDashboardRepository.fetchDashboardConfig(jsonReq);
      updateConfigCount(dashboardConfig);
      generalResources = dashboardConfig.generalResources.resources;
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseDashboardCubit][fetchDashboardConfig] :${e.toString()}");
    }
  }

  updateConfigCount(DashboardConfig dashboardConfig) {
    subjectsCount = dashboardConfig.subjects.noOfItems;
    assignmentsCount = dashboardConfig.assignments.noOfItems;
    currentAffairsCount = dashboardConfig.currentAffairs.noOfItems;
    generalResourcesCount = dashboardConfig.generalResources.noOfItems;
  }
}
