import 'package:SmartLearn/src/domain/models/check_point_data/check_point_data.dart';

import '../models/course_details.dart';
import '../models/page_content.dart';
import '../models/responses/section_details.dart';

abstract class SectionRepository {
  Future<SectionDetails> fetchSectionDetailsInfo(String sectionId);

  // Study Material View
// Fetch Page Content
  Future<PageContent?> fetchPageContent(String instance);

  // Fetch Video info
  Future<CourseVideo?> fetchResourceVideoInfo(String instance);
  Future<CheckPointData?> fetchVideoCheckPointInfo(String moduleId);

  Future<dynamic> fetchResourceImageInfo(String instance);
  Future<dynamic> getCourseProgress(String instance);
}
