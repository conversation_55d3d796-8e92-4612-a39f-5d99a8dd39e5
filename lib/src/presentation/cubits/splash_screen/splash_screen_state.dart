part of 'splash_screen_cubit.dart';

abstract class SplashScreenState extends Equatable {
  final CourseDetails? courseDetailsData;
  final Profile? userInfo;

  final String userId;
  const SplashScreenState(
      {required this.userId, this.courseDetailsData, this.userInfo});

  @override
  List<Object> get props => [userId];
}

class SplashScreenInitial extends SplashScreenState {
  const SplashScreenInitial({required super.userId});
}

class SplashScreenLoading extends SplashScreenState {
  final bool isLoading;
  const SplashScreenLoading({required this.isLoading, required super.userId});
  @override
  List<Object> get props => [isLoading];
}

class UserAuthenticated extends SplashScreenState {
  final bool isAuthenticated;
  const UserAuthenticated(
      {required this.isAuthenticated, required super.userId});
  @override
  List<Object> get props => [isAuthenticated];
}

class UserAuthenticationFailure extends SplashScreenState {
  final bool isAuthenticated;
  const UserAuthenticationFailure(
      {required this.isAuthenticated, required super.userId});
  @override
  List<Object> get props => [isAuthenticated];
}

class CourseDetailsFetched extends SplashScreenState {
  const CourseDetailsFetched({super.courseDetailsData, required super.userId});

  @override
  List<Object> get props => [];
}

class CourseDetailsEmpty extends SplashScreenState {
  const CourseDetailsEmpty({required super.userId});
}

class CourseDetailsError extends SplashScreenState {
  final String error;
  const CourseDetailsError({required this.error, required super.userId});

  @override
  List<Object> get props => [error];
}

class ProfileInfoFetched extends SplashScreenState {
  const ProfileInfoFetched({super.userInfo, required super.userId});

  @override
  List<Object> get props => [];
}

class ProfileInfoError extends SplashScreenState {
  final String error;
  const ProfileInfoError({required this.error, required super.userId});

  @override
  List<Object> get props => [error];
}

class SetAppLocale extends SplashScreenState {
  final String currentLanguage;
  const SetAppLocale({required this.currentLanguage, required super.userId});

  @override
  List<Object> get props => [currentLanguage];
}
