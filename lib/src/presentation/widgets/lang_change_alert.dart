import 'package:flutter/foundation.dart';

import '/src/config/router/app_router.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/locale_change_helper.dart';
import 'alert_popup/single_action_dialogue.dart';

// class LanguageChangeAlertPopup extends StatelessWidget {
//   final bool wasDefaultLangEN;

//   @override
//   Widget build(BuildContext context) {
//     return _showLanguageChangedAlert(context, wasDefaultLangEN);
//   }

showLanguageChangedAlert(
    BuildContext context, bool wasDefaultLangEN, String lang) {
  bool isThereCurrentDialogShowing = ModalRoute.of(context)?.isCurrent != true;
  var currentpage = appRouter.current.route.name;

  if (isThereCurrentDialogShowing && currentpage != ExamViewRoute.name) {
    ///
    /// exam gets submitted when goes to the  background
    /// so no need to show lang change alert in exam view
    ///

    if (!kIsWeb) {
      appRouter.popUntil((route) => route.settings.name == currentpage);
    }
  }
  Future.delayed(const Duration(seconds: 1), () {
    if (context != null && context.routeData.isActive) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return PopScope(
            canPop: false,
            onPopInvoked: (bool didPop) {
              if (didPop) {
                return;
              }
              logoutUserOnLanguageChangeDetection(
                context,
                lang,
              );
              appRouter
                  .pushAndPopUntil(LoginEmailViewRoute(isTokenExpired: false),
                      predicate: (Route<dynamic> route) {
                return route.settings.name == LoginEmailViewRoute.name;
              });
            },
            child: LangChangeAlertPopup(
              title: '',
              message: wasDefaultLangEN
                  ? SLStrings.getTranslatedString(KEY_LANGUAGE_CHANGE_ALERT_EN)
                  : SLStrings.getTranslatedString(KEY_LANGUAGE_CHANGE_ALERT),
              iconWidget: const Icon(Icons.error, size: 60),
              onTapped: () {
                // if (context != null) {
                //   final _userCubit = BlocProvider.of<UserCubit>(context);
                //   _userCubit.userSignout();
                // }
                appRouter
                    .pushAndPopUntil(LoginEmailViewRoute(isTokenExpired: false),
                        predicate: (Route<dynamic> route) {
                  return route.settings.name == LoginEmailViewRoute.name;
                });
              },
              onBackTapped: () {
                logoutUserOnLanguageChangeDetection(
                  context,
                  lang,
                );
                appRouter
                    .pushAndPopUntil(LoginEmailViewRoute(isTokenExpired: false),
                        predicate: (Route<dynamic> route) {
                  return route.settings.name == LoginEmailViewRoute.name;
                });
                return false;
              },
            ),
          );
        },
      ).then((value) {
        if (isThereCurrentDialogShowing) {
        } else {
          if (wasDefaultLangEN) {
            //no change
            //dismiss the popup
          } else {
            logoutUserOnLanguageChangeDetection(
              context,
              lang,
            );
          }
        }
      });
    }
  });
}
// }
