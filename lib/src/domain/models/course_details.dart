import '/src/config/enums/section_type.dart';
import '/src/utils/constants/strings.dart';
import 'package:equatable/equatable.dart';

class CourseDetails extends Equatable {
  final String courseId;
  final String courseName;
  final List<CourseModule?> courseModule;
  final List<CourseVideo?> courseVideos;
  final List<CurrentAffairs?> currentAffairs;
  final List<CourseExam?> exams;

  const CourseDetails({
    this.courseId = '',
    this.courseName = '',
    required this.courseModule,
    required this.courseVideos,
    required this.currentAffairs,
    required this.exams,
  });

  factory CourseDetails.fromJson(Map<String, dynamic> json) {
    List<dynamic> jsonList = json['sections'] ?? [];
    jsonList.removeWhere((element) => element == null);
    jsonList.removeWhere((element) => element['name'] == '');
    List<CourseModule> courseModuleList = jsonList.isNotEmpty
        ? jsonList.map((item) => CourseModule.fromJson(item)).toList()
        : [];

    jsonList = json['modules'] ?? [];
    jsonList.removeWhere((element) => element == null);
    jsonList.removeWhere((element) => element['module_name'] == '');
    jsonList.retainWhere((element) =>
        element['module_type'].toString().toLowerCase() == 'quiz' &&
        (element['quiz_type'].toString().toLowerCase() == 'main' ||
            element['quiz_type'].toString().toLowerCase() == 'practice'));
    List<CourseExam> courseExamList = jsonList.isNotEmpty
        ? jsonList.map((item) => CourseExam.fromJson(item)).toList()
        : [];

    jsonList = json['course_resources'] ?? [];
    jsonList.removeWhere((element) => element == null);

    jsonList.removeWhere((element) =>
        element['type'].toString().toLowerCase() != SectionType.video.value);
    jsonList.retainWhere((element) =>
        element['external_url'].toString().startsWith(VALID_VIDEO) ||
        element['url'].toString().startsWith(VALID_VIDEO));
    List<CourseVideo> courseVideosList = jsonList.isNotEmpty
        ? jsonList.map((e) => CourseVideo.fromJson(e)).toList()
        : [];

    jsonList = json['current_affairs'] ?? [];
    jsonList.removeWhere((element) => element == null);
    jsonList.removeWhere(
        (element) => (element['title'] ?? '').toString().trim() == '');
    jsonList.removeWhere((element) =>
        element['type'].toString().toLowerCase() !=
        SectionType.currentAffairs.value);
    List<CurrentAffairs> courseNewsList = jsonList.isNotEmpty
        ? jsonList.map((e) => CurrentAffairs.fromJson(e)).toList()
        : [];

    return CourseDetails(
      courseId: json['id'] ?? '',
      courseName: json['summary'] ?? '',
      courseModule: courseModuleList,
      courseVideos: courseVideosList,
      currentAffairs: courseNewsList,
      exams: courseExamList,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': courseId,
        'name': courseName,
        'sections': courseModule.map((e) => e?.toJson()).toList(),
        'course_resources': courseVideos.map((e) => e?.toJson()).toList(),
        'news': currentAffairs.map((e) => e?.toJson()).toList(),
        'modules': exams.map((e) => e?.toJson()).toList(),
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [courseId, courseName, courseModule, courseVideos, currentAffairs];
  }
}

class CourseModule extends Equatable {
  final String moduleId;
  final String moduleName;
  final String moduleSummary;

  const CourseModule(
      {this.moduleId = '', this.moduleName = '', this.moduleSummary = ''});

  factory CourseModule.fromJson(Map<String, dynamic> json) {
    return CourseModule(
      moduleId: json['section_id'] ?? '',
      moduleName: json['name'] ?? '',
      moduleSummary: json['summary'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'section_id': moduleId,
        'name': moduleName,
        'summary': moduleSummary,
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [moduleId, moduleName, moduleSummary];
  }
}

class CourseVideo extends Equatable {
  final String videoId;
  final String videoName;
  final String videoURL;
  String videoThumbnail;
  String instanceId;
  final String description;
  double progress;
  int totalDuration;
  String? courseModuleId;
  int? numberOfCheckPoints;
  bool? isRandomCheckPoint;
  bool isCheckPointEnabled;
  bool? alwaysShowCheckPoints;

  CourseVideo({
    required this.videoId,
    required this.videoName,
    required this.videoURL,
    required this.instanceId,
    required this.videoThumbnail,
    required this.description,
    this.progress = 0.0,
    this.totalDuration = 0,
    this.courseModuleId,
    this.numberOfCheckPoints,
    this.isRandomCheckPoint,
    this.isCheckPointEnabled = false,
    this.alwaysShowCheckPoints,
  });

  factory CourseVideo.fromJson(Map<String, dynamic> json) {
    return CourseVideo(
        videoId: json['id'] ?? '',
        videoName: json['name'] ?? '',
        videoURL: json['url'] ?? json['external_url'] ?? '',
        instanceId: json['instance_id'] ?? '',
        description: json['description'] ?? '',
        progress: 0.0,
        totalDuration: 0,
        courseModuleId: json['course_module_id'] ?? "",
        numberOfCheckPoints: json['num_of_checkpoints'] ?? 0,
        isRandomCheckPoint: json['is_random_checkpoint'] ?? false,
        isCheckPointEnabled: json['is_checkpoint_enabled'] ?? false,
        alwaysShowCheckPoints: json['always_show_checkpoints'] ?? false,
        videoThumbnail: '');
  }

  Map<String, dynamic> toJson() => {
        'id': videoId,
        'name': videoName,
        'url': videoURL,
        'progress': progress.toString(),
        'total_duration': totalDuration.toString(),
        'course_module_id': courseModuleId,
        'num_of_checkpoints': numberOfCheckPoints,
        'is_random_checkpoint': isRandomCheckPoint,
        'is_checkpoint_enabled': isCheckPointEnabled,
        'always_show_checkpoints': alwaysShowCheckPoints
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [];
  }
}

class CurrentAffairs extends Equatable {
  final String newsId;
  final String newsTitle;
  final String newsContent;
  final String backgroundImage;
  final DateTime publishedDate;

  const CurrentAffairs(
      {required this.newsId,
      required this.newsTitle,
      required this.newsContent,
      required this.backgroundImage,
      required this.publishedDate});

  factory CurrentAffairs.fromJson(Map<String, dynamic> json) {
    String dateString = json['publish_date'] ?? DateTime.now().toString();
    DateTime date = DateTime.parse(dateString);

    return CurrentAffairs(
      newsId: json['id'] ?? '',
      newsTitle: (json['title'] ?? '').toString().trim(),
      newsContent: (json['content'] ?? '').toString().trim(),
      //TO DO: change to correct key name once api is done
      backgroundImage: json['background_image'] ??
          '$ASSETS_PATH/current_affairs_default.png',
      publishedDate: date,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': newsId,
        'title': newsTitle,
        'content': newsContent,
        'publish_date': publishedDate.toString(),
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [];
  }
}

class CourseExam extends Equatable {
  final String moduleId;
  final String sectionId;
  final String moduleName;
  // final String moduleSummary;

  const CourseExam({
    this.moduleId = '',
    this.sectionId = '',
    this.moduleName = '',
    // this.moduleSummary = '',
  });

  factory CourseExam.fromJson(Map<String, dynamic> json) {
    return CourseExam(
      moduleId: json['module_id'] ?? '',
      sectionId: json['section_id'] ?? '',
      moduleName: json['module_name'] ?? '',
      // moduleSummary: json['summary'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        'module_id': moduleId,
        'section_id': sectionId,
        'module_name': moduleName
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [moduleId, moduleName, sectionId];
  }
}
