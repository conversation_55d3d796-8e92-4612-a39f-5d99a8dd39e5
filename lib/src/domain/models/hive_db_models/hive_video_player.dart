import 'package:hive/hive.dart';

part 'hive_video_player.g.dart';

@HiveType(typeId: 1)
class HiveVideoPlayerData {
  @HiveField(0)
  final int? id;

  @HiveField(1)
  final String videoId;

  @HiveField(2)
  final int startTime;

  @HiveField(3)
  final int endTime;

  @HiveField(4)
  final int mileStonesCompleted;

  @HiveField(5)
  final int lastWatchTime;

  @HiveField(6)
  final String mileStoneDurations;

  HiveVideoPlayerData({
    this.id,
    required this.videoId,
    required this.startTime,
    required this.endTime,
    required this.mileStonesCompleted,
    required this.lastWatchTime,
    required this.mileStoneDurations,
  });
}
