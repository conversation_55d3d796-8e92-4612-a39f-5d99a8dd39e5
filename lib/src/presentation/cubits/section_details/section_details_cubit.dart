import 'dart:io';
import '../../../utils/constants/helper.dart';
import '/src/domain/repositories/user_repository.dart';

import '/src/utils/constants/strings.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../domain/models/check_point_data/check_point_data.dart';

import '/src/domain/models/resource_file.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

import '../../../domain/models/course_details.dart';
import '../../../domain/models/course_progress.dart';
import '../../../domain/models/page_content.dart';
import '../../../domain/models/responses/section_details.dart';
import '../../../domain/repositories/course_respository.dart';
import '../../../domain/repositories/section_repository.dart';

part 'section_details_state.dart';

class SectionDetailsCubit extends Cubit<SectionDetailsState> {
  final SectionRepository _sectionRepository;
  final CourseRepository _courseRepository;
  final UserRepository _userRepository;
  SectionDetailsCubit(
      this._sectionRepository, this._courseRepository, this._userRepository)
      : super(const SectionDetailsInitial());
  bool loading = false;

  late SectionDetails sectionDetails;
  CourseVideo? content;
  CheckPointData? checkPointData;

  /// to set the state to initial state
  /// during section details to study material navigation,
  /// last state change was to SectionDetailsLoading
  /// so in order to reset the state to initial
  ///
  initCubit() {
    emit(const SectionDetailsInitial());
  }

  fetchSectionDetails(String sectionId) async {
    try {
      sectionDetails =
          await _sectionRepository.fetchSectionDetailsInfo(sectionId);

      emit(SectionDetailsFetched(sectionDetailsData: sectionDetails));
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][fetchSectionDetails] :${e.toString()}");
      if (e.code == 'PGRST301') {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          fetchSectionDetails(sectionId);
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      } else {
        String errorMsg = e.message;
        emit(SectionDetailsError(error: errorMsg));
      }
    } on Exception catch (e) {
      emit(SectionDetailsError(error: e.toString()));
    }
  }

  setLoading(bool loadStatus) {
    if (loadStatus) {
      emit(SectionDetailsListLoading(isDataLoading: loadStatus));
    } else {
      emit(const SectionDetailsListLoading(isDataLoading: false));
    }
  }

  Future<void> changeStatus(int index) async {
    emit(SectionDetailschangeStatus(index: index));
  }

  Future<void> expanding(bool value, int index) async {
    if (value == true) {
      emit(SectionDetailsExapanding(index: index, expand: true));
    } else {
      emit(SectionDetailsExapanding(index: index, expand: false));
    }
  }

//
// Section Details UI Enhancement
//
  Future<void> stepItemTapped(int step) async {
    emit(StepItemTapState(step: step));
  }

// Study Material View
// Fetch Page Content

  fetchPageContent(String instance) async {
    try {
      PageContent? content =
          await _sectionRepository.fetchPageContent(instance);
      if (content != null) {
        emit(PageContentsFetched(pageContentData: content));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][fetchPageContent] :${e.toString()}");
      String errorMsg = e.message;
      emit(PageDetailsError(error: errorMsg));
    } on Exception catch (e) {
      emit(PageDetailsError(error: e.toString()));
    }
  }

// PDF View
  Future<String> getFileFromUrl(String url) async {
    try {
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);
      var dir = await getApplicationDocumentsDirectory();

      File file = File("${dir.path}/mypdfonline.pdf");

      File urlFile = await file.writeAsBytes(bytes, flush: true);
      String path = urlFile.path;

      emit(PdfViewer(path: path));
      return path;
    } on Exception catch (e) {
      throw Exception("Error opening url file");
    }
  }

  fetchVideoContent(
    String instance,
  ) async {
    try {
      content = await _sectionRepository.fetchResourceVideoInfo(instance);
      if (content != null) {
        if (content!.isCheckPointEnabled == true) {
          checkPointData = await _sectionRepository
              .fetchVideoCheckPointInfo(content!.courseModuleId!);
          emit(VideoResourceFetched(
              courseVideo: content!,
              checkPoints: checkPointData?.checkPoints ?? []));
        } else {
          emit(VideoResourceFetched(courseVideo: content!, checkPoints: []));
        }
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][fetchVideoContent] :${e.toString()}");
      emit(VideoResourceError(error: e.details));
    } on Exception catch (e) {
      emit(VideoResourceError(error: e.toString()));
    }
  }

  fetchFileContent(String instance) async {
    try {
      ResourseFile content =
          await _sectionRepository.fetchResourceImageInfo(instance);
      if (content != null) {
        emit(FileResourceFetched(resourseFile: content));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][fetchFileContent] :${e.toString()}");
      String errorMsg = e.message;
      emit(FileResourceError(error: errorMsg));
    } on Exception catch (e) {
      emit(FileResourceError(error: e.toString()));
    }
  }

  Future<dynamic> changeDoneStatus(bool doneStatus) async {
    emit(SectionDetailschangeDoneStatus(doneStatus: doneStatus));
  }

  // get course progress
  getCourseprogress(String instance) async {
    try {
      CourseProgress responseData =
          await _sectionRepository.getCourseProgress(instance);

      emit(GetCourseProgressState(progress: responseData));
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][SectionDetailsCubit][getCourseprogress] :${e.toString()}");
      String errorMsg = e.message;
      emit(GetCourseProgressFailureState(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][SectionDetailsCubit][getCourseprogress] :${e.toString()}");
      emit(GetCourseProgressFailureState(error: e.toString()));
    }
  }

  refreshScreen() {
    emit(RefreshState());
  }

  setCourseProgress(
      String courseId, String instance, Map<String, dynamic> duration) async {
    try {
      final responseData = await _courseRepository.setCourseProgress(
          courseId, instance, duration);

      if (responseData["status"] == 'success') {
        emit(SetProgressState());
      } else {
        emit(ResourceFailureState(error: responseData.toString()));
      }
    } on Exception catch (e) {
      emit(ResourceFailureState(error: e.toString()));
    }
  }

/* TODO: Remove later  
Future<void> fetchCheckPointData(String moduleId) async {
    try {
      await _sectionRepository.fetchVideoCheckPointInfo(moduleId);
    } on Exception catch (error) {
      debugPrint("fetchCheckPointData => ${error.toString()}");
    }
  }*/
}
