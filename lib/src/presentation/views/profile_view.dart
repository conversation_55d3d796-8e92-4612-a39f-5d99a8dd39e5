import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/app_config/preference_config.dart';
import '../../config/enums/alert_types.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../widgets/common_button_widget.dart';
import '../widgets/debounced_button.dart';
import '/src/presentation/cubits/connectivity/connectivity_state.dart';

import '../cubits/connectivity/connectivity_cubit.dart';
import '/src/config/themes/lms_fonts.dart';

import '../../config/themes/app_theme.dart';
import '../widgets/connectivity_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/router/app_router.dart';
import '../../config/themes/theme_colors.dart';
import '../../utils/constants/boolean.dart';
import '../../utils/constants/strings.dart';
import '../cubits/profile/profile_cubit.dart';
import '../cubits/profile/profile_state.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/app_bar.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/textfield_widget.dart';

import 'profile_review_view.dart';

@RoutePage()

// ignore: must_be_immutable
class ProfileViewScreen extends HookWidget with WidgetsBindingObserver {
  final bool isFromLogin;
  final bool isFromSideMenu;
  ProfileViewScreen(
      {super.key, this.isFromLogin = false, this.isFromSideMenu = false});

  // late _firstNameController;
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final FocusNode _firstNameFocusNode = FocusNode();
  final FocusNode _lastNameFocusNode = FocusNode();

  String firstName = '';
  String lastName = '';
  String? avatarImage;

  bool _didChangeProfilePic = false;

  /// set to false when firstname or lastname fields are empty
  bool _isValidFirstName = true;
  bool _isValidLastName = true;

  final double _horizontalMargin = 16.0;

  BuildContext? currentContext;

  ValueNotifier<bool> isDataLoading = ValueNotifier<bool>(false);

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    didChangeAppLocale(locales, currentContext);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;

    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final _userCubit = BlocProvider.of<UserCubit>(context);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      _userCubit.resetProfileInfo(_userCubit.state, _userCubit);
      _emailController.text = _userCubit.state.profile?.email ?? '';

      return () {
        WidgetsBinding.instance.removeObserver(this);
        _firstNameController.dispose();
        _lastNameController.dispose();
        _emailController.dispose();
        _firstNameFocusNode.dispose();
        _lastNameFocusNode.dispose();
      };
    }, []);

    _firstNameController.addListener(() {
      _userCubit.updateFirstName(_firstNameController.text);
    });

    _lastNameController.addListener(() {
      _userCubit.updateLastName(_lastNameController.text);
    });

    return PopScope(
        canPop: !isFromLogin,
        onPopInvoked: (bool didPop) async {
          if (didPop && isFromLogin) {
            return;
          }
          UserState state = _userCubit.state;
          if (state is UserLoadingState && state.isLoading) {
            /// stay on page if loading
          } else {
            _userCubit.setLoading(true, _userCubit.state.profile, false);
            await _userCubit.resetProfileInfo(_userCubit.state, _userCubit);
          }
        },
        child: BlocBuilder<UserCubit, UserState>(
            bloc: _userCubit,
            builder: (context, state) {
              if (state is UserLoadingState ||
                  state is ProfilePicUpdatedFromCamera ||
                  state is ProfilePicUpdatedFromGallery) {
                if (state.imageChosen != null &&
                    state.imageChosen!.path != NULL_PATH) {
                  _didChangeProfilePic = true;
                  avatarImage = state.imageChosen!.path;
                }
              }
              if (state.profile != null) {
                firstName = state.profile?.firstName ?? "";
                lastName = state.profile?.lastName ?? "";
                _firstNameController.text = firstName;
                _lastNameController.text = lastName;
                avatarImage = state.imageChosen == null ||
                        state.imageChosen?.path == NULL_PATH
                    ? state.profile?.avatarUrl ?? ""
                    : state.imageChosen?.path;
              }
              // if (state is SubmitButtonStatus) {
              //   _isValidFirstName = state.isValidSubmission;
              // }
              return GestureDetector(
                  onTap: () {
                    FocusScopeNode currentFocus = FocusScope.of(context);
                    if (!currentFocus.hasPrimaryFocus) {
                      currentFocus.unfocus();
                    }
                  },
                  child: Scaffold(
                    resizeToAvoidBottomInset: false,
                    backgroundColor: AppTheme.primaryBlue,
                    appBar: PreferredSize(
                        preferredSize: const Size.fromHeight(70),
                        child: AppBarWidget(
                          title: SLStrings.getTranslatedString(
                              KEY_PROFILE_APPBAR_TITLE),
                          // toolbarHeight: screenWidth / 0.7,
                          isFromLogin: isFromLogin,
                          leadingIconName: BACK_ARROW,
                          trailingWidget: Container(),
                          leadingBtnAction: () async {
                            if (state is UserLoadingState && state.isLoading) {
                              /// stay on page if loading
                            } else {
                              _userCubit.setLoading(
                                  true, _userCubit.state.profile, false);
                              await _userCubit.resetProfileInfo(
                                  _userCubit.state, _userCubit);
                              kIsWeb
                                  ? Beamer.of(context).beamBack()
                                  : Navigator.pop(context);
                            }
                          },
                        )),
                    body: LayoutBuilder(
                      builder:
                          (BuildContext context, BoxConstraints constraints) {
                        return BlocBuilder<UserCubit, UserState>(
                            bloc: _userCubit,
                            builder: (context, state) {
                              if (state is UserLoadingState ||
                                  state is ProfilePicUpdatedFromCamera ||
                                  state is ProfilePicUpdatedFromGallery) {
                                if (state.imageChosen != null &&
                                    state.imageChosen!.path != NULL_PATH) {
                                  _didChangeProfilePic = true;
                                  avatarImage = state.imageChosen!.path;
                                }
                              }
                              if (state.profile != null) {
                                firstName = state.profile?.firstName ?? "";
                                lastName = state.profile?.lastName ?? "";
                                _firstNameController.text = firstName;
                                _lastNameController.text = lastName;
                                avatarImage = state.imageChosen == null ||
                                        state.imageChosen?.path == NULL_PATH
                                    ? state.profile?.avatarUrl ?? ""
                                    : state.imageChosen?.path;
                              }

                              return Stack(
                                children: [
                                  CustomScrollView(
                                    slivers: [
                                      SliverFillRemaining(
                                        hasScrollBody: false,
                                        child: Stack(
                                          children: [
                                            Container(
                                              decoration: const BoxDecoration(
                                                color: AppTheme.whiteColor,
                                                borderRadius: BorderRadius.only(
                                                    topLeft:
                                                        Radius.circular(22),
                                                    topRight:
                                                        Radius.circular(22)),
                                              ),
                                              padding: EdgeInsets.symmetric(
                                                  horizontal:
                                                      _horizontalMargin),
                                              child: Align(
                                                child: SizedBox(
                                                  width:
                                                      constraints.maxWidth < 720
                                                          ? constraints.maxWidth
                                                          : 800,
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      const SizedBox(
                                                          height: 25),
                                                      _profileAvatarWidget(
                                                          context,
                                                          state,
                                                          avatarImage,
                                                          screenWidth,
                                                          _userCubit),
                                                      // _submitButton(
                                                      //     context,
                                                      //     screenWidth,
                                                      //     state,
                                                      //     _userCubit),
                                                      const SizedBox(
                                                          height: 20),
                                                      _textFieldTitle(SLStrings
                                                          .getTranslatedString(
                                                              KEY_PROFILE_FIRST_NAME)),
                                                      const SizedBox(height: 5),
                                                      _textfieldForFirstName(
                                                          screenWidth,
                                                          _firstNameController,
                                                          state,
                                                          _userCubit),
                                                      const SizedBox(
                                                          height: 15),
                                                      _textFieldTitle(SLStrings
                                                          .getTranslatedString(
                                                              KEY_PROFILE_LAST_NAME)),
                                                      const SizedBox(height: 5),
                                                      _textfieldForLastName(
                                                          screenWidth,
                                                          _lastNameController,
                                                          state,
                                                          _userCubit),
                                                      const SizedBox(
                                                          height: 15),
                                                      _textFieldTitle(SLStrings
                                                          .getTranslatedString(
                                                              KEY_EMAIL)),
                                                      const SizedBox(height: 5),
                                                      _textfieldForEmail(
                                                          screenWidth, state),
                                                      const SizedBox(
                                                          height: 20),
                                                      const Spacer(),
                                                      _submitButton(
                                                          context,
                                                          screenWidth,
                                                          state,
                                                          _userCubit),
                                                      const SizedBox(
                                                          height: 20),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                  ConnectivityStatusWidget(),
                                  state is UserLoadingState && state.isLoading
                                      ? Positioned.fill(
                                          child: LoadingIndicatorClass())
                                      : Container(),
                                ],
                              );
                            });
                      },
                    ),
                  ));
            }));
  }

  _showProfileEditOptions(buildContext, UserCubit userCubit, UserState state) {
    showModalBottomSheet(
      backgroundColor: LmsColors.white,
      context: buildContext,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      builder: (BuildContext context) {
        return Container(
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
          ),
          height: 180,
          child: Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                _horizontalLine(buildContext, state),
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _openCamera(buildContext, userCubit),
                      _openGallery(buildContext, userCubit),
                      // _cancelOption(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ).then(
        (value) => userCubit.setLoading(false, userCubit.state.profile, false));
  }

  Widget _profileAvatarWidget(BuildContext buildContext, UserState state,
      String? avatarImage, double screenWidth, UserCubit userCubit) {
    return ProfileImageWidget(
      avatarUrl: avatarImage,
      showCamera: kIsWeb ? false : true,
      radius: 150,
      onEditTapped: !state.isLoading
          ? () {
              _showProfileEditOptions(buildContext, userCubit, state);
            }
          : () {},
    );
  }

  Widget _textFieldTitle(String title) {
    return Align(
      alignment: Alignment.topLeft,
      child: Text(
        title,
        style: LMSFonts.regularFont(14, AppTheme.textFieldTitle),
      ),
    );
  }

  Widget _textfieldForFirstName(
      double screenWidth,
      TextEditingController _firstNameController,
      UserState state,
      UserCubit userCubit) {
    _firstNameController.selection = TextSelection.fromPosition(
        TextPosition(offset: _firstNameController.text.length));

    return StreamBuilder<String>(
        stream: userCubit.firstName,
        builder: (context, snapshot) {
          String? errorMessage = snapshot.error != null && snapshot.error != ''
              ? snapshot.error.toString()
              : null;

          if (errorMessage != null && errorMessage.isNotEmpty) {
            _isValidFirstName = false;
            isDataLoading.value = true;
          } else {
            _isValidFirstName = true;
            isDataLoading.value = false;
          }
          return TextFieldWidget(
            obscureText: false,
            focusNode: _firstNameFocusNode,
            controller: _firstNameController,
            hint: '',
            errorMsg: errorMessage,
            isNameField: true,
            onTap: () {},
            onChange: (val) {
              if (val == '' || val.isEmpty || val.trim().isEmpty) {
                isDataLoading.value = true;
                state.profile?.firstName = val;
              } else {
                state.profile?.firstName = val;
                isDataLoading.value = false;
              }
            },
            onSubmitted: (val) => {userCubit.validateFirstNameInput},
          );
        });
  }

  Widget _textfieldForLastName(
      double screenWidth,
      TextEditingController _lastNameController,
      UserState state,
      UserCubit userCubit) {
    _lastNameController.selection = TextSelection.fromPosition(
        TextPosition(offset: _lastNameController.text.length));
    return StreamBuilder<String>(
        stream: userCubit.lastName,
        builder: (context, snapshot) {
          String? errorMessage = snapshot.error != null && snapshot.error != ''
              ? snapshot.error.toString()
              : null;

          if (errorMessage != null && errorMessage.isNotEmpty) {
            _isValidLastName = false;
            isDataLoading.value = true;
          } else {
            _isValidLastName = true;
            isDataLoading.value = false;
          }
          return TextFieldWidget(
            obscureText: false,
            focusNode: _lastNameFocusNode,
            controller: _lastNameController,
            errorMsg: errorMessage,
            hint: '',
            isNameField: true,
            onTap: () {},
            onChange: (val) {
              if (val == '' || val.isEmpty || val.trim().isEmpty) {
                isDataLoading.value = true;
                state.profile?.lastName = val;
              } else {
                state.profile?.lastName = val;
                isDataLoading.value = false;
              }
            },
            onSubmitted: (val) => {userCubit.validateLastNameInput},
          );
        });
  }

  Widget _textfieldForEmail(double screenWidth, UserState state) {
    return Container(
        margin: const EdgeInsets.only(bottom: 20),
        child: TextFieldWidget(
          obscureText: false,
          isEnabled: false,
          focusNode: FocusNode(),
          enableMultipleLines: true,
          controller: _emailController,
          hint: '',
          onTap: () {},
          onChange: (val) {},
          onSubmitted: (val) => {},
        ));
  }

  Widget _profileOptions(String title, String imageName) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
            height: 65,
            width: 65,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppTheme.whiteColor,
              boxShadow: [
                BoxShadow(
                  color: AppTheme.blackColor.withOpacity(0.25),
                  offset: const Offset(0, 2),
                  blurRadius: 2,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Image.asset(
                '$ASSETS_PATH/$imageName.png',
              ),
            )),
        const SizedBox(height: 15),
        Text(
          title,
          textAlign: TextAlign.center,
          style: LMSFonts.regularFontWithHeight(
              14, AppTheme.profileEditOptions, 0),
        ),
      ],
    );
  }

  Widget _openCamera(context, UserCubit _userCubit) {
    final state = _userCubit.state;
    return GestureDetector(
      child: _profileOptions(
          SLStrings.getTranslatedString(KEY_SELECT_FROM_CAMERA),
          'profile_camera'),
      onTap: () async {
        _userCubit.setLoading(true, state.profile, false);
        Navigator.pop(context);
        await _userCubit.captureProfilePicFromCamera(
            _firstNameController.text, _lastNameController.text, context);
        _userCubit.setLoading(false, state.profile, false);
      },
    );
  }

  Widget _openGallery(BuildContext context, UserCubit _userCubit) {
    final state = _userCubit.state;
    return GestureDetector(
      child: _profileOptions(
          SLStrings.getTranslatedString(KEY_SELECT_FROM_GALLERY), 'gallery'),
      onTap: () async {
        Navigator.pop(context);
        _userCubit.setLoading(true, state.profile, false);
        await _userCubit.pickProfilePicFromGallery(
            _firstNameController.text, _lastNameController.text, context);
        _userCubit.setLoading(false, state.profile, false);
      },
    );
  }

  /*
  Widget _cancelOption() {
    return GestureDetector(
      child: _profileOptions(CANCEL_TEXT, Icons.cancel),
      onTap: () {
        appRouter.pop();
      },
    );
  }
  */

  Widget _horizontalLine(BuildContext context, UserState state) {
    double screenWidth = MediaQuery.sizeOf(context).width;
    return Center(
      child: Container(
        margin: const EdgeInsets.only(top: 15),
        height: 5,
        width: screenWidth / 3,
        decoration: BoxDecoration(
          color: AppTheme.carousalUnSelectedDotColor,
          borderRadius: BorderRadius.circular(30),
        ),
      ),
    );
  }

  Widget _submitButton(BuildContext context, double screenWidth,
      UserState state, UserCubit _userCubit) {
    bool isValidSubmission = _isValidFirstName && _isValidLastName;
    final _connectivityCubit = BlocProvider.of<ConnectivityCubit>(context);
    return ValueListenableBuilder(
        valueListenable: isDataLoading,
        builder: (BuildContext context, bool dataLoading, Widget? _) {
          print("_submitButton => $dataLoading, $isValidSubmission ");
          return Align(
            alignment: Alignment.centerRight,
            child: DebouncedButton(
              onPressed: isValidSubmission
                  ? () async {
                      if (_connectivityCubit.state is InternetConnected) {
                        FocusScope.of(context).unfocus();
                        // isDataLoading.value = true;
                        await _submitUserInfo(
                            context, screenWidth, state, _userCubit);
                      } else {
                        ///network not available popup
                        _showNetworkDisconnectedPopup(context);
                      }
                    }
                  : () {},
              disableButton: dataLoading || !isValidSubmission,
              child: CommonButtonWidget(
                color: !isValidSubmission || dataLoading
                    ? AppTheme.continueBtnDisabled
                    : AppTheme.submitBtnColor,
                borderRadius: 50,
                child: Text(
                  SLStrings.getTranslatedString(KEY_CONTINUE),
                  style:
                      LMSFonts.commonButtonStyle(16, AppTheme.whiteTextColor),
                ),
              ),
            ),
          );
        });
  }

  _submitUserInfo(BuildContext context, double screenWidth, UserState state,
      UserCubit _userCubit) async {
    _userCubit.setLoading(true, state.profile, false);
    state = _userCubit.state;
    if (state.profile != null &&
        (_userCubit.userFirstName != state.profile!.firstName ||
            _userCubit.userLastName != state.profile!.lastName ||
            _userCubit.userProfilePic != state.profile!.avatarUrl)) {
      // user updated details
      didUserInfoChanged = true;
    } else {
      // user didn't update the existing info
      if (_didChangeProfilePic) {
        didUserInfoChanged = _didChangeProfilePic;
      } else {
        didUserInfoChanged = false;
      }
    }
    await _updateUserInfo(context, state, _userCubit);
    _userCubit.setLoading(false, state.profile, false);
  }

  _updateUserInfo(
      BuildContext context, UserState state, UserCubit _userCubit) async {
    var route = ModalRoute.of(context);
    SharedPreferences _prefs = await SharedPreferences.getInstance();

    String userId = _prefs.getString(PREF_KEY_USER_ID) ?? '';
    File? imageFile = state.imageChosen;
    String filePath = imageFile != null && imageFile.path != NULL_PATH
        ? imageFile.path
        : state.profile?.avatarUrl ?? "";

    firstName = state.profile?.firstName?.trim() ?? "";
    lastName = state.profile?.lastName?.trim() ?? "";
    if (didUserInfoChanged) {
      await _userCubit.updateUserInfo(
          userId, firstName, lastName, filePath, imageFile);
      if (_userCubit.state is UserUpdatedState) {
        await _userCubit.getUserInfo(userId);
        if (route != null && route.isActive) {
          _showProfileUpdationSuccessPopup(context, state);
        }
      } else if (_userCubit.state is UserErrorState) {
        if (route != null && route.isActive) {
          isDataLoading.value = false;
          _showProfileUpdationFailure(
              context, _userCubit.state.error.toString());
        }
      }

      isProfileVerified = true;
    } else {
      isDataLoading.value = false;
      await _handleProfileChanges(state, context);
    }
  }

  _handleProfileChanges(UserState state, BuildContext context) async {
    var route = ModalRoute.of(context);
    if (state.orgList != null && state.orgList!.isEmpty) {
      if (route != null && route.isActive) {
        _showOrgEmptyPopup(context);
      }
    } else {
      await _handleNextNavigation(state, context);
    }
  }

  _handleNextNavigation(UserState state, BuildContext context) async {
    print(isFromSideMenu);
    if (state.orgList != null) {
      if (state.orgList!.length > 1) {
        isFromSideMenu == true
            ? kIsWeb
                ? Beamer.of(context).beamToNamed('/course-details', data: {
                    'courseId': COURSE_ID,
                    'courseName': COURSE_NAME,
                    'isFromLogin': true
                  })
                : appRouter.push(CourseDetailsViewRoute(
                    courseId: COURSE_ID,
                    courseName: COURSE_NAME,
                    isFromLogin: true))
            : kIsWeb
                ? Beamer.of(context).beamToNamed('/organization-view')
                : appRouter.push(OrganizationViewRoute());
      } else {
        SharedPreferences _prefs = await SharedPreferences.getInstance();

        String organizationId = state.orgList![0].orgId ?? "";
        _prefs.setString(PREF_KEY_ORG_ID, organizationId);

        isFromSideMenu == true
            ? kIsWeb
                ? Beamer.of(context).beamToReplacementNamed('/course-details',
                    data: {
                        'courseId': COURSE_ID,
                        'courseName': COURSE_NAME,
                        'isFromLogin': true
                      })
                : appRouter.popUntil((route) =>
                    route.settings.name == CourseDetailsViewRoute.name)
            : kIsWeb
                ? Beamer.of(context).beamToReplacementNamed('/topic-view')
                : appRouter.push(TopicListViewRoute());
      }
    }
  }

  _showProfileUpdationFailure(BuildContext context, String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return MultiActionDialogue(
          alertType: AlertType.error,
          title: SLStrings.getTranslatedString(KEY_ERROR),
          content: error,
          cancelBtnText: SLStrings.getTranslatedString(KEY_CLOSE),
          onCancelTap: () {
            kIsWeb ? Navigator.of(context).pop() : appRouter.pop();
          },
          onContinueTap: () {},
          confirmBtnText: '',
        );
      },
    );
  }

  _showOrgEmptyPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return MultiActionDialogue(
          alertType: AlertType.warning,
          title: SLStrings.getTranslatedString(KEY_WARNING),
          content: SLStrings.getTranslatedString(KEY_ORGANIZATION_EMPTY_ALERT),
          cancelBtnText: SLStrings.getTranslatedString(KEY_CLOSE),
          onCancelTap: () {
            kIsWeb ? Navigator.of(context).pop() : appRouter.pop();
          },
          onContinueTap: () {},
          confirmBtnText: '',
        );
      },
    );
  }

  _showProfileUpdationSuccessPopup(BuildContext context, UserState state) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              return;
            }
          },
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SUCCESS),
            alertType: AlertType.success,
            content:
                SLStrings.getTranslatedString(KEY_PROFILE_UPDATION_SUCCESS),
            confirmBtnText: SLStrings.getTranslatedString(KEY_CLOSE),
            onContinueTap: () async {
              Navigator.of(context).pop();
              isDataLoading.value = false;
              await _handleProfileChanges(state, context);
            },
            onCancelTap: () {},
            cancelBtnText: '',
          ),
        );
      },
    );
  }

  _showNetworkDisconnectedPopup(BuildContext context) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return MultiActionDialogue(
          title: SLStrings.getTranslatedString(KEY_ERROR),
          alertType: AlertType.error,
          content: SLStrings.getTranslatedString(KEY_NO_NETWORK_TRY_LATER),
          cancelBtnText: SLStrings.getTranslatedString(KEY_CLOSE),
          onCancelTap: () async {
            kIsWeb ? Navigator.of(context).pop() : appRouter.pop();
          },
          onContinueTap: () {},
          confirmBtnText: '',
        );
      },
    );
  }
}
