import '/src/utils/constants/strings.dart';

import '/src/utils/constants/helper.dart';
import 'package:flutter/foundation.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'dart:io';

import 'regex_strings.dart';

/// check youtube valid url for generating video thumbnail
bool isValidUrlForThumbnail(String url) {
  // Define a regular expression for a simple URL pattern
  final splitUrl = url.split('//');
  // Count occurrences of the specified word
  int count = 0;
  for (String w in splitUrl) {
    if (w.contains(HTTPS)) {
      count++;
    }
  }

  return count == 1;
}

bool checkForValidYoutubeUrls(String url) {
  try {
    final patterns = youtubeURLPatterns;

    for (RegExp pattern in patterns) {
      final Match? match = pattern.firstMatch(url);
      if (match != null) {
        return true; // Return true if match found
      }
    }
  } on Exception catch (e) {
    debugPrint("[Exception][chekcForValidYoutubeUrls] :${e.toString()}");
  }
  return false;
}

String extractVideoId(String url) {
  try {
    final patterns = youtubeURLPatterns;

    for (RegExp pattern in patterns) {
      final Match? match = pattern.firstMatch(url);
      if (match != null) {
        return match.group(1)!; // Return the video ID
      }
    }
  } on Exception catch (e) {
    debugPrint("[Exception][extractVideoId] :${e.toString()}");
  }

  return ''; // Return an empty string if no match is found
}

String generateYouTubeThumbnail(String videoId) {
  try {
    if (videoId.contains(HTTPS)) {
      videoId = videoId.replaceAll(HTTPS, '');
      return 'https://img.youtube.com/vi/$videoId/0.jpg';
    } else {
      return 'https://img.youtube.com/vi/$videoId/0.jpg';
    }
  } on HttpException catch (e) {
    debugPrint("[CourseListCubit][getThumbnailUrl][HttpException]: $e");
  } on Exception catch (e) {
    debugPrint("[CourseListCubit][getThumbnailUrl]: $e");
  }
  return '';
}

/*
TO DO: remove later
Future<String> generateYouTubeThumbnail(String videoUrl) async {
  try {
    bool _isValidUrl = isValidUrlForThumbnail(videoUrl);
    if (_isValidUrl) {
      String videoId = extractVideoId(videoUrl);
      videoId = videoId.isNotEmpty ? videoId : generateRandomNum().toString();
      final yt = YoutubeExplode();

      var video = await yt.videos.get(videoUrl).onError((error, stackTrace) {
        debugPrint(
            "[Exception][generateYouTubeThumbnail] :${error.toString()}");

        return yt.videos.get(videoUrl);
      });
      var thumbnailUrl = video.thumbnails.highResUrl;
      String thumbnailPath = (await getTemporaryDirectory()).path;

      var response = await HttpClient().getUrl(Uri.parse(thumbnailUrl));
      var request = await response.close();
      var bytes = await consolidateHttpClientResponseBytes(request);

      var file = File('$thumbnailPath/$videoId.jpg');
      await file.writeAsBytes(bytes);

      yt.close();
      return file.path;
    }
  } on Exception catch (e) {
    debugPrint("[Exception][generateYouTubeThumbnail] :${e.toString()}");
    return '';
  }
  return '';
}
*/

Future<String> generateVideoThumbnail(String videoUrl) async {
  try {
    final Uint8List? thumbnail = await VideoThumbnail.thumbnailData(
      video: videoUrl, // Replace with your video file path or network URL
      imageFormat: ImageFormat.JPEG,
      maxHeight: 150, // Set the desired height of the thumbnail
      quality: 90, // Set the quality of the thumbnail (0 - 100)
    );

    File thumbNailFile = await convertUint8ListToPath(thumbnail);
    return thumbNailFile.path;
  } on Exception catch (e) {
    debugPrint("[Exception][generateVideoThumbnail] :${e.toString()}");
    return '';
  }
}
