import '../models/course.dart';
import '../models/course_details.dart';
import '../models/resource_comment.dart';

abstract class CourseRepository {
  Future<List<Course>> getUserCourse(String topicId, String orgId);
  Future<List<CourseDetails>> fetchCourseDetailsInfo(String courseId);
  Future<List<CurrentAffairs>> fetchMoreCurrentAffairs();
  Future<List<ResourseComment>> fetchCommentsForId(String instanceId);
  Future<dynamic> uploadCommentsForId(Map<String, dynamic> jsonBody);
  Future<dynamic> setCourseProgress(
      String courseId, String instance, Map<String, dynamic> duration);
}
