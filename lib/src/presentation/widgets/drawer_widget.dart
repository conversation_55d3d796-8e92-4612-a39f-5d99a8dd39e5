import 'dart:io';

import 'package:beamer/beamer.dart';

import '../../config/app_config/preference_config.dart';

import '../../config/enums/alert_types.dart';
import '../../config/themes/app_dynamic_theme.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../cubits/app_config/app_config_cubit.dart';
import '/src/presentation/views/profile_review_view.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:auto_size_text/auto_size_text.dart';

import '../../config/app_config/sl_config.dart';
import '../../config/themes/theme_colors.dart';
import '../../domain/models/rank.dart';
import '/src/presentation/views/feedback_rating_view.dart';
import '/src/utils/constants/helper.dart';

import '../../config/themes/lms_fonts.dart';
import '../cubits/course_feedback/course_feedback_cubit.dart';
import '/src/config/themes/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/router/app_router.dart';
import '../../utils/constants/strings.dart';
import '../cubits/profile/profile_cubit.dart';
import '../cubits/profile/profile_state.dart';
import 'alert_popup/multi_action_dialogue.dart';
import 'alert_popup/confirmation_popup.dart';
import 'package:provider/provider.dart' as provider;

class DrawerWidget extends StatelessWidget {
  final String name;
  final String email;
  final String avatarUrl;
  final String? courseName;
  final bool hasCourseAccess;
  final bool hasProfileAccess;
  final bool hasSubscriptionAccess;

  const DrawerWidget({
    Key? key,
    required this.name,
    required this.email,
    required this.avatarUrl,
    this.courseName,
    required this.hasCourseAccess,
    required this.hasProfileAccess,
    required this.hasSubscriptionAccess,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _userCubit = BlocProvider.of<UserCubit>(context);
    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);

    return Scaffold(
      backgroundColor: appDynamicTheme.sidebarBackgroundColor,
      body: BlocBuilder<UserCubit, UserState>(
        bloc: _userCubit,
        builder: (context, state) {
          return LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
            double webHeight = constraints.maxHeight;
            Widget gapConstraintWidget =
                webHeight <= 440 ? const Spacer() : const SizedBox(height: 30);
            return SizedBox(
              child: Column(
                children: <Widget>[
                  _topContainer(context, constraints.maxHeight,
                      _userCubit.state, constraints.maxWidth),
                  _divider(),
                  const SizedBox(height: 34),
                  // _courseDetails(context),
                  // gapConstraintWidget,
                  hasProfileAccess ? _profileTile(context) : Container(),
                  // const Spacer(),
                  // const SizedBox(height: 34),
                  hasProfileAccess ? gapConstraintWidget : Container(),

                  _rankTile(context),
                  //   const SizedBox(height: 34),
                  // const Spacer(),
                  gapConstraintWidget,

                  _liveClassTile(context),
                  gapConstraintWidget,
                  /* _newsTile(context),
                  const Spacer(),
                  _settingsTile(context),
                  const Spacer(),
                  _notificationsTile(context),
                  const Spacer(),
                  _feebacktile(context),
                  const Spacer(),*/
                  _aboutTile(context),
                  // const SizedBox(height: 34),
                  //  const Spacer(),
                  // _shareTile(context),
                  //   const SizedBox(height: 34),
                  //  const Spacer(),
                  gapConstraintWidget,
                  _subscriptionTile(context),
                  hasSubscriptionAccess ? gapConstraintWidget : Container(),
                  // _themeSwitchTile(context),
                  // gapConstraintWidget,
                  _signoutTile(context, _userCubit, state),
                  //  const SizedBox(height: 34),
                  const Spacer(),
                  _divider(),
                  webHeight <= 440
                      ? const Spacer()
                      : const SizedBox(height: 22),
                  _appVersion(),
                  webHeight <= 440
                      ? const Spacer()
                      : const SizedBox(height: 22),
                ],
              ),
            );
          });
        },
      ),
    );
  }

  Widget _topContainer(context, maxHeight, state, maxWidth) {
    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    return Container(
      padding: const EdgeInsets.only(left: 16, right: 10, top: 10),
      decoration: const BoxDecoration(),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const SizedBox(height: 10),
            Row(
              children: [
                _profileIconWidget(state, maxWidth),
                const SizedBox(width: 15),
                _userInfoWidget(state, maxWidth, appDynamicTheme),
                const SizedBox(width: 5),
                _closeBtn(context),
              ],
            ),
            kIsWeb
                ? const SizedBox(height: 20)
                : SizedBox(height: Platform.isAndroid ? 20 : 0),
          ],
        ),
      ),
    );
  }

  Widget _profileIconWidget(state, maxWidth) {
    return ProfileImageWidget(
      avatarUrl: state.profile?.avatarUrl,
      showCamera: false,
      radius: 50,
      onEditTapped: () {},
    );
  }

  Widget _closeBtn(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          color: Colors.transparent,
          child: Align(
            alignment: Alignment.centerRight,
            child: Image.asset('$ASSETS_PATH/close.png', height: 30, width: 30),
          ),
        ),
      ),
    );
  }

  Widget _userInfoWidget(
      UserState state, double maxWidth, AppDynamicTheme appDynamicTheme) {
    String firstName =
        convertToCamelCase(state.profile?.firstName ?? "").trim();
    String lastName = convertToCamelCase(state.profile?.lastName ?? "").trim();
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AutoSizeText(
            firstName + ' ' + lastName,
            style: LMSFonts.semiBoldFont(
              16,
              AppTheme.whiteColor,
            ),
          ),
          _emailWidget(state, appDynamicTheme),
        ],
      ),
    );
  }

  Widget _emailWidget(UserState state, AppDynamicTheme appDynamicTheme) {
    return state.profile != null && state.profile!.email.isNotEmpty
        ? Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image.asset(
              //   '$ASSETS_PATH/phone.png',
              //   height: 14,
              //   width: 14,
              // ),
              const Padding(
                padding: EdgeInsets.only(top: 3),
                child: Icon(
                  Icons.mail,
                  color: AppTheme.whiteColor,
                  size: 14,
                ),
              ),
              const SizedBox(width: 5),
              Expanded(
                child: AutoSizeText(
                  state.profile != null ? (state.profile?.email ?? '') : '',
                  // maxLines: 2,
                  style: LMSFonts.regularFontWithHeight(
                      14, AppTheme.whiteColor, 0),
                ),
              )
            ],
          )
        : Container();
  }

  Widget _topicWidget(BuildContext context, double maxHeight) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () => showWarning(context),
        child: Container(
          margin: const EdgeInsets.only(right: 5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50.0),
            border: Border.all(color: AppTheme.whiteColor.withOpacity(0.8)),
          ),
          padding: const EdgeInsets.all(3),
          // alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset('$ASSETS_PATH/topic_sidemenu.png',
                  height: 24, width: 24),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  TOPIC_NAME,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: LMSFonts.regularFontWithHeight(
                      14, AppTheme.whiteTextColor, 0),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _divider() {
    return Divider(
      thickness: 0.7,
      height: 1,
      color: AppTheme.whiteColor.withOpacity(0.5),
    );
  }

  Widget _courseDetails(BuildContext context) {
    return DrawerListTile(
        title: 'Subjects View', // SLStrings.getTranslatedString(KEY_RANKING),
        icon: 'sub_topic_closed',
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/CourseDetailsViewRoute')
              : appRouter.push(CourseDetailsViewRoute(
                  courseId: COURSE_ID,
                  courseName: COURSE_NAME,
                ));
        });
  }

  Widget _profileTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_PROFILE),
        icon: 'profile_sidemenu',
        iconSize: 18,
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/profile-review')
              : appRouter.push(ProfileReviewRoute());
        });
  }

  Widget _rankTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_RANKING),
        icon: 'ranking',
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/rank-list', data: {
                  'isCourseRankList': true,
                  'rankListForExam': const <Rank>[],
                })
              : appRouter.push(RankListViewRoute(
                  isCourseRankList: true, rankListForExam: const <Rank>[]));
        });
  }

  Widget _liveClassTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_LIVE_CLASS),
        icon: 'ranking',
        onTapCallback: () {
          Navigator.of(context).pop();
          appRouter.push(LiveClassViewRoute());
        });
  }

  Widget _newsTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_NEWS),
        icon: 'news',
        onTapCallback: () => Navigator.of(context).pop());
  }

  Widget _settingsTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_SETTINGS),
        icon: 'settings',
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/settings')
              : appRouter.push(SettingsViewRoute());
        });
  }

  Widget _notificationsTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_NOTIFICATIONS),
        icon: 'notification_sidemenu',
        onTapCallback: () => Navigator.of(context).pop());
  }

  Widget _aboutTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_ABOUT),
        icon: 'about',
        onTapCallback: () {
          Navigator.of(context).pop();
          kIsWeb
              ? Beamer.of(context).beamToNamed('/about')
              : appRouter.push(const AboutViewRoute());
        });
  }

  Widget _feebacktile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_FEEDBACK),
        icon: 'feedback_sidemenu',
        onTapCallback: () {
          Navigator.of(context).pop();
          showDialog(
              context: context,
              barrierDismissible: true,
              builder: (BuildContext context) {
                TextEditingController xx = TextEditingController();
                int selectedUptoIndex = 0;
                return BlocBuilder<CourseFeedbackCubit, CourseFeedbackState>(
                  builder: (context, state) {
                    if (state is CourseFeedbackUpdate) {
                      selectedUptoIndex = state.uptoIndex;
                    }
                    return FeedbackDialouge(
                      feedbackController: xx,
                      selectedUptoIndex: selectedUptoIndex,
                      updateStars: (val) {
                        context
                            .read<CourseFeedbackCubit>()
                            .updateStars(true, val);
                      },
                      onSubmit: () => kIsWeb
                          ? Beamer.of(context).beamBack()
                          : appRouter.popForced(),
                    );
                  },
                );
              });
        });
  }

  Widget _shareTile(BuildContext context) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_SHARE),
        icon: 'share',
        onTapCallback: () {
          Navigator.of(context).pop();
          shareAppInfo();
        });
  }

  Widget _signoutTile(
      BuildContext context, UserCubit _userCubit, UserState state) {
    return DrawerListTile(
        title: SLStrings.getTranslatedString(KEY_SIGNOUT),
        icon: 'signout',
        onTapCallback: () async {
          Navigator.of(context).pop();
          _showSignoutConfirmationPopup(context, _userCubit, state);
        });
  }

  Widget _subscriptionTile(BuildContext context) {
    return Visibility(
      visible: hasSubscriptionAccess,
      child: DrawerListTile(
          title: SLStrings.getTranslatedString(KEY_SUBSCRIPTION),
          icon: 'subscription',
          onTapCallback: () {
            Navigator.of(context).pop();
            kIsWeb
                ? Beamer.of(context).beamToNamed('/subscription-view')
                : appRouter.push(SubscriptionViewRoute());
          }),
    );
  }

  Widget _themeSwitchTile(BuildContext context) {
    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    final isDark = appDynamicTheme.themeName == 'dark';

    return DrawerListTile(
      title: isDark ? 'Light Mode' : 'Dark Mode',
      icon: isDark ? 'light' : 'dark',
      onTapCallback: () {
        // appDynamicTheme.toggleTheme();
        Navigator.of(context).pop();
      },
    );
  }

  void _showSignoutConfirmationPopup(
      BuildContext context, UserCubit userCubit, UserState state) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return MultiActionDialogue(
          title: SLStrings.getTranslatedString(KEY_WARNING),
          content: SLStrings.getTranslatedString(KEY_SIGNOUT_MSG),
          alertType: AlertType.warning,
          cancelBtnText: SLStrings.getTranslatedString(KEY_NO),
          confirmBtnText: SLStrings.getTranslatedString(KEY_YES),
          onContinueTap: () async {
            final prefs = await SharedPreferences.getInstance();

            prefs.setBool(PREF_KEY_SIGNOUT, true);
            prefs.setBool(PREF_KEY_NAVIGATING_AFTER_LOGOUT, true);

            // Log initial signout intent
            await _logUserActivity(
              dialogContext: dialogContext,
              details: 'User signed out',
              result: 'success',
              comment: 'Signed out at ${DateTime.now()}',
            );

            // Attempt signout
            await userCubit.userSignout();
            final newState = userCubit.state;

            if (newState is UserSignoutError) {
              await _logUserActivity(
                dialogContext: dialogContext,
                details: 'Signout failure',
                result: newState.error,
                comment: 'Signout failed at ${DateTime.now()}',
              );
            }

            await clearCurrentUserInfo();
          },
          onCancelTap: () {
            kIsWeb ? Navigator.of(dialogContext).pop() : appRouter.popForced();
          },
        );
      },
    );
  }

  Future<void> _logUserActivity(
      {required BuildContext dialogContext,
      required String details,
      required String result,
      required String comment}) {
    final appConfigCubit = BlocProvider.of<AppConfigCubit>(dialogContext);
    final reqJson = {
      'activity_type': 'Authentication',
      'screen_name': 'Sign Out',
      'action_details': details,
      'target_id': USER_ID,
      'action_comment': comment,
      'log_result': result,
    };

    return appConfigCubit.setUserActivityLog(reqJson);
  }

  Widget _appVersion() {
    return Container(
      alignment: Alignment.bottomCenter,
      margin: const EdgeInsets.only(right: 10),
      child: Text(
        'v${SLConfig.APP_VERSION}',
        style: LMSFonts.regularFont(14, AppTheme.whiteColor),
      ),
    );
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            cancelBtnText: SLStrings.getTranslatedString(KEY_OK),
            confirmBtnText: '',
            alertType: AlertType.error,
            onContinueTap: () =>
                kIsWeb ? Navigator.of(context).pop() : appRouter.popForced(),
            onCancelTap: () {
              kIsWeb
                  ? Beamer.of(context).beamToReplacementNamed('/login-view')
                  : appRouter.popUntil((route) =>
                      route.settings.name == LoginEmailViewRoute.name);
            });
      },
    );
  }
}

class DrawerListTile extends StatelessWidget {
  final String title;
  final String icon;
  final double iconSize;
  final VoidCallback onTapCallback;

  const DrawerListTile(
      {super.key,
      required this.title,
      required this.icon,
      this.iconSize = 24,
      required this.onTapCallback});

  @override
  Widget build(BuildContext context) {
    final appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () => onTapCallback(),
        child: Container(
          color: Colors.transparent,
          padding: const EdgeInsets.only(left: 16, top: 5, bottom: 5),
          child: Row(
            children: [
              SizedBox(
                height: 24,
                width: 24,
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: (24 - iconSize) / 2),
                  child: icon == 'light'
                      ? Icon(
                          Icons.light_mode,
                          color: appDynamicTheme.iconColor,
                        )
                      : icon == 'dark'
                          ? Icon(
                              Icons.dark_mode,
                              color: appDynamicTheme.iconColor,
                            )
                          : Image.asset(
                              '$ASSETS_PATH/$icon.png',
                              color: AppTheme.whiteColor,
                            ),
                ),
              ),
              const SizedBox(width: 20),
              Text(
                title,
                style: LMSFonts.tabItem(appDynamicTheme),
              ),
            ],
          ),
          // onTap: () => onTapCallback(),
        ),
      ),
    );
  }
}
