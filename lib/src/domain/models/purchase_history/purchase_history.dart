// To parse this JSON data, do
//
//     final purchaseHistory = purchaseHistoryFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

import '../../../utils/constants/helper.dart';
import '../subscription_plan/subscription_plan.dart';

part 'purchase_history.g.dart';

PurchaseHistory purchaseHistoryFromJson(String str) =>
    PurchaseHistory.fromJson(json.decode(str));

String purchaseHistoryToJson(PurchaseHistory data) =>
    json.encode(data.toJson());

@JsonSerializable()
class PurchaseHistory {
  @JsonKey(name: "result")
  final PurchaseHistoryResult? result;
  @JsonKey(name: "status")
  final String? status;

  PurchaseHistory({
    this.result,
    this.status,
  });

  factory PurchaseHistory.fromJson(Map<String, dynamic> json) =>
      _$PurchaseHistoryFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseHistoryToJson(this);
}

@JsonSerializable()
class PurchaseHistoryResult {
  @JsonKey(name: "pending_plan_lists_by_user")
  final List<PendingPlanListsByUser>? pendingPlanListsByUser;
  @JsonKey(name: "purchased_plan_lists_by_user")
  final List<PurchasedPlanListsByUser>? purchasedPlanListsByUser;

  PurchaseHistoryResult({
    this.pendingPlanListsByUser,
    this.purchasedPlanListsByUser,
  });

  factory PurchaseHistoryResult.fromJson(Map<String, dynamic> json) =>
      _$PurchaseHistoryResultFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseHistoryResultToJson(this);
}

@JsonSerializable()
class PendingPlanListsByUser {
  @JsonKey(name: "price")
  final int? price;
  @JsonKey(name: "currency")
  final Currency? currency;
  @JsonKey(name: "status")
  final String? status;
  @JsonKey(
      fromJson: PendingPlanListsByUser._uppercaseFirstName, name: 'plan_name')
  final String? planName;
  @JsonKey(name: "subscription_valid_to")
  final DateTime? subscriptionValidTo;
  @JsonKey(name: "subscription_valid_from")
  final DateTime? subscriptionValidFrom;

  PendingPlanListsByUser({
    this.status,
    this.planName,
    this.price,
    this.currency,
    this.subscriptionValidTo,
    this.subscriptionValidFrom,
  });

  static String? _uppercaseFirstName(String? plan_name) =>
      convertToCamelCase(plan_name ?? '');

  factory PendingPlanListsByUser.fromJson(Map<String, dynamic> json) =>
      _$PendingPlanListsByUserFromJson(json);

  Map<String, dynamic> toJson() => _$PendingPlanListsByUserToJson(this);
}

@JsonSerializable()
class PurchasedPlanListsByUser {
  @JsonKey(name: "price")
  final int? price;
  @JsonKey(name: "currency")
  final Currency? currency;
  @JsonKey(name: "plan_name")
  final String? planName;
  @JsonKey(name: "is_expired")
  bool isExpired;
  @JsonKey(name: "purchase_date")
  final DateTime? purchaseDate;
  @JsonKey(name: "subscription_status")
  final String? subscriptionStatus;
  @JsonKey(name: "subscription_valid_to")
  final DateTime? subscriptionValidTo;
  @JsonKey(name: "subscription_valid_from")
  final DateTime? subscriptionValidFrom;
  @JsonKey(name: "user_subscription_end_date")
  final DateTime? userSubscriptionEndDate;
  @JsonKey(name: "user_subscription_start_date")
  final DateTime? userSubscriptionStartDate;

  PurchasedPlanListsByUser({
    this.price,
    this.currency,
    this.planName,
    this.isExpired = false,
    this.purchaseDate,
    this.subscriptionStatus,
    this.subscriptionValidTo,
    this.subscriptionValidFrom,
    this.userSubscriptionEndDate,
    this.userSubscriptionStartDate,
  });

  factory PurchasedPlanListsByUser.fromJson(Map<String, dynamic> json) =>
      _$PurchasedPlanListsByUserFromJson(json);

  Map<String, dynamic> toJson() => _$PurchasedPlanListsByUserToJson(this);
}
