part of 'login_cubit.dart';

abstract class LoginEmailState extends Equatable {
  final bool isLoading;
  final String? message;
  final List<Organization>? organization;

  const LoginEmailState({
    this.isLoading = false,
    this.message,
    this.organization = const [],
  });

  @override
  List<Object?> get props => [isLoading, message, organization];
}

class LoginEmailInitial extends LoginEmailState {}

class LoginEmailLoading extends LoginEmailState {
  const LoginEmailLoading({super.isLoading});
}

class LoginEmailError extends LoginEmailState {
  final String error;
  const LoginEmailError({required this.error});

  @override
  List<Object> get props => [error];
}

class LoginEmailSuccess extends LoginEmailState {
  String? userId;
  List<Organization> orgList;

  LoginEmailSuccess({required this.userId, required this.orgList});

  @override
  List<Object> get props => [userId!, orgList];
}

class LoginEmailErrorValidation extends LoginEmailState {
  final String error;
  final bool showError;
  const LoginEmailErrorValidation(
      {required this.error, required this.showError});

  @override
  List<Object> get props => [error, showError];
}

class LoginPasswordValidation extends LoginEmailState {
  final String PasswordError;
  final bool showError;

  const LoginPasswordValidation(
      {required this.PasswordError, required this.showError});

  @override
  List<Object> get props => [PasswordError, showError];
}

class LoginButtonColor extends LoginEmailState {
  final bool buttonColor;

  const LoginButtonColor({required this.buttonColor});

  @override
  List<Object> get props => [buttonColor];
}

class AlertMessageVisibility extends LoginEmailState {
  final bool showAlert;

  const AlertMessageVisibility({required this.showAlert});

  @override
  List<Object> get props => [showAlert];
}

class ChangeAppLangauge extends LoginEmailState {
  final String selectedLang;

  const ChangeAppLangauge({required this.selectedLang});

  @override
  List<Object> get props => [selectedLang];
}
