import 'package:SmartLearn/src/config/themes/app_theme.dart';
import 'package:flutter/material.dart';

class CommonButtonWidget extends StatelessWidget {
  @required
  final Color? color;
  final double elevation;
  @required
  final Widget child;
  final Size? minimumSize;
  final double? horizontalPadding;
  final double? borderRadius;
  final Color borderColor;

  const CommonButtonWidget({
    super.key,
    this.color,
    this.elevation = 0.0,
    this.minimumSize,
    required this.child,
    this.horizontalPadding = 12.0,
    this.borderRadius = 10,
    this.borderColor = AppTheme.whiteTextColor,
  });

  @override
  Widget build(BuildContext context) {
    Size screenSize = MediaQuery.sizeOf(context);
    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: 50,
        minWidth: minimumSize?.width ?? screenSize.width,
      ),
      child: Container(
        child: child,
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding ?? 12.0, vertical: 5),
        decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(borderRadius ?? 10),
            border: Border.all(color: borderColor, width: 2)),
      ),
    );
  }
}
