// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_point_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CheckPointData _$CheckPointDataFromJson(Map<String, dynamic> json) =>
    CheckPointData(
      status: json['status'] as String?,
      checkPoints: (json['check_points'] as List<dynamic>?)
          ?.map((e) => CheckPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CheckPointDataToJson(CheckPointData instance) =>
    <String, dynamic>{
      'status': instance.status,
      'check_points': instance.checkPoints,
    };

CheckPoint _$CheckPointFromJson(Map<String, dynamic> json) => CheckPoint(
      orgId: json['org_id'] as String?,
      sequence: (json['sequence'] as num?)?.toInt(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      createdBy: json['created_by'] as String?,
      startPage: (json['start_page'] as num?)?.toInt(),
      startTime: json['start_time'] as String?,
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      updatedBy: json['updated_by'] as String?,
      instanceId: json['instance_id'] as String?,
      moduleName: json['module_name'] as String?,
      isMandatory: json['is_mandatory'] as bool?,
      checkpointId: json['checkpoint_id'] as String?,
      moduleTypeId: json['module_type_id'] as String?,
      checkpointName: json['checkpoint_name'] as String?,
      checkpointType: json['checkpoint_type'] as String?,
      courseModuleId: json['course_module_id'] as String?,
      instanceEndTime: json['instance_end_time'] == null
          ? null
          : DateTime.parse(json['instance_end_time'] as String),
    );

Map<String, dynamic> _$CheckPointToJson(CheckPoint instance) =>
    <String, dynamic>{
      'org_id': instance.orgId,
      'sequence': instance.sequence,
      'created_at': instance.createdAt?.toIso8601String(),
      'created_by': instance.createdBy,
      'start_page': instance.startPage,
      'start_time': instance.startTime,
      'updated_at': instance.updatedAt?.toIso8601String(),
      'updated_by': instance.updatedBy,
      'instance_id': instance.instanceId,
      'module_name': instance.moduleName,
      'is_mandatory': instance.isMandatory,
      'checkpoint_id': instance.checkpointId,
      'module_type_id': instance.moduleTypeId,
      'checkpoint_name': instance.checkpointName,
      'checkpoint_type': instance.checkpointType,
      'course_module_id': instance.courseModuleId,
      'instance_end_time': instance.instanceEndTime?.toIso8601String(),
    };
