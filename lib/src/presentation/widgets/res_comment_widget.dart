import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/models/resource_comment.dart';
import '../../utils/constants/strings.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../cubits/course_list/course_list_cubit.dart';
import '../cubits/app_config/app_config_cubit.dart';
import '../views/course_video_view.dart';
import 'profile_placeholder_widget.dart';

class CommentFeatureWidget extends StatelessWidget {
  final String resourceId;
  final List<ResourseComment> comments;
  final TextEditingController commentController;
  final ValueNotifier<int> tabIndex;
  final ValueNotifier<AddCommentFunc> commentNotifier;
  final CourseListCubit courseCubit;
  final bool isFullScreen;
  final Function() enableClickOnPlayer;

  const CommentFeatureWidget({
    Key? key,
    required this.resourceId,
    required this.comments,
    required this.commentController,
    required this.tabIndex,
    required this.commentNotifier,
    required this.courseCubit,
    required this.isFullScreen,
    required this.enableClickOnPlayer,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _commentsHead(),
        const SizedBox(height: 8),
        _chatListView(context),
      ],
    );
  }

  Widget _commentsHead() {
    return Visibility(
      visible: comments.isNotEmpty && resourceId.isNotEmpty,
      child: Text(
        'Comments',
        style: LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
      ),
    );
  }

  Widget _commentsTabItem(String label, bool isActive) {
    return Container(
      height: 35,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: isActive ? Colors.white : Colors.transparent,
      ),
      width: double.infinity / 1,
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Tab(text: label),
    );
  }

  Widget _chatListView(BuildContext context) {
    int feedbackCount = comments
        .where((element) => element.commentType == CommentType.FEEDBACK)
        .toList()
        .length;

    int suggestionCount = comments
        .where((element) => element.commentType == CommentType.SUGGESTION)
        .toList()
        .length;
    return Visibility(
      visible: resourceId.isNotEmpty && comments.isNotEmpty,
      child: ValueListenableBuilder<int>(
        valueListenable: tabIndex,
        builder: (context, updatedTabIndex, __) {
          return DefaultTabController(
            length: 2,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade300,
                  ),
                  child: TabBar(
                    labelColor: AppTheme.courseVideoPrimaryTextColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: AppTheme.transparentColor,
                    padding: EdgeInsets.zero,
                    tabs: [
                      _commentsTabItem(
                          'Feedback ($feedbackCount)', tabIndex.value == 0),
                      _commentsTabItem(
                          'Suggestion ($suggestionCount)', tabIndex.value == 1),
                    ],
                    onTap: (index) {
                      tabIndex.value = index;
                      commentNotifier.value = commentNotifier.value.copyWith(
                        selectedCommentType:
                            index == 0 ? 'Feedback' : 'Suggestion',
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
                _buildCommentList(
                  context,
                  comments
                      .where((comment) =>
                          comment.commentType ==
                          (tabIndex.value == 0
                              ? CommentType.FEEDBACK
                              : CommentType.SUGGESTION))
                      .toList(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCommentList(
      BuildContext context, List<ResourseComment> comments) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(comments.length, (index) {
        final comment = comments[index];
        return Column(
          children: [
            CommentItemWidget(
              commentObj: comment,
              onReplyTap: () => _showCommentBox(
                context,
                parentId: comment.parentId,
                roleName: comment.roleName,
              ),
            ),
            if (comment.children != null)
              Padding(
                padding: const EdgeInsets.only(left: 34),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(
                    comment.children!.length,
                    (childIndex) => CommentItemWidget(
                      commentObj: comment.children![childIndex],
                    ),
                  ),
                ),
              ),
          ],
        );
      }),
    );
  }

  void _showCommentBox(BuildContext context,
      {required String? parentId, required RoleName? roleName}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AddCommentDialogue(
          commentController: commentController,
          courseCubit: courseCubit,
          selectedType: commentNotifier.value.selectedCommentType.isEmpty
              ? (tabIndex.value == 0 ? 'Feedback' : 'Suggestion')
              : commentNotifier.value.selectedCommentType,
          enableSubmitBtn: commentNotifier.value.enableSubmitBtn,
          enableDropdown: parentId == null,
          onSubmit: () async {
            await _addNewComment(
              dialogContext,
              commentController.text.trim(),
              parentId: parentId,
              roleName: roleName,
            );
            commentController.clear();
          },
          onTypeSelected: (val) {
            commentNotifier.value = commentNotifier.value.copyWith(
              selectedCommentType: val,
            );
          },
          onChanged: (val) {
            commentNotifier.value = commentNotifier.value.copyWith(
              enableSubmitBtn: commentController.text.trim().isNotEmpty,
            );
          },
          onClose: () {
            enableClickOnPlayer();
            Navigator.of(dialogContext).pop();
          },
        );
      },
    );
  }

  Future<void> _addNewComment(
    BuildContext context,
    String comment, {
    required String? parentId,
    required RoleName? roleName,
  }) async {
    final appConfigCubit = BlocProvider.of<AppConfigCubit>(context);

    // Create comment request body
    Map<String, dynamic> jsonReqBody = {
      "instance_id": resourceId,
      "user_id": USER_ID,
      "comment_data": {
        if (parentId != null) "parent_id": parentId,
        "subject": TOPIC_ID,
        "message": comment,
        "type": commentNotifier.value.selectedCommentType,
        "activity_type": ResActivityType.comment.name
      }
    };

    // Upload comment
    await courseCubit.uploadComment(jsonReqBody);
    final state = courseCubit.state;

    // Log activity
    final isSuccess = state is CourseCommentsUploaded;
    await appConfigCubit.setUserActivityLog({
      'activity_type': 'Course_Resource',
      'screen_name': 'Comments',
      'action_details': isSuccess ? 'Comment added' : 'Add comment failure',
      'target_id': resourceId,
      'action_comment': isSuccess
          ? 'Commented at ${DateTime.now()}'
          : 'Comment failed at ${DateTime.now()}',
      'log_result': isSuccess
          ? 'success'
          : state is CommentsError
              ? state.error
              : 'unknown error',
    });

    Navigator.pop(context);

    if (state is CommentsError) {
      _showCommentError(context, state.error);
    } else {
      commentController.clear();
      _showSubmissionDialogue(context);
    }
  }

  void _showCommentError(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Error'),
        content: Text(error),
        actions: [
          TextButton(
            child: Text('OK'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  void _showSubmissionDialogue(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Success'),
        content: Text('Comment submitted successfully'),
        actions: [
          TextButton(
            child: Text('OK'),
            onPressed: () {
              enableClickOnPlayer();
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}

class CommentItemWidget extends StatelessWidget {
  final ResourseComment commentObj;
  final VoidCallback? onReplyTap;

  const CommentItemWidget({
    Key? key,
    required this.commentObj,
    this.onReplyTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _profileIcon(),
          const SizedBox(width: 14),
          _commentContent(),
          const SizedBox(width: 10),
          _commentMetadata(),
          const SizedBox(width: 5),
        ],
      ),
    );
  }

  Widget _profileIcon() {
    return commentObj.profilePic != null
        ? Container(
            height: 40,
            width: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              border: Border.all(
                width: 2,
                color: AppTheme.whiteColor,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.blackColor.withOpacity(0.1),
                  blurRadius: 1,
                  spreadRadius: 1,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: CircleAvatar(
              backgroundColor: AppTheme.transparentColor,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5.0),
                child: commentObj.profilePic!.trim().isNotEmpty
                    ? CachedNetworkImage(
                        imageUrl: commentObj.profilePic!,
                        fit: BoxFit.cover,
                        placeholder: (context, url) =>
                            const CircularProgressIndicator(),
                        errorWidget: (context, url, error) =>
                            const ProfilePlaceholderWidget(maxWidth: 44 * 2),
                      )
                    : const ProfilePlaceholderWidget(maxWidth: 44 * 2),
              ),
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _commentContent() {
    return Expanded(
      child: Container(
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              commentObj.user,
              style: LMSFonts.mediumFont(
                14,
                AppTheme.courseVideoPrimaryTextColor,
                1.2,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              commentObj.message.toString().trim(),
              style: LMSFonts.regularFontWithHeight(
                12,
                AppTheme.courseVideoPrimaryTextColor,
                1.2,
              ),
            ),
            const SizedBox(height: 5),
            if (commentObj.roleName == RoleName.ADMIN && onReplyTap != null)
              GestureDetector(
                onTap: onReplyTap,
                child: Container(
                  color: Colors.transparent,
                  child: Row(
                    children: [
                      const Icon(
                        Icons.reply,
                        color: AppTheme.coursePecentageTxtGrey,
                        size: 16,
                      ),
                      Text(
                        ' Reply',
                        style: LMSFonts.regularFontWithHeight(
                          12,
                          AppTheme.coursePecentageTxtGrey,
                          1.2,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            SizedBox(height: commentObj.commentedTime != null ? 5 : 0),
          ],
        ),
      ),
    );
  }

  Widget _commentMetadata() {
    return Container(
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const SizedBox(height: 2),
          _commentStatusIcon(),
          const SizedBox(height: 7),
          Text(
            commentObj.commentedTime != null
                ? AppDateFormatter().formatDatedmmmmy(commentObj.commentedTime!)
                : '',
            style: LMSFonts.regularFontWithHeight(
              12,
              AppTheme.courseCommentDate,
              1.2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _commentStatusIcon() {
    IconData iconData;
    Color iconColor;

    switch (commentObj.commentStatus.name.toLowerCase()) {
      case 'approved':
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case 'pending':
        iconData = Icons.hourglass_empty;
        iconColor = Colors.orange;
        break;
      case 'rejected':
        iconData = Icons.cancel;
        iconColor = Colors.red;
        break;
      default:
        iconData = Icons.help_outline;
        iconColor = Colors.grey;
    }

    return Icon(
      iconData,
      color: iconColor,
      size: 20,
    );
  }
}

class AddCommentDialogue extends StatelessWidget {
  final TextEditingController commentController;
  final CourseListCubit courseCubit;
  final String selectedType;
  final bool enableSubmitBtn;
  final bool enableDropdown;
  final Function() onSubmit;
  final Function(String) onTypeSelected;
  final Function(String) onChanged;
  final Function() onClose;

  const AddCommentDialogue({
    Key? key,
    required this.commentController,
    required this.courseCubit,
    required this.selectedType,
    required this.enableSubmitBtn,
    required this.enableDropdown,
    required this.onSubmit,
    required this.onTypeSelected,
    required this.onChanged,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _dialogueHeader(context),
            const SizedBox(height: 20),
            if (enableDropdown) _commentTypeOptions(),
            const SizedBox(height: 16),
            _commentTextField(),
            const SizedBox(height: 20),
            _actionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _dialogueHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Add Comment',
          style: LMSFonts.semiBoldFont(18, AppTheme.primaryTextColorBlack),
        ),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: onClose,
        ),
      ],
    );
  }

  Widget _commentTypeOptions() {
    return Row(
      children: [
        _commentTypeChip('Feedback', selectedType == 'Feedback'),
        const SizedBox(width: 10),
        _commentTypeChip('Suggestion', selectedType == 'Suggestion'),
      ],
    );
  }

  Widget _commentTypeChip(String title, bool isSelected) {
    return GestureDetector(
      onTap: () => onTypeSelected(title),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          title,
          style: LMSFonts.mediumFont(14,
              isSelected ? Colors.white : AppTheme.primaryTextColorBlack, 1.0),
        ),
      ),
    );
  }

  Widget _commentTextField() {
    return TextField(
      controller: commentController,
      maxLines: 4,
      decoration: InputDecoration(
        hintText: 'Write your comment here...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.primaryColor),
        ),
      ),
      onChanged: onChanged,
    );
  }

  Widget _actionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: onClose,
          child: Text(
            'Cancel',
            style: LMSFonts.mediumFont(14, AppTheme.primaryTextColorBlack, 1.0),
          ),
        ),
        const SizedBox(width: 10),
        ElevatedButton(
          onPressed: enableSubmitBtn ? onSubmit : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'Submit',
            style: LMSFonts.mediumFont(14, Colors.white, 1.0),
          ),
        ),
      ],
    );
  }
}
