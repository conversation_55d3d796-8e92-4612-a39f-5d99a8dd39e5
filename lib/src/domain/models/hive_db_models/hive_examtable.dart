import 'package:hive_flutter/hive_flutter.dart';
part 'hive_examtable.g.dart';

@HiveType(typeId: 0)
class HiveExamTable {
  @HiveField(0)
  final int id;
  @HiveField(1)
  final String examId;
  @HiveField(2)
  final String quizAttemptId;
  @HiveField(3)
  final String examName;
  @HiveField(4)
  final String description;
  @HiveField(5)
  final String duration;
  @HiveField(6)
  final String endTime;
  @HiveField(7)
  final String startTime;
  @HiveField(8)
  final String passMark;
  @HiveField(9)
  final String mainTopic;
  @HiveField(10)
  final String totalMark;
  @HiveField(11)
  final String numOfQuestions;
  @HiveField(12)
  final dynamic questAnswers;
  @HiveField(13)
  final String networkStatus;
  @HiveField(14)
  final int examUploadStatus;
  @HiveField(15)
  final int examCompletedStatus;
  @HiveField(16)
  HiveExamTable(
    this.id,
    this.examId,
    this.quizAttemptId,
    this.examName,
    this.description,
    this.duration,
    this.endTime,
    this.startTime,
    this.passMark,
    this.mainTopic,
    this.totalMark,
    this.numOfQuestions,
    this.questAnswers,
    this.networkStatus,
    this.examUploadStatus,
    this.examCompletedStatus,
  );
}
