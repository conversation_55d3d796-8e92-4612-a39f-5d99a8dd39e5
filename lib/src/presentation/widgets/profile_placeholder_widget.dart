import 'package:flutter/material.dart';

class ProfilePlaceholderWidget extends StatelessWidget {
  final double maxWidth;

  const ProfilePlaceholderWidget({super.key, this.maxWidth = 160});

  @override
  Widget build(BuildContext context) {
    return _placeHolderWidget();
  }

  Widget _placeHolderWidget() {
    return Image.asset(
      "assets/images/no_profile.png",
      width: maxWidth,
      height: maxWidth,
      fit: BoxFit.cover,
    );
  }
}
