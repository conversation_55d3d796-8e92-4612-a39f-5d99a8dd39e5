import '../../config/app_config/api_config.dart';

import '/src/domain/models/rank.dart';
import '/src/domain/repositories/rank_list_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class RankListRepositoryImpl implements RankListRepository {
  RankListRepositoryImpl();

  /// fetch rank list for the selected course
  @override
  Future<List<Rank>> getCourseRankList(String orgId, String courseId) async {
    final response = await Supabase.instance.client.rpc(
        APIConfig.FETCH_RANK_LIST_FOR_COURSE,
        params: {"course_id": courseId, "org_id": orgId});
    Map<String, dynamic> responseData = response ?? {};
    List<dynamic> rankResponse = responseData['rank_list'] ?? [];

    List<Rank> rankFromJson =
        List<Rank>.from(rankResponse.map((x) => Rank.fromJson(x)));

    return rankFromJson;
  }

  /// fetch rank list for the selected exam
  @override
  Future<List<Rank>> getExamRankList(String orgId, String examId) async {
    final response = await Supabase.instance.client.rpc(
        APIConfig.FETCH_RANK_LIST_FOR_EXAM,
        params: {"quiz_id": examId, "org_id": orgId});
    Map<String, dynamic> responseData = response ?? {};
    List<dynamic> rankResponse = responseData['rank_list'] ?? [];

    List<Rank> rankFromJson =
        List<Rank>.from(rankResponse.map((x) => Rank.fromJson(x)));

    return rankFromJson;
  }
}
