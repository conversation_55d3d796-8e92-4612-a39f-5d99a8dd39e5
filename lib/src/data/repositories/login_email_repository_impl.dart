import 'package:SmartLearn/src/config/app_config/api_config.dart';
import 'package:SmartLearn/src/utils/constants/helper.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/repositories/login_email_repository.dart';
import '../../domain/services/network_services.dart';

class LoginEmailRepositoryImpl implements LoginEmailRepository {
  LoginEmailRepositoryImpl();

  @override
  Future<dynamic> loginEmail(String email, String password) async {
    try {
      Supabase.instance;
      // ignore: unused_catch_clause
      // ignore: avoid_catching_errors
    } on AssertionError catch (_) {
      /// if the network wasn't available during
      /// supabase init, then this api call will throw assertion error
      /// in such case, re-init supabase again when the network is available
      ///
      bool isNetworkAvailable = await checkNetworkStatus();
      if (isNetworkAvailable) {
        await NetworkServices().initSupabase();
      }
      return '';
    }

    final response = await Supabase.instance.client.auth
        .signInWithPassword(email: email, password: password);

    return response;
  }

  @override
  Future<dynamic> resetPassword(String email) async {
    final response =
        await Supabase.instance.client.auth.resetPasswordForEmail(email);

    return true;
  }

  @override
  Future onuserSignin() async {
    Map<String, dynamic> reqBody = {};

    if (kIsWeb) {
      String? _deviceWebInfoObj = await fetchWebDeviceInfo();
      reqBody = {
        "device_id": _deviceWebInfoObj,
      };
    } else {
      final _deviceInfoObj = await fetchDeviceInfo();
      reqBody = {
        "device_id": _deviceInfoObj,
      };
    }

    final response = await Supabase.instance.client
        .rpc(APIConfig.ON_USER_LOGIN, params: reqBody);
  }
}
