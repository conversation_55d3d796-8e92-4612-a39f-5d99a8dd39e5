// To parse this JSON data, do
//
//     final comment = commentFrom<PERSON>son(jsonString);

import 'dart:convert';

import 'package:equatable/equatable.dart';

List<ResourseComment> commentFromJson(String str) => List<ResourseComment>.from(
    json.decode(str).map((x) => ResourseComment.fromJson(x, '')));

String commentToJson(List<ResourseComment> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ResourseComment extends Equatable {
  final String commentId;
  final String parentId;
  final String message;
  final String subject;
  final String user;
  final String profilePic;
  final CommentType commentType;
  final DateTime? commentedTime;
  final RoleName roleName;
  final List<ResourseComment>? children;
  final CommentStatus commentStatus;
  final ResActivityType resActivityTpe;

  const ResourseComment({
    required this.commentId,
    required this.parentId,
    required this.message,
    required this.subject,
    required this.user,
    required this.profilePic,
    required this.commentType,
    required this.commentedTime,
    this.children,
    required this.commentStatus,
    required this.roleName,
    required this.resActivityTpe,
  });

  factory ResourseComment.fromJson(
          Map<String, dynamic> json, String parentId) =>
      ResourseComment(
          commentId: json["id"] ?? '',
          parentId: parentId,
          message: json["message"] ?? '',
          subject: json["subject"] ?? '',
          user: json["name"] ?? '',
          profilePic: json["avatar_url"] ?? '',
          commentedTime: DateTime.tryParse(json["created_at"]),
          commentType: typeValues.map[json["type"]] ?? CommentType.FEEDBACK,
          commentStatus:
              statusValues.map[json["status"]] ?? CommentStatus.PENDING,
          roleName: roleNameValues.map[json["role_name"]] ?? RoleName.STUDENT,
          children: json["children"] == null
              ? []
              : List<ResourseComment>.from(
                  json["children"]!.map((x) {
                    print("json['id']: ${json["id"]} ");
                    return ResourseComment.fromJson(x, json['id'] ?? '');
                  }),
                ),
          resActivityTpe: activityTypevalues.map[json["activity_type"]] ??
              ResActivityType.comment);

  Map<String, dynamic> toJson() => {
        "id": commentId,
        "message": message,
        "subject": subject,
        "user": user,
        "profile_pic": profilePic,
        "children": children == null
            ? []
            : List<dynamic>.from(children!.map((x) => x.toJson())),
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [commentId, message, subject, user, profilePic, children];
  }

  ResourseComment copyWith({
    String? commentId,
    String? message,
    String? subject,
    String? user,
    String? profilePic,
    CommentType? commentType,
    DateTime? commentedTime,
    List<ResourseComment>? children,
  }) {
    return ResourseComment(
        commentId: commentId ?? this.commentId,
        message: message ?? this.message,
        subject: subject ?? this.subject,
        user: user ?? this.user,
        profilePic: profilePic ?? this.profilePic,
        commentType: commentType ?? this.commentType,
        commentedTime: commentedTime ?? this.commentedTime,
        children: children ?? this.children,
        commentStatus: commentStatus ?? this.commentStatus,
        roleName: roleName ?? this.roleName,
        parentId: parentId,
        resActivityTpe: ResActivityType.comment);
  }
}

enum ResActivityType { comment, like }

final activityTypevalues = EnumValues(
    {"comment": ResActivityType.comment, "like": ResActivityType.like});

enum RoleName { ADMIN, STUDENT }

final roleNameValues =
    EnumValues({"Admin": RoleName.ADMIN, "Student": RoleName.STUDENT});

enum CommentStatus { APPROVED, PENDING, REJECTED }

final statusValues = EnumValues({
  "Approved": CommentStatus.APPROVED,
  "Pending": CommentStatus.PENDING,
  "Rejected": CommentStatus.REJECTED
});

enum CommentType { FEEDBACK, SUGGESTION }

final typeValues = EnumValues(
    {"Feedback": CommentType.FEEDBACK, "Suggestion": CommentType.SUGGESTION});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
