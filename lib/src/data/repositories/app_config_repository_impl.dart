import 'package:SmartLearn/src/config/app_config/api_config.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../domain/models/branding_config/branding_config.dart';
import '../../domain/repositories/app_config_repository.dart';

class AppConfigRepositoryImpl implements AppConfigRepository {
  @override
  Future fetchAppBrandingConfig(Map<String, dynamic> reqJson) async {
    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_BRANDING_CONFIG, params: reqJson);

    if (response == null || response.isEmpty) {
      return null;
    }
    // Assuming response is a List with a single Map
    final data =
        response is List && response.isNotEmpty ? response[0] : response;
    final brandingConfig =
        BrandingConfig.fromJson(Map<String, dynamic>.from(data));

    return brandingConfig;
  }

  @override
  Future setUserActivityLog(Map<String, dynamic> reqJson) async {
    debugPrint("setUserActivityLog reqJson: $reqJson");

    final response = await Supabase.instance.client
        .rpc(APIConfig.USER_ACTIVITY_LOG, params: reqJson);

    debugPrint("setUserActivityLog response: $response");

    if (response != null) {
      Map<String, dynamic> responseData = response;
      return responseData['status'];
    } else {
      return false;
    }
  }
}
