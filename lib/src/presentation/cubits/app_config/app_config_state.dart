part of 'app_config_cubit.dart';

class AppConfigState extends Equatable {
  const AppConfigState();

  @override
  List<Object> get props => [];
}

class AppConfigInitial extends AppConfigState {}

class AppConfigLoading extends AppConfigState {}

class AppConfigLoaded extends AppConfigState {}

class AppConfigError extends AppConfigState {
  final String error;

  const AppConfigError(this.error);

  @override
  List<Object> get props => [error];
}

class AppConfigLoadedWithBranding extends AppConfigState {
  final BrandingConfig brandingConfig;

  const AppConfigLoadedWithBranding(this.brandingConfig);

  @override
  List<Object> get props => [brandingConfig];
}

class UserLogSubmitted extends AppConfigState {
  const UserLogSubmitted();

  @override
  List<Object> get props => [];
}
