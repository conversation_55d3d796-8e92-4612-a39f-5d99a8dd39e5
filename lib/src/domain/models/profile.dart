import 'package:equatable/equatable.dart';

import '../../utils/constants/strings.dart';

class Profile extends Equatable {
  final String? userId;
  String? firstName;
  String? lastName;
  String? avatarUrl;
  String phoneNum;
  final String email;
  Profile(
      {this.userId,
      this.firstName,
      this.lastName,
      this.avatarUrl,
      this.email = '',
      this.phoneNum = ''});

  factory Profile.fromJson(Map<String, dynamic> json) {
    String phoneNumber = json['phonenumber1'] ?? '';
    phoneNumber = phoneNumber.startsWith(IN_COUNTRY_CODE)
        ? phoneNumber.replaceFirst(IN_COUNTRY_CODE, '')
        : phoneNumber;
    return Profile(
      userId: json['user_id'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      avatarUrl: json['avatar_url'] ?? '',
      phoneNum: phoneNumber.trim(),
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> to<PERSON>son(Profile profile) => {
        "first_name": profile.firstName,
        "last_name": profile.lastName,
        "avatar_url": profile.avatarUrl,
        "phone_num": profile.phoneNum,
        "email": profile.email
      };

  @override
  bool get stringify => true;

  @override
  List<Object?> get props {
    return [userId, firstName, lastName, avatarUrl, email, phoneNum];
  }
}
