import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';

import '../../../src/domain/models/course_dashboard/course_page_resource.dart';
import '../../../src/presentation/widgets/app_bar.dart';
import '../../../src/utils/constants/nums.dart';
import 'package:auto_route/auto_route.dart';
import 'package:awesome_circular_chart/awesome_circular_chart.dart';
import 'package:beamer/beamer.dart';
import 'package:d_chart/d_chart.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:pie_chart/pie_chart.dart' as piechart;

import '../../config/app_config/api_constants.dart';
import '../../config/enums/alert_types.dart';
import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_dashboard/courseDetailUserStats.dart';
import '../../domain/models/course_dashboard/course_assignments.dart';
import '../../domain/models/course_dashboard/course_file_resource.dart';
import '../../domain/models/course_dashboard/course_details_dashboard_obj.dart';
import '../../domain/models/course_dashboard/courses_info.dart';
import '../../domain/models/course_dashboard/examPerformanceCatwise.dart';
import '../../domain/models/course_dashboard/userCourseProgress.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/helper.dart';
import '../../utils/constants/strings.dart';
import '../../utils/helper/app_date_formatter.dart';
import '../../utils/helper/course_helper.dart';
import '../cubits/course_dashboard/course_dashboard_cubit.dart';
import '../cubits/course_details/course_details_cubit.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';

import '../widgets/linear_progress_indicator_widget.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/valid_network_image_view.dart';
import 'course_dashboard_view.dart';
import 'ppt_viewer.dart';

@RoutePage()
class CourseDetailsDashboardScreen extends HookWidget {
  final CoursesInfo courseInfo;
  CourseDetailsDashboardScreen({required this.courseInfo, super.key});

  final ValueNotifier<bool> _isDataLoading = ValueNotifier<bool>(true);

  List<Map<String, dynamic>> courseOverviewInfo = [];
  List<CourseDetailsDashboardObj> _courseDetailsDashboard = [];
  final AppDateFormatter _appDateFormatter = AppDateFormatter.instance;
  ValueNotifier<bool> updateData = ValueNotifier<bool>(true);
  bool didUpdateScreen = false;

  setCourseInfo(List<CourseDetailsDashboardObj> _courseDetailsDashboard) {
    courseOverviewInfo = [
      {
        "title": 'Total Marks',
        "iconpath": "total-marks.png",
        "content": _courseDetailsDashboard.first.totalMarks.toString(),
      },
      {
        "title": "Progress",
        "iconpath": "progress.png",
        "content":
            _courseDetailsDashboard.first.totalProgress.toStringAsFixed(2) +
                '%',
      },
      {
        "title": "Time Spent",
        "iconpath": "time-spent.png",
        "content": _courseDetailsDashboard.first.totalTimeSpent.toString(),
      },
      {
        "title": "Achievements",
        "iconpath": "acheivment.png",
        "content": _courseDetailsDashboard.first.totalAchievements.toString(),
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    final _courseDashboardCubit = BlocProvider.of<CourseDetailsCubit>(context);
    updateData = useState(true);
    useEffect(() {
      _isDataLoading.value = true;

      Future<void>.microtask(() async {
        await _courseDashboardCubit
            .fetchCourseResourcesInfo(courseInfo.courseId);
        _isDataLoading.value = false;
      });
      return null;
    }, const []);

    return Scaffold(
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
              title: courseInfo.courseName ?? "",
              leadingIconName: BACK_ARROW,
              trailingWidget: Container(),
              leadingBtnAction: () => kIsWeb
                  ? Beamer.of(context).beamBack()
                  : appRouter.popForced(didUpdateScreen))),
      body: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          return BlocBuilder<CourseDetailsCubit, CourseDetailsState>(
            builder: (context, state) {
              if (state is CourseDetailsDashboardInfoFetched) {
                _courseDetailsDashboard =
                    state.courseDetailsDashboardData != null
                        ? [state.courseDetailsDashboardData!]
                        : [];
              }
              if (_courseDetailsDashboard.isNotEmpty) {
                setCourseInfo(_courseDetailsDashboard);
              }
              return ValueListenableBuilder(
                valueListenable: _isDataLoading,
                builder: (context, _, __) {
                  return Container(
                    height: constraints.maxHeight,
                    decoration: const BoxDecoration(
                      color: AppTheme.whiteColor,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16)),
                    ),
                    child: _isDataLoading.value
                        ? LoadingIndicatorClass()
                        : Align(
                            alignment: Alignment.topCenter,
                            child: Container(
                              width: constraints.maxWidth < 720
                                  ? double.infinity
                                  : 800,
                              padding: const EdgeInsets.only(
                                  left: horizontalMargin,
                                  right: horizontalMargin,
                                  bottom: horizontalMargin,
                                  top: 8),
                              child: SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // courseOverviewInfo.isNotEmpty
                                    //     ? _titleWidget(
                                    //         constraints, 'Learning Summary')
                                    //     : const SizedBox.shrink(),
                                    Align(
                                      alignment: Alignment.topRight,
                                      child: IconButton(
                                        padding: EdgeInsets.zero,
                                        icon: const Icon(
                                            Icons.dashboard_outlined,
                                            color: Colors.red),
                                        iconSize: 35.0,
                                        onPressed: () {
                                          kIsWeb
                                              ? Beamer.of(context).beamToNamed(
                                                  '/CourseDetailsViewRoute')
                                              : appRouter
                                                  .push(CourseDetailsViewRoute(
                                                  courseId: COURSE_ID,
                                                  courseName: COURSE_NAME,
                                                  isFromLogin: false,
                                                ));
                                        },
                                        tooltip: "Course Details",
                                      ),
                                    ),

                                    courseOverviewInfo.isNotEmpty
                                        ? CourseOverviewWidget(
                                            courseListData: [],
                                            courseInfo: courseOverviewInfo,
                                            resourceListData:
                                                _courseDetailsDashboard
                                                        .isNotEmpty
                                                    ? _courseDetailsDashboard
                                                        .first
                                                        .courseWiseUserStats
                                                    : [],
                                            onCourseSelected: (CoursesInfo
                                                    courseInfo) async =>
                                                {},
                                          )
                                        : Container(),
                                    const SizedBox(height: 10),
                                    _titleWidget(constraints, 'Analytics'),
                                    AnalyticsWidget(
                                        courseDetailsDashboardObj:
                                            _courseDetailsDashboard.isNotEmpty
                                                ? _courseDetailsDashboard.first
                                                : null),
                                    const SizedBox(height: 10),
                                    _titleWidget(constraints, 'Assignments'),
                                    _assignmentsContainer(
                                        context,
                                        constraints,
                                        _courseDetailsDashboard.isNotEmpty
                                            ? _courseDetailsDashboard
                                                .first.courseAssignments
                                            : []),
                                    const SizedBox(height: 10),
                                    _titleWidget(constraints, 'Practices'),
                                    _practicesContainer(context, constraints),
                                  ],
                                ),
                              ),
                            ),
                          ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _titleWidget(BoxConstraints constraints, String title) {
    return Container(
      margin: const EdgeInsets.only(bottom: 5),
      child: Text(
        title,
        style: LMSFonts.semiBoldFont(18, AppTheme.pitchBlack),
      ),
    );
  }

  Widget _assignmentsContainer(BuildContext context, BoxConstraints constraints,
      List<CourseAssignments> courseAssignments) {
    courseAssignments.retainWhere((element) =>
        element.resourceType == ResourceType.FILE ||
        element.resourceType == ResourceType.PAGE ||
        element.resourceType == ResourceType.URL);

    return Container(
      child: LayoutBuilder(
        builder: (layoutContext, constraints) {
          final totalWidth = constraints.maxWidth;
          const crossAxisCount = 2;
          const spacing = 8.0;

          // Tile width and height
          final tileWidth =
              (totalWidth - ((crossAxisCount - 1) * spacing)) / crossAxisCount;
          return Wrap(
            spacing: spacing,
            runSpacing: spacing,
            children: List.generate(
              courseAssignments.length,
              (index) {
                CourseAssignments courseAssignment = courseAssignments[index];

                return Container(
                  color: Colors.transparent,
                  width: tileWidth,
                  child: Stack(
                    children: [
                      _assignmentItem(context, courseAssignment, tileWidth),
                      !courseAssignment.isEnabled
                          ? _overlayWidget(tileWidth)
                          : Container(),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _overlayWidget(double tileWidth) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(width: 0.5, color: AppTheme.primaryTextColorBlack),
        ),
      ),
    );
  }

  Widget _assignmentItem(BuildContext context,
      CourseAssignments courseAssignment, double tileWidth) {
    ResourceType _resourceType = courseAssignment.resourceType;
    if (_resourceType == ResourceType.FILE &&
        courseAssignment.resourceUrl != null) {
      if (courseAssignment.resourceUrl!.endsWith(IMAGE_EXTENSIONJPG) ||
          courseAssignment.resourceUrl!.endsWith(IMAGE_EXTENSIONJPEG) ||
          courseAssignment.resourceUrl!.endsWith(IMAGE_EXTENSIONPNG)) {
        _resourceType = ResourceType.IMAGE;
      }
    }
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(width: 0.5, color: AppTheme.primaryTextColorBlack),
      ),
      child: GestureDetector(
        onTap: () async {
          ResourceProgress? _courseProgress =
              await _fetchCourseProgress(context, courseAssignment.resourceId);

          switch (courseAssignment.resourceType) {
            case ResourceType.FILE:
              _handleFileImageTypeNavigation(
                  context, courseAssignment, _courseProgress);
              break;
            case ResourceType.URL:
              _handleVideoTypeNavigation(
                  context, courseAssignment, _courseProgress);
              break;
            case ResourceType.PAGE:
              _handlePageTypeNavigation(
                  context, courseAssignment, _courseProgress);
              break;
            default:
          }
        },
        child: Container(
          color: Colors.transparent,
          child: Stack(
            children: [
              Column(
                children: [
                  const SizedBox(height: 8),
                  _thumbnailWidget(courseAssignment.thumbnailUrl,
                      courseAssignment.resourceType),
                  const Divider(
                      indent: 10, endIndent: 10, color: AppTheme.infoBlue),
                  _assignmentTitleWidget(courseAssignment),
                  const SizedBox(height: 5),
                  _progressIndicator(courseAssignment),
                  const SizedBox(height: 8),
                ],
              ),
              courseAssignment.isCheckpointEnabled
                  ? Card(
                      elevation: 4,
                      color: AppTheme.primaryOrange,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 5, vertical: 2),
                        child: Text(
                          'Checkpoint',
                          style: LMSFonts.mediumFont(
                              12, AppTheme.whiteTextColor, 1.0),
                        ),
                      ),
                    )
                  : Container(),
              Visibility(
                visible: (courseAssignment.progress ?? 0) >= 100 ? false : true,
                child: Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 3, top: 3),
                    child: Tooltip(
                      message:
                          'You cannot access the next resource until progress reaches 100%..',
                      showDuration: const Duration(seconds: 3),
                      padding: const EdgeInsets.all(8.0),
                      margin: const EdgeInsets.all(16),
                      triggerMode: TooltipTriggerMode.tap,
                      textStyle:
                          LMSFonts.mediumFont(12, AppTheme.whiteTextColor, 1.0),
                      decoration: BoxDecoration(
                        color: AppTheme.wrongANswerRed,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _assignmentTitleWidget(CourseAssignments courseAssignment) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      alignment: Alignment.center,
      height: 40,
      // color: AppTheme.reportQstnSD,
      child: Text(
        courseAssignment.resourceName,
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: LMSFonts.mediumFont(12, AppTheme.primaryTextColorBlack, 1),
      ),
    );
  }

  Widget _thumbnailWidget(String? url, ResourceType resourceType) {
    String path = 'image.png';
    debugPrint(resourceType.name);
    switch (resourceType) {
      case ResourceType.FILE:
        path = 'thumbnail_docs';
        break;
      case ResourceType.URL:
        path = 'thumbnail_video';
        break;
      case ResourceType.PAGE:
        path = 'thumbnail_docs';
        break;
      case ResourceType.IMAGE:
        path = 'thumbnail_image';
        break;

      default:
    }

    return Container(
      height: 85,
      width: double.infinity,
      // color: Colors.amber,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            color: Colors.transparent,
            child: url != null
                ? ValidNetworkImage(
                    url: url,
                    fallbackAssetPath: '$ASSETS_PATH/$path.png',
                  )
                //  CachedNetworkImage(
                //     imageUrl: url,
                //     placeholder: (context, url) =>
                //         const CircularProgressIndicator(),
                //     errorWidget: (context, url, error) => Image.asset(
                //       '$ASSETS_PATH/$path.png',
                //       fit: BoxFit.cover,
                //     ),
                //     fit: BoxFit.cover,
                //   )
                : Image.asset(
                    '$ASSETS_PATH/$path.png',
                    fit: BoxFit.cover,
                  ),
          ),
          _shadowWidgetForIcon(_imageWidget(resourceType, 25.0)),
        ],
      ),
    );
  }

  Widget _shadowWidgetForIcon(Widget childWidget) {
    return Positioned(
      bottom: 0,
      left: 8,
      child: Material(
        elevation: 2, // Simple elevation effect
        shape: const CircleBorder(),
        color: Colors.transparent, // Keeps the background transparent
        child: Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withOpacity(0.9),
          ),
          child:
              Padding(padding: const EdgeInsets.all(2.5), child: childWidget),
        ),
      ),
    );
  }

  Widget _imageWidget(ResourceType resourceType, double height) {
    String path = 'file_dock.png';
    debugPrint(resourceType.name);
    switch (resourceType) {
      case ResourceType.FILE:
        path = 'file_dock.png';
        break;
      case ResourceType.URL:
        path = 'video_light.png';
        break;
      case ResourceType.PAGE:
        path = 'file_light.png';
        break;
      case ResourceType.IMAGE:
        path = 'gallery.png';
        break;

      default:
    }

    if (resourceType == ResourceType.FILE) {}

    return Image.asset(
      '$ASSETS_PATH/$path',
      height: height,
      color: AppTheme.iconEnabledColor,
    );
  }

  Widget _progressIndicator(CourseAssignments courseAssignment) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top Row: Text and Warning Icon
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Linear Progress Indicator
              Expanded(
                child: LinearProgressIndicatorWidget(
                    progress: (courseAssignment.progress ?? 0) / 100,
                    progressColor: Colors.green,
                    backgroundColor: Colors.grey[300]),
              ),
              const SizedBox(width: 5),
              Text(
                (courseAssignment.progress ?? 0) <= 0
                    ? '0%'
                    : (courseAssignment.progress ?? 0) >= 100
                        ? '100%'
                        : '${(courseAssignment.progress ?? 0).toStringAsFixed(2)}%',
                style: LMSFonts.mediumFont(
                    12, AppTheme.primaryTextColorBlack.withOpacity(0.7), 1.0),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _practicesContainer(BuildContext context, BoxConstraints constraints) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(width: 0.5, color: AppTheme.primaryTextColorBlack),
      ),
      width: (constraints.maxWidth / 2) - 16 - 0,
      child: GestureDetector(
        onTap: () {
          kIsWeb
              ? Beamer.of(context)
                  .beamToNamed('/exam-list-tab', data: {'tabIndex': 0})
              : appRouter.push(ExamListViewRoute(tabIndex: 0));
          // if (_exams.isNotEmpty) { } else {
          //   _showAlertDialog(context,
          //       SLStrings.getTranslatedString(KEY_MODULE_DETAILS_NOT_FOUND));
          // }
        },
        child: Column(
          children: [
            Image.asset('$ASSETS_PATH/exams.jpg',
                height: constraints.maxWidth / 3),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset('assets/images/exam.png', width: 20, height: 20),
                const SizedBox(width: 5),
                Text(
                  'Exams',
                  style: LMSFonts.mediumFont(
                      14, AppTheme.primaryTextColorBlack, 1),
                ),
              ],
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  ///fetch the progress value for any type resource
  Future<ResourceProgress?> _fetchCourseProgress(
      BuildContext context, String instanceId) async {
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

    await _courseDetailsCubit.getVideoResProgress(instanceId);

    CourseDetailsState state = _courseDetailsCubit.state;

    if (state is GetCourseResProgressState) {
      ResourceProgress courseProgress = state.progress;
      return courseProgress;
    } else if (state is GetCourseResProgressFailureState) {
      ResourceProgress courseProgress = ResourceProgress(
          progress: 0.0,
          timeSpent: '',
          instanceId: instanceId,
          markedAsDone: false);

      bool isActiveRouteInWeb = false;

      if (kIsWeb) {
        var beamerRouter = Beamer.of(context);
        isActiveRouteInWeb = beamerRouter.active;
      }

      bool isThereCurrentDialogShowing = kIsWeb
          ? isActiveRouteInWeb
          : ModalRoute.of(context)?.isCurrent != true;
      if (!isThereCurrentDialogShowing) {
        showPGExceptionPopup(context, state.error);
      }
      return courseProgress;
    }
    return null;
  }

  _handleFileImageTypeNavigation(
      BuildContext context,
      CourseAssignments courseAssignment,
      ResourceProgress? courseProgress) async {
    try {
      final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

      _isDataLoading.value = true;

      await _courseDetailsCubit.fetchFileResource(
          courseAssignment.resourceId, courseAssignment.courseModuleId);
      // For Image View

      CourseDetailsState state = _courseDetailsCubit.state;

      if (state is CourseDetailsFileResFetched) {
        if (state.courseFileResource.isNotEmpty) {
          final CourseFileResource _resourceFile =
              state.courseFileResource.first;

          final CheckPointData checkPointData = state.checkPointData;

          // bool isNetworkUrl = await validateImage(_resourceFile.url ?? '');
          // debugPrint(
          //     '----courseAssignment.url= ${courseAssignment.resourceUrl}');

          if (courseAssignment.fileExtension == PPT_EXTENSION ||
              courseAssignment.fileExtension == PPTX_EXTENSION) {
            ///
            /// PPT
            ///

            _resourceFile.progress = courseProgress?.progress ?? 0.0;

            await _handleNavigationToPPTReader(
                context, _resourceFile, checkPointData);
          } else if (_resourceFile != null &&
              !_resourceFile.url.endsWith(HTML)) {
            if (_resourceFile.url.endsWith(IMAGE_EXTENSIONJPG) ||
                _resourceFile.url.endsWith(IMAGE_EXTENSIONJPEG) ||
                _resourceFile.url.endsWith(IMAGE_EXTENSIONPNG)) {
              ///
              /// IMAGE
              ///

              await _handleNavigationToImageView(
                  context, courseAssignment, courseProgress, _resourceFile);
            } else {
              _handleInvalidFile(context);
            }
          } else {
            _handleInvalidFile(context);
          }
        }
      } else if (state is CourseResInfoFetchError) {
        _isDataLoading.value = false;
        showPGExceptionPopup(context, state.error);
      } else {
        _handleInvalidFile(context);
      }
    } on Exception catch (_) {
      // TODO
      _isDataLoading.value = false;
    }
  }

  _handleInvalidFile(BuildContext context) {
    _isDataLoading.value = false;
    _showAlertDialog(
        context, SLStrings.getTranslatedString(KEY_INVALID_FILE_FORMAT));
  }

  _updateDashboardUI(BuildContext context) async {
    _isDataLoading.value = true;
    try {
      final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);
      await _courseDetailsCubit.fetchCourseResourcesInfo(courseInfo.courseId);
      didUpdateScreen = true;
    } on Exception catch (e) {
      _isDataLoading.value = false;
    }
    _isDataLoading.value = false;
  }

  _handleNavigationToPPTReader(BuildContext context,
      CourseFileResource _resourceFile, CheckPointData checkPointData) async {
    Future.delayed(const Duration(milliseconds: 500), () {
      _isDataLoading.value = false;
    });
    appRouter
        .push(PPTViewerRoute(
      courseFileResource: _resourceFile,
      checkPoints: checkPointData.checkPoints ?? [],
      isFromExamScreen: false,
      navigateBackTo: NavigationTo.course_details,
    ))
        .then((value) async {
      await _updateDashboardUI(context);
      _isDataLoading.value = false;
    });
  }

  _handleNavigationToImageView(
    BuildContext context,
    CourseAssignments courseAssignment,
    ResourceProgress? courseProgress,
    CourseFileResource _resourceFile,
  ) async {
    String instanceId = courseAssignment.resourceId;
    String courseId = courseAssignment.courseId;
    int folderResourceLength =
        _courseDetailsDashboard.first.courseAssignments.length;
    Future.delayed(const Duration(milliseconds: 500), () {
      _isDataLoading.value = false;
    });

    kIsWeb
        ? Beamer.of(context).beamToNamed('/image-view', data: {
            'imagePath': _resourceFile.url,
            'resourceFile': _resourceFile,
            'instance': instanceId,
            'courseId': courseId,
            'progess': courseProgress,
            'moduleLength': folderResourceLength,
          })
        : appRouter
            .push(ImageViewRoute(
                resourceFile: _resourceFile,
                moduleLength: folderResourceLength,
                resourceProgress: courseProgress))
            .then((value) async {
            if (value != null) {
              _isDataLoading.value = true;
              Map<String, dynamic> args = value as Map<String, dynamic>;

              double progress = double.parse(args['progress'].toString());
              _isDataLoading.value = true;

              if (_courseDetailsDashboard.isNotEmpty) {
                int itemIndex = _courseDetailsDashboard.first.courseAssignments
                    .indexWhere((element) =>
                        element.resourceId == courseAssignment.resourceId);
                _courseDetailsDashboard
                    .first.courseAssignments[itemIndex].progress = progress;
                bool isCompleted =
                    double.parse(args['progress'].toString()) == 100.0;
                if (isCompleted) {
                  updateResourceStatus(itemIndex, isCompleted);
                }
              }
              _isDataLoading.value = false;
            }
          });
  }

  updateResourceStatus(int itemIndex, bool enableResource) {
    if (_courseDetailsDashboard.isEmpty) {
      return;
    }
    _courseDetailsDashboard.first.courseAssignments[itemIndex].progress = 100.0;

    final nextInprogressItem = _courseDetailsDashboard.first.courseAssignments
        .indexWhere((element) =>
            element.resourceType != ResourceType.QUIZ && !element.isEnabled);
    final hasInProgressItems =
        _courseDetailsDashboard.first.courseAssignments.any(
      (e) => e.isEnabled && e.progress != null && e.progress! < 100,
    );
    if (nextInprogressItem != -1) {
      _courseDetailsDashboard.first.courseAssignments[nextInprogressItem]
          .isEnabled = !hasInProgressItems;
    }
  }

  _handleVideoTypeNavigation(
      BuildContext context,
      CourseAssignments courseAssignment,
      ResourceProgress? courseProgress) async {
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

    await _courseDetailsCubit.fetchVideoResource(
        courseAssignment.resourceId, courseAssignment.courseModuleId);

    CourseDetailsState state = _courseDetailsCubit.state;

    List<CheckPoint> _checkPoints = [];
    if (state is CourseResInfoFetchError) {
      _isDataLoading.value = false;

      showPGExceptionPopup(context, state.error);
    } else if (state is CourseDetailsVideoResFetched) {
      if (state.courseVideoResource.isNotEmpty) {
        final _courseVideoObj = state.courseVideoResource.first;

        _checkPoints.addAll(_courseVideoObj.checkpoints);

        _isDataLoading.value = false;

        kIsWeb
            ? Beamer.of(context).beamToNamed(
                "/video-view",
                data: {
                  "courseVideo": _courseVideoObj,
                  "courseId": courseAssignment.courseId,
                  "progess": courseProgress,
                  "isFromSectionDetails": true,
                  "checkPoints": _checkPoints
                },
              )
            : appRouter
                .push(CourseVideoViewRoute(
                courseVideo: _courseVideoObj,
                courseProgress: courseProgress,
                isFromSectionDetails: true,
                checkPoints: _checkPoints,
                navigateBackTo: NavigationTo.course_details,
              ))
                .then((value) async {
                await _updateDashboardUI(context);

                ResourceProgress? _courseProgress = await _fetchCourseProgress(
                    context, courseAssignment.resourceId);
                if (_courseProgress != null) {
                  if (_courseDetailsDashboard.isNotEmpty) {
                    int itemIndex = _courseDetailsDashboard
                        .first.courseAssignments
                        .indexWhere((element) =>
                            element.resourceId == courseAssignment.resourceId);
                    _courseDetailsDashboard.first.courseAssignments[itemIndex]
                        .progress = _courseProgress.progress;
                    bool isCompleted = _courseProgress.progress >= 100.0;
                    if (isCompleted) {
                      updateResourceStatus(itemIndex, isCompleted);
                    }
                    _isDataLoading.value = false;
                  }
                }
              });
      }
    }

    _isDataLoading.value = false;
  }

  _handlePageTypeNavigation(
      BuildContext context,
      CourseAssignments courseAssignment,
      ResourceProgress? courseProgress) async {
    final _courseDetailsCubit = BlocProvider.of<CourseDetailsCubit>(context);

    String instanceId = courseAssignment.resourceId;
    String courseId = courseAssignment.courseId;
    int folderResourceLength =
        _courseDetailsDashboard.first.courseAssignments.length;
    Future.delayed(const Duration(milliseconds: 500), () {
      _isDataLoading.value = false;
    });

    await _courseDetailsCubit.fetchPageResource(
        instanceId, courseAssignment.courseModuleId);
    CourseDetailsState state = _courseDetailsCubit.state;

    if (state is PageResourceFetched) {
      CoursePageResource pageContent = state.pageContentData;
      _isDataLoading.value = false;
      kIsWeb
          ? Beamer.of(context).beamToNamed('/study-materials', data: {
              'pageContent': pageContent,
              'instance': instanceId,
              'courseId': courseId,
              'progess': courseProgress,
              'moduleLength': folderResourceLength,
            })
          : appRouter
              .push(StudyMaterialViewRoute(
                  pageContent: pageContent,
                  moduleLength: folderResourceLength,
                  resourceProgress: courseProgress))
              .then((value) async {
              if (value != null) {
                _isDataLoading.value = true;
                Map<String, dynamic> args = value as Map<String, dynamic>;

                double progress = double.parse(args['progress'].toString());

                _isDataLoading.value = true;

                if (_courseDetailsDashboard.isNotEmpty) {
                  int itemIndex = _courseDetailsDashboard
                      .first.courseAssignments
                      .indexWhere((element) =>
                          element.resourceId == courseAssignment.resourceId);
                  _courseDetailsDashboard
                      .first.courseAssignments[itemIndex].progress = progress;
                  bool isCompleted =
                      double.parse(args['progress'].toString()) == 100.0;
                  if (isCompleted) {
                    updateResourceStatus(itemIndex, isCompleted);
                  }
                }
                _isDataLoading.value = false;
              }
            });
    } else if (state is PageResourceError) {
      _isDataLoading.value = false;
      showPGExceptionPopup(context, state.error);
    }
    _isDataLoading.value = false;
  }

  void _showAlertDialog(BuildContext context, String msg) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: msg,
          iconWidget: const Icon(Icons.error, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  showPGExceptionPopup(BuildContext context, String msg) {
    bool isThereCurrentDialogShowing = false;
    String routeNameInApp = '';
    String routeNameInWeb = '';

    if (kIsWeb) {
      final beamerRouter = Beamer.of(context);
      isThereCurrentDialogShowing = !beamerRouter.active;
      routeNameInWeb = beamerRouter.configuration.uri.path;
    } else {
      isThereCurrentDialogShowing = ModalRoute.of(context)?.isCurrent != true;
      routeNameInApp = appRouter.current.route.name;
    }
    if (isThereCurrentDialogShowing) {
      appRouter.popUntil((route) => route.settings.name == routeNameInApp);
    }
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              return;
            }
          },
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            alertType: AlertType.error,
            onContinueTap: () =>
                kIsWeb ? Beamer.of(context).beamBack() : Navigator.pop(context),
            onCancelTap: () {},
            cancelBtnText: '',
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
          ),
        );
      },
    );
  }
}

class AnalyticsWidget<T> extends StatelessWidget {
  final CourseDetailsDashboardObj? courseDetailsDashboardObj;

  AnalyticsWidget({
    required this.courseDetailsDashboardObj,
    super.key,
  });

  List<T> resourceList = [];

  List<Map<String, dynamic>> analyticsItems = [
    {
      "topColor": Colors.red,
      "bandColor": Colors.red.withOpacity(0.3),
      "heading": "Score",
    },
    {
      "topColor": Colors.green,
      "bandColor": Colors.green.withOpacity(0.3),
      "heading": "Completion",
    },
    {
      "topColor": Colors.purple,
      "bandColor": Colors.purple.withOpacity(0.3),
      "heading": "Skills",
    },
    {
      "topColor": Colors.teal,
      "bandColor": Colors.teal.withOpacity(0.3),
      "heading": "Marks",
    },
    {
      "topColor": Colors.cyan,
      "bandColor": Colors.cyan.withOpacity(0.3),
      "heading": "Progress",
    },
  ];

  final GlobalKey<AnimatedCircularChartState> _chartKey =
      GlobalKey<AnimatedCircularChartState>();
  final GlobalKey<AnimatedCircularChartState> chartKey1 =
      GlobalKey<AnimatedCircularChartState>();

  final _completionChartSize = const Size(140.0, 140.0);
  final _skillsChartSize = const Size(140.0, 140.0);
  double averageProgress = 0.0;

  Color? labelColor = Colors.blue[200];

  List<CircularStackEntry> _setProgressRadial() {
    // Static data for 4 subjects with predefined percentage values
    var results = courseDetailsDashboardObj?.userCourseProgress?.result ?? [];
    if (results.isNotEmpty) {
      var totalProgressSum =
          results.map((item) => item.totalProgress).reduce((a, b) => a + b);
      averageProgress =
          double.parse((totalProgressSum / results.length).toStringAsFixed(2));
      debugPrint("Average Progress: $averageProgress");
    } else {
      debugPrint("No results found, average cannot be calculated.");
    }

    List<CircularStackEntry> data = <CircularStackEntry>[
      CircularStackEntry(
        <CircularSegmentEntry>[
          CircularSegmentEntry(
            averageProgress,
            Colors.blue,
            rankKey: 'Math',
          ),
          CircularSegmentEntry(
            100,
            Colors.grey[200],
            rankKey: 'Background',
          ),
        ],
      ),
      // Science
    ];

    return data;
  }

  Color getColor(int index) {
    // Define a fixed list of colors
    List<Color> colors = [
      Colors.red,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
    ];

    // Return a color based on the index, cycling through the list
    return colors[index % colors.length];
  }

  List<CircularStackEntry> _getScoreRadial() {
    // Ensure `state` has the expected structure and values
    if ((courseDetailsDashboardObj?.courseWiseUserStats ?? []).isEmpty) {
      return [];
    } else {
      courseDetailsDashboardObj!.courseWiseUserStats
          .sort((a, b) => a.totalMarks.compareTo(b.totalMarks));
    }

    // Map the `questionCategory` data to CircularStackEntry
    List<CircularStackEntry> data = courseDetailsDashboardObj!
        .courseWiseUserStats
        .asMap()
        .entries
        .skip(max(0, courseDetailsDashboardObj!.courseWiseUserStats.length - 6))
        .map<CircularStackEntry>((entry) {
      int index = entry.key;
      var userstat = entry.value;

      return CircularStackEntry(
        [
          CircularSegmentEntry(
            userstat.totalMarks.toDouble(),
            getColor(index),
            rankKey: userstat.resourceId,
          ),
          CircularSegmentEntry(
            100 - userstat.totalMarks.toDouble(), // Remaining percentage
            Colors.grey[200],
            rankKey: 'Background',
          ),
        ],
        // rankKey: category.categoryId,
      );
    }).toList();

    return data;
  }

  List<OrdinalData> getMarksData() {
    // Ensure `state` has the expected structure and values
    if ((courseDetailsDashboardObj?.courseWiseUserStats ?? []).isEmpty) {
      return [];
    } else {
      courseDetailsDashboardObj!.courseWiseUserStats
          .sort((a, b) => a.totalMarks.compareTo(b.totalMarks));
    }

    // Map the `questionCategory` data to CircularStackEntry
    List<OrdinalData> data = courseDetailsDashboardObj!.courseWiseUserStats
        .skip(max(0, courseDetailsDashboardObj!.courseWiseUserStats.length - 6))
        .map<OrdinalData>((user) {
      return OrdinalData(
          //  domain: course.courseName.split(" ").first,

          domain: (user.resourceName.length >= 6
                  ? user.resourceName.substring(0, 6)
                  : user.resourceName) +
              "..",
          measure: user.totalMarks as num);
    }).toList();

    return data;
  }

  List<OrdinalData> _getprogressData() {
    // Ensure `state` has the expected structure and values
    if ((courseDetailsDashboardObj?.userCourseProgress?.result ?? []).isEmpty) {
      return [];
    } else {
      courseDetailsDashboardObj!.userCourseProgress?.result
          .sort((a, b) => a.progressPercent.compareTo(b.progressPercent));
    }

    List<OrdinalData> data = courseDetailsDashboardObj != null &&
            courseDetailsDashboardObj!.userCourseProgress != null
        ? (courseDetailsDashboardObj!.userCourseProgress?.result ?? [])
            .skip(max(
                0,
                courseDetailsDashboardObj!.userCourseProgress!.result.length -
                    6))
            .map<OrdinalData>((course) {
            return OrdinalData(
                domain: (course.resourceName.length >= 4
                        ? course.resourceName.substring(0, 4)
                        : course.resourceName) +
                    "..",
                measure: course.progressPercent);
          }).toList()
        : [];

    return data;
  }

  List<Color> colorList = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.red,
  ];

  Map<String, double> getSkillData() {
    if ((courseDetailsDashboardObj
                ?.examPerformanceCatwise?.performanceSummary.questionCategory ??
            [])
        .isEmpty) {
      return {};
    }
    courseDetailsDashboardObj!
        .examPerformanceCatwise.performanceSummary.questionCategory
        .sort((a, b) =>
            a.details.marksObtained.compareTo(b.details.marksObtained));

    // Transform the questionCategory list into a Map<String, double>
    Map<String, double> data = {
      for (var category in courseDetailsDashboardObj!
          .examPerformanceCatwise.performanceSummary.questionCategory)
        (category.name.length >= 6
                    ? category.name.substring(0, 6)
                    : category.name) +
                ".." +
                " (${category.details.marksObtained.toDouble().toString()})":
            category.details.marksObtained.toDouble()
    };

    return data;
  }

  Map<String, double> dataMap = {
    "Flutter": 50,
    "React": 3,
    "Xamarin": 2,
    "Ionic": 2,
  };

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CourseDetailsCubit, CourseDetailsState>(
      builder: (context, state) {
        return LayoutBuilder(builder: (context, constraints) {
          final totalWidth = constraints.maxWidth;
          const crossAxisCount = 2;
          const spacing = 8.0;

          // Tile width and height
          final tileWidth =
              (totalWidth - (crossAxisCount - 1) * spacing) / crossAxisCount;
          return Wrap(
            spacing: spacing,
            runSpacing: spacing,
            children: List.generate(
              analyticsItems.length,
              (index) {
                // Adjust width for the last row
                final width = (analyticsItems[index]["heading"] == "Marks" ||
                        analyticsItems[index]["heading"] == "Progress" ||
                        analyticsItems[index]["heading"] == "Skills")
                    ? totalWidth
                    : tileWidth;

                final item = analyticsItems[index];

                return SizedBox(
                  height: 250,
                  width: width, // index > 1 ? totalWidth : (totalWidth) / 2,
                  child: Card(
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(15), // Rounded corners
                    ),
                    elevation: 4,
                    child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              15), // Match the Card's radius
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(
                              15), // Ensure the child respects the border radius
                          child: Column(
                            children: [
                              Container(
                                height: 12,
                                color: item['topColor'],
                              ),
                              Container(
                                height: 30,
                                width: double.infinity,
                                color: item['bandColor'],
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  item['heading'],
                                  textAlign: TextAlign.left,
                                  style: LMSFonts.semiBoldFont(
                                      15, item['topColor']),
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  child: Container(
                                    child: (() {
                                      switch (item["heading"]) {
                                        case "Score":
                                          return _getScoreRadial().isEmpty
                                              ? _emptyChartData()
                                              : _scoreGraph(context, item);
                                        case "Completion":
                                          return _setProgressRadial().isEmpty
                                              ? _emptyChartData()
                                              : _progressRadialGraph(
                                                  context, item);
                                        case "Skills":
                                          return getSkillData().isEmpty
                                              ? _emptyChartData()
                                              : _skillsGraph(context, item);
                                        case "Marks":
                                          return getMarksData().isEmpty
                                              ? _emptyChartData()
                                              : _marksGraph(context, item);
                                        case "Progress":
                                          return _getprogressData().isEmpty
                                              ? _emptyChartData()
                                              : _progressGraph(context, item);
                                      }
                                    }()),
                                  ),
                                ),
                              )
                            ],
                          ),
                        )),
                  ),
                );
              },
            ).toList(),
          );
        });
      },
    );
  }

  Widget _scoreGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        onTap: () {
          if (courseDetailsDashboardObj != null) {
            resourceList =
                courseDetailsDashboardObj!.courseWiseUserStats.cast<T>();
          }
          showDialog(
            context: context,
            builder: (context) {
              return CustomPopup<CourseDetailUserStatiModel>(
                mainTitle: item["heading"],
                title1: 'Course Name',
                title2: 'Marks',
                modelObjList: resourceList.cast<CourseDetailUserStatiModel>(),
                itemBuilder: (CourseDetailUserStatiModel resInfo) =>
                    _popupContent(
                        resInfo.resourceName, resInfo.totalMarks.toString()),
                onCancel: () => Navigator.of(context).pop(),
              );
            },
          );
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Container(
                child: AnimatedCircularChart(
                  key: _chartKey,
                  size: _skillsChartSize,
                  initialChartData: _getScoreRadial(),
                  edgeStyle: SegmentEdgeStyle.round,
                  percentageValues: true,
                  // holeLabel: '$value%',
                  duration: Durations.extralong1,
                  // holeRadius: ,
                ),
              ),
            ),
            Container(
              width: 170,
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: ListView(
                children: courseDetailsDashboardObj!.courseWiseUserStats
                    .sublist(max(
                        0,
                        courseDetailsDashboardObj!.courseWiseUserStats.length -
                            3)) // Only the last 3 items
                    .map<Widget>((category) {
                  int index = courseDetailsDashboardObj!.courseWiseUserStats
                      .indexOf(category);
                  return Row(
                    children: [
                      Container(
                        height: 8,
                        width: 8,
                        color: getColor(index),
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Text(
                        category.resourceName.length > 6
                            ? category.resourceName.substring(
                                    0, min(6, category.resourceName.length)) +
                                '...' +
                                " (${category.totalMarks})"
                            : category.resourceName +
                                " (${category.totalMarks})",
                        style: LMSFonts.mediumFont(
                            12, AppTheme.primaryTextColorBlack, 1.0),
                      ),
                    ],
                  );
                }).toList(),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _progressRadialGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        onTap: () {
          if (courseDetailsDashboardObj != null) {
            resourceList = courseDetailsDashboardObj?.userCourseProgress?.result
                    .cast<T>() ??
                [];
          }
          showDialog(
            context: context,
            builder: (context) {
              return CustomPopup<UserCourseProgressResult>(
                mainTitle: item["heading"],
                title1: 'Course Name',
                title2: 'Progress',
                modelObjList: resourceList.cast<UserCourseProgressResult>(),
                itemBuilder: (UserCourseProgressResult resInfo) =>
                    _popupContent(resInfo.resourceName,
                        resInfo.progressPercent.toString()),
                onCancel: () => Navigator.of(context).pop(),
              );
            },
          );
        },
        child: AnimatedCircularChart(
          key: chartKey1,
          size: _completionChartSize,
          initialChartData: _setProgressRadial(),
          edgeStyle: SegmentEdgeStyle.round,
          percentageValues: true,
          holeLabel: '$averageProgress%',
          duration: Durations.extralong1,
          holeRadius: 35,
        ),
      ),
    );
  }

  Widget _skillsGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        onTap: () {
          if (courseDetailsDashboardObj != null) {
            resourceList = courseDetailsDashboardObj?.examPerformanceCatwise
                    ?.performanceSummary.questionCategory
                    .cast<T>() ??
                [];
          }
          showDialog(
            context: context,
            builder: (context) {
              return CustomPopup<QuestionCategory>(
                mainTitle: item["heading"],
                title1: 'Course Name',
                title2: 'Value',
                modelObjList: resourceList.cast<QuestionCategory>(),
                itemBuilder: (QuestionCategory resInfo) => _popupContent(
                    resInfo.name, resInfo.details.marksObtained.toString()),
                onCancel: () => Navigator.of(context).pop(),
              );
            },
          );
        },
        child: piechart.PieChart(
          dataMap: getSkillData(),
          colorList: colorList,
          chartType: piechart.ChartType.ring,
          baseChartColor: Colors.grey[200]!,
          chartLegendSpacing: 25.0,
          chartRadius: 135,
          emptyColor: Colors.grey[200]!,
        ),
      ),
    );
  }

  Widget _marksGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        onTap: () {
          if (courseDetailsDashboardObj != null) {
            resourceList =
                courseDetailsDashboardObj?.courseWiseUserStats.cast<T>() ?? [];
          }
          showDialog(
            context: context,
            builder: (context) {
              return CustomPopup<CourseDetailUserStatiModel>(
                mainTitle: item["heading"],
                title1: 'Course Name',
                title2: 'Marks',
                modelObjList: resourceList.cast<CourseDetailUserStatiModel>(),
                itemBuilder: (CourseDetailUserStatiModel resInfo) =>
                    _popupContent(
                        resInfo.resourceName, resInfo.totalMarks.toString()),
                onCancel: () => Navigator.of(context).pop(),
              );
            },
          );
        },
        child: Stack(
          children: [
            AspectRatio(
              aspectRatio: 16 / 9,
              child: DChartBarO(
                layoutMargin: LayoutMargin(40, 20, 20, 20),
                groupList: [
                  OrdinalGroup(
                    id: '1',
                    data: getMarksData(),
                  ),
                ],
              ),
            ),
            Positioned.fill(
                child: Container(
              color: Colors.transparent,
            )),
          ],
        ),
      ),
    );
  }

  Widget _progressGraph(BuildContext context, Map<String, dynamic> item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        highlightColor: Colors.grey.shade200,
        onTap: () {
          if (courseDetailsDashboardObj != null) {
            resourceList = courseDetailsDashboardObj?.userCourseProgress?.result
                    .cast<T>() ??
                [];
          }
          showDialog(
            context: context,
            builder: (context) {
              return CustomPopup<UserCourseProgressResult>(
                mainTitle: item["heading"],
                title1: 'Course Name',
                title2: 'Progress',
                modelObjList: resourceList.cast<UserCourseProgressResult>(),
                itemBuilder: (UserCourseProgressResult resInfo) =>
                    _popupContent(resInfo.resourceName,
                        resInfo.progressPercent.toString()),
                onCancel: () => Navigator.of(context).pop(),
              );
            },
          );
        },
        child: Stack(
          children: [
            AspectRatio(
              aspectRatio: 16 / 9,
              child: DChartBarO(
                vertical: false,
                layoutMargin: LayoutMargin(40, 20, 20, 20),
                groupList: [
                  OrdinalGroup(
                    id: '1',
                    data: _getprogressData(),
                  ),
                ],
              ),
            ),
            Positioned.fill(
                child: Container(
              color: Colors.transparent,
            )),
          ],
        ),
      ),
    );
  }

  Widget _emptyChartData() {
    return const Center(child: Text('No data available!'));
  }

  Widget _popupCourseName(String col1, TextAlign textAlign) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Text(col1,
            textAlign: textAlign,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style:
                LMSFonts.mediumFont(14, AppTheme.primaryTextColorBlack, 1.0)),
      ),
    );
  }

  Widget _popupContent(String name, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _popupCourseName(name, TextAlign.left),
        const SizedBox(width: 5),
        _col2Widget(value),
      ],
    );
  }

  Widget _col2Widget(String value) {
    return _popupCourseName(value, TextAlign.right);
  }
}
