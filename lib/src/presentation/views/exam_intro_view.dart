import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

import '../../config/app_config/preference_config.dart';
import 'package:beamer/beamer.dart';

import '../../config/enums/alert_types.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';

import '/src/presentation/widgets/common_button_widget.dart';
import '/src/presentation/widgets/debounced_button.dart';
import 'package:auto_route/auto_route.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '/src/presentation/cubits/exam/exam_cubit.dart';
import '/src/utils/helper/app_date_formatter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../widgets/alert_popup/single_action_dialogue.dart';
import '../widgets/loading_indicator.dart';
import '/src/utils/constants/strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/models/question_data.dart';
import '../widgets/app_bar.dart';

@RoutePage()

// ignore: must_be_immutable
class ExamIntroductionViewScreen extends HookWidget
    with WidgetsBindingObserver {
  final QuestionData questionData;
  final bool isFromSectionDetails;
  final bool isFromVideoScreen;

  ExamIntroductionViewScreen({
    Key? key,
    required this.questionData,
    this.isFromSectionDetails = false,
    this.isFromVideoScreen = false,
  }) : super(key: key);

  String? duration;
  late final WebViewController controller;

  BuildContext? currentContext;
  ValueNotifier<bool> isDataLoading = ValueNotifier<bool>(false);

  @override
  void didChangeLocales(List<Locale>? locales) async {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    await didChangeAppLocale(locales, currentContext);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final _examCubit = BlocProvider.of<ExamCubit>(context);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);

      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);
    return PopScope(
      canPop: !isFromVideoScreen,
      onPopInvoked: (bool didPop) {
        if (didPop && isFromVideoScreen) {
          return;
        }
      },
      child: Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: questionData.examName, //questionData.mainTopic,
              trailingWidget: Container(),
              isFromLogin: isFromVideoScreen,
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {
                kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop();
              },
            )),
        body: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            return BlocBuilder<ExamCubit, ExamState>(
              builder: (context, state) {
                double screenWidth =
                    constraints.maxWidth <= 720 ? constraints.maxWidth : 800;
                return Container(
                    height: MediaQuery.of(context).size.height,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16)),
                    ),
                    child: Stack(
                      children: [
                        Container(
                            padding: const EdgeInsets.only(bottom: 80),
                            child: ScrollConfiguration(
                              behavior:
                                  ScrollConfiguration.of(context).copyWith(
                                dragDevices: {
                                  PointerDeviceKind.touch,
                                  PointerDeviceKind.mouse,
                                },
                              ),
                              child: SingleChildScrollView(
                                  child: Stack(
                                children: [
                                  Align(
                                    child: SizedBox(
                                      width: screenWidth,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const SizedBox(height: 25),
                                          _headingWidget(questionData.examName),
                                          const SizedBox(height: 15),
                                          _durationWidget(
                                              SLStrings.getTranslatedString(
                                                  KEY_DURATION),
                                              "alarm_clock.png"),
                                          _dividerWidget(),
                                          _markWidget(
                                              SLStrings.getTranslatedString(
                                                  KEY_MARK),
                                              "mark.png"),
                                          _dividerWidget(),
                                          _examAvailableWidget(
                                              SLStrings.getTranslatedString(
                                                  KEY_EXAM_AVAILABLE),
                                              "file.png"),
                                          _dividerWidget(),
                                          questionData.intro.isNotEmpty
                                              ? _tipsWidget(
                                                  SLStrings.getTranslatedString(
                                                      KEY_EXAM_TIPS),
                                                  "exam_tip.png",
                                                  context,
                                                  constraints.maxWidth)
                                              : Container()
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              )),
                            )),
                        state is ExamsLoading && state.loadingStatus
                            ? Positioned.fill(child: LoadingIndicatorClass())
                            : Container(),
                      ],
                    ));
              },
            );
          },
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        floatingActionButton: _buttonClick(context, screenWidth, _examCubit),
      ),
    );
  }

  TextSpan _introDataWidget(String value) {
    return TextSpan(
        text: value + ' ',
        style: LMSFonts.examIntroDescriptionStyle(
            14, AppTheme.stepperColor, FontWeight.w700));
  }

  Widget imageWidget(String title, Color color, double paddingVal) {
    return Container(
        width: 45,
        height: 45,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Padding(
            padding: EdgeInsets.all(paddingVal),
            child: Image.asset(
              title,
              height: 40,
              width: 40,
            )));
  }

  Widget _titleWidget(String title, Color color) {
    return Text(
      title,
      textAlign: TextAlign.center,
      style: LMSFonts.boldFont(20, color),
    );
  }

  Widget _headingWidget(String title) {
    return Align(
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: Text(title,
            textAlign: TextAlign.center,
            style:
                LMSFonts.examIntroTextStyle(22, AppTheme.examIntroTextColor)),
      ),
    );
  }

  Widget _durationWidget(String title, String path) {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 9, bottom: 9),
      child: Row(children: [
        _imageContainer(path),
        const SizedBox(width: 15),
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: LMSFonts.examIntroTextStyle(
                    16, AppTheme.examIntroTextColor),
              ),
              _durationDescription()
            ],
          ),
        )
      ]),
    );
  }

  Widget _markWidget(String title, String path) {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 9, bottom: 9),
      child: Row(children: [
        _imageContainer(path),
        const SizedBox(width: 15),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style:
                  LMSFonts.examIntroTextStyle(16, AppTheme.examIntroTextColor),
            ),
            _markDescription()
          ],
        )
      ]),
    );
  }

  Widget _tipsWidget(
      String title, String path, BuildContext context, double screenWidth) {
    return Flexible(
      child: Container(
        margin: const EdgeInsets.only(left: 16, right: 16, top: 9),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _imageContainer(path),
            const SizedBox(width: 15),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: LMSFonts.examIntroTextStyle(
                      16, AppTheme.examIntroTextColor),
                ),
                tipWidget(context, screenWidth)
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _examAvailableWidget(String title, String path) {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, top: 9, bottom: 9),
      child: Row(children: [
        _imageContainer(path),
        const SizedBox(width: 15),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: LMSFonts.examIntroTextStyle(
                    16, AppTheme.examIntroTextColor),
              ),
              _examAvailableDescription()
            ],
          ),
        )
      ]),
    );
  }

  Widget _imageContainer(String path) {
    return Container(
        padding: const EdgeInsets.all(10),
        height: 60,
        width: 60,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: AppTheme.containerBorderColor,
            width: 2,
          ),
        ),
        child: Center(
          child: Image.asset(
            '$ASSETS_PATH/${path}',
          ),
        ));
  }

  RichText _markDescription() {
    return RichText(
      text: TextSpan(
        style: LMSFonts.examIntroDescriptionStyle(
            14, AppTheme.stepperColor, FontWeight.w400),
        children: <TextSpan>[
          _introDataWidget(questionData.totalMark.toString() + ' '),
          TextSpan(
              text: SLStrings.getTranslatedString(KEY_EXAM_TOTAL_MARK) + ' '),
          _introDataWidget(
              questionData.passMark.toStringAsFixed(1) + ' ' + '\n'),
          TextSpan(
            text: SLStrings.getTranslatedString(KEY_EXAM_PASS_MARK),
          ),
        ],
      ),
    );
  }

  RichText _examAvailableDescription() {
    DateTime startTime = questionData.startTime;
    DateTime endTime = questionData.endTime;

    String formattedStartDate =
        AppDateFormatter().formatToDateTimeString(startTime);
    String formattedEndDate =
        AppDateFormatter().formatToDateTimeString(endTime);
    return RichText(
      text: TextSpan(
        style: LMSFonts.examIntroDescriptionStyle(
            14, AppTheme.stepperColor, FontWeight.w400),
        children: <TextSpan>[
          TextSpan(
            text: SLStrings.getTranslatedString(KEY_FROM) + ' ',
          ),
          _introDataWidget(formattedStartDate),
          TextSpan(text: SLStrings.getTranslatedString(KEY_TO) + ' '),
          _introDataWidget(formattedEndDate),
        ],
      ),
    );
  }

  RichText _durationDescription() {
    return RichText(
      text: TextSpan(
        style: LMSFonts.examIntroDescriptionStyle(
            14, AppTheme.stepperColor, FontWeight.w400),
        children: <TextSpan>[
          TextSpan(
            text: SLStrings.getTranslatedString(KEY_EXAM_ATTEND) + ' ',
          ),
          _introDataWidget(questionData.numOfQuestions.toString() + ' '),
          TextSpan(
              text: SLStrings.getTranslatedString(KEY_QUESTION_WITHIN) + ' '),
          _introDataWidget(questionData.duration.toString() + ' '),
          TextSpan(
            text: SLStrings.getTranslatedString(KEY_EXAM_MINUTES) + ' ',
          ),
        ],
      ),
    );
  }

  Widget tipWidget(BuildContext context, double screenMaxWidth) {
    String intro = questionData.intro;
    double screenWidth = screenMaxWidth < 800 ? screenMaxWidth : 800;
    return SizedBox(
      width: screenWidth - 110,
      child: HtmlWidget(
        intro,
        // style: LMSFonts.htmlStyleForExamIntro,
      ),
    );
  }

  Widget _dividerWidget() {
    return const Padding(
        padding: EdgeInsets.only(left: 20, right: 20),
        child: Divider(
          color: AppTheme.dividerColor,
        ));
  }

  Widget _buttonClick(
      BuildContext context, double screenWidth, ExamCubit examCubit) {
    ExamState state = examCubit.state;
    return Align(
      alignment: FractionalOffset.bottomCenter,
      child: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
        screenWidth = constraints.maxWidth < 720 ? constraints.maxWidth : 800;
        return Container(
            margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            height: 50,
            child: ValueListenableBuilder(
                valueListenable: isDataLoading,
                builder: (context, dataLoading, _) {
                  return DebouncedButton(
                    disableButton: dataLoading ||
                        state is ExamsLoading && state.loadingStatus,
                    child: SizedBox(
                      width: screenWidth,
                      child: CommonButtonWidget(
                        borderColor: Colors.transparent,
                        color: dataLoading
                            ? AppTheme.continueBtnDisabled
                            : AppTheme.submitBtnColor,
                        borderRadius: 50,
                        child: Text(
                          SLStrings.getTranslatedString(KEY_START),
                          style: LMSFonts.commonButtonStyle(
                              16.0, AppTheme.whiteColor),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    onPressed: () async {
                      await _startExam(context, examCubit);
                    },
                  );
                }));
      }),
    );
  }

  Future<bool> _startExam(BuildContext context, ExamCubit _examCubit) async {
    _examCubit.updateNavigationStatustoIntro(disableBackNavigation: true);
    _examCubit.updateNavigationStatustoExamView(disableBackNavigation: false);
    try {
      int examDuration = questionData.duration;
      _examCubit.setLoading(true);
      if (isFromVideoScreen) {
        _startExamInCheckpointFlow(context, _examCubit, examDuration);
      } else {
        _startExamInNormalFlow(context, _examCubit, examDuration);
      }
    } on Exception catch (e) {
      debugPrint('[Exam Intro View][StartExam]: $e');
      return false;
    }
    return false;
  }

  /// course video >> exam intro
  _startExamInCheckpointFlow(
      BuildContext context, ExamCubit _examCubit, int examDuration) async {
    SharedPreferences _pref = await SharedPreferences.getInstance();
    String quizAttemptId = _pref.getString(PREF_KEY_QUIZ_ATTEMPT_ID) ?? '';
    if (quizAttemptId.isNotEmpty) {
      await _examCubit.saveQstnsToDB(questionData);

      kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop();
      kIsWeb
          ? Beamer.of(context).beamToReplacementNamed(
              '/exam-view',
              data: {
                "examDuration": examDuration,
                "shouldShowTimer": true,
                "isFromViewResult": false,
                "questionData": questionData,
                "quizAttemptId": quizAttemptId,
                "isFromVideoView": isFromVideoScreen,
              },
            )
          : appRouter.push(
              ExamViewRoute(
                examDuration: examDuration,
                shouldShowTimer: true,
                isFromViewResult: false,
                questionData: questionData,
                quizAttemptId: quizAttemptId,
                isFromVideoView: isFromVideoScreen,
              ),
            );
      _examCubit.setLoading(false);
      return true;
    } else {
      appRouter.pop();
      _examCubit.setLoading(false);
      _showInvalidQuizIdPopup(context);
      return false;
    }
  }

  /// exam list >> exam intro
  _startExamInNormalFlow(
      BuildContext context, ExamCubit _examCubit, int examDuration) async {
    await _examCubit.startExam(
        questionData.questions, questionData.questions[0].quizId ?? '');

    ExamState _state = _examCubit.state;
    if (_state is ExamsStarted) {
      _examCubit.setLoading(true);
      SharedPreferences _pref = await SharedPreferences.getInstance();
      String quizAttemptId = _pref.getString(PREF_KEY_QUIZ_ATTEMPT_ID) ?? '';
      if (isFromSectionDetails == true) {
        _pref.setBool(PREF_KEY_FROM_SECTION_DETAILS, true);
      } else {
        _pref.setBool(PREF_KEY_FROM_SECTION_DETAILS, false);
      }
      if (quizAttemptId.isNotEmpty) {
        if (!kIsWeb) {
          await _examCubit.saveQstnsToDB(questionData);
        }
        if (kIsWeb) {
          Beamer.of(context).beamBack();
          Beamer.of(context).beamToNamed('/exam-view', data: {
            'examDuration': examDuration,
            'shouldShowTimer': true,
            'isFromViewResult': false,
            'questionData': questionData,
            'quizAttemptId': quizAttemptId,
            'isFromVideoView': isFromVideoScreen,
          });
        } else {
          appRouter.pop();
          appRouter.push(
            ExamViewRoute(
              examDuration: examDuration,
              shouldShowTimer: true,
              isFromViewResult: false,
              questionData: questionData,
              quizAttemptId: quizAttemptId,
              isFromVideoView: isFromVideoScreen,
            ),
          );
        }
        _examCubit.setLoading(false);
      } else {
        appRouter.pop();
        _examCubit.setLoading(false);
        _showInvalidQuizIdPopup(context);
      }
      return true;
    } else if (_state is ExamPGException) {
      /// no quiz id
      /// request has been rejected by admin
      /// show popup
      _examCubit.setLoading(false);

      var route = ModalRoute.of(context);
      if (route != null && route.isActive) {
        if (kIsWeb) {
          _showPGExceptionPopup(context, _state.error);
        } else {
          if (route.settings.name == ExamIntroductionViewRoute.name) {
            _showPGExceptionPopup(context, _state.error);
          }
        }
      }

      return false;
    } else if (_examCubit.state is ExamQuizIdFailure) {
      /// no quiz id
      /// request has been rejected by admin
      /// show popup
      _examCubit.setLoading(false);
      _showInvalidQuizIdPopup(context);
      return false;
    }
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    final _examCubit = BlocProvider.of<ExamCubit>(context);
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            confirmBtnText: SLStrings.getTranslatedString(KEY_CHOOSE_EXAM),
            cancelBtnText: '',
            alertType: AlertType.error,
            onCancelTap: () => appRouter.pop(),
            onContinueTap: () {
              if (isFromSectionDetails) {
                _examCubit.updateNavigationStatustoIntro(
                    disableBackNavigation: false);
                kIsWeb
                    ? Beamer.of(context)
                        .beamToReplacementNamed('/section-details')
                    : appRouter.popUntil((route) =>
                        route.settings.name == SectionDetailsViewRoute.name);
              } else {
                _examCubit.updateNavigationStatustoIntro(
                    disableBackNavigation: false);
                kIsWeb
                    ? Beamer.of(context)
                        .beamToReplacementNamed('/exam-list-tab')
                    : appRouter.popUntil((route) =>
                        route.settings.name == ExamListViewRoute.name);
              }
            });
      },
    );
  }

  _showInvalidQuizIdPopup(BuildContext context) {
    var route = ModalRoute.of(context);
    if (route != null && route.isActive) {
      showDialog(
        context: context,
        builder: (context) {
          return SingleActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            message: SLStrings.getTranslatedString(KEY_REQUEST_REJECTED),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: true,
            handleOkCallback: () {},
          );
        },
      );
    }
  }
}
