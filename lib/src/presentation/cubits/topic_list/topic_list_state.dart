part of 'topic_list_cubit.dart';

abstract class TopicListState extends Equatable {
  final bool buttonColor;
  final List<Map<String, dynamic>> topicList;
  const TopicListState({
    this.buttonColor = false,
    this.topicList = const [],
  });

  @override
  List<Object> get props => [];
}

class TopicListInitial extends TopicListState {
  const TopicListInitial({super.buttonColor});
}

class TopicListSuccess extends TopicListState {
  final List<Map<String, dynamic>> topicList;

  const TopicListSuccess({required this.topicList});
  @override
  List<Object> get props => [topicList];
}

class TopicFetchSuccess extends TopicListState {
  final List<UserCategory> categoryList;

  const TopicFetchSuccess({required this.categoryList});
  @override
  List<Object> get props => [categoryList];
}

class TopicListLoading extends TopicListState {
  final bool isDataLoading;
  const TopicListLoading({required this.isDataLoading});

  @override
  List<Object> get props => [isDataLoading];
}

class TopicListExapanding extends TopicListState {
  final int index;
  final bool expand;
  const TopicListExapanding({required this.index, required this.expand});
  @override
  List<Object> get props => [index, expand];
}

class TopicListItemColour extends TopicListState {
  final bool color;
  const TopicListItemColour({required this.color});
  @override
  List<Object> get props => [color];
}

class TopicListError extends TopicListState {
  final String error;
  const TopicListError({required this.error});

  @override
  List<Object> get props => [error];
}
