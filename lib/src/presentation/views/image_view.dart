import 'package:SmartLearn/src/domain/services/localizations/string_keys.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../utils/helper/privilege_access_mapper.dart';
import '/src/config/themes/theme_colors.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/themes/lms_fonts.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/resource_file.dart';
import '../../domain/models/responses/section_details.dart';
import '../../utils/constants/nums.dart';
import '../cubits/section_details/section_details_cubit.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../widgets/button_widget.dart';
import '/src/config/themes/app_theme.dart';

import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';

import '../widgets/app_bar.dart';
import 'package:photo_view/photo_view.dart';

@RoutePage()
// ignore: must_be_immutable
class ImageViewScreen extends HookWidget {
  final String imagePath;
  final ResourseFile resourceFile;
  final String instance;
  final String courseId;
  CourseProgress? progess;
  ImageViewScreen({
    Key? key,
    required this.imagePath,
    required this.resourceFile,
    required this.instance,
    required this.courseId,
    required this.progess,
  }) : super(key: key);

  SectionDetails? sectionDetails;

  bool status = false;

  final double _horizontalMargin = 16.0;

  late ValueNotifier<bool> _hasAccessToSaveProgress;

  // decides whether to disable/enable save progress
  _checkPrivilegeAcccess() async {
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_UPDATE_COURSE_PROGRESS;
    _hasAccessToSaveProgress.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);
  }

  @override
  Widget build(BuildContext context) {
    String time = '0 $HOURS 0 $MINUTES 0 $SECONDS';
    Map<String, dynamic> args = {'progress': 100, 'time_spent': time};

    final _sectionDetailsCubit = BlocProvider.of<SectionDetailsCubit>(context);
    _hasAccessToSaveProgress = useState<bool>(false);

    useEffect(() {
      Future<void>.microtask(() async {
        await _checkPrivilegeAcccess();
        if (progess?.markedAsDone == true) {
          context.read<SectionDetailsCubit>().changeDoneStatus(true);
          status = true;
        }
      });
      return null;
    }, []);

    return BlocListener<SectionDetailsCubit, SectionDetailsState>(
        listener: (context, state) async {
      if (state is SectionDetailschangeDoneStatus) {
        status = state.doneStatus;
      }
    }, child: BlocBuilder<SectionDetailsCubit, SectionDetailsState>(
            builder: (context, state) {
      return Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: resourceFile.courseName,
              trailingWidget: Container(),
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {
                kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop();
              },
            )),
        body: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
              return Stack(children: [
                Align(
                  child: Container(
                      width: constraints.maxWidth < 720 ? double.infinity : 720,
                      margin:
                          EdgeInsets.symmetric(horizontal: _horizontalMargin),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          status == false || progess?.markedAsDone == false
                              ? _doneBtnAction(context, MARK_AS_DONE,
                                  _sectionDetailsCubit, state, args)
                              : Container(),
                          SizedBox(
                              height: status == false ||
                                      progess?.markedAsDone == false
                                  ? 6
                                  : 20),
                          Expanded(
                            child: Container(
                              height: MediaQuery.sizeOf(context).height - 300,
                              // width: MediaQuery.sizeOf(context).width - 70,

                              child: Align(
                                alignment: Alignment.topCenter,
                                child: ClipRRect(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(10)),
                                  child: PhotoView(
                                    tightMode: true,
                                    loadingBuilder: (BuildContext context,
                                        ImageChunkEvent? loadingProgress) {
                                      return const Center(
                                        child: CircularProgressIndicator(),
                                      );
                                    },
                                    minScale:
                                        PhotoViewComputedScale.contained * 0.8,
                                    maxScale:
                                        PhotoViewComputedScale.contained * 2.0,
                                    backgroundDecoration: const BoxDecoration(
                                      color: LmsColors.white,
                                    ),
                                    imageProvider: NetworkImage(imagePath),
                                    errorBuilder: (BuildContext context,
                                        Object exception,
                                        StackTrace? stackTrace) {
                                      return const Text(IMAGE_VIEW_ERROR_TEXT);
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ),
                          kIsWeb
                              ? closeButton(
                                  constraints, _sectionDetailsCubit, context)
                              : Container(),
                        ],
                      )),
                )
              ]);
            })),
      );
    }));
  }

  Widget _doneBtnAction(
      BuildContext context,
      String title,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      Map<String, dynamic> args) {
    return Visibility(
      visible: _hasAccessToSaveProgress.value,
      child: Container(
        decoration: const BoxDecoration(
          borderRadius:
              BorderRadiusDirectional.vertical(top: Radius.circular(16)),
        ),
        height: 60,
        margin: const EdgeInsets.only(top: 10),
        child: Align(
          alignment: Alignment.topRight,
          child: ButtonWidget(
            minimumSize: const Size(10, 10),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(buttonRadius),
                side: const BorderSide(
                  color: AppTheme.examInfoTextColor,
                )),
            color: AppTheme.whiteTextColor,
            child: Text(
              MARK_AS_DONE,
              style: LMSFonts.semiBoldFont(16, AppTheme.examInfoTextColor),
            ),
            onPressed: () async => _showConfirmationDialogue(
                context, _sectionDetailsCubit, state, args),
          ),
        ),
      ),
    );
  }

  _showConfirmationDialogue(
      BuildContext context,
      SectionDetailsCubit _sectionDetailsCubit,
      SectionDetailsState state,
      Map<String, dynamic> args) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (popcontext) {
        return MultiActionDialogue(
          alertType: AlertType.warning,
          title: CONFIRM,
          content: ALERT_TITLE,
          cancelBtnText: CANCEL_TEXT,
          confirmBtnText: EXAM_ALERT_OK,
          onCancelTap: () {
            kIsWeb ? Navigator.of(context).pop() : appRouter.pop();
          },
          onContinueTap: () async {
            Navigator.of(popcontext).pop();
            _sectionDetailsCubit.setLoading(true);

            await _sectionDetailsCubit.setCourseProgress(
                courseId!, instance, args);
            state = _sectionDetailsCubit.state;

            if (state is SetProgressState) {
              status = true;
              _showProgressDialog(context, args);
            } else {
              status = false;
            }
            _sectionDetailsCubit.setLoading(false);
          },
        );
      },
    );
  }

  _showProgressDialog(BuildContext context, Map<String, dynamic> args) {
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
          alertType: AlertType.success,
          title: SUCESS,
          content: PROGRESS_SAVED,
          cancelBtnText: "",
          confirmBtnText: OK,
          onCancelTap: () {},
          onContinueTap: () {
            if (!status) {
              kIsWeb
                  ? Beamer.of(context).beamBack()
                  : Navigator.of(context).pop();
            } else if (status) {
              if (kIsWeb) {
                Beamer.of(context)
                    .beamToReplacementNamed('/section-details', data: args);
              } else {
                appRouter.pop();
                Navigator.of(context).pop(args);
              }
            }
          },
        );
      },
    );
  }

  Widget closeButton(BoxConstraints constraints,
      SectionDetailsCubit _sectionDetailsCubit, BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Container(
        margin: constraints.maxWidth < 720
            ? const EdgeInsets.only(bottom: 10)
            : const EdgeInsets.only(bottom: 20),
        child: ButtonWidget(
            minimumSize: const Size(10, 10),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(buttonRadius),
                side: const BorderSide(
                  color: AppTheme.examInfoTextColor,
                )),
            color: AppTheme.whiteColor,
            child: Text(SLStrings.getTranslatedString(KEY_CLOSE),
                style: LMSFonts.regularFont(14, AppTheme.examIntroTextColor)),
            onPressed: () async {
              if (_sectionDetailsCubit.sectionDetails.modules.length > 1) {
                kIsWeb
                    ? Beamer.of(context)
                        .beamToReplacementNamed('/section-details')
                    : Navigator.of(context).pop();
              } else {
                kIsWeb
                    ? Beamer.of(context)
                        .beamToReplacementNamed('/course-details')
                    : appRouter.popUntil((route) =>
                        route.settings.name == CourseDetailsViewRoute.name);
              }
            }),
      ),
    );
  }
}
