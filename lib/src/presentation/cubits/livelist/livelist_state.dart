part of 'livelist_cubit.dart';

abstract class LivelistState extends Equatable {
  const LivelistState();

  @override
  List<Object> get props => [];
}

class LivelistInitial extends LivelistState {}

class LivelistLoading extends LivelistState {}

class LivelistLoaded extends LivelistState {
  final List<LiveClassModel> liveClasses;

  const LivelistLoaded(this.liveClasses);

  @override
  List<Object> get props => [liveClasses];
}

class LivelistError extends LivelistState {
  final String message;

  const LivelistError(this.message);

  @override
  List<Object> get props => [message];
}
