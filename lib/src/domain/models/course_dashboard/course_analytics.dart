// To parse this JSON data, do
//
//     final courseAnalyticsModel = courseAnalyticsModelFromJson(jsonString);

import 'dart:convert';

CourseAnalyticsModel courseAnalyticsModelFromJson(String str) =>
    CourseAnalyticsModel.fromJson(json.decode(str));

String courseAnalyticsModelToJson(CourseAnalyticsModel data) =>
    json.encode(data.toJson());

class CourseAnalyticsModel {
  List<List<QuizSummary>> quizSummary;
  String quizAttemptId;
  PerformanceSummary performanceSummary;

  CourseAnalyticsModel({
    required this.quizSummary,
    required this.quizAttemptId,
    required this.performanceSummary,
  });

  factory CourseAnalyticsModel.fromJson(Map<String, dynamic> json) =>
      CourseAnalyticsModel(
        quizSummary: [], // only performance summary is used from this response
        quizAttemptId: json["quiz_attempt_id"] ?? '',
        performanceSummary: PerformanceSummary.fromJson(
            json["performance_summary"] ?? []),
      );

  Map<String, dynamic> toJson() => {
        "quiz_summary": [],
        "quiz_attempt_id": quizAttemptId,
        "performance_summary": performanceSummary.toJson(),
      };
}

class PerformanceSummary {
  List<QuestionCategory> questionCategory;

  PerformanceSummary({
    required this.questionCategory,
  });

  factory PerformanceSummary.fromJson(Map<String, dynamic> json) =>
      PerformanceSummary(
        questionCategory: json["question_category"] != null
            ? List<QuestionCategory>.from(json["question_category"]
                .map((x) => QuestionCategory.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "question_category":
            List<dynamic>.from(questionCategory.map((x) => x.toJson())),
      };
}

class QuestionCategory {
  String categoryId;
  String categoryName;
  double marksObtained;
  int correctAnswers;
  int totalQuestions;

  QuestionCategory({
    required this.categoryId,
    required this.categoryName,
    required this.marksObtained,
    required this.correctAnswers,
    required this.totalQuestions,
  });

  factory QuestionCategory.fromJson(Map<String, dynamic> json) =>
      QuestionCategory(
        categoryId: json["category_id"],
        categoryName: json["category_name"],
        marksObtained: (json["marks_obtained"] ?? 0) * 1.0,
        correctAnswers: json["correct_answers"],
        totalQuestions: json["total_questions"],
      );

  Map<String, dynamic> toJson() => {
        "category_id": categoryId,
        "category_name": categoryName,
        "marks_obtained": marksObtained,
        "correct_answers": correctAnswers,
        "total_questions": totalQuestions,
      };
}

class QuizSummary {
  Course course;
  String quizId;
  String quizName;
  double passMarks;
  double totalMarks;
  int totalQuestions;
  double totalMarksObtained;

  QuizSummary({
    required this.course,
    required this.quizId,
    required this.quizName,
    required this.passMarks,
    required this.totalMarks,
    required this.totalQuestions,
    required this.totalMarksObtained,
  });

  factory QuizSummary.fromJson(Map<String, dynamic> json) => QuizSummary(
        course: Course.fromJson(json["course"]),
        quizId: json["quiz_id"],
        quizName: json["quiz_name"],
        passMarks: (json["pass_marks"] ?? 0) * 1.0,
        totalMarks: (json["total_marks"] ?? 0) * 1.0,
        totalQuestions: json["total_questions"],
        totalMarksObtained: (json["total_marks_obtained"] ?? 0) * 1.0,
      );

  Map<String, dynamic> toJson() => {
        "course": course.toJson(),
        "quiz_id": quizId,
        "quiz_name": quizName,
        "pass_marks": passMarks,
        "total_marks": totalMarks,
        "total_questions": totalQuestions,
        "total_marks_obtained": totalMarksObtained,
      };
}

class Course {
  String id;
  String name;
  String shortName;
  String description;

  Course({
    required this.id,
    required this.name,
    required this.shortName,
    required this.description,
  });

  factory Course.fromJson(Map<String, dynamic> json) => Course(
        id: json["id"],
        name: json["name"],
        shortName: json["short_name"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "short_name": shortName,
        "description": description,
      };
}
