import 'dart:io';

import 'package:get/utils.dart';

import '../../../config/app_config/api_constants.dart';
import '../../../domain/models/category/user_category.dart';
import '../../../domain/models/check_point_data/check_point_data.dart';
import '../../../domain/models/course_dashboard/course_assignments.dart';
import '../../../domain/models/course_dashboard/ppt_data.dart';
import '../../../domain/repositories/course_details_repository.dart';
import '/src/domain/repositories/user_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../utils/constants/helper.dart';
import '/src/config/app_config/preference_config.dart';

import '/src/domain/models/hive_db_models/hive_video_player.dart';
import '/src/domain/services/hive_db_service.dart';

import '/src/domain/models/milestone_data.dart';
import '/src/domain/models/videoplayer_data.dart';
import '/src/domain/services/db_helper.dart';
import 'dart:async';

import '/src/utils/constants/strings.dart';
import '/src/utils/helper/thumbnail_generator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../domain/models/course_details.dart';
import '../../../domain/models/resource_comment.dart';
import '../../../domain/repositories/course_respository.dart';

part 'course_list_state.dart';

class CourseListCubit extends Cubit<CourseListState> {
  final CourseRepository _courseRepository;
  final CourseDetailsRepository _courseDetailsRepository;
  final HiveDbHelper _hiveDbHelper;
  final UserRepository _userRepository;

  CourseListCubit(this._courseRepository, this._courseDetailsRepository,
      this._hiveDbHelper, this._userRepository)
      : super(CourseListInitial());

  String topicId = '';
  String orgId = '';
  List<DateTime> months = [];

  Map<String, dynamic> videoPlayerData = {
    "lastWatchTime": 0,
    "checkPointIndex": 0,
  };

  Map<String, dynamic> pptDataCoj = {
    "url": "",
    "Index": 0,
  };

  updatepptStatus(String url, int index) {
    pptDataCoj['url'] = url;
    pptDataCoj['Index'] = index;
  }

  initCourseCubit() {
    emit(CourseListInitial());
  }

  Future<void> getUserCourse() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    topicId = _prefs.getString(PREF_KEY_TOPIC_ID) ?? TOPIC_ID;
    orgId = _prefs.getString(PREF_KEY_ORG_ID) ?? ORG_ID;

    //bool networkStatus = _prefs.getBool("networkStatus") ?? false;
    emit(await _getUserCourse(false, topicId));
  }

  Future<CourseListState> _getUserCourse(
      bool updateBtnTheme, String topicId) async {
    try {
      if (topicId.isNotEmpty) {
        final course =
            <Course>[]; //await _courseRepository.getUserCourse(topicId, orgId);
        return CourseListSuccess(course: course, buttonColor: updateBtnTheme);
      } else {
        return CourseListError(error: '');
      }
      //course.sort();
    } on Exception catch (e) {
      return CourseListError(error: e.toString());
    }
  }

  Future<void> handleCourseSelection(String? selectedCourseName) async {
    if (selectedCourseName != null && selectedCourseName.isNotEmpty) {
      emit(UpdateSelectedCourseColor(shouldChangeColor: true));
    } else {
      emit(UpdateSelectedCourseColor(shouldChangeColor: false));
    }
  }

  ///
  /// Course Details
  ///
  ///
  fetchCourseDetails(String courseId) async {
    String orgId = ORG_ID;
    String userId = USER_ID;
    if (courseId.isEmpty || orgId.isEmpty || userId.isEmpty) {
      emit(CourseDetailsEmpty(course: state.course));
      return;
    }
    try {
      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "user_id": USER_ID,
        "course_id": courseId
      };
      final courseDetails = await _courseRepository.fetchCourseDetailsInfo(
          courseId, orgId, userId);

      final courseAssignments =
          await _courseDetailsRepository.fetchCourseAssignments(jsonReq);
      courseAssignments
          .sort(((a, b) => a.moduleOrder.compareTo(b.moduleOrder)));

      final hasInProgressItems = courseAssignments.any(
        (e) =>
            (e.isEnabled && e.progress != null && e.progress! < 100) &&
            !e.isSkipped,
      );

      final firstInProgressItem = courseAssignments.firstWhereOrNull(
        (e) =>
            e.resourceType != ResourceType.QUIZ && !e.isEnabled && !e.isSkipped,
      );
      final firstInProgressIndex = firstInProgressItem != null
          ? courseAssignments.indexOf(firstInProgressItem)
          : -1;

      if (firstInProgressIndex != -1) {
        courseAssignments[firstInProgressIndex].isEnabled = !hasInProgressItems;
      }

      if (courseDetails.isNotEmpty) {
        if ((courseDetails.first.courseModule.isEmpty &&
            courseDetails.first.courseVideos.isEmpty &&
            courseDetails.first.currentAffairs.isEmpty &&
            courseDetails.first.exams.isEmpty &&
            courseAssignments.isEmpty)) {
          emit(CourseDetailsEmpty(course: state.course));
        } else {
          final _courseDetails =
              await generateVideoThumbnails(courseDetails.first);
          _courseDetails.courseAssignments = courseAssignments;
          emit(CourseDetailsFetched(
              courseDetailsData: _courseDetails, course: state.course));
        }
      } else {
        emit(CourseDetailsEmpty(course: state.course));
      }
    } on PostgrestException catch (e) {
      if (e.code == TOKEN_EXPIRED_STATUS_CODE) {
        final response = await Supabase.instance.client.auth.refreshSession();
        if (response.session?.user != null) {
          fetchCourseDetails(courseId);
        } else {
          await _userRepository.userSignout();
          clearCurrentUserInfo();
        }
      } else {
        debugPrint(
            "[PostgrestException][CourseListCubit][fetchCourseDetails] :${e.details.toString()}");
        emit(CourseDetailsError(error: e.details.toString()));
      }
    } on SocketException catch (_) {
      emit(CourseDetailsEmpty(course: state.course));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][fetchCourseDetails] :${e.toString()}");
      emit(CourseDetailsEmpty(course: state.course));
    } catch (e) {
      setLoading(false);
      emit(CourseDetailsEmpty(course: state.course));
    }
  }

  Future<CourseDetails> generateVideoThumbnails(
      CourseDetails courseDetails) async {
    try {
      for (var courseVideoArg in courseDetails.courseVideos) {
        String thumbnail = '';
        bool _isYouTubeVideo = courseVideoArg != null &&
            courseVideoArg.videoURL != '' &&
            courseVideoArg.videoURL.startsWith(YOUTUBE_URL);
        if (_isYouTubeVideo) {
          String videoId = extractVideoId(courseVideoArg.videoURL);
          thumbnail = await getThumbnailUrl(videoId);
          courseVideoArg.videoThumbnail = thumbnail;
        }
      }
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][generateVideoThumbnails] :${e.toString()}");
    }
    return courseDetails;
  }

  String getThumbnailUrl(String videoId) {
    if (videoId.contains(HTTPS)) {
      videoId = videoId.replaceAll(HTTPS, '');
      return 'https://img.youtube.com/vi/$videoId/0.jpg';
    } else {
      return 'https://img.youtube.com/vi/$videoId/0.jpg';
    }
  }

  ///
  /// Current affairs for current month
  ///
  fetchMoreCurrentAffairs() async {
    try {
      final currentAffairs = await _courseRepository.fetchMoreCurrentAffairs();
      if (currentAffairs == null || currentAffairs.isEmpty) {
        currentAffairs
            .sort((a, b) => b.publishedDate.compareTo(a.publishedDate));

        emit(CurrentAffairsFailure(error: 'Empty list'));
      } else {
        emit(CurrentAffairsFetched(
            currentAffairs: currentAffairs, course: state.course));
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][fetchMoreCurrentAffairs] :${e.toString()}");
      emit(CurrentAffairsFailure(error: e.toString()));
    }
  }

  setLoading(bool loadStatus) {
    emit(CourseDetailsLoading(isDataLoading: loadStatus, course: state.course));
  }

  /// remove scroll indicator arrow for affairs items
  removeArrow(bool canScroll) {
    emit(CourseNewsScroll(canScroll: canScroll));
  }

  ///
  /// Course Videos View
  ///
  videoCallback(bool isFulllScreen) {
    emit(CourseVideoStatus(isFullSCreen: isFulllScreen));
  }

  pauseVideo() {
    emit(VideoPlayerStatus());
  }

  handleVideoPlayerError() {
    emit(VideoPlayerError());
  }

  fetchCommentsForId(String instanceId) async {
    try {
      List<ResourseComment> comments = await _courseRepository
          .fetchCommentsForId(instanceId, ResActivityType.comment.name);
      List<ResourseComment> likes = await _courseRepository.fetchCommentsForId(
          instanceId, ResActivityType.like.name);
      setLoading(false);
      emit(CourseCommentsFetched(comments: comments + likes));
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][CommentsError] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(CommentsError(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][fetchCommentsForId] :${e.toString()}");
      emit(CommentsError(error: e.toString()));
    }
  }

  uploadComment(Map<String, dynamic> jsonReqBody) async {
    Map<String, dynamic> jsonBody = jsonReqBody;
    try {
      dynamic response = await _courseRepository.uploadCommentsForId(jsonBody);
      setLoading(false);
      if (response != null &&
          response['status'].toLowerCase() == SUCCESS.toLowerCase()) {
        emit(CourseCommentsUploaded(comments: []));
      } else {
        emit(CommentsError(error: 'Failed to upload comment'));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][CommentsError] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(CommentsError(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][uploadComment] :${e.toString()}");
      emit(CommentsError(error: e.toString()));
    }
  }

  addComment(
      ResourseComment comment, List<ResourseComment> existingComments) async {
    List<ResourseComment> updatedCommentList = existingComments;

    try {
      if (comment.parentId.isNotEmpty) {
        for (var existingComment in updatedCommentList) {
          if (existingComment.commentId == comment.parentId) {
            existingComment.children?.add(comment);
            if (existingComment.children != null) {
              existingComment.children!.sort((a, b) =>
                  (a.commentedTime ?? DateTime.fromMillisecondsSinceEpoch(0))
                      .compareTo(b.commentedTime ??
                          DateTime.fromMillisecondsSinceEpoch(0)));
            }
            break;
          }
        }
      } else {
        updatedCommentList.add(comment);
      }
      updatedCommentList.sort((a, b) =>
          (a.commentedTime ?? DateTime.fromMillisecondsSinceEpoch(0)).compareTo(
              b.commentedTime ?? DateTime.fromMillisecondsSinceEpoch(0)));
      emit(CommentsAdded(comments: updatedCommentList));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][uploadComment] :${e.toString()}");
      emit(CommentsError(error: e.toString()));
    }
  }

  handleCommentTypeSelection(String type) {
    emit(CommentTypeSelected(commentType: type));
  }

  setQuestionLoading(bool status) {
    emit(SetQuestionLoading(status: status));
  }

  ///
  /// Course News View
  ///
  dateSelectionCallback(
      DateTime selectedDate, List<CurrentAffairs> availableData) {
    final List<CurrentAffairs> filteredList = List.from(availableData.where(
        (element) =>
            element.publishedDate.month == selectedDate.month &&
            element.publishedDate.year == selectedDate.year));
    filteredList.sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
    emit(CourseNewsSelection(
        selectedDate: selectedDate, filteredList: filteredList));
  }

  generateMonthListForAffairs() {
    months = generateMonthList();
    emit(CourseNewsMonthGeneration(monthList: months));
  }

  // set course progress
  setCourseProgress(
      String courseId, String instance, Map<String, dynamic> duration) async {
    try {
      final responseData = await _courseRepository.setCourseProgress(
          courseId, instance, duration);

      if (responseData["status"] == 'success') {
        emit(SetResourceProgressState());
      } else {
        emit(ResourceProgressFailureState(error: responseData.toString()));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][setCourseProgress] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(ResourceProgressFailureState(error: errorMsg));
    } on Exception catch (e) {
      emit(ResourceProgressFailureState(error: e.toString()));
    }
  }

// To Enable Progress Button
  Future<void> enableSaveProgressBtn(bool value) async {
    // if (value == true) {
    //   emit(CourseListButtonEnable(enable: true));
    // } else {
    //   emit(CourseListButtonEnable(enable: false));
    // }
  }

  setSkippedResource(CourseAssignments courseResource) async {
    try {
      Map<String, dynamic> jsonReq = {
        "org_id": ORG_ID,
        "course_id": courseResource.courseId,
        "resource_id": courseResource.resourceId,
        "user_id": USER_ID,
        "type": courseResource.resourceType.name.capitalizeFirst,
        "action": "skipped",
        "progress": courseResource.progress,
        "course_module_id": courseResource.courseModuleId,
      };

      final responseData = await _courseRepository.setSkippedResource(jsonReq);

      if (responseData["status"] == 1) {
        emit(SkipResourceSuccess());
      } else {
        emit(SkipResourceFailure(error: responseData.toString()));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][SkipResourceFailure] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(SkipResourceFailure(error: errorMsg));
    } on Exception catch (e) {
      emit(SkipResourceFailure(error: e.toString()));
    }
  }

  updateResourceLike(Map<String, dynamic> jsonReqBody) async {
    Map<String, dynamic> jsonBody = jsonReqBody;
    try {
      dynamic response = await _courseRepository.setLikeCount(jsonBody);
      setLoading(false);
      if (response != null &&
          response['status'].toLowerCase() == SUCCESS.toLowerCase()) {
        emit(ResourceLikeSuccess());
      } else {
        emit(ResourceLikeError(error: 'Failed to update like'));
      }
    } on PostgrestException catch (e) {
      debugPrint(
          "[PostgrestException][CourseListCubit][ResourceLikeError] :${e.toString()}");
      String errorMsg = e.details.toString();
      emit(ResourceLikeError(error: errorMsg));
    } on Exception catch (e) {
      debugPrint(
          "[Exception][CourseListCubit][ResourceLikeError] :${e.toString()}");
      emit(ResourceLikeError(error: e.toString()));
    }
  }

  ///
  /// UI Enchancements
  ///

  changeCarouselIndex(int updatedIndex) {
    emit(CarouselChangeIndex(carouselIndex: updatedIndex));
  }

  //VideoplayerData
  Future<List<VideoPlayerData>> getVideoPlayerData(String id) async {
    return await DbHelper.instance.getVideoPlayerData(id);
  }

  Future<void> insertToVideoData(VideoPlayerData data) async {
    await DbHelper.instance.insertToVideoData(data);
  }

  Future<void> insertToMilestoneData(MilestoneData data) async {
    await DbHelper.instance.insertMilestoneData(data);
  }

  Future<void> getMilestoneData(String id) async {
    await DbHelper.instance.getMilestoneData(id);
  }

  Future<void> insertToHiveVideoData(HiveVideoPlayerData data) async {
    await _hiveDbHelper.hiveInsertOrUpdateVideoData(data);
  }

  Future<HiveVideoPlayerData?> hiveGetVideoPlayerData(String videoId) async {
    return await _hiveDbHelper.hiveGetVideoPlayerData(videoId);
  }

  updatelastWatchTime(int watchTime, int index) {
    videoPlayerData['lastWatchTime'] = watchTime;
    videoPlayerData['checkPointIndex'] = index;
  }

  void updateYoutubePlayerStatus(bool status) {
    emit(YoutubePlayerLoaded(status: status));
  }

///////////////////

  Future<void> insertToCheckPointData(
      String resid, List<CheckPoint> checkPoints) async {
    await DbHelper.instance.insertCheckPoints(resid, checkPoints);
  }

  Future<List<CheckPoint>> getCheckPointData(String resid) async {
    return await DbHelper.instance.getCheckPoints(resid);
  }

  Future<void> insertToCompletedCheckPoint(
      CheckPoint checkPoint, String resid) async {
    await DbHelper.instance.insertCompletedCheckPoint(checkPoint, resid);
  }

  Future<CheckPoint?> getCompletedCheckPointData(String resid) async {
    return await DbHelper.instance.getCompletedCheckPoint(resid);
  }

  Future<void> insertToPPTData(PPTData pptData, String resid) async {
    await DbHelper.instance.insertPPTData(pptData, resid);
  }

  Future<PPTData?> getPPTData(
    String resid,
  ) async {
    return await DbHelper.instance.getPPTData(resid);
  }

  Future<void> insertToPPTSlideData(PPTData pptData) async {
    await DbHelper.instance.insertPPTSlideData(pptData);
  }

  Future<PPTData?> getPPTSlideData(String checkpointId) async {
    return await DbHelper.instance.getPPTSlideData(checkpointId);
  }
}
