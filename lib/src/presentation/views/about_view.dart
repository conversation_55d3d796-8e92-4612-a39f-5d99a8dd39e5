import '/src/config/themes/lms_fonts.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';

import '/src/config/themes/app_theme.dart';

import '/src/utils/constants/strings.dart';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../config/router/app_router.dart';

import '../widgets/app_bar.dart';

@RoutePage()
class AboutViewScreen extends HookWidget {
  const AboutViewScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppTheme.bgColor,
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(70),
            child: AppBarWidget(
              title: ABOUT_APPBAR_TITLE,
              trailingWidget: Container(),
              leadingIconName: BACK_ARROW,
              leadingBtnAction: () {
                kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop();
              },
            )),
        body: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
          return Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: ScrollConfiguration(
              behavior: ScrollConfiguration.of(context).copyWith(
                dragDevices: {
                  PointerDeviceKind.touch,
                  PointerDeviceKind.mouse,
                },
              ),
              child: CustomScrollView(
                slivers: [
                  SliverFillRemaining(
                    hasScrollBody: false,
                    child: Container(
                      margin:
                          const EdgeInsets.only(left: 16, right: 16, top: 20),
                      child: Align(
                        child: SizedBox(
                          width: constraints.maxWidth < 720
                              ? constraints.maxWidth
                              : 800,
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  height: 150,
                                  width: 150,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(100),
                                    color: AppTheme.primaryAppColor,
                                  ),
                                  child: Image.asset(
                                    'assets/launcher/logo.png',
                                    height: 150,
                                    width: 150,
                                  ),
                                ),
                                const SizedBox(height: 20),
                                Text(
                                  ABOUT_STATEMENT_ONE,
                                  style: LMSFonts.regularFont(
                                      14, AppTheme.primaryTextColorBlack),
                                  textAlign: TextAlign.justify,
                                ),
                                // Text(ABOUT_FACEBOOK,
                                //     style: LMSFonts.regularFont(
                                //         14, AppTheme.primaryTextColorBlack)),
                                const Spacer(),
                                Text(ABOUT_ADDRESS,
                                    style: LMSFonts.regularFont(
                                        14, AppTheme.primaryTextColorBlack)),
                                // Text(
                                //   ABOUT_EMAIL_HEADING,
                                //   style: LMSFonts.regularFont(
                                //       14, AppTheme.primaryTextColorBlack),
                                // ),
                                // Text(
                                //   ABOUT_EMAIL_ADDRESS,
                                //   style: LMSFonts.regularFont(
                                //       14, AppTheme.primaryTextColorBlack),
                                // )
                              ]),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }));
  }
}
