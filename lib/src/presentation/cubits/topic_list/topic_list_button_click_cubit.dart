// import 'package:bloc/bloc.dart';
// import 'package:equatable/equatable.dart';


// part 'topic_list_button_click_state.dart';


// class TopicListButtonClickCubit extends Cubit<TopicListButtonClickState> {
//   TopicListButtonClickCubit() : super( const TopicListButtonClickInitial());

//   void updateColor(String text) {
//     if (text.isNotEmpty) {
//       emit( const TopicListButtonClickInitial(buttonColor: true));
//     } else {
//       emit( const TopicListButtonClickInitial());
//     }
  
//   }
// }