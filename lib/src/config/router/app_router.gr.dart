// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

abstract class _$AppRouter extends RootStackRouter {
  // ignore: unused_element
  _$AppRouter({super.navigatorKey});

  @override
  final Map<String, PageFactory> pagesMap = {
    AboutViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const AboutViewScreen(),
      );
    },
    CourseDetailsViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseDetailsViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseDetailsViewScreen(
          key: args.key,
          courseId: args.courseId,
          courseName: args.courseName,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    CourseListViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseListViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseListViewScreen(
          key: args.key,
          topic: args.topic,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    CourseModuleViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseModuleViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseModuleViewScreen(
          key: args.key,
          courseModule: args.courseModule,
        ),
      );
    },
    CourseNewsViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseNewsViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseNewsViewScreen(
          key: args.key,
          currentAffairs: args.currentAffairs,
        ),
      );
    },
    CourseVideoViewRoute.name: (routeData) {
      final args = routeData.argsAs<CourseVideoViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: CourseVideoViewScreen(
          args.courseVideo,
          key: args.key,
          courseId: args.courseId,
          isFromSectionDetails: args.isFromSectionDetails,
          progess: args.progess,
          checkPoints: args.checkPoints,
          isFromExamScreen: args.isFromExamScreen,
          isExamPassed: args.isExamPassed,
        ),
      );
    },
    DailyCourseListViewRoute.name: (routeData) {
      final args = routeData.argsAs<DailyCourseListViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: DailyCourseListViewScreen(
          key: args.key,
          title: args.title,
        ),
      );
    },
    DrawingPadViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: DrawingPadViewScreen(),
      );
    },
    DrawingReviewViewRoute.name: (routeData) {
      final args = routeData.argsAs<DrawingReviewViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: DrawingReviewViewScreen(
          args.imageData,
          args.remarks,
          key: args.key,
        ),
      );
    },
    ExamIntroductionViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamIntroductionViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamIntroductionViewScreen(
          key: args.key,
          questionData: args.questionData,
          isFromSectionDetails: args.isFromSectionDetails,
          isFromVideoScreen: args.isFromVideoScreen,
        ),
      );
    },
    ExamListViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamListViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamListViewScreen(
          args.tabIndex,
          key: args.key,
        ),
      );
    },
    ExamResultViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamResultViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamResultViewScreen(
          key: args.key,
          examSummary: args.examSummary,
          tabIndex: args.tabIndex,
        ),
      );
    },
    ExamReviewViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamReviewViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamReviewViewScreen(
          key: args.key,
          quizId: args.quizId,
          quizAttemptId: args.quizAttemptId,
          isGradeCalculated: args.isGradeCalculated,
        ),
      );
    },
    ExamViewRoute.name: (routeData) {
      final args = routeData.argsAs<ExamViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ExamViewScreen(
          key: args.key,
          examDuration: args.examDuration,
          shouldShowTimer: args.shouldShowTimer,
          isFromViewResult: args.isFromViewResult,
          quizAttemptId: args.quizAttemptId,
          questionData: args.questionData,
          isFromVideoView: args.isFromVideoView,
        ),
      );
    },
    ImageViewRoute.name: (routeData) {
      final args = routeData.argsAs<ImageViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ImageViewScreen(
          key: args.key,
          imagePath: args.imagePath,
          resourceFile: args.resourceFile,
          instance: args.instance,
          courseId: args.courseId,
          progess: args.progess,
        ),
      );
    },
    LoginEmailViewRoute.name: (routeData) {
      final args = routeData.argsAs<LoginEmailViewRouteArgs>(
          orElse: () => const LoginEmailViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: LoginEmailViewScreen(
          key: args.key,
          isTokenExpired: args.isTokenExpired,
        ),
      );
    },
    NewsViewMoreViewRoute.name: (routeData) {
      final args = routeData.argsAs<NewsViewMoreViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: NewsViewMoreViewScreen(
          args.affairsList,
          key: args.key,
        ),
      );
    },
    NoAccessViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const NoAccessViewScreen(),
      );
    },
    OnboardingViewRoute.name: (routeData) {
      final args = routeData.argsAs<OnboardingViewRouteArgs>(
          orElse: () => const OnboardingViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: OnboardingViewScreen(key: args.key),
      );
    },
    OrganizationViewRoute.name: (routeData) {
      final args = routeData.argsAs<OrganizationViewRouteArgs>(
          orElse: () => const OrganizationViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: OrganizationViewScreen(
          key: args.key,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    PDFViewRoute.name: (routeData) {
      final args = routeData.argsAs<PDFViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: PDFViewScreen(
          key: args.key,
          urlPDFPath: args.urlPDFPath,
        ),
      );
    },
    ProfileReviewRoute.name: (routeData) {
      final args = routeData.argsAs<ProfileReviewRouteArgs>(
          orElse: () => const ProfileReviewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ProfileReviewScreen(key: args.key),
      );
    },
    ProfileViewRoute.name: (routeData) {
      final args = routeData.argsAs<ProfileViewRouteArgs>(
          orElse: () => const ProfileViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ProfileViewScreen(
          key: args.key,
          isFromLogin: args.isFromLogin,
          isFromSideMenu: args.isFromSideMenu,
        ),
      );
    },
    RankListViewRoute.name: (routeData) {
      final args = routeData.argsAs<RankListViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: RankListViewScreen(
          args.isCourseRankList,
          args.rankListForExam,
          key: args.key,
        ),
      );
    },
    ResetPasswordViewRoute.name: (routeData) {
      final args = routeData.argsAs<ResetPasswordViewRouteArgs>(
          orElse: () => const ResetPasswordViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: ResetPasswordViewScreen(key: args.key),
      );
    },
    SectionDetailsViewRoute.name: (routeData) {
      final args = routeData.argsAs<SectionDetailsViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SectionDetailsViewScreen(
          key: args.key,
          sectionId: args.sectionId,
          title: args.title,
        ),
      );
    },
    SettingsViewRoute.name: (routeData) {
      final args = routeData.argsAs<SettingsViewRouteArgs>(
          orElse: () => const SettingsViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SettingsViewScreen(key: args.key),
      );
    },
    SignUpViewRoute.name: (routeData) {
      final args = routeData.argsAs<SignUpViewRouteArgs>(
          orElse: () => const SignUpViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SignUpViewScreen(key: args.key),
      );
    },
    SplashRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SplashScreen(),
      );
    },
    StudyMaterialViewRoute.name: (routeData) {
      final args = routeData.argsAs<StudyMaterialViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: StudyMaterialViewScreen(
          key: args.key,
          instance: args.instance,
          courseId: args.courseId,
          progess: args.progess,
          pageContent: args.pageContent,
        ),
      );
    },
    SubjectsDetailedTabViewRoute.name: (routeData) {
      final args = routeData.argsAs<SubjectsDetailedTabViewRouteArgs>();
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SubjectsDetailedTabViewScreen(args.title),
      );
    },
    SubscriptionViewRoute.name: (routeData) {
      final args = routeData.argsAs<SubscriptionViewRouteArgs>(
          orElse: () => const SubscriptionViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: SubscriptionViewScreen(key: args.key),
      );
    },
    TopicListViewRoute.name: (routeData) {
      final args = routeData.argsAs<TopicListViewRouteArgs>(
          orElse: () => const TopicListViewRouteArgs());
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: TopicListViewScreen(
          key: args.key,
          isFromLogin: args.isFromLogin,
        ),
      );
    },
    WelcomeViewRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const WelcomeViewScreen(),
      );
    },
  };
}

/// generated route for
/// [AboutViewScreen]
class AboutViewRoute extends PageRouteInfo<void> {
  const AboutViewRoute({List<PageRouteInfo>? children})
      : super(
          AboutViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'AboutViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [CourseDetailsViewScreen]
class CourseDetailsViewRoute extends PageRouteInfo<CourseDetailsViewRouteArgs> {
  CourseDetailsViewRoute({
    Key? key,
    required String courseId,
    required String courseName,
    bool isFromLogin = false,
    List<PageRouteInfo>? children,
  }) : super(
          CourseDetailsViewRoute.name,
          args: CourseDetailsViewRouteArgs(
            key: key,
            courseId: courseId,
            courseName: courseName,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseDetailsViewRoute';

  static const PageInfo<CourseDetailsViewRouteArgs> page =
      PageInfo<CourseDetailsViewRouteArgs>(name);
}

class CourseDetailsViewRouteArgs {
  const CourseDetailsViewRouteArgs({
    this.key,
    required this.courseId,
    required this.courseName,
    this.isFromLogin = false,
  });

  final Key? key;

  final String courseId;

  final String courseName;

  final bool isFromLogin;

  @override
  String toString() {
    return 'CourseDetailsViewRouteArgs{key: $key, courseId: $courseId, courseName: $courseName, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [CourseListViewScreen]
class CourseListViewRoute extends PageRouteInfo<CourseListViewRouteArgs> {
  CourseListViewRoute({
    Key? key,
    required String topic,
    bool isFromLogin = false,
    List<PageRouteInfo>? children,
  }) : super(
          CourseListViewRoute.name,
          args: CourseListViewRouteArgs(
            key: key,
            topic: topic,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseListViewRoute';

  static const PageInfo<CourseListViewRouteArgs> page =
      PageInfo<CourseListViewRouteArgs>(name);
}

class CourseListViewRouteArgs {
  const CourseListViewRouteArgs({
    this.key,
    required this.topic,
    this.isFromLogin = false,
  });

  final Key? key;

  final String topic;

  final bool isFromLogin;

  @override
  String toString() {
    return 'CourseListViewRouteArgs{key: $key, topic: $topic, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [CourseModuleViewScreen]
class CourseModuleViewRoute extends PageRouteInfo<CourseModuleViewRouteArgs> {
  CourseModuleViewRoute({
    Key? key,
    required CourseModule courseModule,
    List<PageRouteInfo>? children,
  }) : super(
          CourseModuleViewRoute.name,
          args: CourseModuleViewRouteArgs(
            key: key,
            courseModule: courseModule,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseModuleViewRoute';

  static const PageInfo<CourseModuleViewRouteArgs> page =
      PageInfo<CourseModuleViewRouteArgs>(name);
}

class CourseModuleViewRouteArgs {
  const CourseModuleViewRouteArgs({
    this.key,
    required this.courseModule,
  });

  final Key? key;

  final CourseModule courseModule;

  @override
  String toString() {
    return 'CourseModuleViewRouteArgs{key: $key, courseModule: $courseModule}';
  }
}

/// generated route for
/// [CourseNewsViewScreen]
class CourseNewsViewRoute extends PageRouteInfo<CourseNewsViewRouteArgs> {
  CourseNewsViewRoute({
    Key? key,
    required CurrentAffairs currentAffairs,
    List<PageRouteInfo>? children,
  }) : super(
          CourseNewsViewRoute.name,
          args: CourseNewsViewRouteArgs(
            key: key,
            currentAffairs: currentAffairs,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseNewsViewRoute';

  static const PageInfo<CourseNewsViewRouteArgs> page =
      PageInfo<CourseNewsViewRouteArgs>(name);
}

class CourseNewsViewRouteArgs {
  const CourseNewsViewRouteArgs({
    this.key,
    required this.currentAffairs,
  });

  final Key? key;

  final CurrentAffairs currentAffairs;

  @override
  String toString() {
    return 'CourseNewsViewRouteArgs{key: $key, currentAffairs: $currentAffairs}';
  }
}

/// generated route for
/// [CourseVideoViewScreen]
class CourseVideoViewRoute extends PageRouteInfo<CourseVideoViewRouteArgs> {
  CourseVideoViewRoute({
    required CourseVideo courseVideo,
    Key? key,
    String? courseId = "",
    bool isFromSectionDetails = false,
    required CourseProgress? progess,
    List<CheckPoint> checkPoints = const [],
    bool isFromExamScreen = false,
    bool isExamPassed = false,
    List<PageRouteInfo>? children,
  }) : super(
          CourseVideoViewRoute.name,
          args: CourseVideoViewRouteArgs(
            courseVideo: courseVideo,
            key: key,
            courseId: courseId,
            isFromSectionDetails: isFromSectionDetails,
            progess: progess,
            checkPoints: checkPoints,
            isFromExamScreen: isFromExamScreen,
            isExamPassed: isExamPassed,
          ),
          initialChildren: children,
        );

  static const String name = 'CourseVideoViewRoute';

  static const PageInfo<CourseVideoViewRouteArgs> page =
      PageInfo<CourseVideoViewRouteArgs>(name);
}

class CourseVideoViewRouteArgs {
  const CourseVideoViewRouteArgs({
    required this.courseVideo,
    this.key,
    this.courseId = "",
    this.isFromSectionDetails = false,
    required this.progess,
    this.checkPoints = const [],
    this.isFromExamScreen = false,
    this.isExamPassed = false,
  });

  final CourseVideo courseVideo;

  final Key? key;

  final String? courseId;

  final bool isFromSectionDetails;

  final CourseProgress? progess;

  final List<CheckPoint> checkPoints;

  final bool isFromExamScreen;

  final bool isExamPassed;

  @override
  String toString() {
    return 'CourseVideoViewRouteArgs{courseVideo: $courseVideo, key: $key, courseId: $courseId, isFromSectionDetails: $isFromSectionDetails, progess: $progess, checkPoints: $checkPoints, isFromExamScreen: $isFromExamScreen, isExamPassed: $isExamPassed}';
  }
}

/// generated route for
/// [DailyCourseListViewScreen]
class DailyCourseListViewRoute
    extends PageRouteInfo<DailyCourseListViewRouteArgs> {
  DailyCourseListViewRoute({
    Key? key,
    required String title,
    List<PageRouteInfo>? children,
  }) : super(
          DailyCourseListViewRoute.name,
          args: DailyCourseListViewRouteArgs(
            key: key,
            title: title,
          ),
          initialChildren: children,
        );

  static const String name = 'DailyCourseListViewRoute';

  static const PageInfo<DailyCourseListViewRouteArgs> page =
      PageInfo<DailyCourseListViewRouteArgs>(name);
}

class DailyCourseListViewRouteArgs {
  const DailyCourseListViewRouteArgs({
    this.key,
    required this.title,
  });

  final Key? key;

  final String title;

  @override
  String toString() {
    return 'DailyCourseListViewRouteArgs{key: $key, title: $title}';
  }
}

/// generated route for
/// [DrawingPadViewScreen]
class DrawingPadViewRoute extends PageRouteInfo<void> {
  const DrawingPadViewRoute({List<PageRouteInfo>? children})
      : super(
          DrawingPadViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'DrawingPadViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [DrawingReviewViewScreen]
class DrawingReviewViewRoute extends PageRouteInfo<DrawingReviewViewRouteArgs> {
  DrawingReviewViewRoute({
    required Uint8List imageData,
    required String remarks,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          DrawingReviewViewRoute.name,
          args: DrawingReviewViewRouteArgs(
            imageData: imageData,
            remarks: remarks,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'DrawingReviewViewRoute';

  static const PageInfo<DrawingReviewViewRouteArgs> page =
      PageInfo<DrawingReviewViewRouteArgs>(name);
}

class DrawingReviewViewRouteArgs {
  const DrawingReviewViewRouteArgs({
    required this.imageData,
    required this.remarks,
    this.key,
  });

  final Uint8List imageData;

  final String remarks;

  final Key? key;

  @override
  String toString() {
    return 'DrawingReviewViewRouteArgs{imageData: $imageData, remarks: $remarks, key: $key}';
  }
}

/// generated route for
/// [ExamIntroductionViewScreen]
class ExamIntroductionViewRoute
    extends PageRouteInfo<ExamIntroductionViewRouteArgs> {
  ExamIntroductionViewRoute({
    Key? key,
    required QuestionData questionData,
    bool isFromSectionDetails = false,
    bool isFromVideoScreen = false,
    List<PageRouteInfo>? children,
  }) : super(
          ExamIntroductionViewRoute.name,
          args: ExamIntroductionViewRouteArgs(
            key: key,
            questionData: questionData,
            isFromSectionDetails: isFromSectionDetails,
            isFromVideoScreen: isFromVideoScreen,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamIntroductionViewRoute';

  static const PageInfo<ExamIntroductionViewRouteArgs> page =
      PageInfo<ExamIntroductionViewRouteArgs>(name);
}

class ExamIntroductionViewRouteArgs {
  const ExamIntroductionViewRouteArgs({
    this.key,
    required this.questionData,
    this.isFromSectionDetails = false,
    this.isFromVideoScreen = false,
  });

  final Key? key;

  final QuestionData questionData;

  final bool isFromSectionDetails;

  final bool isFromVideoScreen;

  @override
  String toString() {
    return 'ExamIntroductionViewRouteArgs{key: $key, questionData: $questionData, isFromSectionDetails: $isFromSectionDetails, isFromVideoScreen: $isFromVideoScreen}';
  }
}

/// generated route for
/// [ExamListViewScreen]
class ExamListViewRoute extends PageRouteInfo<ExamListViewRouteArgs> {
  ExamListViewRoute({
    required int tabIndex,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ExamListViewRoute.name,
          args: ExamListViewRouteArgs(
            tabIndex: tabIndex,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamListViewRoute';

  static const PageInfo<ExamListViewRouteArgs> page =
      PageInfo<ExamListViewRouteArgs>(name);
}

class ExamListViewRouteArgs {
  const ExamListViewRouteArgs({
    required this.tabIndex,
    this.key,
  });

  final int tabIndex;

  final Key? key;

  @override
  String toString() {
    return 'ExamListViewRouteArgs{tabIndex: $tabIndex, key: $key}';
  }
}

/// generated route for
/// [ExamResultViewScreen]
class ExamResultViewRoute extends PageRouteInfo<ExamResultViewRouteArgs> {
  ExamResultViewRoute({
    Key? key,
    required ExamSummary examSummary,
    required int tabIndex,
    List<PageRouteInfo>? children,
  }) : super(
          ExamResultViewRoute.name,
          args: ExamResultViewRouteArgs(
            key: key,
            examSummary: examSummary,
            tabIndex: tabIndex,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamResultViewRoute';

  static const PageInfo<ExamResultViewRouteArgs> page =
      PageInfo<ExamResultViewRouteArgs>(name);
}

class ExamResultViewRouteArgs {
  const ExamResultViewRouteArgs({
    this.key,
    required this.examSummary,
    required this.tabIndex,
  });

  final Key? key;

  final ExamSummary examSummary;

  final int tabIndex;

  @override
  String toString() {
    return 'ExamResultViewRouteArgs{key: $key, examSummary: $examSummary, tabIndex: $tabIndex}';
  }
}

/// generated route for
/// [ExamReviewViewScreen]
class ExamReviewViewRoute extends PageRouteInfo<ExamReviewViewRouteArgs> {
  ExamReviewViewRoute({
    Key? key,
    required String quizId,
    required String quizAttemptId,
    required bool isGradeCalculated,
    List<PageRouteInfo>? children,
  }) : super(
          ExamReviewViewRoute.name,
          args: ExamReviewViewRouteArgs(
            key: key,
            quizId: quizId,
            quizAttemptId: quizAttemptId,
            isGradeCalculated: isGradeCalculated,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamReviewViewRoute';

  static const PageInfo<ExamReviewViewRouteArgs> page =
      PageInfo<ExamReviewViewRouteArgs>(name);
}

class ExamReviewViewRouteArgs {
  const ExamReviewViewRouteArgs({
    this.key,
    required this.quizId,
    required this.quizAttemptId,
    required this.isGradeCalculated,
  });

  final Key? key;

  final String quizId;

  final String quizAttemptId;

  final bool isGradeCalculated;

  @override
  String toString() {
    return 'ExamReviewViewRouteArgs{key: $key, quizId: $quizId, quizAttemptId: $quizAttemptId, isGradeCalculated: $isGradeCalculated}';
  }
}

/// generated route for
/// [ExamViewScreen]
class ExamViewRoute extends PageRouteInfo<ExamViewRouteArgs> {
  ExamViewRoute({
    Key? key,
    required int examDuration,
    required bool shouldShowTimer,
    required bool isFromViewResult,
    required String quizAttemptId,
    required QuestionData questionData,
    bool isFromVideoView = false,
    List<PageRouteInfo>? children,
  }) : super(
          ExamViewRoute.name,
          args: ExamViewRouteArgs(
            key: key,
            examDuration: examDuration,
            shouldShowTimer: shouldShowTimer,
            isFromViewResult: isFromViewResult,
            quizAttemptId: quizAttemptId,
            questionData: questionData,
            isFromVideoView: isFromVideoView,
          ),
          initialChildren: children,
        );

  static const String name = 'ExamViewRoute';

  static const PageInfo<ExamViewRouteArgs> page =
      PageInfo<ExamViewRouteArgs>(name);
}

class ExamViewRouteArgs {
  const ExamViewRouteArgs({
    this.key,
    required this.examDuration,
    required this.shouldShowTimer,
    required this.isFromViewResult,
    required this.quizAttemptId,
    required this.questionData,
    this.isFromVideoView = false,
  });

  final Key? key;

  final int examDuration;

  final bool shouldShowTimer;

  final bool isFromViewResult;

  final String quizAttemptId;

  final QuestionData questionData;

  final bool isFromVideoView;

  @override
  String toString() {
    return 'ExamViewRouteArgs{key: $key, examDuration: $examDuration, shouldShowTimer: $shouldShowTimer, isFromViewResult: $isFromViewResult, quizAttemptId: $quizAttemptId, questionData: $questionData, isFromVideoView: $isFromVideoView}';
  }
}

/// generated route for
/// [ImageViewScreen]
class ImageViewRoute extends PageRouteInfo<ImageViewRouteArgs> {
  ImageViewRoute({
    Key? key,
    required String imagePath,
    required ResourseFile resourceFile,
    required String instance,
    required String courseId,
    required CourseProgress? progess,
    List<PageRouteInfo>? children,
  }) : super(
          ImageViewRoute.name,
          args: ImageViewRouteArgs(
            key: key,
            imagePath: imagePath,
            resourceFile: resourceFile,
            instance: instance,
            courseId: courseId,
            progess: progess,
          ),
          initialChildren: children,
        );

  static const String name = 'ImageViewRoute';

  static const PageInfo<ImageViewRouteArgs> page =
      PageInfo<ImageViewRouteArgs>(name);
}

class ImageViewRouteArgs {
  const ImageViewRouteArgs({
    this.key,
    required this.imagePath,
    required this.resourceFile,
    required this.instance,
    required this.courseId,
    required this.progess,
  });

  final Key? key;

  final String imagePath;

  final ResourseFile resourceFile;

  final String instance;

  final String courseId;

  final CourseProgress? progess;

  @override
  String toString() {
    return 'ImageViewRouteArgs{key: $key, imagePath: $imagePath, resourceFile: $resourceFile, instance: $instance, courseId: $courseId, progess: $progess}';
  }
}

/// generated route for
/// [LoginEmailViewScreen]
class LoginEmailViewRoute extends PageRouteInfo<LoginEmailViewRouteArgs> {
  LoginEmailViewRoute({
    Key? key,
    bool isTokenExpired = false,
    List<PageRouteInfo>? children,
  }) : super(
          LoginEmailViewRoute.name,
          args: LoginEmailViewRouteArgs(
            key: key,
            isTokenExpired: isTokenExpired,
          ),
          initialChildren: children,
        );

  static const String name = 'LoginEmailViewRoute';

  static const PageInfo<LoginEmailViewRouteArgs> page =
      PageInfo<LoginEmailViewRouteArgs>(name);
}

class LoginEmailViewRouteArgs {
  const LoginEmailViewRouteArgs({
    this.key,
    this.isTokenExpired = false,
  });

  final Key? key;

  final bool isTokenExpired;

  @override
  String toString() {
    return 'LoginEmailViewRouteArgs{key: $key, isTokenExpired: $isTokenExpired}';
  }
}

/// generated route for
/// [NewsViewMoreViewScreen]
class NewsViewMoreViewRoute extends PageRouteInfo<NewsViewMoreViewRouteArgs> {
  NewsViewMoreViewRoute({
    required List<CurrentAffairs> affairsList,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          NewsViewMoreViewRoute.name,
          args: NewsViewMoreViewRouteArgs(
            affairsList: affairsList,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'NewsViewMoreViewRoute';

  static const PageInfo<NewsViewMoreViewRouteArgs> page =
      PageInfo<NewsViewMoreViewRouteArgs>(name);
}

class NewsViewMoreViewRouteArgs {
  const NewsViewMoreViewRouteArgs({
    required this.affairsList,
    this.key,
  });

  final List<CurrentAffairs> affairsList;

  final Key? key;

  @override
  String toString() {
    return 'NewsViewMoreViewRouteArgs{affairsList: $affairsList, key: $key}';
  }
}

/// generated route for
/// [NoAccessViewScreen]
class NoAccessViewRoute extends PageRouteInfo<void> {
  const NoAccessViewRoute({List<PageRouteInfo>? children})
      : super(
          NoAccessViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'NoAccessViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [OnboardingViewScreen]
class OnboardingViewRoute extends PageRouteInfo<OnboardingViewRouteArgs> {
  OnboardingViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          OnboardingViewRoute.name,
          args: OnboardingViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'OnboardingViewRoute';

  static const PageInfo<OnboardingViewRouteArgs> page =
      PageInfo<OnboardingViewRouteArgs>(name);
}

class OnboardingViewRouteArgs {
  const OnboardingViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'OnboardingViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [OrganizationViewScreen]
class OrganizationViewRoute extends PageRouteInfo<OrganizationViewRouteArgs> {
  OrganizationViewRoute({
    Key? key,
    bool isFromLogin = false,
    List<PageRouteInfo>? children,
  }) : super(
          OrganizationViewRoute.name,
          args: OrganizationViewRouteArgs(
            key: key,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'OrganizationViewRoute';

  static const PageInfo<OrganizationViewRouteArgs> page =
      PageInfo<OrganizationViewRouteArgs>(name);
}

class OrganizationViewRouteArgs {
  const OrganizationViewRouteArgs({
    this.key,
    this.isFromLogin = false,
  });

  final Key? key;

  final bool isFromLogin;

  @override
  String toString() {
    return 'OrganizationViewRouteArgs{key: $key, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [PDFViewScreen]
class PDFViewRoute extends PageRouteInfo<PDFViewRouteArgs> {
  PDFViewRoute({
    Key? key,
    required String urlPDFPath,
    List<PageRouteInfo>? children,
  }) : super(
          PDFViewRoute.name,
          args: PDFViewRouteArgs(
            key: key,
            urlPDFPath: urlPDFPath,
          ),
          initialChildren: children,
        );

  static const String name = 'PDFViewRoute';

  static const PageInfo<PDFViewRouteArgs> page =
      PageInfo<PDFViewRouteArgs>(name);
}

class PDFViewRouteArgs {
  const PDFViewRouteArgs({
    this.key,
    required this.urlPDFPath,
  });

  final Key? key;

  final String urlPDFPath;

  @override
  String toString() {
    return 'PDFViewRouteArgs{key: $key, urlPDFPath: $urlPDFPath}';
  }
}

/// generated route for
/// [ProfileReviewScreen]
class ProfileReviewRoute extends PageRouteInfo<ProfileReviewRouteArgs> {
  ProfileReviewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ProfileReviewRoute.name,
          args: ProfileReviewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'ProfileReviewRoute';

  static const PageInfo<ProfileReviewRouteArgs> page =
      PageInfo<ProfileReviewRouteArgs>(name);
}

class ProfileReviewRouteArgs {
  const ProfileReviewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'ProfileReviewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [ProfileViewScreen]
class ProfileViewRoute extends PageRouteInfo<ProfileViewRouteArgs> {
  ProfileViewRoute({
    Key? key,
    bool isFromLogin = false,
    bool isFromSideMenu = false,
    List<PageRouteInfo>? children,
  }) : super(
          ProfileViewRoute.name,
          args: ProfileViewRouteArgs(
            key: key,
            isFromLogin: isFromLogin,
            isFromSideMenu: isFromSideMenu,
          ),
          initialChildren: children,
        );

  static const String name = 'ProfileViewRoute';

  static const PageInfo<ProfileViewRouteArgs> page =
      PageInfo<ProfileViewRouteArgs>(name);
}

class ProfileViewRouteArgs {
  const ProfileViewRouteArgs({
    this.key,
    this.isFromLogin = false,
    this.isFromSideMenu = false,
  });

  final Key? key;

  final bool isFromLogin;

  final bool isFromSideMenu;

  @override
  String toString() {
    return 'ProfileViewRouteArgs{key: $key, isFromLogin: $isFromLogin, isFromSideMenu: $isFromSideMenu}';
  }
}

/// generated route for
/// [RankListViewScreen]
class RankListViewRoute extends PageRouteInfo<RankListViewRouteArgs> {
  RankListViewRoute({
    required bool isCourseRankList,
    required List<Rank> rankListForExam,
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          RankListViewRoute.name,
          args: RankListViewRouteArgs(
            isCourseRankList: isCourseRankList,
            rankListForExam: rankListForExam,
            key: key,
          ),
          initialChildren: children,
        );

  static const String name = 'RankListViewRoute';

  static const PageInfo<RankListViewRouteArgs> page =
      PageInfo<RankListViewRouteArgs>(name);
}

class RankListViewRouteArgs {
  const RankListViewRouteArgs({
    required this.isCourseRankList,
    required this.rankListForExam,
    this.key,
  });

  final bool isCourseRankList;

  final List<Rank> rankListForExam;

  final Key? key;

  @override
  String toString() {
    return 'RankListViewRouteArgs{isCourseRankList: $isCourseRankList, rankListForExam: $rankListForExam, key: $key}';
  }
}

/// generated route for
/// [ResetPasswordViewScreen]
class ResetPasswordViewRoute extends PageRouteInfo<ResetPasswordViewRouteArgs> {
  ResetPasswordViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          ResetPasswordViewRoute.name,
          args: ResetPasswordViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordViewRoute';

  static const PageInfo<ResetPasswordViewRouteArgs> page =
      PageInfo<ResetPasswordViewRouteArgs>(name);
}

class ResetPasswordViewRouteArgs {
  const ResetPasswordViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'ResetPasswordViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [SectionDetailsViewScreen]
class SectionDetailsViewRoute
    extends PageRouteInfo<SectionDetailsViewRouteArgs> {
  SectionDetailsViewRoute({
    Key? key,
    required String sectionId,
    required String title,
    List<PageRouteInfo>? children,
  }) : super(
          SectionDetailsViewRoute.name,
          args: SectionDetailsViewRouteArgs(
            key: key,
            sectionId: sectionId,
            title: title,
          ),
          initialChildren: children,
        );

  static const String name = 'SectionDetailsViewRoute';

  static const PageInfo<SectionDetailsViewRouteArgs> page =
      PageInfo<SectionDetailsViewRouteArgs>(name);
}

class SectionDetailsViewRouteArgs {
  const SectionDetailsViewRouteArgs({
    this.key,
    required this.sectionId,
    required this.title,
  });

  final Key? key;

  final String sectionId;

  final String title;

  @override
  String toString() {
    return 'SectionDetailsViewRouteArgs{key: $key, sectionId: $sectionId, title: $title}';
  }
}

/// generated route for
/// [SettingsViewScreen]
class SettingsViewRoute extends PageRouteInfo<SettingsViewRouteArgs> {
  SettingsViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          SettingsViewRoute.name,
          args: SettingsViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SettingsViewRoute';

  static const PageInfo<SettingsViewRouteArgs> page =
      PageInfo<SettingsViewRouteArgs>(name);
}

class SettingsViewRouteArgs {
  const SettingsViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'SettingsViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [SignUpViewScreen]
class SignUpViewRoute extends PageRouteInfo<SignUpViewRouteArgs> {
  SignUpViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          SignUpViewRoute.name,
          args: SignUpViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SignUpViewRoute';

  static const PageInfo<SignUpViewRouteArgs> page =
      PageInfo<SignUpViewRouteArgs>(name);
}

class SignUpViewRouteArgs {
  const SignUpViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'SignUpViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [SplashScreen]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [StudyMaterialViewScreen]
class StudyMaterialViewRoute extends PageRouteInfo<StudyMaterialViewRouteArgs> {
  StudyMaterialViewRoute({
    Key? key,
    required String instance,
    required String courseId,
    required CourseProgress? progess,
    required PageContent pageContent,
    List<PageRouteInfo>? children,
  }) : super(
          StudyMaterialViewRoute.name,
          args: StudyMaterialViewRouteArgs(
            key: key,
            instance: instance,
            courseId: courseId,
            progess: progess,
            pageContent: pageContent,
          ),
          initialChildren: children,
        );

  static const String name = 'StudyMaterialViewRoute';

  static const PageInfo<StudyMaterialViewRouteArgs> page =
      PageInfo<StudyMaterialViewRouteArgs>(name);
}

class StudyMaterialViewRouteArgs {
  const StudyMaterialViewRouteArgs({
    this.key,
    required this.instance,
    required this.courseId,
    required this.progess,
    required this.pageContent,
  });

  final Key? key;

  final String instance;

  final String courseId;

  final CourseProgress? progess;

  final PageContent pageContent;

  @override
  String toString() {
    return 'StudyMaterialViewRouteArgs{key: $key, instance: $instance, courseId: $courseId, progess: $progess, pageContent: $pageContent}';
  }
}

/// generated route for
/// [SubjectsDetailedTabViewScreen]
class SubjectsDetailedTabViewRoute
    extends PageRouteInfo<SubjectsDetailedTabViewRouteArgs> {
  SubjectsDetailedTabViewRoute({
    required String title,
    List<PageRouteInfo>? children,
  }) : super(
          SubjectsDetailedTabViewRoute.name,
          args: SubjectsDetailedTabViewRouteArgs(title: title),
          initialChildren: children,
        );

  static const String name = 'SubjectsDetailedTabViewRoute';

  static const PageInfo<SubjectsDetailedTabViewRouteArgs> page =
      PageInfo<SubjectsDetailedTabViewRouteArgs>(name);
}

class SubjectsDetailedTabViewRouteArgs {
  const SubjectsDetailedTabViewRouteArgs({required this.title});

  final String title;

  @override
  String toString() {
    return 'SubjectsDetailedTabViewRouteArgs{title: $title}';
  }
}

/// generated route for
/// [SubscriptionViewScreen]
class SubscriptionViewRoute extends PageRouteInfo<SubscriptionViewRouteArgs> {
  SubscriptionViewRoute({
    Key? key,
    List<PageRouteInfo>? children,
  }) : super(
          SubscriptionViewRoute.name,
          args: SubscriptionViewRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SubscriptionViewRoute';

  static const PageInfo<SubscriptionViewRouteArgs> page =
      PageInfo<SubscriptionViewRouteArgs>(name);
}

class SubscriptionViewRouteArgs {
  const SubscriptionViewRouteArgs({this.key});

  final Key? key;

  @override
  String toString() {
    return 'SubscriptionViewRouteArgs{key: $key}';
  }
}

/// generated route for
/// [TopicListViewScreen]
class TopicListViewRoute extends PageRouteInfo<TopicListViewRouteArgs> {
  TopicListViewRoute({
    Key? key,
    bool isFromLogin = false,
    List<PageRouteInfo>? children,
  }) : super(
          TopicListViewRoute.name,
          args: TopicListViewRouteArgs(
            key: key,
            isFromLogin: isFromLogin,
          ),
          initialChildren: children,
        );

  static const String name = 'TopicListViewRoute';

  static const PageInfo<TopicListViewRouteArgs> page =
      PageInfo<TopicListViewRouteArgs>(name);
}

class TopicListViewRouteArgs {
  const TopicListViewRouteArgs({
    this.key,
    this.isFromLogin = false,
  });

  final Key? key;

  final bool isFromLogin;

  @override
  String toString() {
    return 'TopicListViewRouteArgs{key: $key, isFromLogin: $isFromLogin}';
  }
}

/// generated route for
/// [WelcomeViewScreen]
class WelcomeViewRoute extends PageRouteInfo<void> {
  const WelcomeViewRoute({List<PageRouteInfo>? children})
      : super(
          WelcomeViewRoute.name,
          initialChildren: children,
        );

  static const String name = 'WelcomeViewRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}
