import 'package:flutter/material.dart';

import '../../config/themes/app_theme.dart';

class CurrentAffairsLeading extends StatelessWidget {
  final String assetPath;
  final double width;
  final double height;
  const CurrentAffairsLeading({
    super.key,
    required this.assetPath,
    required this.width,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      width: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          width: 2,
          color: AppTheme.currentAffairsBorder.withOpacity(0.2),
        ),
      ),
      child: Center(
        child: Image.asset(
          assetPath,
          color: AppTheme.answerOptedColor,
          height: height,
          width: width,
        ),
      ),
    );
  }
}
