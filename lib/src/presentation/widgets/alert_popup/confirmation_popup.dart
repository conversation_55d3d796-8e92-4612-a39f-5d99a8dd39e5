import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../config/enums/alert_types.dart';
import '../../../domain/services/localizations/sl_strings.dart';
import '../../../domain/services/localizations/string_keys.dart';
import '/src/config/router/app_router.dart';
import 'multi_action_dialogue.dart';
import 'package:flutter/material.dart';

import '../../../utils/constants/strings.dart';

showWarning(BuildContext context) {
  showDialog(
      context: context,
      builder: (context1) {
        return MultiActionDialogue(
          title: WARNING,
          content: CHANGE_TOPIC,
          alertType: AlertType.warning,
          cancelBtnText: SLStrings.getTranslatedString(KEY_NO),
          confirmBtnText: SLStrings.getTranslatedString(KEY_YES),
          onContinueTap: () async {
            SharedPreferences _prefs = await SharedPreferences.getInstance();
            if (kIsWeb) {
              Navigator.of(context).pop();
              Beamer.of(context).beamToReplacementNamed('/topic-view');
            } else {
              appRouter.popForced();
              appRouter.push(TopicListViewRoute());
            }
          },
          onCancelTap: () {
            kIsWeb ? Navigator.of(context).pop() : appRouter.popForced();
          },
        );
      });
}

showPGExceptionPopup(BuildContext context, String msg) {
  bool isThereCurrentDialogShowing = false;
  String routeNameInApp = '';
  String routeNameInWeb = '';

  if (kIsWeb) {
    final beamerRouter = Beamer.of(context);
    isThereCurrentDialogShowing = !beamerRouter.active;
    routeNameInWeb = beamerRouter.configuration.uri.path;
  } else {
    isThereCurrentDialogShowing = ModalRoute.of(context)?.isCurrent != true;
    routeNameInApp = appRouter.current.route.name;
  }
  if (isThereCurrentDialogShowing) {
    appRouter.popUntil((route) => route.settings.name == routeNameInApp);
  }
  showDialog(
    context: context,
    builder: (context) {
      return PopScope(
        canPop: false,
        onPopInvoked: (bool didPop) {
          if (didPop) {
            return;
          }
        },
        child: MultiActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          content: msg,
          alertType: AlertType.error,
          onContinueTap: () =>
              kIsWeb ? Beamer.of(context).beamBack() : Navigator.pop(context),
          onCancelTap: () {},
          cancelBtnText: '',
          confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
        ),
      );
    },
  );
}

showNoAccessPopup(BuildContext context, String msg) {
  // TODO: change msg
  showDialog(
    context: context,
    builder: (context) {
      return PopScope(
        canPop: false,
        onPopInvoked: (bool didPop) {
          if (didPop) {
            return;
          }
        },
        child: MultiActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          content:
              'User has no access to perform the action. Please contact admin!',
          alertType: AlertType.error,
          onContinueTap: () => Navigator.pop(context),
          onCancelTap: () {},
          cancelBtnText: '',
          confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
        ),
      );
    },
  );
}
