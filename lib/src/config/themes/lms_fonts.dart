import 'dart:ui';

import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_tex/flutter_tex.dart';

import '../app_config/sl_config.dart';
import '/src/config/themes/app_theme.dart';
import '/src/config/themes/theme_colors.dart';
import 'package:flutter/material.dart';

class LMSFonts {
  static TextStyle appBarStyle() {
    return const TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        // color: LmsColors.white, // AppTheme.primaryTextColor,
        fontSize: 22,
        fontWeight: FontWeight.w600);
  }

  static TextStyle boldFont(double fontSize, Color textColor) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,

        //letterSpacing: 0.4,
        // overflow: TextOverflow.ellipsis,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.bold);
  }

  static TextStyle boldFontUnderlined(double fontSize, Color textColor) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        decoration: TextDecoration.underline,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.bold);
  }

  static TextStyle semiBoldFont(double fontSize, Color textColor) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        // overflow: TextOverflow.ellipsis,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.w600);
  }

  static TextStyle semiBoldFontForSymbol(double fontSize, Color textColor) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        // overflow: TextOverflow.ellipsis,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.w600,
        fontFeatures: const [FontFeature.subscripts()]);
  }

  static TextStyle semiBoldFontWithHeight(
      double fontSize, Color textColor, double height) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        height: height,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.w600);
  }

  static TextStyle mediumFont(double fontSize, Color textColor, double height) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.w500);
  }

  static TextStyle regularFontWithOverflow(
      double fontSize, Color textColor, double height) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        height: height,
        fontSize: fontSize,
        overflow: TextOverflow.ellipsis,
        color: textColor,
        fontWeight: FontWeight.w400);
  }

  static TextStyle regularFont(double fontSize, Color textColor) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.w400);
  }

  static TextStyle regularFontWithHeight(
      double fontSize, Color textColor, double height) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        height: height,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.w400);
  }

  static TextStyle regularFontUndelined(double fontSize, Color textColor) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        decoration: TextDecoration.underline,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.w400);
  }

  static TextStyle tabItem() {
    return const TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: 16.0,
        color: AppTheme.whiteColor,
        fontWeight: FontWeight.w600);
  }

  static TextStyle buttonStyle(double fontSize) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: fontSize,
        // color: AppTheme.pitchBlack,
        fontWeight: FontWeight.w500);
  }

  static TextStyle commonButtonStyle(double fontSize, Color textColor) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: fontSize,
        color: textColor,
        fontWeight: FontWeight.w600);
  }

  static TextStyle emptyTextStyle(double? fontSize) {
    return TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: fontSize ?? 20.0,
        color: AppTheme.pitchBlack,
        fontWeight: FontWeight.w500);
  }

  static TextStyle secondaryButtonStyle() {
    return const TextStyle(
      fontFamily: SLConfig.FONT_FAMILY,
      fontSize: 16.0,
      // color: AppTheme.pitchBlack,
    );
  }

  static TextStyle onboardingTextStyle() {
    return const TextStyle(
      fontFamily: SLConfig.FONT_FAMILY,
      fontSize: 25,
      fontWeight: FontWeight.w600,
      color: AppTheme.answerOptedColor,
    );
  }

  static TextStyle onboardingContentTextStyle() {
    return const TextStyle(
      fontFamily: SLConfig.FONT_FAMILY,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: AppTheme.examIntroTextColor,
    );
  }

  static TextStyle textFieldValueTextStyle() {
    return const TextStyle(
        fontSize: 14,
        fontFamily: SLConfig.FONT_FAMILY,
        color: AppTheme.textFieldTitle,
        // AppTheme.primaryTextColor,
        fontWeight: FontWeight.w500);
  }

  static TextStyle textFieldPlaceHolderTextStyle() {
    return const TextStyle(
        fontSize: 14,
        fontFamily: SLConfig.FONT_FAMILY,
        color: AppTheme.placeHolderText,
        // AppTheme.primaryTextColor,
        fontWeight: FontWeight.w400);
  }

  static TextStyle textFieldErrorTextStyle() {
    return const TextStyle(
        fontSize: 12,
        fontFamily: SLConfig.FONT_FAMILY,
        color: AppTheme.textFieldErrorText,
        // AppTheme.primaryTextColor,
        fontWeight: FontWeight.w400);
  }

  static TextStyle loginSignupTitle() {
    return const TextStyle(
        fontSize: 22, color: LmsColors.white, fontWeight: FontWeight.w600);
  }

  static TextStyle textFieldFloatingTextStyle() {
    return const TextStyle(
        fontSize: 18,
        fontFamily: SLConfig.FONT_FAMILY,
        color: Color(0xFF2B4031),
        fontWeight: FontWeight.w600);
  }

  static TextStyle sessionExpiredText() {
    return const TextStyle(
        fontSize: 14,
        fontFamily: SLConfig.FONT_FAMILY,
        color: AppTheme.sessionExpiredTextColor,
        fontWeight: FontWeight.w500);
  }

  static TextStyle examMarkHeadStyle() {
    return const TextStyle(
      fontFamily: SLConfig.FONT_FAMILY,
      color: AppTheme.titleColor,
      fontSize: 20,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle examMarkStyle() {
    return const TextStyle(
      color: AppTheme.examInfoTextColor,
      fontSize: 16,
      fontWeight: FontWeight.w600,
    );
  }

  static TextStyle examPassedMarkStyle() {
    return const TextStyle(
      fontFamily: SLConfig.EXAM_MARK_FONT_FAM,
      color: AppTheme.correctANswerGreen,
      fontSize: 24,
      fontStyle: FontStyle.italic,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle examFailedMarkStyle() {
    return const TextStyle(
      fontFamily: SLConfig.EXAM_MARK_FONT_FAM,
      color: AppTheme.wrongANswerRed,
      fontSize: 24,
      fontStyle: FontStyle.italic,
      fontWeight: FontWeight.bold,
    );
  }

  static TextStyle snackbarStyle() {
    return const TextStyle(
      fontFamily: SLConfig.FONT_FAMILY,
      color: AppTheme.whiteTextColor,
      fontSize: 14,
      fontWeight: FontWeight.w400,
    );
  }

  static TextStyle sectionProgressTextStyle(double fontSize, Color textColor) {
    return TextStyle(
        color: textColor, fontSize: fontSize, fontWeight: FontWeight.w700);
  }

  static TextStyle examIntroTextStyle(double fontSize, Color textColor) {
    return TextStyle(
        color: textColor, fontSize: fontSize, fontWeight: FontWeight.w600);
  }

  static TextStyle examIntroDescriptionStyle(
      double fontSize, Color textColor, FontWeight fontWeight) {
    return TextStyle(
        color: textColor, fontSize: fontSize, fontWeight: fontWeight);
  }

  ///
  ///HTML
  ///

  static Map<String, Style> htmlStyle = {
    'h1': h1Tag(),
    'p': pTag(),
  };
  static Map<String, Style> htmlStyleForExamIntro = {
    'h': examIntroStyle(),
    'h1': examIntroStyle(),
    'p': examIntroStyle(),
  };

  static Map<String, Style> htmlStyeForCurrentAffairs(double screenWidth) => {
        'a': currentAffairsATag(),
        'h': currentAffairsHeader(),
        'h1': currentAffairsHeader(),
        'h2': currentAffairsHeader(),
        'h3': currentAffairsHeader(),
        'h4': currentAffairsHeader(),
        'h5': currentAffairsHeader(),
        'h6': currentAffairsHeader(),
        'p': currentAffairsPTag(),
        'img': currentAffairsIMGTag(screenWidth),
      };

  static Style h1Tag() {
    return Style(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: FontSize(20.0),
        color: AppTheme.primaryTextColorBlack,
        fontWeight: FontWeight.w500);
  }

  static Style pTag() {
    return Style(
        textAlign: TextAlign.justify,
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: FontSize(13.0),
        color: AppTheme.sectionDetailsTextColor,
        fontWeight: FontWeight.w400);
  }

  static Style examIntroStyle() {
    return Style(
        textAlign: TextAlign.justify,
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: FontSize(14.0),
        color: AppTheme.primaryOrange,
        fontWeight: FontWeight.w400);
  }

  static Style currentAffairsHeader() {
    return Style(
      fontFamily: SLConfig.FONT_FAMILY,
      fontSize: FontSize(16.0),
      color: AppTheme.examIntroTextColor,
      fontWeight: FontWeight.w600,
    );
  }

  static Style currentAffairsPTag() {
    return Style(
        textAlign: TextAlign.justify,
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: FontSize(14.0),
        color: AppTheme.examIntroTextColor,
        fontWeight: FontWeight.w400);
  }

  static Style currentAffairsATag() {
    return Style(
      color: AppTheme.infoBlue, // Change color here
      backgroundColor: Colors.transparent,
      fontFamily: SLConfig.FONT_FAMILY,
      fontSize: FontSize(14.0),
      fontWeight: FontWeight.w100,
    );
  }

  static Style currentAffairsIMGTag(double screenWidth) {
    return Style(
      width: Width(screenWidth),
    );
  }

  static TextStyle selectedTabItem() {
    return const TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: 14.0,
        color: AppTheme.primaryBlue,
        fontWeight: FontWeight.w400);
  }

  static TextStyle unSelectedTabItem() {
    return const TextStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: 14.0,
        color: AppTheme.unselectedTabColor,
        fontWeight: FontWeight.w400);
  }

  static TeXViewFontStyle questionText() {
    return TeXViewFontStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: 15,
        fontWeight: TeXViewFontWeight.w400);
  }

  static TeXViewFontStyle answerText() {
    return TeXViewFontStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: 15,
        fontWeight: TeXViewFontWeight.w400);
  }

  static TeXViewStyle questionAnswerTextInReview({required bool isQstn}) {
    return TeXViewStyle(
      fontStyle: questionText(),
      contentColor: AppTheme.examTitleColorInReview,
    );
  }

  static TeXViewFontStyle questionTextFontInReview() {
    return TeXViewFontStyle(
        fontFamily: SLConfig.FONT_FAMILY,
        fontSize: 14,
        fontWeight: TeXViewFontWeight.w400);
  }

  static TextStyle mediumFontWithHeight(
      double fontSize, Color textColor, double height) {
    return TextStyle(
      fontFamily: SLConfig.FONT_FAMILY,
      fontSize: fontSize,
      color: textColor,
      fontWeight: FontWeight.w500,
      height: height,
    );
  }
}
