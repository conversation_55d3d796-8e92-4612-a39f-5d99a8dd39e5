import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';

import '../../config/enums/alert_types.dart';
import '../../utils/constants/locale_change_helper.dart';
import '/src/domain/services/localizations/sl_strings.dart';
import '/src/domain/services/localizations/string_keys.dart';

import '/src/presentation/widgets/current_affairs_leading_widget.dart';
import 'package:auto_route/auto_route.dart';

import '../widgets/alert_popup/multi_action_dialogue.dart';
import '/src/presentation/widgets/empty_screen_view.dart';

import '/src/config/themes/app_theme.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/domain/models/course_details.dart';
import '/src/utils/constants/strings.dart';
import '/src/utils/helper/app_date_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../cubits/course_list/course_list_cubit.dart';
import '../widgets/app_bar.dart';

@RoutePage()
class NewsViewMoreViewScreen extends HookWidget with WidgetsBindingObserver {
  final List<CurrentAffairs> _affairsList;

  NewsViewMoreViewScreen(this._affairsList, {super.key});

  GlobalKey dataKey = GlobalKey();

  late ScrollController _monthsScrollController;

  List<DateTime> _monthList = [];
  List<CurrentAffairs> _filteredAffairsList = [];

  BuildContext? currentContext;

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);
    didChangeAppLocale(locales, currentContext);
  }

  void _scrollToIndex(
      int index, GlobalKey key, BoxConstraints constraints) async {
    double itemPos = constraints.maxWidth < 720 ? index * 130.0 : index * 150;
    double viewportWidth = constraints.maxWidth;
    double targetOffset = constraints.maxWidth < 720
        ? itemPos - (viewportWidth - 130) / 2
        : itemPos - (viewportWidth - 150) / 2;
    await _monthsScrollController.animateTo(targetOffset,
        duration: const Duration(milliseconds: 100), curve: Curves.easeInOut);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final screenSize = MediaQuery.of(context).size;
    final courseCubit = context.read<CourseListCubit>();
    ValueNotifier<DateTime> _selectedMonth = useState<DateTime>(DateTime.now());
    _monthsScrollController = useScrollController();

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);

      Future<void>.microtask(() async {
        courseCubit.generateMonthListForAffairs();
        await courseCubit.dateSelectionCallback(
            _selectedMonth.value, _affairsList);
        CourseListState state = courseCubit.state;
        if (state is CourseNewsSelection) {
          _selectedMonth.value = state.selectedDate;
          _filteredAffairsList = state.filteredList;
        }
      });
      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, const []);
    return Scaffold(
      backgroundColor: AppTheme.bgColor,
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
              title: SLStrings.getTranslatedString(KEY_CURRENT_AFFAIRS),
              leadingIconName: BACK_ARROW,
              trailingWidget: Container(),
              leadingBtnAction: () =>
                  kIsWeb ? Beamer.of(context).beamBack() : appRouter.pop())),
      body: BlocBuilder<CourseListCubit, CourseListState>(
        builder: (context, state) {
          if (state is CourseNewsSelection) {
            _selectedMonth.value = state.selectedDate;
            _filteredAffairsList = state.filteredList;
            _monthList = courseCubit.months;
          }

          return LayoutBuilder(builder:
              (BuildContext layoutContext, BoxConstraints constraints) {
            return Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16)),
                ),
                child: ScrollConfiguration(
                  behavior: ScrollConfiguration.of(context).copyWith(
                    dragDevices: {
                      PointerDeviceKind.touch,
                      PointerDeviceKind.mouse,
                    },
                  ),
                  child: Align(
                    child: Container(
                      width: constraints.maxWidth < 720 ? double.infinity : 800,
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        children: [
                          _monthsChipWidget(
                              context, state, _selectedMonth, constraints),
                          const SizedBox(height: 20),
                          _newsListView(screenSize, context),
                        ],
                      ),
                    ),
                  ),
                ));
          });
        },
      ),
    );
  }

  Widget _monthsChipWidget(BuildContext context, CourseListState state,
      _selectedMonth, BoxConstraints constraints) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      color: AppTheme.whiteColor,
      height: 35,
      child: ListView.builder(
          itemCount: _monthList.length,
          scrollDirection: Axis.horizontal,
          // clipBehavior: Clip.none,
          controller: _monthsScrollController,
          itemBuilder: (BuildContext _currentAffairsContext, int index) {
            return _chipItem(index, context, _selectedMonth, constraints);
          }),
    );
  }

  Widget _chipItem(int index, BuildContext context, _selectedMonth,
      BoxConstraints constraints) {
    bool isSelecetdMonth =
        AppDateFormatter().formatDateMMMMyyyy(_selectedMonth.value) ==
            AppDateFormatter().formatDateMMMMyyyy(_monthList[index]);
    dataKey = GlobalKey();
    return GestureDetector(
      onTap: () {
        /// TO DO: filter data based on selected date
        ///
        context
            .read<CourseListCubit>()
            .dateSelectionCallback(_monthList[index], _affairsList);
        _selectedMonth.value = _monthList[index];
        _scrollToIndex(index, dataKey, constraints);
      },
      child: Container(
        key: dataKey,
        margin: const EdgeInsets.only(right: 10),
        child: Chip(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(30.0)),
          side: BorderSide(
            color: isSelecetdMonth
                ? AppTheme.primaryAppColor
                : AppTheme.unSelectedChipBorder,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8),
          backgroundColor: isSelecetdMonth
              ? AppTheme.primaryAppColor
                  .withOpacity(0.1) /*AppTheme.primaryBlue*/
              : AppTheme.unselectedChipBg,
          label: Container(
            decoration:
                BoxDecoration(borderRadius: BorderRadius.circular(12.0)),
            child: Center(
                child: Text(
              AppDateFormatter().formatDateMMMMyyyy(_monthList[index]),
              style: LMSFonts.regularFont(
                14,
                isSelecetdMonth
                    ? AppTheme.primaryAppColor /* LmsColors.white */
                    : AppTheme.examIntroTextColor,
              ),
            )),
          ),
        ),
      ),
    );
  }

  Widget _newsListView(Size maxSize, BuildContext context) {
    return Expanded(
      child: _filteredAffairsList.isEmpty
          ? const EmptyScreenView(
              title: CURRENT_AFFAIRS_EMPTY,
              fontSize: 16,
            )
          : ListView.builder(
              itemCount: _filteredAffairsList.length,
              itemBuilder: (BuildContext _currentAffairsContext, int index) {
                return _newsItemWidget(index, maxSize, context);
              }),
    );
  }

  Widget _newsItemWidget(int index, Size maxSize, BuildContext context) {
    String date = AppDateFormatter()
        .formatForCurrentAffairs(_filteredAffairsList[index].publishedDate);
    // : AppDateFormatter().formatDatedMy(DateTime.now());
    return GestureDetector(
      onTap: () async {
        // ignore: unnecessary_null_comparison
        if (_filteredAffairsList[index] != null &&
            _filteredAffairsList[index].newsTitle.trim().isNotEmpty &&
            _filteredAffairsList[index].newsContent.trim().isNotEmpty) {
          kIsWeb
              ? Beamer.of(context).beamToNamed('/course-news',
                  data: {'currentAffairs': _filteredAffairsList[index]})
              : appRouter.push(CourseNewsViewRoute(
                  currentAffairs: _filteredAffairsList[index],
                ));
        } else {
          await showDialog(
            context: context,
            builder: (context) {
              return MultiActionDialogue(
                  alertType: AlertType.warning,
                  title: WARNING,
                  content: SLStrings.getTranslatedString(
                      KEY_CURRENT_AFFAIRS_CONTENT_EMPTY),
                  cancelBtnText: CLOSE,
                  confirmBtnText: '',
                  onCancelTap: () {
                    kIsWeb ? Navigator.of(context).pop() : appRouter.pop();
                  },
                  onContinueTap: () {});
            },
          );
        }
      },
      child: Container(
        height: 90,
        width: maxSize.width,
        decoration: BoxDecoration(
            border: Border.all(color: AppTheme.blackColor.withOpacity(0.1)),
            color: AppTheme.whiteColor,
            borderRadius: BorderRadius.circular(10.0)),
        padding: const EdgeInsets.only(left: 12),
        margin: const EdgeInsets.only(bottom: 15, top: 5),
        child: Row(
          children: [
            const CurrentAffairsLeading(
                assetPath: '$ASSETS_PATH/file_dock_fill.png',
                width: 40,
                height: 40),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // const SizedBox(height: 8),
                  Text(
                    date,
                    style: LMSFonts.boldFont(14, AppTheme.examIntroTextColor),
                  ),
                  SizedBox(
                      height: _filteredAffairsList[index]
                              .newsTitle
                              .toString()
                              .trim()
                              .isEmpty
                          ? 0
                          : 4),
                  _filteredAffairsList[index]
                          .newsTitle
                          .toString()
                          .trim()
                          .isEmpty
                      ? Container()
                      : Container(
                          padding: const EdgeInsets.only(right: 12),
                          child: Text(
                            _filteredAffairsList[index].newsTitle,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 3,
                            style: LMSFonts.regularFontWithHeight(
                                14, AppTheme.examIntroTextColor, 1.2),
                          ),
                        ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
