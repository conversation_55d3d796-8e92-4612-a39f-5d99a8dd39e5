import 'dart:math';
import 'dart:ui';

import '/src/config/themes/app_theme.dart';
import 'package:flutter/material.dart';

class RandomColorGenerator {
  static Color randomColor() {
    List aesColors = <Color>[
      Color.fromRGBO(102, 84, 94, 1),
      Color.fromRGBO(163, 145, 147, 1),
      Color.fromRGBO(170, 111, 115, 1),
      Color.fromRGBO(238, 169, 144, 1),
      Color.fromRGBO(246, 224, 181, 1)
    ];

    return aesColors[Random().nextInt(aesColors.length)];
  }

  static Map<String, Color> affairsColor(int index, int totalCount) {
    List aesColorsBg = const <Color>[
      Color.fromRGBO(209, 250, 245, 1),
      Color.fromRGBO(255, 229, 231, 1),
      Color.fromRGBO(238, 234, 253, 1),
      Color.fromRGBO(209, 250, 245, 1),
    ];
    List aesColorsText = const <Color>[
      Color.fromRGBO(0, 211, 205, 1),
      Color.fromRGBO(255, 71, 87, 1),
      Color.fromRGBO(113, 92, 201, 1),
      Color.fromRGBO(0, 211, 205, 1),
    ];

    int itemIndex = (index % 3);
    Map<String, Color> alternativeItem = {
      'bg': aesColorsBg[itemIndex],
      'text': aesColorsText[itemIndex]
    };
    return alternativeItem;
  }

  static Map<String, Color> tilesColor(int index, int totalCount) {
    /* List aesColorsBg = const <Color>[
      Color.fromRGBO(146, 199, 199, 1),
      Color.fromRGBO(120, 199, 199, 1),
      Color.fromRGBO(190, 255, 255, 1),
      Color.fromRGBO(146, 199, 199, 1),
    ];
    List aesColorsText = const <Color>[
      Color(0xFFF2F5ED),
      Color(0xFFE8F0D5),
      Color(0xFFD1DEA9),
    ];

    List aesColorsExam = const <Color>[
      Color(0xFFE8F0D5),
      Color(0xFFF2F5ED),
      Color(0xFFD1DEA9),
    ];*/

    List aesBlueShadesSubjects = const <Color>[
      Color(0xFFE1EBEE),
      Color(0xFFB0C4DE),
      Color(0xFFADD8E6),
      Color(0xFFB9D9EB),
      Color(0xFFF0F8FF),
      Color(0xFFB0C4DE),
    ];

    List aesBlueShadesExams = const <Color>[
      Color(0xFFE1EBEE),
      Color(0xFFA4DDED),
      Color(0xFFAFDBF5),
      Color(0xFFADD8E6),
      Color(0xFFcaf0f8),
    ];

    int itemIndex = (index % 3);
    Map<String, Color> alternativeItem = {
      'bg': aesBlueShadesSubjects[itemIndex],
      'text': aesBlueShadesExams[itemIndex]
    };
    return alternativeItem;
  }

  static List<Map<String, Color>> subscriptionCardColor() {
    return [
      {'bg': AppTheme.subscriptionC1, "border": AppTheme.subscriptionC1Border},
      {'bg': AppTheme.subscriptionC2, "border": AppTheme.subscriptionC2Border},
      {'bg': AppTheme.subscriptionC3, "border": AppTheme.subscriptionC3Border},
      {'bg': AppTheme.subscriptionC4, "border": AppTheme.subscriptionC4Border},
      {'bg': AppTheme.subscriptionC5, "border": AppTheme.subscriptionC5Border},
      {'bg': AppTheme.subscriptionC6, "border": AppTheme.subscriptionC6Border},
      {'bg': AppTheme.subscriptionC7, "border": AppTheme.subscriptionC7Border},
      {'bg': AppTheme.subscriptionC8, "border": AppTheme.subscriptionC8Border},
      {'bg': AppTheme.subscriptionC9, "border": AppTheme.subscriptionC9Border}
    ];
  }
}
