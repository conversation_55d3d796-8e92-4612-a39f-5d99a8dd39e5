import 'package:SmartLearn/src/domain/models/exam.dart';
import 'package:SmartLearn/src/domain/models/exam_review.dart';
import 'package:SmartLearn/src/domain/models/exam_summary.dart';
import 'package:SmartLearn/src/domain/models/question_data.dart';
import 'package:SmartLearn/src/domain/repositories/exam_repository.dart';
import 'package:SmartLearn/src/domain/services/db_helper.dart';
import 'package:SmartLearn/src/presentation/cubits/exam/exam_cubit.dart';

import 'package:SmartLearn/src/utils/constants/api_response.dart';
import 'package:SmartLearn/src/utils/constants/strings.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  late ExamRepository _examRepository;
  late ExamCubit _examListCubit;
  late Database _database;
  late DbHelper _dbHelper;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    SharedPreferences.setMockInitialValues({});
    _dbHelper = DbHelper.instance;

    // init db
    // Initialize the FFI version of databaseFactory

    // Change the default factory for unit testing calls for SQFlite
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
    _database = await databaseFactory.openDatabase(inMemoryDatabasePath);
    _dbHelper.db = _database;
    _dbHelper.onCreate(_database, 1);

    USER_ID = MOCK_USER_ID;
    ORG_ID = MOCK_ORG_ID;
    COURSE_ID = MOCK_COURSE_ID;

    MOCK_QSTN_DATA.quizAttemptId = MOCK_QUIZ_ATTEMPT_ID;

    _examRepository = MockExamRepository();
    _examListCubit = ExamCubit(_examRepository);
  });

  tearDown(() {
    _examListCubit.close();
    // _database.close();
  });

  group('ExamListCubit', () {
    blocTest<ExamCubit, ExamState>(
        'emits [ ExamsFetched] when ExamsFetched event is added',
        build: () => _examListCubit,
        act: (cubit) => cubit.fetchExamList(),
        expect: () => [ExamsFetched(exams: MOCK_EXAM_INFO)]);

    blocTest<ExamCubit, ExamState>(
        'emits [ ExamQuestionsFetched] when fetchQuestions event is added',
        build: () => _examListCubit,
        act: (cubit) => cubit.fetchQuestions(""),
        expect: () => [ExamQuestionsFetched(examData: MOCK_QSTN_DATA_INFO)]);

    blocTest<ExamCubit, ExamState>(
        'emits [ ExamsStarted] when startExam event is added',
        build: () => _examListCubit,
        act: (cubit) => cubit.startExam(MOCK_QUESTION_INFO, MOCK_EXAM_ID),
        expect: () => [ExamsStarted(questions: MOCK_QUESTION_INFO)]);

    blocTest<ExamCubit, ExamState>(
        'Should change the text  color if user select course',
        build: () => _examListCubit,
        act: (cubit) => cubit.updateColor("", MOCK_INDEX_INFO),
        expect: () => [
              ExamListButtonColor(buttonColor: false, indexes: MOCK_INDEX_INFO)
            ]);

    blocTest<ExamCubit, ExamState>(
        'emits [ExamSaveAnswer] when saveanswer event occurs',
        build: () => _examListCubit,
        act: (cubit) {
          cubit.saveAnswer(MOCK_QSTN_DATA_INFO.first, MOCK_QUESTION_INFO.first,
              0, MOCK_ANSWERED_QSTN_LIST_INFO, MOCK_EXAM_JSON);
        },
        wait: const Duration(milliseconds: 1000),
        expect: () => [
              ExamSaveAnswer(
                  questionData: MOCK_QSTN_DATA_INFO.first,
                  examJsonBody: MOCK_SAVE_ANSWER)
            ]);

    blocTest<ExamCubit, ExamState>(
        'emits [ExamsSubmitted] when submitExam event occurs',
        build: () => _examListCubit,
        act: (cubit) => cubit.submitExam(MOCK_EXAM_JSON, MOCK_QSTN_DATA),
        expect: () => [ExamsSubmitted()]);
  });

  blocTest<ExamCubit, ExamState>(
      'emits [ExamsQuizGradeState] when calculateQuizGrades event is executed',
      build: () => _examListCubit,
      act: (cubit) => cubit
        ..fetchExamResults(MOCK_QUIZ_ATTEMPT_ID, MOCK_EXAM_ID)
        ..calculateQuizGrades(MOCK_EXAM_ID, MOCK_QUIZ_ATTEMPT_ID),
      wait: const Duration(milliseconds: 1000),
      expect: () => [ExamsQuizGradeState(status: true)]);

  blocTest<ExamCubit, ExamState>(
      'emits [ExamResultSummaryFetched] when fetchExamResultStatisticalData event is executed',
      build: () => _examListCubit,
      act: (cubit) => cubit.fetchExamResultStatisticalData(
          MOCK_QUIZ_ATTEMPT_ID, MOCK_EXAM_ID),
      expect: () =>
          [ExamResultSummaryFetched(examSummaryResult: MOCK_EXAM_SUMMARY)]);

  blocTest<ExamCubit, ExamState>(
      'emits [ExamReviewFetched] when fetchReviewList event is added',
      build: () => _examListCubit,
      act: (cubit) => cubit.fetchReviewList(MOCK_EXAM_ID, MOCK_QUIZ_ATTEMPT_ID),
      expect: () => [ExamReviewFetched(examReviewData: MOCK_EXAM_REVIEW_INFO)]);
}

class MockExamRepository implements ExamRepository {
  @override
  Future<List<ExamReview>> fetchExamReviewList(
      String examId, String quizAttemptId) {
    return Future(() => MOCK_EXAM_REVIEW_INFO);
  }

  @override
  Future<List<QuestionData>> fetchQuestionsOfQuiz(String examId) {
    return Future(() => MOCK_QSTN_DATA_INFO);
  }

  @override
  Future startExam(Map<String, dynamic> jsonBody) {
    return Future(() => {'quiz_attempt_id': MOCK_QUIZ_ATTEMPT_ID});
  }

  @override
  Future submitExam(Map<String, dynamic> jsonBody) {
    return Future(() => 'success');
  }

  @override
  Future calculateExamResult(String quizAttemptId) {
    return Future(() => {'status': 'success'});
  }

  @override
  Future<List<Exam>> fetchAttemptedExamList(
      String orgId, String courseId, String userId) {
    return Future(() => MOCK_EXAM_INFO);
  }

  @override
  Future<List<ExamSummary>> fetchExamResultStatisticalData(
      quizAttemptId, quizId) {
    return Future(() => MOCK_EXAM_SUMMARY);
  }

  @override
  Future<List<Exam>> fetchExamList(
      String orgId, String courseId, String userId) {
    return Future(() => MOCK_EXAM_INFO);
  }
}
