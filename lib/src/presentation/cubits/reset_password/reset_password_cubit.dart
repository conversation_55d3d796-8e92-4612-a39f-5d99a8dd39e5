import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:rxdart/rxdart.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../domain/repositories/login_email_repository.dart';
import '../validation/validation_cubit.dart';

part 'reset_password_state.dart';

class ResetPasswordCubit extends Cubit<ResetPasswordState> {
  final LoginEmailRepository _LoginEmailRepository;
  ResetPasswordCubit(this._LoginEmailRepository)
      : super(ResetPasswordInitial());
  bool isLoading = false;
  Future<dynamic> resetPassword(String email) async {
    restPasswordLoading(false);
    try {
      final response = await _LoginEmailRepository.resetPassword(email);
      if (response == true) {
        emit(ResetPasswordSuccess());
      }else{
        restPasswordLoading(false);
      emit(ResetPasswordError(error: " Email did not register")); 
      }
    } on AuthException catch (e) {
      restPasswordLoading(false);
      emit(ResetPasswordError(error: e.message));
    } on SocketException catch (e) {
      restPasswordLoading(false);
      emit(ResetPasswordError(error: 'Network Disconnected'));
    } on Exception catch (e) {
      restPasswordLoading(false);
      emit(ResetPasswordError(error: e.toString()));
    }
  }

  void restPasswordLoading(bool text) {
    isLoading = text;
    emit(ResetPasswordLoading(isLoading: isLoading));
  }

  /// Validate email using streams when focus loses
  final _resetEmailController = BehaviorSubject<String>();
  Stream<String> get resetEmail =>
      _resetEmailController.stream.transform(validateResetEmailInput);

  Function(String) get resetChangeEmail => _resetEmailController.sink.add;

  final validateResetEmailInput =
      StreamTransformer<String, String>.fromHandlers(
          handleData: (_resetEmail, sink) {
    var error = EmailValidationError.validate(_resetEmail);
    if (error != null) {
      sink.addError(error);
    } else {
      sink.add(_resetEmail);
    }
  });
}
