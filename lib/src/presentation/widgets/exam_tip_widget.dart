import '/src/config/themes/lms_fonts.dart';
import '/src/utils/constants/strings.dart';
import 'package:flutter/material.dart';
import '../../config/themes/app_theme.dart';

class ExamTipPopup extends StatelessWidget {
  final String examName;
  final int minutes;
  final int marks;

  final VoidCallback onCancelTap;
  final VoidCallback onContinueTap;

  const ExamTipPopup(
      {super.key,
      required this.examName,
      required this.minutes,
      required this.marks,
      required this.onCancelTap,
      required this.onContinueTap});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(EXAM_TIP, style: LMSFonts.boldFont(20, AppTheme.primaryColor)),
            const SizedBox(height: 16.0),
            _rowWidget(Icons.event_note, examName),
            const SizedBox(height: 8.0),
            _rowWidget(Icons.access_time, '$minutes $C_MINUTES'),
            const SizedBox(height: 8.0),
            _rowWidget(Icons.functions_outlined, '$marks $MARKS'),
            const SizedBox(height: 20.0),
            _buttonWidget(),
          ],
        ),
      ),
    );
  }

  Widget _rowWidget(IconData iconObj, String textObj) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          iconObj,
          size: 20.0,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(
            textObj,
            style: LMSFonts.regularFontWithHeight(
                16, AppTheme.primaryTextColorBlack, 1.0),
          ),
        ),
      ],
    );
  }

  Widget _buttonWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        RoundedButton(
          text: CONTINUE,
          onPressed: () => onContinueTap(),
        ),
        RoundedButton(
          text: CANCEL_TEXT,
          onPressed: () => onCancelTap(),
        ),
      ],
    );
  }
}

class RoundedButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;

  const RoundedButton({
    required this.text,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        padding: const EdgeInsets.symmetric(
          vertical: 12.0,
          horizontal: 24.0,
        ),
      ),
      child: Text(
        text,
        style: LMSFonts.regularFontWithHeight(
            16, AppTheme.primaryTextColorBlack, 1.0),
      ),
    );
  }
}
