// ignore_for_file: non_constant_identifier_names

import '../../config/enums/exam_status.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/exam_summary.dart';
import '../../domain/models/resource_comment.dart';
import '../../domain/models/resource_file.dart';
import '/src/domain/models/course_details.dart';
import 'package:supabase/supabase.dart';

import '../../config/enums/question_status.dart';
import '../../domain/models/course.dart';
import '../../domain/models/exam.dart';
import '../../domain/models/exam_review.dart';
import '../../domain/models/organization.dart';
import '../../domain/models/page_content.dart';
import '../../domain/models/profile.dart';
import '../../domain/models/question_data.dart';
import '../../domain/models/responses/section_details.dart';
import '../../domain/models/topic.dart';

const String MOCK_USER_ID = "d1f30244-76f4-4ee0-8a5d-e577f24cd3ac";
const String MOCK_FIRSTNAME = "anu";
const String MOCK_LASTNAME = "mathew";
const String MOCK_AVATARURL =
    "https://kfpwszxuivydjftikxts.supabase.co/storage/v1/object/sign/5fbf2909-f0b4-45a5-8109-07780999c22e/avatars/data/user/0/com.smartlearn.app/cache/32b79d2f-3f67-4f36-a153-af5e8815cc79/eduardo-bergen-a1V5iA9UTDc-unsplash.jpg?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************.rrfUe5VCJo4o67tlOqG5WXyFoab2RV_ZEUBLLd2hb0U";

const String MOCK_COURSE_ID = "0843b5da-6b61-4945-8917-83b1ca63b804";
const String MOCK_ORG_ID = "19579370-a028-484d-8f35-8af2023068dd";
const String MOCK_TOPIC_ID = "05fe6b55-e337-4f91-8635-a3d0a94195f1";
const String MOCK_EXAM_ID = "0123";
const String MOCK_QUIZ_ATTEMPT_ID = "0123";
const String MOCK_SECTION_ID = "70d29f8a-78e5-440a-8aaf-81937d0caf31";
const String MOCK_INSTANCE = "0f149e48-6b2b-4766-be59-3d27556477c2";

const List<Course> MOCK_COURSE_LIST_RESPONSE = [
  Course(
    course_id: "ddcc58e5-0a4e-4e0f-9880-f83faed1edcc",
    category_id: "ce7ea1b0-9670-459d-8e55-7c57e6dbe0d3",
    full_name: "ACCP Pro",
    short_name: "The complete IT career program",
  )
];

const List<Organization> ORGANIZATIONLIST_APIRESPONSE = [
  Organization(
    userId: "d1f30244-76f4-4ee0-8a5d-e577f24cd3ac",
    orgId: "5fbf2909-f0b4-45a5-8109-07780999c22e",
    orgName: "CitrusInformatics",
  )
];

const List<TopicList> TOPIC_LIST = [
  TopicList(
      id: " 9377357e-4515-4955-a68b-fd0759002d2b",
      name: "physics",
      description: "Astro Physics",
      parent_id: "9377357e-4515-4955-a68b-fd0759002d2b")
];

Profile MOCK_USER_PROFILE_INFO = Profile(
    userId: "d1f30244-76f4-4ee0-8a5d-e577f24cd3ac",
    firstName: "anu",
    lastName: "mathew",
    avatarUrl:
        "https://kfpwszxuivydjftikxts.supabase.co/storage/v1/object/sign/5fbf2909-f0b4-45a5-8109-07780999c22e/avatars/data/user/0/com.smartlearn.app/cache/32b79d2f-3f67-4f36-a153-af5e8815cc79/eduardo-bergen-a1V5iA9UTDc-unsplash.jpg?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************.rrfUe5VCJo4o67tlOqG5WXyFoab2RV_ZEUBLLd2hb0U",
    email: "<EMAIL>");

const User MOCK_USER_RESPONSE = User(
    id: '88ad666e-4854-427f-b316-92704f0d7847',
    appMetadata: {
      "provider": "email",
      "providers": ["email"]
    },
    userMetadata: {
      "first_name": "Misha",
      "last_name": "Muraly",
      "phonenumber1": "**********"
    },
    aud: 'authenticated',
    createdAt: '2023-05-17T06:13:25.428037Z');

const List<Organization> MOCK_ORGANIZATION_LIST = <Organization>[
  Organization(
    userId: MOCK_USER_ID,
    orgId: MOCK_ORG_ID,
    orgName: "CitrusInformatics",
  )
];

List<dynamic> MOCK_EXAM_RESPONSE = [
  {
    "id": "7fbcf790-8a32-461f-94fc-2f82994493e6",
    "name": "S.I unit of Pressure Energy?",
    "answers": [
      {
        "id": "40f9e1b5-0a6f-4ba6-a7be-121c58996b89",
        "answer": "Nm",
        "org_id": "5fbf2909-f0b4-45a5-8109-07780999c22e",
        "fraction": 0,
        "ans_format": "",
        "created_at": "2023-06-22T16:10:07.350389+00:00",
        "updated_at": "2023-06-22T16:10:07.350389+00:00",
        "question_id": "7fbcf790-8a32-461f-94fc-2f82994493e6"
      },
      {
        "id": "69623db6-35e5-46e1-bf10-1715cc05ded4",
        "answer": "Watt",
        "org_id": "5fbf2909-f0b4-45a5-8109-07780999c22e",
        "fraction": 0,
        "ans_format": "",
        "created_at": "2023-06-22T16:10:07.350389+00:00",
        "updated_at": "2023-06-22T16:10:07.350389+00:00",
        "question_id": "7fbcf790-8a32-461f-94fc-2f82994493e6"
      },
      {
        "id": "3adbb6c4-2fe1-4b85-85cf-a110a010f608",
        "answer": "Joule",
        "org_id": "5fbf2909-f0b4-45a5-8109-07780999c22e",
        "fraction": 1,
        "ans_format": "",
        "created_at": "2023-06-22T16:10:07.350389+00:00",
        "updated_at": "2023-06-22T16:10:07.350389+00:00",
        "question_id": "7fbcf790-8a32-461f-94fc-2f82994493e6"
      },
      {
        "id": "b778e299-1f94-43d3-9793-e4676f336b7e",
        "answer": "No units",
        "org_id": "5fbf2909-f0b4-45a5-8109-07780999c22e",
        "fraction": 0,
        "ans_format": "",
        "created_at": "2023-06-22T16:10:07.350389+00:00",
        "updated_at": "2023-06-22T16:10:07.350389+00:00",
        "question_id": "7fbcf790-8a32-461f-94fc-2f82994493e6"
      }
    ],
    "question_text": "What is the S.I unit of Pressure Energy?"
  }
];

List<CourseDetails> MOCK_COURSE_DETAILS_DATA_LIST = <CourseDetails>[
  CourseDetails(courseModule: const <CourseModule>[
    CourseModule(
      moduleId: "c6eaea63-e54e-48c0-94cb-358bd9d90468",
      moduleName: "General",
    )
  ], courseVideos: <CourseVideo>[
    CourseVideo(
        videoId: '',
        videoName: '',
        videoURL: '',
        instanceId: '',
        description: '',
        videoThumbnail: '')
  ], currentAffairs: <CurrentAffairs>[
    CurrentAffairs(
      newsId: '',
      newsTitle: '',
      newsContent: '',
      backgroundImage: '',
      publishedDate: DateTime.now(),
    )
  ], exams: const <CourseExam>[
    CourseExam(
      moduleId: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
      sectionId: "170fdeff-f41d-4cac-b011-fd502cb17095",
      moduleName: "Flutter Experience",
    )
  ])
];

CourseDetails MOCK_COURSE_DETAILS_DATA =
    CourseDetails(courseModule: const <CourseModule>[
  CourseModule(
    moduleId: "c6eaea63-e54e-48c0-94cb-358bd9d90468",
    moduleName: "General",
  )
], courseVideos: <CourseVideo>[
  CourseVideo(
      videoId: '',
      videoName: '',
      videoURL: '',
      instanceId: '',
      description: '',
      videoThumbnail: '')
], currentAffairs: <CurrentAffairs>[
  CurrentAffairs(
    newsId: '',
    newsTitle: '',
    newsContent: '',
    backgroundImage: '',
    publishedDate: DateTime.now(),
  )
], exams: const <CourseExam>[
  CourseExam(
    moduleId: "dd33330a-7ab4-452b-b8bd-9e37192f0ffb",
    sectionId: "170fdeff-f41d-4cac-b011-fd502cb17095",
    moduleName: "Flutter Experience",
  )
]);

SectionDetails MOCK_SECTION_DETAILS_INFO = SectionDetails(
    sectionId: "70d29f8a-78e5-440a-8aaf-81937d0caf31",
    courseId: "ddcc58e5-0a4e-4e0f-9880-f83faed1edcc",
    name: ".NET",
    summary: "",
    sectionOrder: 1,
    resources: [],
    modules: [MOCK_MODULE_INFO]);

Modules MOCK_MODULE_INFO = Modules(
  courseId: "dcc58e5-0a4e-4e0f-9880-f83faed1edcc",
  moduleId: "65331446-ca27-48bf-acc4-27d09ffa9bed",
  instanceId: "0f149e48-6b2b-4766-be59-3d27556477c2",
  sectionId: "70d29f8a-78e5-440a-8aaf-81937d0caf31",
  moduleType: "Page",
  moduleName: "Term 2: .NET",
);

ResourseFile MOCK_RESOURCE_FILE = ResourseFile(
    fileId: "",
    courseId: "",
    courseName: "",
    instanceId: "",
    description: "",
    url: "",
    moduleResource: "");

PageContent MOCK_PAGE_CONTENT_INFO = PageContent(
    id: "0f149e48-6b2b-4766-be59-3d27556477c2",
    name: "Term 2: .NET",
    content: "");

List<QuestionData> MOCK_QSTN_DATA_INFO = <QuestionData>[
  QuestionData(
    id: 1,
    examId: 'x',
    quizAttemptId: '',
    examName: "Flutter Experience",
    intro: "",
    duration: 30,
    endTime: DateTime.now(),
    passMark: 35,
    mainTopic: "10+2/College Students",
    startTime: DateTime.now(),
    numOfQuestions: 50,
    totalMark: 200,
    questions: MOCK_QUESTION_INFO,
    networkStatus: 'true',
    examUploadStatus: 1,
    examCompletionStatus: 1,
  )
];

QuestionData MOCK_QSTN_DATA = QuestionData(
  id: 1,
  examId: 'x',
  quizAttemptId: '',
  examName: "Flutter Experience",
  intro: "",
  duration: 30,
  endTime: DateTime.now(),
  passMark: 35,
  mainTopic: "10+2/College Students",
  startTime: DateTime.now(),
  numOfQuestions: 50,
  totalMark: 200,
  questions: MOCK_QUESTION_INFO,
  networkStatus: 'true',
  examUploadStatus: 1,
  examCompletionStatus: 1,
);

List<Question> MOCK_QUESTION_INFO = <Question>[
  Question(
      id: 0,
      questionId: '',
      questionName: "",
      questionText: "",
      quizId: "",
      options: [MOCK_ANSWER],
      selectedAnswerIds: [],
      selectedAnswerIndexes: [],
      chosenAnswer: '',
      correctAnswer: '',
      didAnswerCorrectly: false,
      questionType: '')
];

Answer MOCK_ANSWER = Answer(
    answerId: "",
    orgId: "",
    fraction: 1,
    answerVal: "",
    answerStatus: AnswerStatus.right,
    correctAnswer: true,
    id: 0,
    answerType: '');

List<ExamReview> MOCK_EXAM_REVIEW_INFO = [
  ExamReview(
      quizId: "",
      questionWithOptions: "",
      responseSummary: "",
      orgId: "",
      courseId: "",
      courseName: "",
      quizName: "",
      endTime: "",
      startTime: "",
      duration: "")
];

List<int> MOCK_INDEX_INFO = [1, 2, 3];

List<Map<String, dynamic>> MOCK_ANSWERED_QSTN_LIST_INFO = [
  {
    'question_id': '46f40e6d-033e-4143-967d-4ddc71ecfd7a',
    'question_with_options': '<p>What is Flutter?</p>',
    'response_summary': 'It is a DBMS toolkit',
    'selected_answer_ids': ['310f0816-a7c3-4897-ad37-f5cc2f734969']
  },
  {
    'question_id': 'c835fa3b-9d9f-4b54-b0e5-1fc91ab60c0a',
    'question_with_options': '<p>What is Dart?</p>',
    'response_summary': 'It is a programming language',
    'selected_answer_ids': [
      'f1350138-8b2d-4e4b-9f6f-6e74de248a78',
      '56c2e5fe-398d-4a3e-8315-6fdef88a82b7'
    ]
  },
];

Map<String, dynamic> MOCK_SAVE_ANSWER = {
  "org_id": MOCK_ORG_ID,
  "quiz_id": '',
  "user_id": MOCK_USER_ID,
  "submit_datas": MOCK_ANSWERED_QSTN_LIST_INFO
};

Map<String, dynamic> MOCK_EXAM_JSON = {
  "org_id": MOCK_ORG_ID,
  "quiz_id": MOCK_EXAM_ID,
  "user_id": MOCK_USER_ID,
  "quiz_attempt_id": MOCK_QUIZ_ATTEMPT_ID,
  "submit_datas": MOCK_ANSWERED_QSTN_LIST_INFO
};

List<Exam> MOCK_EXAM_INFO = [
  Exam(
      id: "740b0cb4-41d4-458d-933f-4616eeaa40df",
      name: "Flutter Experience",
      orgId: "7e6fc1ea-1a4a-4caa-8764-77a9b17044cc",
      duration: 30,
      endTime: DateTime.now(),
      courseId: "ddcc58e5-0a4e-4e0f-9880-f83faed1edcc",
      passMark: 35,
      createdAt: DateTime.now(),
      mainTopic: "10+2/College Students",
      startTime: DateTime.now(),
      totalMark: 50,
      updatedAt: DateTime.now(),
      numOfQuestions: 5,
      intro: "<p>Flutter Experience</p>",
      pendingAttempts: 0,
      quizAttemptId: '',
      quizId: '',
      result: ExamResult.failed,
      examType: ExamType.main)
];

List<ExamSummary> MOCK_EXAM_SUMMARY = [
  ExamSummary(
      examName: '',
      mainTopic: '',
      duration: 10,
      passMark: 40.0,
      totalMark: 40.0,
      scoredMark: 80.0,
      didPassExam: true,
      startTime: DateTime.now(),
      endTime: DateTime.now(),
      numOfQuestions: 10,
      skippedAnsCount: 10,
      wrongAnswerCount: 10,
      correctAnswerCount: 10,
      description: '',
      questions: MOCK_QUESTION_INFO,
      categoeyWiseSummary: MOCK_CATEGORY_SUMMARY)
];

List<CategoeyWiseSummary> MOCK_CATEGORY_SUMMARY = [
  CategoeyWiseSummary(
      skippedAnsCount: 4,
      wrongAnswerCount: 4,
      correctAnswerCount: 4,
      questionCategoryId: "",
      questionCategoryName: "")
];

List<Map<String, dynamic>> MOCK_TOPIC_LIST_INFO = [
  {
    'Parent': {
      'id': 'ce7ea1b0-9670-459d-8e55-7c57e6dbe0d3',
      'name': '10+2/College Students',
      'description': 'Courses for 10th, Plus-two'
    },
    'Children': [],
  },
];

CourseProgress MOCK_COURSE_PROGRESS = CourseProgress(
    progress: 10.0, timeSpent: '', instanceId: '', markedAsDone: false);

CourseVideo MOCK_COURSE_VIDEO = CourseVideo(
    videoId: '',
    videoName: '',
    videoURL: '',
    videoThumbnail: '',
    instanceId: '',
    description: '',
    progress: 10.0,
    totalDuration: 6);

List<ResourseComment> MOCK_COMMENTS = [
  ResourseComment(
      commentId: "",
      message: "",
      subject: "",
      user: "",
      profilePic: "",
      commentType: "",
      commentedTime: DateTime.now(),
      children: [])
];

List<CurrentAffairs> MOCK_CURRENT_AFFAIRS = [
  CurrentAffairs(
      newsId: "",
      newsTitle: "",
      newsContent: "",
      backgroundImage: "",
      publishedDate: DateTime.now())
];

Map<String, dynamic> MOCK_DURATION = {'progress': "", 'time_spent': ""};

Map<String, dynamic> MOCK_UPLOAD_COMMENT_JSON = {
  "comment_data": {"subject": MOCK_TOPIC_ID, "message": "", "type": ""},
  "instance_id": MOCK_INSTANCE,
  "user_id": MOCK_USER_ID
};
