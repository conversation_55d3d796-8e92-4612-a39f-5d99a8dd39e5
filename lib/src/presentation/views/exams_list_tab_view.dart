import '../../config/app_config/api_constants.dart';
import '../../config/themes/app_dynamic_theme.dart';
import '/src/utils/helper/privilege_access_mapper.dart';
import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';

import '../../config/enums/exam_status.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../widgets/alert_popup/confirmation_popup.dart';
import '/src/domain/models/tabbar_item.dart';

import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '/src/domain/models/rank.dart';

import '../cubits/rank/rank_list_cubit.dart';
import '/src/utils/helper/app_date_formatter.dart';

import '../../domain/models/exam.dart';
import '../../domain/models/exam_summary.dart';
import '../widgets/connectivity_widget.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '/src/presentation/widgets/loading_indicator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../config/themes/lms_fonts.dart';
import '../../domain/models/question_data.dart';
import '../cubits/exam/exam_cubit.dart';
import '../widgets/app_bar.dart';
import '../widgets/tab_bar_widget.dart';
import '/src/config/themes/app_theme.dart';
import '/src/config/themes/theme_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

import '../../config/router/app_router.dart';
import '../../utils/constants/exam_list_json.dart';
import '../../utils/constants/strings.dart';
import '../widgets/button_widget.dart';
import 'ppt_viewer.dart';
import 'package:provider/provider.dart' as provider;

enum ExamListType { newExams, attempted, passed }

@RoutePage()

// ignore: must_be_immutable
class ExamListViewScreen extends HookWidget with WidgetsBindingObserver {
  final int tabIndex;
  ExamListViewScreen(this.tabIndex, {super.key});

  final List<Map<String, dynamic>> examLists = NewExams.examDetails();
  final List attemptedDetailslist = NewExams.attemptedDetails();
  final List passedDetailslist = NewExams.passedDetails();
  final List noNewExamDetailsList = NewExams.noNewExamDetails();

  late TabController _tabController;

  final List<TabbarItem> _tabBarItems = [
    TabbarItem(
        id: 0,
        label: SLStrings.getTranslatedString(KEY_NEW_EXAMS_TAB),
        icon: '$ASSETS_PATH/quiz_add.png'),
    TabbarItem(
        id: 1,
        label: SLStrings.getTranslatedString(KEY_ATTEMPTED_TAB),
        icon: '$ASSETS_PATH/dock_search.png'),
    TabbarItem(
        id: 2,
        label: SLStrings.getTranslatedString(KEY_PASSED_TAB),
        icon: '$ASSETS_PATH/file_dock.png')
  ];

  List<Exam> _exams = [];
  List<Exam> _attemptedExams = [];
  List<Exam> _passedExams = [];
  List<Question> _questions = [];

  QuestionData? _questionData;

  bool _isDataLoading = true;

  BuildContext? currentContext;
  AppDynamicTheme? appDynamicTheme;

  _updateTabView(BuildContext context, ExamCubit _examCubit) async {
    _attemptedExams = [];
    _passedExams = [];
    _examCubit.setLoading(true);
    _isDataLoading = true;
    await _examCubit.fetchExamList();
    await _updateExamsList(context, _examCubit.state);
    await _examCubit.fetchAttemptedExamList();
    await _updateExamsList(context, _examCubit.state);
    _isDataLoading = false;
    _examCubit.setLoading(false);
  }

  _updateExamsList(BuildContext context, ExamState state) async {
    if (state is ExamsFailureState) {
      var route = ModalRoute.of(context);
      if (route != null && route.isActive) {
        showPGExceptionPopup(context, state.error);
      }
      return;
    }

    if (state.exams.isNotEmpty) {
      _exams = state.exams;
    }
    _attemptedExams = state.attemptedExams;
    _passedExams = _attemptedExams.isNotEmpty
        ? List.from(_attemptedExams
            .where((element) => element.result == ExamResult.passed))
        : [];
  }

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    didChangeAppLocale(locales, currentContext);
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    appDynamicTheme = provider.Provider.of<AppDynamicTheme>(context);
    final _examCubit = BlocProvider.of<ExamCubit>(context);
    final screenWidth = MediaQuery.of(context).size.width;
    _tabController = useTabController(
        initialLength: _tabBarItems.length, initialIndex: tabIndex);
    _passedExams = _passedExams;
    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      Future<void>.microtask(() async {
        await _updateTabView(context, _examCubit);
        _examCubit.enableAllRoutes();
      });

      return (() {
        WidgetsBinding.instance.removeObserver(this);
      });
    }, []);

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
        _handleBackNavigation(context);
      },
      child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: PreferredSize(
              preferredSize: const Size.fromHeight(70),
              child: AppBarWidget(
                title:
                    SLStrings.getTranslatedString(KEY_EXAM_LIST_APPBAR_TITLE),
                leadingIconName: BACK_ARROW,
                leadingBtnAction: () {
                  _handleBackNavigation(context);
                },
                trailingWidget: Container(),
              )),
          body: BlocBuilder<ExamCubit, ExamState>(
            builder: (context, state) {
              // if (state is ExamsLoading) {
              //   _isDataLoading = state.loadingStatus;
              // }
              return LayoutBuilder(
                  builder: (BuildContext context, BoxConstraints constraints) {
                return Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16)),
                  ),
                  child: Stack(
                    children: [
                      Align(
                        child: SizedBox(
                          height: constraints.maxHeight,
                          width: constraints.maxWidth < 720
                              ? constraints.maxWidth
                              : 800,
                          child: Column(
                            children: [
                              const SizedBox(height: 20),
                              TabbarWidget(
                                  tabBarItems: _tabBarItems,
                                  tabController: _tabController),
                              Expanded(
                                child: ScrollConfiguration(
                                  behavior:
                                      ScrollConfiguration.of(context).copyWith(
                                    dragDevices: {
                                      PointerDeviceKind.touch,
                                      PointerDeviceKind.mouse,
                                    },
                                  ),
                                  child: Container(
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 16),
                                    child: TabBarView(
                                      controller: _tabController,
                                      children: [
                                        _exams.isEmpty && !_isDataLoading
                                            ? _noExamView(ExamListType.newExams)
                                            : _examListView(screenWidth,
                                                _examCubit, _exams, true),
                                        _attemptedExams.isEmpty &&
                                                !_isDataLoading
                                            ? _noExamView(
                                                ExamListType.attempted)
                                            : _examListView(
                                                screenWidth,
                                                _examCubit,
                                                _attemptedExams,
                                                false),
                                        _passedExams.isEmpty && !_isDataLoading
                                            ? _noExamView(ExamListType.passed)
                                            : _examListView(
                                                screenWidth,
                                                _examCubit,
                                                _passedExams,
                                                false),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      ConnectivityStatusWidget(),
                      state is ExamsLoading || _isDataLoading
                          ? LoadingIndicatorClass()
                          : Container(),
                    ],
                  ),
                );
              });
            },
          )),
    );
  }
// Ui for No new exam

  Widget _noExamView(ExamListType examListType) {
    List noExamList = examListType == ExamListType.newExams
        ? noNewExamDetailsList
        : examListType == ExamListType.attempted
            ? attemptedDetailslist
            : passedDetailslist;
    return Container(
      margin: const EdgeInsets.only(top: 30),
      padding: const EdgeInsets.all(12),
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: AppTheme.sectionCardBgColor,
            border: Border.all(color: LmsColors.white),
            boxShadow: [
              BoxShadow(
                  color: LmsColors.greyForButton.withOpacity(0.5),
                  blurRadius: 10.0,
                  offset: const Offset(0, 1),
                  spreadRadius: 1),
            ],
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  noExamList[0]["exams"],
                  style: LMSFonts.boldFont(14, AppTheme.unselectedTabColor),
                ),
              ),
              const SizedBox(height: 10),
              _divider(),
              Center(child: _examDetails(noExamList[0]["listOfExams"])),
              // _divider(),
              // _examDetails(noExamList[0]["profile"]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _attendedExamBtnsWidget(BuildContext context, double screenWidth,
      String examId, ExamCubit examCubit, Exam examInfo) {
    return Row(
      children: [
        // Review Button
        Expanded(
          child: _reviewBtnAction(context, screenWidth, examInfo, SLStrings.getTranslatedString(KEY_REVIEW_BTN)),
        ),
        const SizedBox(width: 8),
        // View Results Button
        _attemptedBtnActions(
            screenWidth,
            context,
            SLStrings.getTranslatedString(KEY_VIEW_RESULTS_BTN),
            examId,
            examCubit,
            examInfo,
          ),
        
        const SizedBox(width: 8),
        // Rank List Button
        Visibility(
        //  visible: true, // or your original condition
          visible: examInfo.examType != ExamType.practice,
       
            child: _attemptedBtnActions(
              screenWidth,
              context,
              SLStrings.getTranslatedString(KEY_RANK_LIST_BTN),
              examId,
              examCubit,
              examInfo,
            ),
          
        ),
      ],
    );
  }

  Widget _divider() {
    return const Divider(
      color: LmsColors.black,
      thickness: 0.5,
      indent: 10,
      endIndent: 10,
    );
  }

  Widget _examDetails(String data) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Text(
        data,
        style: LMSFonts.regularFontWithHeight(
            12, AppTheme.unselectedTabColor, 1.2),
      ),
    );
  }

  Widget _examListView(double screenWidth, ExamCubit _examCubit,
      List<Exam> exams, bool isNewExamsTab) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: exams.length,
      padding: const EdgeInsets.only(top: 16, bottom: 16),
      itemBuilder: (context, index) {
        final _examInfo = exams[index];

        return Card(
          elevation: 0,
          color: AppTheme.sectionCardBgColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
          margin: const EdgeInsets.only(bottom: 16, left: 5, right: 5, top: 2),
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 20, 16, 15),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _examTitleWidget(
                    _examInfo, context, screenWidth, _examCubit, isNewExamsTab),
                const SizedBox(height: 15),
                _examInfoMainWidget(
                    _examInfo, screenWidth, context, _examCubit, isNewExamsTab),
                const SizedBox(height: 15),
                isNewExamsTab
                    ? Container()
                    : _attendedExamBtnsWidget(context, screenWidth,
                        _examInfo.id, _examCubit, _examInfo)
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _examTitleWidget(Exam _examInfo, BuildContext context,
      double screenWidth, ExamCubit _examCubit, bool isNewExamsTab) {
    return Row(
      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(_examInfo.name,
              style: LMSFonts.semiBoldFont(14, AppTheme.unselectedTabColor)),
        ),
        _attendButton(
            context,
            screenWidth,
            _examInfo,
            _examCubit,
            isNewExamsTab
                ? SLStrings.getTranslatedString(KEY_ATTEND_BTN)
                : SLStrings.getTranslatedString(KEY_RE_ATTEND_BTN),
            isNewExamsTab)
      ],
    );
  }

  Widget _examInfoMainWidget(Exam _examInfo, double screenWidth,
      BuildContext context, ExamCubit _examCubit, bool isNewExamsTab) {
    double screenWidth = MediaQuery.of(context).size.width;
    double examBoxWidth = (screenWidth / 350) * 200;
    return Container(
      width: screenWidth,
      decoration: BoxDecoration(
          border: Border.all(
              color: AppTheme.secondaryAppColor.withOpacity(0.5), width: 2),
          borderRadius: BorderRadius.circular(10),
          color: AppTheme.blackColor.withOpacity(0.03)),
      padding: const EdgeInsets.all(12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _examInfoWidget([
            SLStrings.getTranslatedString(KEY_QUESTIONS),
            SLStrings.getTranslatedString(KEY_PASSMARK)
          ], [
            _examInfo.numOfQuestions,
            _examInfo.passMark
          ], false, examBoxWidth),
          const SizedBox(width: 5),
          _examInfoWidget([
            SLStrings.getTranslatedString(KEY_MARKS),
            SLStrings.getTranslatedString(KEY_MIN)
          ], [
            _examInfo.totalMark,
            _examInfo.duration
          ], true, examBoxWidth),
        ],
      ),
    );
  }

  Widget _examInfoWidget(List<String> titleParam, List<dynamic> valueParam,
      bool isDetails, double examBoxWidth) {
    return Expanded(
      child: Wrap(
        runSpacing: 12,
        children: [
          _questionDetails(valueParam[0], titleParam[0], isDetails),
          _questionDetails(valueParam[1], titleParam[1], isDetails),
        ],
      ),
    );
  }

  Widget _questionDetails(dynamic description, String details, bool value) {
    return Container(
      alignment: Alignment.centerLeft,
      child: RichText(
        maxLines: 3,
        text: TextSpan(
          children: [
            TextSpan(
              text: description.toString().split('.').first,
              style: LMSFonts.semiBoldFont(14, AppTheme.examInfoTextColor),
            ),
            TextSpan(
              text: ' ' + details,
              style: LMSFonts.regularFontWithHeight(
                  14, AppTheme.examInfoTextColor, 1),
            ),
          ],
        ),
      ),
    );
  }

  //button for Rank list, view Solution
  Widget _attemptedBtnActions(double screenWidth, BuildContext context,
      String title, String examId, ExamCubit examCubit, Exam examInfo) {
    final buttonWidth = screenWidth * 0.4;
    return Expanded(
      child: ButtonWidget(
        child: Text(title,
            textAlign: TextAlign.center,
            style: LMSFonts.mediumFont(
                16,
                title == SLStrings.getTranslatedString(KEY_VIEW_RESULTS_BTN)
                    ? appDynamicTheme?.buttonSecondaryColor ??
                        AppTheme.buttonBgColour
                    : AppTheme.whiteTextColor,
                1.0)),
        minimumSize: Size(buttonWidth, 35),
        color: title == SLStrings.getTranslatedString(KEY_VIEW_RESULTS_BTN)
            ? AppTheme.viewResultButtonColor.withOpacity(0)
            : appDynamicTheme?.buttonSecondaryColor ?? AppTheme.buttonBgColour,
        shape: RoundedRectangleBorder(
          side: BorderSide(
            color: appDynamicTheme?.buttonSecondaryColor ??
                AppTheme.buttonBgColour,
          ),
          borderRadius: BorderRadius.circular(50),
        ),
        onPressed: title == SLStrings.getTranslatedString(KEY_VIEW_RESULTS_BTN)
            ? () async {
                var route = ModalRoute.of(context);

                String quizAttemptId = examInfo.attemptedIdList.isNotEmpty
                    ? examInfo.attemptedIdList.first.attemptId
                    : '';

                if (examInfo.quizId.isNotEmpty && quizAttemptId.isNotEmpty) {
                  examCubit.setLoading(true);

                  /// block navigation
                  examCubit.updateNavigationStatustoList(
                      disableBackNavigation: true);

                  await examCubit.fetchExamResultStatisticalData(
                      quizAttemptId, examInfo.quizId);
                  ExamState state = examCubit.state;
                  if (state is ExamResultSummaryFetched &&
                      state.examSummaryResult.isNotEmpty) {
                    ExamSummary examSummaryResult =
                        state.examSummaryResult.first;
                    examCubit.setLoading(false);
                    if (route != null && route.isActive) {
                      kIsWeb
                          ? Beamer.of(context).beamToNamed('/exam-result',
                              data: {
                                  'examSummary': examSummaryResult,
                                  'tabIndex': _tabController.index
                                })
                          : appRouter.push(ExamResultViewRoute(
                              examSummary: examSummaryResult,
                              tabIndex: _tabController.index,
                              navigateBackTo: NavigationTo.exam_list,
                            ));
                    }
                  } else if (state is ExamsQuestionsFailureState) {
                    if (route != null && route.isActive) {
                      _showViewResultsErrorPopup(context, state.error);
                    }
                  } else {
                    if (route != null && route.isActive) {
                      _showViewResultsErrorPopup(
                          context,
                          SLStrings.getTranslatedString(
                              KEY_VIEW_RESULTS_FAILURE));
                    }
                  }
                } else {
                  if (route != null && route.isActive) {
                    _showViewResultsErrorPopup(
                        context,
                        SLStrings.getTranslatedString(
                            KEY_VIEW_RESULTS_QUIZ_ATTEMPT_ID_EMPTY));
                  }
                }
              }
            : () async => await _rankListBtnAction(context, examId),
      ),
    );
  }
  
  Widget _reviewBtnAction(BuildContext context, double screenWidth, Exam examInfo,String title,) 
  {
  final String quizAttemptId = examInfo.attemptedIdList.isNotEmpty
      ? examInfo.attemptedIdList.first.attemptId
      : '';

  return ButtonWidget(
    child: Text(
      title,
      textAlign: TextAlign.center,
      style: LMSFonts.mediumFont(16, AppTheme.whiteColor, 1.0),
    ),
    minimumSize: Size(screenWidth * 0.4, 35),
    color: AppTheme.buttonBgColour,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(50),
    ),
    onPressed: (examInfo.quizId.isNotEmpty && quizAttemptId.isNotEmpty)
        ? () {
            appRouter.push(
              ExamReviewViewRoute(
                quizId: examInfo.quizId,
                quizAttemptId: quizAttemptId,
                isGradeCalculated: true,
                navigateBackTo: NavigationTo.exam_list,
              ),
            );
          }
        : null,
  );
}



  _rankListBtnAction(BuildContext context, String examId) async {
    var route = ModalRoute.of(context);
    final _rankCubit = BlocProvider.of<RankListCubit>(context);
    final _examCubit = BlocProvider.of<ExamCubit>(context);

    try {
      bool _hasAccess = await _checkAccessToFetchRankList();
      if (_hasAccess) {
        _examCubit.setLoading(true);
        await _rankCubit.fetchExamRankList(examId);
        final List<Rank> _rankList = _rankCubit.state.rankList;

        RankListState state = _rankCubit.state;
        if (state is RankListFetched) {
          _examCubit.setLoading(false);
          if (_rankList.isNotEmpty) {
            if (route != null && route.isActive) {
              kIsWeb
                  ? Beamer.of(context).beamToNamed('/rank-list', data: {
                      'isCourseRankList': false,
                      'rankListForExam': _rankList
                    })
                  : appRouter.push(RankListViewRoute(
                      isCourseRankList: false, rankListForExam: _rankList));
            }
          } else {
            ///rank list not available for this exam
            if (route != null && route.isActive) {
              _showRankListEmptyPopup(context,
                  SLStrings.getTranslatedString(KEY_RANK_NOT_AVAILABLE));
            }
          }
        } else if (state is RankListError) {
          _examCubit.setLoading(false);
          if (route != null && route.isActive) {
            // _showRankListEmptyPopup(context, state.error);
            showPGExceptionPopup(context, state.error);
          }
        } else {
          _examCubit.setLoading(false);
          if (route != null && route.isActive) {
            _showRankListEmptyPopup(
                context, SLStrings.getTranslatedString(KEY_RANK_NOT_AVAILABLE));
          }
        }
      } else {
        if (route != null && route.isActive) {
          showNoAccessPopup(context, '');
        }
      }
    } on Exception catch (_) {
      // TODO
    }
  }

  Widget _attendButton(BuildContext context, double screenWidth, Exam examInfo,
      ExamCubit _examCubit, String title, bool isNewExamsTab) {
    DateTime currentTime = DateTime.now();
    DateTime startTime = examInfo.startTime;
    DateTime endTime = examInfo.endTime;
    bool isExpiredExam = endTime.isBefore(currentTime);

    return InkWell(
      child: SizedBox(
        height: 21,
        child: Wrap(
          // mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Image.asset(
              '$ASSETS_PATH/Chat_alt_add.png',
              height: 20,
              width: 20,
              color: isNewExamsTab
                  ? AppTheme.viewResultButtonColor
                  : examInfo.pendingAttempts > 0 && !isExpiredExam
                      ? AppTheme.viewResultButtonColor
                      : AppTheme.viewResultButtonColor.withOpacity(0.3),
            ),
            const SizedBox(width: 5),
            Text(
              title,
              style: LMSFonts.regularFont(
                14,
                isNewExamsTab
                    ? AppTheme.viewResultButtonColor
                    : examInfo.pendingAttempts > 0 && !isExpiredExam
                        ? AppTheme.viewResultButtonColor
                        : AppTheme.viewResultButtonColor.withOpacity(0.3),
              ),
            )
          ],
        ),
      ),
      onTap: isNewExamsTab
          ? () async {
              await _fetchExamQuestions(_examCubit, context, examInfo.id);
            }
          : examInfo.pendingAttempts > 0 && !isExpiredExam
              ? () async {
                  await _fetchExamQuestions(_examCubit, context, examInfo.id);
                }
              : null,
      // onTap: () {
      //   _showQuestionsEmptyPopup(context);
      // },
    );
  }

// TODO: confirm with backend team

  Future<bool> _checkAccessToFetchQuestions() async {
    // decides whether to disable/enable attend  button
    String screen = PrivilegeAccessConsts.SCREEN_EXAM;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_GET_QUIZ_QUESTIONS;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

/*
  Future<bool> _checkAccessToFetchResults() async {
     // decides whether to disable/enable attend  button
    String screen = PrivilegeAccessConsts.SCREEN_COURSE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_COURSE_LIST;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }
*/
  Future<bool> _checkAccessToFetchRankList() async {
    // decides whether to disable/enable attend  button
    String screen = PrivilegeAccessConsts.SCREEN_EVALUATION;
    String accessRequiredFeature =
        PrivilegeAccessConsts.ACTION_FETCH_QUIZ_RANK_LIST;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _handleBackNavigation(BuildContext context) {
    if (showCourseDetailsDashboard) {
      kIsWeb
          ? Beamer.of(context).beamToNamed('/course-details-dashboard')
          : appRouter.popUntil((route) =>
              route.settings.name == CourseDetailsDashboardRoute.name);
    } else {
      kIsWeb
          ? Beamer.of(context).beamToNamed('/course-details')
          : appRouter.popUntil(
              (route) => route.settings.name == CourseDetailsViewRoute.name);
    }
  }

  _fetchExamQuestions(
      ExamCubit _examCubit, BuildContext context, String examId) async {
    var route = ModalRoute.of(context);
    bool _hasAccess = await _checkAccessToFetchQuestions();

    if (_hasAccess) {
      _examCubit.setLoading(true);
      await _examCubit.fetchQuestions(examId);
      ExamState state = _examCubit.state;
      if (state is ExamsQuestionsFailureState) {
        if (route != null && route.isActive) {
          showPGExceptionPopup(context, state.error);
        }
      } else {
        if (_examCubit.state.examData.isNotEmpty) {
          _questionData = _examCubit.state.examData.first;
        }
        _questions = _questionData?.questions ?? [];
        _examCubit.setLoading(false);

        DateTime currentTime = DateTime.now();
        DateTime startTime = _questionData?.startTime ?? currentTime;
        DateTime endTime = _questionData?.endTime ?? currentTime;

        if (_questions.isEmpty && route != null && route.isActive) {
          _showQuestionsEmptyPopup(context);
        } else {
          if ((currentTime.isAfter(startTime) &&
                  currentTime.isBefore(endTime)) ||
              (currentTime.isAtSameMomentAs(startTime))) {
            // check if the exam is valid
            if (_questions.isNotEmpty && route != null && route.isActive) {
              // start exam popup
              kIsWeb
                  ? Beamer.of(context).beamToNamed('/exam-intro',
                      data: {'questionData': _questionData!})
                  : appRouter
                      .push(ExamIntroductionViewRoute(
                      questionData: _questionData!,
                      navigateBackTo: NavigationTo.exam_list,
                    ))
                      .then((value) async {
                      await _updateTabView(context, _examCubit);
                    });
            } else {
              // no questions available for the exam
              if (route != null && route.isActive) {
                _showQuestionsEmptyPopup(context);
              }
            }
          } else {
            // not valid
            // exam expired

            if (route != null && route.isActive) {
              if (currentTime.isBefore(startTime)) {
                String startTimeFormatted = AppDateFormatter()
                    .formatForExamAvailabilityPopup(startTime);
                _showInvalidExamPopup(context,
                    '${SLStrings.getTranslatedString(KEY_EXAM_AVAILABLE_POPUP)} $startTimeFormatted');
              } else {
                _showInvalidExamPopup(
                    context, SLStrings.getTranslatedString(KEY_EXAM_EXPIRED));
              }
            }
          }
        }
      }
    } else {
      if (route != null && route.isActive) {
        showNoAccessPopup(context, '');
      }
    }
  }

  _showInvalidExamPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: msg,
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showQuestionsEmptyPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: SLStrings.getTranslatedString(KEY_QUESTION_NOT_AVAILABLE),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showRankListEmptyPopup(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: message,
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }

  _showViewResultsErrorPopup(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) {
        return SingleActionDialogue(
          title: SLStrings.getTranslatedString(KEY_SORRY),
          message: message,
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    );
  }
}
