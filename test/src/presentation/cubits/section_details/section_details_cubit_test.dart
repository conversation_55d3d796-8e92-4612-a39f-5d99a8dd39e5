import 'package:SmartLearn/src/domain/models/course_details.dart';
import 'package:SmartLearn/src/domain/models/page_content.dart';
import 'package:SmartLearn/src/domain/models/responses/section_details.dart';
import 'package:SmartLearn/src/domain/repositories/section_repository.dart';
import 'package:SmartLearn/src/presentation/cubits/section_details/section_details_cubit.dart';
import 'package:SmartLearn/src/utils/constants/api_response.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../course_list/course_list_cubit_test.dart';

class MockSectionRepository implements SectionRepository {
  @override
  Future<PageContent> fetchPageContent(String instance) {
    return Future(() => MOCK_PAGE_CONTENT_INFO);
  }

  @override
  Future<SectionDetails> fetchSectionDetailsInfo(String sectionId) {
    return Future(() => MOCK_SECTION_DETAILS_INFO);
  }

  @override
  Future fetchResourceImageInfo(String instance) {
    return Future(() => MOCK_RESOURCE_FILE);
  }

  @override
  Future<CourseVideo?> fetchResourceVideoInfo(String instance) {
    return Future(() => MOCK_COURSE_VIDEO);
  }

  @override
  Future getCourseProgress(String instance) {
    return Future(() => MOCK_COURSE_PROGRESS);
  }
}

void main() {
  testWidgets('Section Details cubit ...', (tester) async {});

  late MockSectionRepository _mockSectionRepo;
  late MockCourseRepository _mockCourseRepo;
  late SectionDetailsCubit _sectionDetailsCubit;

  setUp(() {
    SharedPreferences.setMockInitialValues({});
    _mockSectionRepo = MockSectionRepository();
    _mockCourseRepo = MockCourseRepository();
    _sectionDetailsCubit =
        SectionDetailsCubit(_mockSectionRepo, _mockCourseRepo);
  });

  tearDown(() {
    _sectionDetailsCubit.close();
  });

  group('SectionDetailscubit', () {
    blocTest<SectionDetailsCubit, SectionDetailsState>(
      'emits SectionDetailsSuccess when SectionDetails are fetched',
      build: () => _sectionDetailsCubit,
      act: (bloc) => bloc.fetchSectionDetails(MOCK_SECTION_ID),
      expect: () => [
        SectionDetailsFetched(sectionDetailsData: MOCK_SECTION_DETAILS_INFO)
      ],
    );

    blocTest<SectionDetailsCubit, SectionDetailsState>(
      'emits PageContentsFetched when pagecontent are fetched',
      build: () => _sectionDetailsCubit,
      act: (bloc) => bloc.fetchPageContent(MOCK_INSTANCE),
      expect: () =>
          [PageContentsFetched(pageContentData: MOCK_PAGE_CONTENT_INFO)],
    );

    blocTest<SectionDetailsCubit, SectionDetailsState>(
        'expand section details item',
        build: () => _sectionDetailsCubit,
        act: (bloc) => bloc.expanding(true, 1),
        expect: () => [SectionDetailsExapanding(index: 1, expand: true)]);

    blocTest<SectionDetailsCubit, SectionDetailsState>(
      'emits Course Progress',
      build: () => _sectionDetailsCubit,
      act: (bloc) => bloc.getCourseprogress(MOCK_INSTANCE),
      expect: () => [GetCourseProgressState(progress: MOCK_COURSE_PROGRESS)],
    );

    blocTest<SectionDetailsCubit, SectionDetailsState>(
      'emits Video Resource',
      build: () => _sectionDetailsCubit,
      act: (bloc) => bloc.fetchVideoContent(MOCK_INSTANCE),
      expect: () => [VideoResourceFetched(courseVideo: MOCK_COURSE_VIDEO)],
    );
  });
}
