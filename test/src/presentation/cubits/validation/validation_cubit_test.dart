import 'dart:async';
import 'package:SmartLearn/src/presentation/cubits/validation/validation_cubit.dart';
import 'package:bloc/src/change.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:bloc_test/bloc_test.dart';

class MockValidationCubit extends Mock implements ValidationCubit {
  @override
  String? error;

  @override
  bool isLoading = false;

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    // TODO: implement addError
  }

  @override
  // TODO: implement changeConfirmPassword
  Function(String p1) get changeConfirmPassword => throw UnimplementedError();

  @override
  // TODO: implement changeEmail
  Function(String p1) get changeEmail => throw UnimplementedError();

  @override
  // TODO: implement changeNumber
  Function(String p1) get changeNumber => throw UnimplementedError();

  @override
  // TODO: implement changePassword
  Function(String p1) get changePassword => throw UnimplementedError();

  @override
  void checkSubmitButtonStatus(
      bool allFieldsOccupied,
      String text,
      String firstName,
      String lastName,
      String? emailError,
      String? passwordError,
      String? confirmPasswordError,
      String? phNoError) {
    // TODO: implement checkSubmitButtonStatus
  }

  @override
  Future<void> close() {
    // TODO: implement close
    throw UnimplementedError();
  }

  @override
  // TODO: implement confirmPassword
  Stream<String> get confirmPassword => throw UnimplementedError();

  @override
  // TODO: implement email
  Stream<String> get email => throw UnimplementedError();

  @override
  void emit(ValidationState state) {
    // TODO: implement emit
  }

  @override
  // TODO: implement isClosed
  bool get isClosed => throw UnimplementedError();

  @override
  void onChange(Change<ValidationState> change) {
    // TODO: implement onChange
  }

  @override
  void onError(Object error, StackTrace stackTrace) {
    // TODO: implement onError
  }

  @override
  // TODO: implement password
  Stream<String> get password => throw UnimplementedError();

  @override
  // TODO: implement phoneNum
  Stream<String> get phoneNum => throw UnimplementedError();

  @override
  // TODO: implement state
  ValidationState get state => throw UnimplementedError();

  @override
  // TODO: implement stream
  Stream<ValidationState> get stream => throw UnimplementedError();

  @override
  // TODO: implement validateConfirmPasswordInput
  StreamTransformer<String, String> get validateConfirmPasswordInput =>
      throw UnimplementedError();

  @override
  validateEmail(String email) {
    return '';
  }

  @override
  // TODO: implement validateEmailInput
  StreamTransformer<String, String> get validateEmailInput =>
      throw UnimplementedError();

  @override
  validatePassword(String password, String confirmPassword) {
    // TODO: implement validatePassword
    throw UnimplementedError();
  }

  @override
  // TODO: implement validatePasswordInput
  StreamTransformer<String, String> get validatePasswordInput =>
      throw UnimplementedError();

  @override
  // TODO: implement validatePhoneInput
  StreamTransformer<String, String> get validatePhoneInput =>
      throw UnimplementedError();

  @override
  validatePhoneNumber(String phNum) {
    // TODO: implement validatePhoneNumber
    throw UnimplementedError();
  }
}

void main() {
  testWidgets('validation cubit ...', (tester) async {
    // TODO: Implement test
  });

  late MockValidationCubit _mockValidationCubit;

  setUp(() {
    _mockValidationCubit = MockValidationCubit();
  });

  tearDown(() => _mockValidationCubit.close());

  group('ValidationCubit', () {
    whenListen(_mockValidationCubit, Stream.value(''));

    //TO DO: Complete test
    blocTest(
      'emits [] if email is valid',
      build: () {
        // when(_mockValidationCubit.validateEmail('misha')).thenThrow(EmailValidationFailure(emailError: ''));
        return _mockValidationCubit;
      },
      act: (bloc) {
        // bloc.validateEmail('mishamuraly003');
        _mockValidationCubit.validateEmail('misha');
      },
      // expect: () => [EmailValidationFailure(emailError: 'invalid email')],
      errors: () => [isA<EmailValidationFailure>()],
    );

    // test('emits [] is valid email', () {
    //   final mockValidationCubit = MockValidationCubit();
    //   whenListen(mockValidationCubit, Stream.value(''));
    //   expectLater(mockValidationCubit,
    //       emitsError(EmailValidationFailure(emailError: '')));
    // });

    //   blocTest<ValidationCubit, ValidationState>(
    //   'emits instance of User upon a success registration',
    //   build: () => _mockValidationCubit,
    //   act: (bloc)=> bloc.validateEmail('<EMAIL>'),
    //   expect: () => [],
    // );
  });
}
