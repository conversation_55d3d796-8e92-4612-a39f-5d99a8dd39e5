import 'dart:convert';
import 'dart:io';

import 'package:SmartLearn/src/domain/repositories/organization_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../config/app_config/preference_config.dart';
import '../../../domain/models/privilege_access/privilege_access.dart';
import '../subscription/subscription_cubit.dart';
import '/src/config/app_config/sl_config.dart';
import '/src/domain/services/localizations/sl_strings.dart';
import 'package:supabase/supabase.dart';
import 'package:provider/provider.dart' as provider;

import '../../../domain/services/provider/language_provider.dart';
import '/src/domain/repositories/user_repository.dart';
import 'package:flutter/material.dart';
import '../../../domain/models/profile.dart';
import '../../../utils/constants/boolean.dart';
import '/src/utils/constants/strings.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../domain/models/course_details.dart';
import '../../../domain/repositories/course_respository.dart';

part 'splash_screen_state.dart';

class SplashScreenCubit extends Cubit<SplashScreenState> {
  final CourseRepository _courseRepository;
  final UserRepository _userRepository;
  final OrganizationRepository _orgRepository;

  SplashScreenCubit(
      this._courseRepository, this._userRepository, this._orgRepository)
      : super(const SplashScreenInitial(userId: ''));

  initUserInfo() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    didShowWelcomeScreens =
        _prefs.getBool(PREF_KEY_SHOW_WELCOME_SCREEN) ?? false;

    USER_ID = _prefs.getString(PREF_KEY_USER_ID) ?? '';
    COURSE_ID = _prefs.getString(PREF_KEY_COURSE_ID) ?? '';
    COURSE_NAME = _prefs.getString(PREF_KEY_COURSE_NAME) ?? '';
    TOPIC_ID = _prefs.getString(PREF_KEY_TOPIC_ID) ?? '';
    TOPIC_NAME = _prefs.getString(PREF_KEY_TOPIC_NAME) ?? '';
    ORG_ID = _prefs.getString(PREF_KEY_ORG_ID) ?? '';

    debugPrint(
        '***_prefs.getString("userId"): ${_prefs.getString(PREF_KEY_USER_ID)}');
    debugPrint('***userid: $USER_ID');

    emit(SplashScreenInitial(userId: USER_ID));
  }

  void setLoading(bool isLoading) {
    emit(SplashScreenLoading(isLoading: isLoading, userId: USER_ID));
  }

  /// check user authentication status
  checkTokenAndRefresh() async {
    try {
      final currentUser = await _userRepository.checkTokenAndRefresh();
      if (currentUser == false) {
        // no network
        emit(UserAuthenticated(isAuthenticated: true, userId: USER_ID));
      } else {
        if (currentUser != null) {
          // User is authenticated
          debugPrint('User is authenticated with user ID: ${currentUser.id}');
          emit(
              UserAuthenticated(isAuthenticated: true, userId: currentUser.id));
        } else {
          // User is not authenticated
          String existingUserId = USER_ID;

          debugPrint('User is not authenticated');
          emit(UserAuthenticated(
              isAuthenticated: false, userId: existingUserId));
        }
      }
    } on AuthException catch (e) {
      debugPrint(
          '[SplashScreenCubit][checkTokenAndRefresh][AuthException]: $e');
      emit(const UserAuthenticated(isAuthenticated: false, userId: ''));
    } on Exception catch (e) {
      debugPrint('[SplashScreenCubit][checkTokenAndRefresh][Exception]: $e');
      emit(const UserAuthenticated(isAuthenticated: false, userId: ''));
    }
  }

//change app lanaguage based on the selected language
  initCurrentLanguageStatus(BuildContext context) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    String savedLang =
        _prefs.getString(PREF_KEY_CURRENT_LANG) ?? SLConfig.DEFAULT_LOCALE_EN;

    String languageCode = savedLang;
    if (Platform.localeName.contains(SLConfig.DEFAULT_LOCALE_EN)) {
      languageCode = SLConfig.DEFAULT_LOCALE_EN;
    } else if (Platform.localeName.contains(SLConfig.LOCALE_DE)) {
      languageCode = SLConfig.LOCALE_DE;
    }
    if (languageCode != savedLang) {
      /// device locale changed
      /// different than one set in the app
    }
    _updateAppLocale(context, [Locale(languageCode)]);
  }

  _updateAppLocale(BuildContext context, List<Locale>? locale) {
    provider.Provider.of<LanguageProvider>(context, listen: false)
        .changeCurrentLanguage(SLStrings.getCurrentLangauge(locale));

    emit(SetAppLocale(
        currentLanguage: SLStrings.currentLanguage, userId: USER_ID));
  }

  ///
  /// User details
  ///
  fetchCurrentUserInfo() async {
    if (USER_ID.isNotEmpty) {
      try {
        final userProfileInfo = await _userRepository.getUserInfo(USER_ID);

        emit(ProfileInfoFetched(
          userInfo: userProfileInfo,
          userId: USER_ID,
        ));
      } on Exception catch (e) {
        debugPrint(
            "[Exception][SplashScreenCubit][fetchCurrentUserInfo] :${e.toString()}");
        emit(CourseDetailsError(error: e.toString(), userId: USER_ID));
      }
    }
  }

  ///
  /// Course Details
  ///
  fetchCourseDetails(String courseId) async {
    try {
      String orgId = ORG_ID;
      String userId = USER_ID;
      final courseDetails = await _courseRepository.fetchCourseDetailsInfo(
          courseId, orgId, userId);
      if (courseDetails.isNotEmpty) {
        emit(CourseDetailsFetched(
            courseDetailsData: courseDetails.first, userId: USER_ID));
      } else {
        emit(CourseDetailsEmpty(userId: USER_ID));
      }
    } on Exception catch (e) {
      debugPrint(
          "[Exception][SplashScreenCubit][fetchCourseDetails] :${e.toString()}");
      emit(CourseDetailsError(error: e.toString(), userId: USER_ID));
    }
  }

  /// fetch subscription plans
  getSubscriptionPlansforOrg(BuildContext context) async {
    // if (ORG_ID.isNotEmpty) {
    //   final _subscriptionCubit = BlocProvider.of<SubscriptionCubit>(context);
    //   await _subscriptionCubit.getSubscriptionPlans();
    // }
  }

  /// get privilege access list
  getPrivilegeAccessList() async {
    SharedPreferences _pref = await SharedPreferences.getInstance();
    try {
      if (USER_ID.isNotEmpty && ORG_ID.isNotEmpty) {
        List<PrivilegeAccess> _privilegeAccess =
            await _orgRepository.getUserPrevileges(USER_ID, ORG_ID);
        if (_privilegeAccess.isNotEmpty) {
          _pref.setString(PREF_KEY_PREVILEGE_ACCESS_LIST,
              jsonEncode(_privilegeAccess.first));
        } else {
          debugPrint('Error while fetching privilege access!');
        }
      } else {
        debugPrint('Error while fetching privilege access!');
      }
    } on PostgrestException catch (e) {
      String error = e.details;
      debugPrint(
          '[OrganizationCubit][getPrivilegeAccessList][PostgrestException]: $error');
    } on Exception catch (e) {
      debugPrint(
          '[OrganizationCubit][getPrivilegeAccessList][Exception]: ${e.toString()}');
    }
  }
}
