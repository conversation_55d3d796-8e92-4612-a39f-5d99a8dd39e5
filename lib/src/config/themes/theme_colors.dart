import 'package:flutter/material.dart';

import 'app_theme.dart';

//define app theme colour

class LmsColors {
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color blue = Colors.blue;
  static const Color purple = Colors.indigoAccent;
  static const Color green = Colors.green;
  static const Color pink = Colors.pinkAccent;
  static const Color orange = Colors.orange;
  static const Color primaryBlue = Color.fromRGBO(5, 158, 249, 1.0);
  static const Color appBarGreen = Color.fromARGB(255, 6, 63, 6);
  static const Color secondaryGreen = Color.fromARGB(179, 6, 129, 6);
  static const Color buttonColor = Colors.blue;
  static const Color buttonBgColor = Color.fromARGB(255, 79, 3, 120);
  static const Color grey = Color.fromRGBO(238, 238, 238, 0.981);
  static const Color greyForButton = Color.fromRGBO(116, 112, 112, 0.973);
  static const Color greyForText = Color.fromARGB(203, 238, 238, 238);
  static const Color validationError = Colors.red;
  static const Color transparent = Colors.transparent;
  static Color red = Colors.red.shade300;
  static Color graphBlue = Colors.blue.shade200;
  static const Color goldenColor = Color(0xFFD4AF37);
  static const Color bronzeColor = Color(0xFFCD7F32);
  static Color silverColor = Colors.blue.shade200; // Color(0xFFC0C0C0);
  static const Color ironColor = Color(0xFFa19d94);
  static const Color rankListGrey = Colors.grey;
  static const Color amber = Colors.amber;
  static const Color correct = AppTheme.correctANswerGreen;
  static const Color correctAnswer = AppTheme.correctANswerGreen;
}
