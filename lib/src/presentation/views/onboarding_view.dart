import 'package:auto_route/auto_route.dart';
import 'package:beamer/beamer.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../config/app_config/preference_config.dart';
import '../../config/app_config/sl_config.dart';
import '../../config/router/app_router.dart';
import 'package:introduction_screen/introduction_screen.dart';
import 'package:flutter/foundation.dart';

import '../../config/themes/app_theme.dart';
import '../../config/themes/lms_fonts.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../domain/services/provider/language_provider.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../../utils/constants/nums.dart';
import '../../utils/constants/strings.dart';
import '../widgets/app_bar.dart';
import '../widgets/button_widget.dart';
import 'package:provider/provider.dart' as provider;

@RoutePage()
class OnboardingViewScreen extends HookWidget with WidgetsBindingObserver {
  OnboardingViewScreen({Key? key}) : super(key: key);

  final _introKey = GlobalKey<IntroductionScreenState>();

  late PageDecoration pageDecoration;

  List<PageViewModel> _onBoardingScreens = [];

  ValueNotifier<int>? _selectedIndex;

  ValueNotifier<String> langNotifier =
      ValueNotifier<String>(SLConfig.DEFAULT_LOCALE_EN);

  BuildContext? currentContext;

  _initPageDecoration() {
    pageDecoration = PageDecoration(
      pageColor: AppTheme.whiteColor,
      imageAlignment: Alignment.topCenter,
      imagePadding: const EdgeInsets.only(top: 55),
      titleTextStyle: LMSFonts.onboardingTextStyle(),
    );
  }

  _initOnboardingScreens() {
    _onBoardingScreens = [
      PageViewModel(
          image: imageWidget("$ASSETS_PATH/onboarding1.png"),
          title: SLStrings.getTranslatedString(KEY_LEARN),
          bodyWidget:
              bodyWidget(SLStrings.getTranslatedString(KEY_LEARN_PROFESSIONAL)),
          decoration: pageDecoration),
      PageViewModel(
          image: imageWidget("$ASSETS_PATH/onboarding2.png"),
          title: SLStrings.getTranslatedString(KEY_PRACTICE),
          bodyWidget:
              bodyWidget(SLStrings.getTranslatedString(KEY_PRACTICE_SESSION)),
          decoration: pageDecoration),
      PageViewModel(
          image: imageWidget("$ASSETS_PATH/onboarding3.png"),
          title: SLStrings.getTranslatedString(KEY_STUDY),
          bodyWidget:
              bodyWidget(SLStrings.getTranslatedString(KEY_STUDY_MATERIALS)),
          decoration: pageDecoration),
      PageViewModel(
          image: imageWidget("$ASSETS_PATH/onboarding4.png"),
          title: SLStrings.getTranslatedString(KEY_WATCH),
          bodyWidget:
              bodyWidget(SLStrings.getTranslatedString(KEY_WATCH_FEATURES)),
          decoration: pageDecoration),
      PageViewModel(
          image: imageWidget("$ASSETS_PATH/onboarding5.png"),
          title: SLStrings.getTranslatedString(KEY_CONQUER),
          bodyWidget:
              bodyWidget(SLStrings.getTranslatedString(KEY_UNLOCK_LEVEL)),
          decoration: pageDecoration),
    ];
  }

  @override
  Future<void> didChangeLocales(List<Locale>? locales) async {
    super.didChangeLocales(locales);

    if (locales != null && locales.isNotEmpty) {
      String newLang = locales[0].languageCode;
      if (SLStrings.currentLanguage.contains(newLang)) {
        // language not changed
      } else {
        // language changed
        String _selectedLanguage = await getUpdatedLang(newLang);
        _selectedLanguage = changeLocaleWithoutAlert(
            currentContext, newLang, _selectedLanguage);
        if (currentContext != null && currentContext!.routeData.isActive) {
          langNotifier.value = _selectedLanguage;
          _updateAppLocale(currentContext!, _selectedLanguage);
        }
      }
    }
  }

  _updateAppLocale(BuildContext context, String _selectedLanguage) async {
    _selectedIndex?.value = _selectedIndex?.value ?? 0;
    langNotifier.value = _selectedLanguage;
    provider.Provider.of<LanguageProvider>(context, listen: false)
        .changeCurrentLanguage(_selectedLanguage);
    _initOnboardingScreens();
  }

  @override
  Widget build(BuildContext context) {
    _selectedIndex = useState(0);
    currentContext = context;

    useEffect(() {
      langNotifier = ValueNotifier(SLStrings.currentLanguage);
      WidgetsBinding.instance.addObserver(this);
      _initPageDecoration();
      _initOnboardingScreens();
      return () {
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) {
        if (didPop) {
          return;
        }
      },
      child: Scaffold(
        backgroundColor: kIsWeb ? AppTheme.whiteColor : AppTheme.bgColor,
        appBar: kIsWeb
            ? PreferredSize(
                preferredSize: const Size.fromHeight(0), child: Container())
            : PreferredSize(
                preferredSize: const Size.fromHeight(70),
                child: AppBarWidget(
                  title: _onBoardingScreens[_selectedIndex?.value ?? 0].title ??
                      "",
                  isFromLogin: true,
                  leadingIconName: BACK_ARROW,
                  trailingWidget: Container(),
                  leadingBtnAction: () async {},
                )),
        body: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
          return ValueListenableBuilder(
              valueListenable: langNotifier,
              builder: (context, foo, _) {
                return Align(
                  child: Container(
                    width: constraints.maxWidth < 720 ? double.infinity : 800,
                    padding: const EdgeInsets.fromLTRB(6, 16, 8, 0),
                    decoration: const BoxDecoration(
                      color: AppTheme.whiteColor,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16)),
                    ),
                    child: ScrollConfiguration(
                      behavior: ScrollConfiguration.of(context).copyWith(
                        dragDevices: {
                          PointerDeviceKind.touch,
                          PointerDeviceKind.mouse,
                        },
                      ),
                      child: IntroductionScreen(
                        key: _introKey,
                        globalBackgroundColor: AppTheme.whiteColor,
                        pages: _onBoardingScreens,
                        onDone: () => _onIntroEnd(context),
                        onSkip: () => _onIntroEnd(context),
                        skipOrBackFlex: 0,
                        showSkipButton: true,
                        next: _actionButton(KEY_NEXT, context),
                        nextFlex: 0,
                        skipStyle: TextButton.styleFrom(
                            alignment: Alignment.centerLeft),
                        nextStyle: TextButton.styleFrom(
                            alignment: Alignment.centerRight),
                        skip: kIsWeb
                            ? _skipButton(context)
                            : Text(
                                SLStrings.getTranslatedString(KEY_SKIP),
                                style: LMSFonts.onboardingContentTextStyle(),
                              ),
                        done: _actionButton(KEY_DONE, context),
                        curve: Curves.fastLinearToSlowEaseIn,
                        controlsPadding: kIsWeb
                            ? const EdgeInsets.all(12.0)
                            : const EdgeInsets.fromLTRB(0.0, 4.0, 0.0, 4.0),
                        dotsDecorator: const DotsDecorator(
                          size: Size(12.0, 12.0),
                          color: AppTheme.carousalUnSelectedDotColor,
                          activeColor: AppTheme.onBoardingSelectedIndex,
                          activeSize: Size(12.0, 12.0),
                          activeShape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(25.0)),
                          ),
                        ),
                        onChange: (index) {
                          _selectedIndex?.value = index;
                        },
                      ),
                    ),
                  ),
                );
              });
        }),
      ),
    );
  }

  Widget imageWidget(String imageName) {
    return Image.asset(
      imageName,
      height: 225,
      width: 225,
    );
  }

  Widget bodyWidget(String content) {
    return Text(
      content,
      style: LMSFonts.onboardingContentTextStyle(),
      textAlign: TextAlign.center,
    );
  }

  Widget _skipButton(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: ButtonWidget(
          minimumSize: const Size(10, 10),
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(buttonRadius),
              side: const BorderSide(
                color: AppTheme.examInfoTextColor,
              )),
          color: AppTheme.whiteColor,
          child: Text(SLStrings.getTranslatedString(KEY_SKIP),
              style: LMSFonts.regularFont(14, AppTheme.examIntroTextColor)),
          onPressed: () async {
            _onIntroEnd(context);
          }),
    );
  }

  Widget _actionButton(String action, BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius:
            BorderRadiusDirectional.vertical(top: Radius.circular(16)),
        color: Colors.white,
      ),
      child: Align(
        alignment:
            action == KEY_NEXT ? Alignment.centerRight : Alignment.centerLeft,
        child: ButtonWidget(
            minimumSize: const Size(10, 10),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(buttonRadius),
                side: const BorderSide(
                  color: AppTheme.examInfoTextColor,
                )),
            color: action == KEY_NEXT
                ? AppTheme.whiteTextColor
                : AppTheme.examInfoTextColor,
            child: Text(SLStrings.getTranslatedString(action),
                style: LMSFonts.regularFont(
                    14,
                    action == KEY_NEXT
                        ? AppTheme.examInfoTextColor
                        : AppTheme.whiteColor)),
            onPressed: () async {
              action == KEY_NEXT
                  ? _introKey.currentState?.next()
                  : _onIntroEnd(context);
            }),
      ),
    );
  }

  Future<void> _onIntroEnd(context) async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    _prefs.setBool(PREF_KEY_SHOW_WELCOME_SCREEN, true);
    _checkNavigationStatus(context);
  }

  _checkNavigationStatus(BuildContext context) {
    if (USER_ID.isNotEmpty) {
      if (COURSE_ID.isNotEmpty) {
        if (!kIsWeb) {
          appRouter.push(CourseDashboardRoute(isFromLogin: false));
        } else {
          Beamer.of(context).beamToReplacementNamed('/course-details',
              data: {"courseId": COURSE_ID, "courseName": COURSE_NAME});
        }
      } else {
        if (!kIsWeb) {
          appRouter.push(TopicListViewRoute());
        } else {
          Beamer.of(context).beamToReplacementNamed('/topic-view');
        }
      }
    } else {
      if (!kIsWeb) {
        appRouter.replace(LoginEmailViewRoute(isTokenExpired: false));
      } else {
        Beamer.of(context).beamToReplacementNamed(
          '/login-view',
          data: {'isTokenExpired': false},
        );
      }
    }
  }
}
