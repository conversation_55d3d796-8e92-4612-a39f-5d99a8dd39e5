import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../config/app_config/preference_config.dart';
import '../../../domain/models/topic.dart';
import '../../../domain/repositories/topic_repository.dart';

part 'topic_list_state.dart';

class TopicListCubit extends Cubit<TopicListState> {
  final TopicRepository _topicRepository;

  TopicListCubit(this._topicRepository) : super(const TopicListInitial());

  String orgId = '';
  bool loading = false;

  Future<void> updateItemColor(String text) async {
    if (text.isNotEmpty) {
      emit(const TopicListItemColour(color: true));
    } else {
      emit(const TopicListItemColour(color: false));
    }
  }

  setLoading(bool loadStatus) {
    loading = loadStatus;
    emit(TopicListLoading(isDataLoading: loadStatus));
  }

  Future<void> getUserTopic() async {
    try {
      SharedPreferences _prefs = await SharedPreferences.getInstance();
      orgId = _prefs.getString(PREF_KEY_ORG_ID) ?? '';
      if (orgId.isNotEmpty) {
        emit(await _getUserTopic(orgId));
      }
    } on AuthException catch (e) {
      debugPrint('[TopicListCubit][getUserTopic][AuthException]: $e');
    } on Exception catch (e) {
      debugPrint('[TopicListCubit][getUserTopic][Exception]: $e');
    }
  }

  Future<TopicListState> _getUserTopic(String orgId) async {
    List<Map<String, dynamic>> processedList = [];

    final topicList = await _topicRepository.getTopicList(orgId);

    List<TopicList> firstTopicList = [];
    List<TopicList> secondTopicList = [];
    for (int i = 0; i < topicList.length; i++) {
      if (topicList[i].parent_id == null) {
        firstTopicList.add(topicList[i]);
      }
    }
    for (int i = 0; i < topicList.length; i++) {
      if (topicList[i].parent_id != null) {
        secondTopicList.add(topicList[i]);
      }
    }

    firstTopicList.forEach((item) {
      final parentItem = item;
      final childData = secondTopicList
          .where((childitem) => childitem.parent_id == item.id)
          .toList();

      List<Map<String, dynamic>> myObjectMaps = childData
          .map((obj) => {
                'id': obj.id,
                'name': obj.name.toString().trim(),
                'description': obj.description,
              })
          .toList();

      processedList.add({
        'Parent': {
          'id': parentItem.id,
          'name': parentItem.name.toString().trim(),
          'description': parentItem.description,
        },
        'Children': myObjectMaps
      });
    });

    processedList.sort((a, b) => a['Parent']['name']
        .toString()
        .toLowerCase()
        .compareTo(b['Parent']['name'].toString().toLowerCase()));

    return TopicListSuccess(topicList: processedList);
  }

  Future<void> expanding(bool value, int index) async {
    if (value == true) {
      emit(TopicListExapanding(index: index, expand: true));
    } else {
      emit(TopicListExapanding(index: index, expand: false));
    }
  }
}
