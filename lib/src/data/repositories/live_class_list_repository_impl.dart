import 'dart:convert';
import 'dart:developer';

import '/src/config/app_config/api_config.dart';
import '/src/domain/models/live_class_model.dart';
import '/src/domain/repositories/live_class_list_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class LiveClassListRepositoryImpl implements LiveListRepository {
  @override
  Future<List<LiveClassModel>> fetchLiveClassList(
      Map<String, dynamic> jsonReq) async {
    List<LiveClassModel> allLiveInfo = [];

    final response = await Supabase.instance.client
        .rpc(APIConfig.GET_LIVE_LIST, params: jsonReq);

    List<dynamic> responseData = response ?? [];

    allLiveInfo = List<LiveClassModel>.from(
        responseData.map((x) => LiveClassModel.fromJson(x))).toList();
    final filteredLiveClasses = allLiveInfo
        .where((liveClass) =>
            liveClass.meetingId != null &&
            liveClass.currentMeetingStatus != CurrentMeetingStatus.ENDED)
        .toList();
    return filteredLiveClasses;
  }
}
