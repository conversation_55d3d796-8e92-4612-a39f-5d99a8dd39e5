// ignore_for_file: must_be_immutable

import 'dart:async';
import 'package:flutter/gestures.dart';

import '../../utils/helper/privilege_access_mapper.dart';
import '/src/presentation/widgets/alert_popup/confirmation_popup.dart';
import 'package:beamer/beamer.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/foundation.dart';

import '../../config/enums/alert_types.dart';
import '../../domain/models/hive_db_models/hive_video_player.dart';
import '../../domain/services/localizations/sl_strings.dart';
import '../../domain/services/localizations/string_keys.dart';
import '../../utils/constants/locale_change_helper.dart';
import '../widgets/app_bar.dart';
import '../widgets/profile_placeholder_widget.dart';
import '/src/config/enums/checkpoint_type.dart';
import 'package:auto_route/auto_route.dart';

import '/src/presentation/widgets/loading_indicator.dart';

import '/src/domain/models/milestone.dart';
import '/src/domain/models/milestone_data.dart';
import '/src/domain/models/question_data.dart';
import '/src/domain/models/videoplayer_data.dart';
import '/src/presentation/cubits/exam/exam_cubit.dart';
import 'package:timer_count_down/timer_controller.dart';

import '../../domain/models/check_point_data/check_point_data.dart';
import '../../domain/models/course_progress.dart';
import '../../domain/models/resource_comment.dart';
import '../../utils/constants/nums.dart';
import '../../utils/helper/thumbnail_generator.dart';
import '../widgets/alert_popup/multi_action_dialogue.dart';
import '../../domain/models/sl_user.dart';
import '/src/presentation/cubits/connectivity/connectivity_state.dart';
import '/src/presentation/widgets/textfield_widget.dart';
import '/src/utils/constants/lists.dart';
import '/src/utils/helper/app_date_formatter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import '../../config/themes/app_theme.dart';
import '../cubits/connectivity/connectivity_cubit.dart';
import '../widgets/alert_popup/single_action_dialogue.dart';
import '../widgets/button_widget.dart';
import '../widgets/connectivity_widget.dart';
import '/src/config/router/app_router.dart';
import '/src/config/themes/lms_fonts.dart';
import '/src/config/themes/theme_colors.dart';
import '/src/domain/models/course_details.dart';
import '/src/presentation/cubits/course_list/course_list_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:youtube_player_iframe_plus/youtube_player_iframe_plus.dart'
    as IFrame;
import 'package:universal_html/js.dart' as js;

import '../../utils/constants/strings.dart';

@RoutePage()
class CourseVideoViewScreen extends HookWidget with WidgetsBindingObserver {
  final CourseVideo courseVideo;
  final String? courseId;
  final CourseProgress? progess;
  final bool isFromSectionDetails;
  final List<CheckPoint> checkPoints;
  final bool isFromExamScreen;
  final bool isExamPassed;

  CourseVideoViewScreen(
    this.courseVideo, {
    super.key,
    this.courseId = "",
    this.isFromSectionDetails = false,
    required this.progess,
    this.checkPoints = const [],
    this.isFromExamScreen = false,
    this.isExamPassed = false,
  });

  YoutubePlayerController? _youtubePlayerController;
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  IFrame.YoutubePlayerController? _webPlayerController;

  ValueNotifier<int> counter = ValueNotifier<int>(0);
  ValueNotifier<List<String>> selectedAnswerIds =
      ValueNotifier<List<String>>([]);
  ValueNotifier<int> selectedQuestionIndex = ValueNotifier<int>(0);
  ValueNotifier<bool> isPlayerReady = ValueNotifier<bool>(false);
  ValueNotifier<bool> readMore = ValueNotifier<bool>(false);

  final TextEditingController _promptController = TextEditingController();
  final countdownController = CountdownController(autoStart: true);
  final _commentListController = ScrollController();

  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();

  final _formKey = GlobalKey<FormState>();
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  List<ResourseComment> _comments = [];
  final List<CheckPoint> _checkPoints = [];
  final List<Milestone> _milestoneDurations = [];
  final List<CheckPoint> _completedCheckPoints = [];
  List<Question> _questions = [];

  List<Positioned> stackList = [];

  List<int> selectedAnswerIndexes = [];
  List<int> questionIndex = [];

  QuestionData? _questionData;
  CourseListCubit? _courseCubitObj;
  Timer? _timer;

  double _currentDuration = 0.0;

  bool _fullScreenView = false;
  bool _isYoutubeVideo = true;
  bool didSaveProgress = false;
  bool _didSelectBtn = false;
  bool isExamLoading = false;
  bool isDataLoading = false;

  String videoURL = '';
  String commentType = resourseCommentType[0];

  int lastWatchTime = 0;

  ValueNotifier<bool> isPlayerClosed = ValueNotifier<bool>(false);
  ValueNotifier<bool> enableCommentSubmitBtn = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _enableSaveProgressBtn = ValueNotifier<bool>(false);

  final double _horizontalMargin = 16.0;

  BuildContext? currentContext;

  late ValueNotifier<bool> _hasAccessToAddComments;
  late ValueNotifier<bool> _hasAccessToSaveProgress;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // This method is called when the app's lifecycle state changes.
    // You can respond to the different lifecycle states here.
    switch (state) {
      case AppLifecycleState.resumed:
        // The app is in the foreground.
        break;
      case AppLifecycleState.inactive:
        // The app is in an inactive state (e.g., switching apps).
        break;
      case AppLifecycleState.paused:
        // The app is in the background.
        _pauseOrDisposeVideo(pauseVideo: true);
        break;
      case AppLifecycleState.detached:
        // The app is detached (not running).
        break;
      case AppLifecycleState.hidden:
        // All views of an application are hidden.
        break;
    }
  }

  _pauseOrDisposeVideo({required bool pauseVideo}) {
    if (_chewieController != null) {
      pauseVideo ? _chewieController?.pause() : _chewieController!.dispose();
    }
    if (_youtubePlayerController != null) {
      pauseVideo
          ? _youtubePlayerController!.pause()
          : _youtubePlayerController!.dispose();
    }
    if (_videoPlayerController != null) {
      pauseVideo
          ? _videoPlayerController!.pause()
          : _videoPlayerController!.dispose();
    }
  }

  @override
  void didChangeLocales(List<Locale>? locales) {
    super.didChangeLocales(locales);

    // Here locales is a list of all the locales enabled on the device.
    // Like: [Locale('en_US'), Locale('ar_SA')]

    // The first locale is the phone's main locale, but in reality you should
    // traverse until you find a supported locale.

    _pauseOrDisposeVideo(pauseVideo: true);
    // TO DO: context should not be equal to null
    didChangeAppLocale(locales, currentContext);
  }

  _initCheckpointInfo(CourseListCubit _courseCubit) async {
    if (courseVideo.isCheckPointEnabled && checkPoints.isNotEmpty) {
      _checkPoints.addAll(checkPoints);
      _sortCheckpoints(_checkPoints);
      _sortCheckpoints(checkPoints);
    }
    _courseCubitObj = _courseCubit;
    if (isFromExamScreen) {
      _manageCheckPoints(_courseCubit);
    } else {
      _getLatsWatchTime(_courseCubit);
    }
  }

  Future<bool> _checkAccessToGetCheckPoints() async {
    // decides whether to fetch checkpoints
    String screen = PrivilegeAccessConsts.SCREEN_CHECKPOINT;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_CHECK_POINT;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _checkAccessPrivilege() async {
    // decides whether to disable/enable comment  button
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_ADD_COMMENT;
    _hasAccessToAddComments.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);

    // decides whether to disable/enable save progress
    screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    accessRequiredFeature = PrivilegeAccessConsts.ACTION_UPDATE_COURSE_PROGRESS;
    _hasAccessToSaveProgress.value =
        await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
            accessRequiredFeature: accessRequiredFeature);
  }

  Future<bool> _checkAccessToGetComments() async {
    // decides whether to fetch comments
    String screen = PrivilegeAccessConsts.SCREEN_COURSE_RESOURCE;
    String accessRequiredFeature = PrivilegeAccessConsts.ACTION_GET_COMMENT;
    return await PrivilegeAccessMapper.checkPrivilegeAccessFor(screen,
        accessRequiredFeature: accessRequiredFeature);
  }

  _initWebYoutubePlayer(BuildContext context, CourseListCubit cubit) {
    List<CheckPoint> mileStones = [];
    mileStones.addAll(checkPoints ?? []);
    String vedioId = extractVideoId(courseVideo.videoURL);
    _webPlayerController = IFrame.YoutubePlayerController(
      initialVideoId: vedioId,
      params: IFrame.YoutubePlayerParams(
        showControls: checkPoints.isEmpty,
        enableKeyboard: checkPoints.isEmpty,
        startAt: Duration(
          seconds: lastWatchTime,
        ),
      ),
    )..listen((event) {
        // Temporary list to check milestone is completed or not

        _currentDuration =
            ((_webPlayerController?.value.position.inSeconds) ?? 0).toDouble();

        if (_webPlayerController?.value.isReady == true) {
          isPlayerReady.value = true;
        }

        // _webPlayerController
        //     ?.seekTo(Duration(seconds: _currentDuration.toInt()));

        final mileStoneContains = _checkPoints.any((element) {
          int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
          return startTimeInSec ==
              _webPlayerController!.value.position.inSeconds;
        });
        if (mileStoneContains) {
          _webPlayerController?.pause();

          final mileStoneRemoveIndex = _checkPoints.indexWhere((element) {
            int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
            return startTimeInSec ==
                _webPlayerController!.value.position.inSeconds;
          });
          _checkPoints.remove(_checkPoints[mileStoneRemoveIndex]);
          final mileStoneIndex = mileStones.indexWhere((element) {
            int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
            return startTimeInSec ==
                _webPlayerController!.value.position.inSeconds;
          });
          _courseCubitObj?.updatelastWatchTime(
              _webPlayerController?.value.position.inSeconds ?? 0,
              mileStoneIndex);
          _showCheckPointInfoPopup(context, mileStones[mileStoneIndex]);
        }
        cubit.enableSaveProgressBtn(false);
        if (_currentDuration >= 20.0 &&
            (isFromSectionDetails || isFromExamScreen)) {
          cubit.enableSaveProgressBtn(true);
        }

        final videoCompleted = _webPlayerController!.value.position.inSeconds ==
            _webPlayerController!.metadata.duration.inSeconds - 4;

        if (videoCompleted) {
          _webPlayerController?.seekTo(const Duration(seconds: 1));
          _webPlayerController?.pause();
          disableClickOnPlayer();
          _showVideoCompletePopup(context);
        }
      });
  }

  ///
  /// initialize video players
  ///

  _initPlayer(CourseListCubit cubit, BuildContext context) {
    videoURL = courseVideo.videoURL;

    try {
      bool _isYouTubeVideo =
          videoURL != '' && checkForValidYoutubeUrls(videoURL);
      if (kIsWeb) {
        _initWebYoutubePlayer(context, cubit);
      } else {
        if (_isYouTubeVideo) {
          ///Youtube video
          ///use youtube player
          ///
          _initYoutubePlayer(cubit, context);
        } else {
          ///Supabase Hosted Video
          ///use video player
          ///
          _initChewieVideoPlayer(cubit);
        }
      }
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  _initYoutubePlayer(CourseListCubit cubit, BuildContext context) {
    try {
      _isYoutubeVideo = true;
      String videoId = extractVideoId(videoURL);
      int startDuration = 0;
      if (courseVideo.totalDuration != 0) {
        startDuration =
            ((courseVideo.progress * (courseVideo.totalDuration * 60)) / 60)
                .round();
      }
      _youtubePlayerController = YoutubePlayerController(
        initialVideoId: videoId,
        flags: YoutubePlayerFlags(
          useHybridComposition: false,
          enableCaption: false,
          startAt: lastWatchTime,
          disableDragSeek:
              courseVideo.isCheckPointEnabled && checkPoints.isNotEmpty
                  ? true
                  : false,
        ),
      );

      // Temporary list to check milestone is completed or not
      List<CheckPoint> mileStones = [];
      mileStones.addAll(checkPoints);
      _youtubePlayerController?.addListener(() {
        final mileStoneContains = _checkPoints.any((element) {
          int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
          return startTimeInSec ==
              _youtubePlayerController!.value.position.inSeconds;
        });
        if (mileStoneContains) {
          isPlayerReady.value = false;
          _youtubePlayerController?.pause();

          final mileStoneRemoveIndex = _checkPoints.indexWhere((element) {
            int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
            return startTimeInSec ==
                _youtubePlayerController!.value.position.inSeconds;
          });
          _checkPoints.remove(_checkPoints[mileStoneRemoveIndex]);
          final mileStoneIndex = mileStones.indexWhere((element) {
            int startTimeInSec = convertCheckPointTimetoSec(element.startTime);
            return startTimeInSec ==
                _youtubePlayerController!.value.position.inSeconds;
          });
          _courseCubitObj?.updatelastWatchTime(
              _youtubePlayerController?.value.position.inSeconds ?? 0,
              mileStoneIndex);
          _showCheckPointInfoPopup(context, mileStones[mileStoneIndex]);
        }

        cubit.enableSaveProgressBtn(false);
        _currentDuration =
            ((_youtubePlayerController?.value.position.inSeconds) ?? 0)
                .toDouble();
        if (_currentDuration >= 20.0 && isFromSectionDetails) {
          cubit.enableSaveProgressBtn(true);
        }
      });
    } on Exception catch (e) {
      debugPrint('[course video view][_initYoutubePlayer]: $e');
      _handlePlayerError();
      cubit.handleVideoPlayerError();
    }
  }

  _initChewieVideoPlayer(CourseListCubit cubit) async {
    _isYoutubeVideo = false;
    await initializeVideoPlayer(cubit);
  }

  Future<void> initializeVideoPlayer(CourseListCubit cubit) async {
    try {
      Uri url = Uri.parse(videoURL);

      _videoPlayerController = VideoPlayerController.networkUrl(url);
      await _videoPlayerController?.initialize();
      cubit.videoCallback(false);
      await _createChewieController(_videoPlayerController, cubit);
      await Future.wait([]);
    } on Exception catch (e) {
      debugPrint('[course video view][initializePlayer]: $e');
      _handlePlayerError();
      cubit.handleVideoPlayerError();
    }
  }

  _handlePlayerError() {
    if (currentContext != null) {
      disableClickOnPlayer();

      _showVideoPlayerErrorPopup(currentContext!);
    }
  }

  Future<void> _createChewieController(
      _videoPlayerController, CourseListCubit cubit) async {
    int startDuration = 0;
    if (courseVideo.totalDuration != 0) {
      startDuration =
          ((courseVideo.progress * (courseVideo.totalDuration * 60)) / 60)
              .round();
    }
    _chewieController = ChewieController(
      errorBuilder: (context, errorMessage) {
        debugPrint(
            '[CourseVideoScreen][_createChewieController][errorBuilder]: $errorMessage');
        return Text(errorMessage);
      },
      videoPlayerController: _videoPlayerController,
      autoPlay: true,
      showOptions: false,
      startAt: Duration(seconds: startDuration),
      controlsSafeAreaMinimum: const EdgeInsets.all(8),
      cupertinoProgressColors: ChewieProgressColors(
          backgroundColor: LmsColors.white,
          playedColor: AppTheme.primaryBlue,
          handleColor: AppTheme.primaryBlue,
          bufferedColor: LmsColors.white),
      customControls: const CupertinoControls(
          backgroundColor: Colors.black, iconColor: Colors.white),
      subtitleBuilder: (context, dynamic subtitle) => Container(
        padding: const EdgeInsets.all(10.0),
        child: subtitle is InlineSpan
            ? RichText(
                text: subtitle,
              )
            : Text(
                subtitle.toString(),
                style: LMSFonts.regularFont(14, AppTheme.commentBoxContent),
              ),
      ),
      hideControlsTimer: const Duration(seconds: 5),
    );
    _chewieController?.play();

    cubit.enableSaveProgressBtn(false);

    _currentDuration =
        ((_videoPlayerController?.value.position.inSeconds) ?? 0).toDouble();
    if (_currentDuration >= 20.0 && isFromSectionDetails) {
      cubit.enableSaveProgressBtn(true);
    }
  }

  Future<bool> _changePlayerStatus() async {
    if (!_fullScreenView) {
      isPlayerClosed.value = true;
    } else {
      _youtubePlayerController?.toggleFullScreenMode();
    }
    return isPlayerClosed.value;
  }

  @override
  Widget build(BuildContext context) {
    currentContext = context;
    final _courseCubit = BlocProvider.of<CourseListCubit>(context);
    Size screenSize = MediaQuery.of(context).size;
    _hasAccessToAddComments = useState<bool>(false);
    _hasAccessToSaveProgress = useState<bool>(false);

    useEffect(() {
      WidgetsBinding.instance.addObserver(this);
      _courseCubit.initCourseCubit();
      _courseCubitObj?.setQuestionLoading(true);
      _initCheckpointInfo(_courseCubit);
      _courseCubitObj?.setQuestionLoading(false);
      if (kIsWeb) {
        _initWebYoutubePlayer(context, _courseCubit);
      } else {
        _initPlayer(_courseCubit, context);
      }

      Future<void>.microtask(() async {
        await _checkAccessPrivilege();
        if (courseVideo.instanceId.isNotEmpty) {
          await _fetchComments(context, _courseCubit);
        }
      });

      _commentController.addListener(() {
        enableCommentSubmitBtn.value = _commentController.text.isNotEmpty;
      });
      return () {
        _commentController.dispose();
        _commentFocusNode.dispose();
        _pauseOrDisposeVideo(pauseVideo: false);
        // Save video player data to SQLite when the user exits the video screen
        if (!kIsWeb) {
          _courseCubit.insertToVideoData(VideoPlayerData(
            videoId: courseVideo.videoId,
            startTime: DateTime.now().millisecondsSinceEpoch,
            endTime: DateTime.now().millisecondsSinceEpoch,
            milestonesCompleted: _completedCheckPoints.length,
            lastWatchTime:
                _youtubePlayerController?.value.position.inSeconds ?? 0,
            milestoneDurations: _completedCheckPoints.toString(),
          ));
        } else {
          final videoData = HiveVideoPlayerData(
              videoId: courseVideo.videoId,
              startTime: DateTime.now().millisecondsSinceEpoch,
              endTime: DateTime.now().millisecondsSinceEpoch,
              mileStonesCompleted: _completedCheckPoints.length,
              lastWatchTime:
                  _webPlayerController?.value.position.inSeconds ?? 0,
              mileStoneDurations: _completedCheckPoints.toString());
          _courseCubit.insertToHiveVideoData(videoData);
        }
        WidgetsBinding.instance.removeObserver(this);
      };
    }, []);

    return BlocBuilder<CourseListCubit, CourseListState>(
      builder: (context1, state) {
        if (state is CourseVideoStatus) {
          _fullScreenView = state.isFullSCreen;
        } else if (state is CourseCommentsFetched) {
          isDataLoading = false;
          _comments = state.comments;
        } else if (state is CommentsAdded) {
          isDataLoading = false;
          _comments = state.comments;
        }
        if (state is CommentTypeSelected) {
          commentType = state.commentType;
        }
        if (state is CourseListButtonEnable) {
          _enableSaveProgressBtn.value = state.enable;
        }
        if (state is SetQuestionLoading) {
          isExamLoading = state.status;
        }
        if (state is CourseDetailsLoading) {
          isDataLoading = state.isDataLoading;
        }
        if (state is CommentsError) {
          isDataLoading = false;
        }

        return PopScope(
          canPop: !isExamLoading,
          onPopInvoked: (bool didPop) {
            if (didPop) {
              _changePlayerStatus();
            } else {
              return;
            }
          },
          child: GestureDetector(
            onTap: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus) {
                currentFocus.unfocus();
              }
            },
            child: Material(
              child: Stack(
                children: [
                  Container(
                      child: kIsWeb
                          ? _mainContainer(
                              context1,
                              _courseCubit,
                              state,
                              screenSize,
                              videoPlayerWidget: IFrame.YoutubePlayerIFramePlus(
                                aspectRatio: 16 / 7,
                                controller: _webPlayerController,
                              ),
                            )
                          : _isYoutubeVideo
                              ? _youtubeWidget(
                                  context, state, _courseCubit, screenSize)
                              : _chewiePlayerWidget(
                                  context, _courseCubit, state, screenSize)),
                  // _textFieldWidget(screenSize),

                  _fullScreenView
                      ? Container()
                      : _hasAccessToAddComments.value
                          ? _commentBtn(context, _courseCubit)
                          : Container(),
                  SafeArea(child: ConnectivityStatusWidget()),
                  isExamLoading || isDataLoading
                      ? LoadingIndicatorClass()
                      : Container(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _youtubeWidget(BuildContext context, CourseListState state,
      CourseListCubit _courseCubit, Size screenSize) {
    // (10*2)--> left right margin
    // (8*2)--> actions padding from YoutubePlayer
    // (16 * 2)--> horizontal margin given to screen
    // 10--> sizedbox width
    final double bottomActionWidth = _fullScreenView
        ? screenSize.width - ((10 * 2) + (8 * 2))
        : screenSize.width - ((10 * 2) + (8 * 2) + (16 * 2));
    return YoutubePlayerBuilder(
        player: YoutubePlayer(
          bottomActions:
              courseVideo.isCheckPointEnabled && checkPoints.isNotEmpty
                  ? [
                      Align(
                        child: Container(
                          width: bottomActionWidth,
                          margin: const EdgeInsets.only(left: 10, right: 10),
                          decoration: BoxDecoration(
                            color: AppTheme.border,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            children: [
                              SizedBox(width: _fullScreenView ? 30 : 10),
                              CurrentPosition(),
                              const SizedBox(width: 10),
                              Expanded(
                                child: AbsorbPointer(
                                  child: Stack(
                                    children: [
                                      ProgressBar(
                                        controller: _youtubePlayerController,
                                        // isExpanded: true,
                                        colors: const ProgressBarColors(
                                          backgroundColor: AppTheme.whiteColor,
                                          playedColor: AppTheme.bgColor,
                                          handleColor: AppTheme.bgColor,
                                        ),
                                      ),
                                      // TODO : checkPoint Marks
                                      // ...stackList
                                    ],
                                  ),
                                ),
                              ),
                              _rewindButton(),
                              RemainingDuration(),
                              FullScreenButton(),
                            ],
                          ),
                        ),
                      ),
                    ]
                  : null,
          controller: _youtubePlayerController!,
          showVideoProgressIndicator: true,
          progressColors: const ProgressBarColors(
              backgroundColor: Colors.white,
              playedColor: AppTheme.primaryBlue,
              handleColor: AppTheme.primaryBlue,
              bufferedColor: Colors.white),
          onEnded: (metaData) {
            _youtubePlayerController?.seekTo(Duration.zero);
            _youtubePlayerController?.pause();
            _courseCubit.insertToVideoData(VideoPlayerData(
              videoId: courseVideo.videoId,
              startTime: DateTime.now().millisecondsSinceEpoch,
              endTime: DateTime.now().millisecondsSinceEpoch,
              milestonesCompleted: _completedCheckPoints.length,
              lastWatchTime: 0,
              milestoneDurations: _completedCheckPoints.toString(),
            ));
          },
          onReady: () async {
            // Setting the last watched time in the YouTube player to resume the video
            _youtubePlayerController?.seekTo(Duration(seconds: lastWatchTime));
            isPlayerReady.value = true;
            // await FirebaseAnalytics.instance.logEvent(
            //   name: "video_session_started",
            //   parameters: {
            //     "content_type": "video",
            //     "item_id": 01,
            //     "start_at": DateTime.now().toString(),
            //   },
            // );
          },
        ),
        onEnterFullScreen: () =>
            context.read<CourseListCubit>().videoCallback(true),
        onExitFullScreen: () =>
            context.read<CourseListCubit>().videoCallback(false),
        builder: (context, player) {
          Widget _youtubePlayerWidget = ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(16)),
            child: ValueListenableBuilder(
                valueListenable: isPlayerClosed,
                builder: (context, isPlayer, _) {
                  return isPlayer
                      ? const SizedBox()
                      : _youtubePlayerController != null
                          ? player
                          : _thumbnailPlaceHolder(context);
                }),
          );
          return _mainContainer(context, _courseCubit, state, screenSize,
              videoPlayerWidget: _youtubePlayerWidget);
        });
  }

  Widget _rewindButton() {
    return IconButton(
      onPressed: () {
        if (kIsWeb) {
          if (_webPlayerController?.value.isReady == true &&
              _webPlayerController!.value.position.inSeconds > 5) {
            _webPlayerController?.seekTo(Duration(
                seconds: _webPlayerController!.value.position.inSeconds - 5));
          }
        } else {
          if (_youtubePlayerController?.value.isReady == true &&
              _youtubePlayerController!.value.position.inSeconds > 5) {
            _youtubePlayerController?.seekTo(Duration(
                seconds:
                    _youtubePlayerController!.value.position.inSeconds - 5));
          }
        }
      },
      icon: const Icon(
        Icons.fast_rewind,
        color: AppTheme.whiteColor,
      ),
    );
  }

  Widget _mainContainer(BuildContext context, CourseListCubit _courseCubit,
      CourseListState state, Size screenSize,
      {required Widget videoPlayerWidget}) {
    return Scaffold(
      key: _scaffoldKey,
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color.fromRGBO(0, 199, 214, 1),
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(70),
          child: AppBarWidget(
            title: SLStrings.getTranslatedString(KEY_VIDEO),
            trailingWidget: Container(),
            leadingIconName: BACK_ARROW,
            leadingBtnAction: () => _onBackTapped(state, context),
          )),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16), topRight: Radius.circular(16)),
        ),
        clipBehavior: Clip.antiAlias,
        child: LayoutBuilder(builder: (context, constraints) {
          return Align(
            child: SizedBox(
              width: constraints.maxWidth < 720 ? double.infinity : 800,
              child: Stack(
                children: [
                  _customScrollWidget(videoPlayerWidget, context, constraints),
                  _hasAccessToSaveProgress.value
                      ? _saveProgressBtn(
                          context, _courseCubit, state, screenSize)
                      : Container(),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget webPlayerWidget(Widget videoPlayerWidget, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 16),
      child: Column(
        children: [
          ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: videoPlayerWidget),
          const SizedBox(height: 5),
          courseVideo.isCheckPointEnabled && kIsWeb && checkPoints.isNotEmpty
              ? webPlayerCustomControls(context)
              : Container(),
          _videoTitle(),
          const SizedBox(height: 1),
          _videoDesc(context),
          Expanded(
            child: Scrollbar(
              controller: _commentListController,
              thumbVisibility: true,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: ScrollConfiguration(
                  behavior: ScrollConfiguration.of(context)
                      .copyWith(scrollbars: false, dragDevices: {
                    PointerDeviceKind.touch,
                    PointerDeviceKind.mouse,
                  }),
                  child: ListView(
                    shrinkWrap: true,
                    controller: _commentListController,
                    children: [
                      _comments.isNotEmpty
                          ? const Divider(
                              thickness: 1,
                              height: 1,
                              color: AppTheme.dividerColorInReview,
                            )
                          : Container(),
                      SizedBox(height: _comments.isEmpty ? 0 : 15),
                      _commentsHead(),
                      SizedBox(height: _comments.isEmpty ? 0 : 15),
                      _chatListView(),
                      const SizedBox(height: 60),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _customScrollWidget(Widget videoPlayerWidget, BuildContext context,
      BoxConstraints constraints) {
    final courseCubit = BlocProvider.of<CourseListCubit>(context);
    double width = MediaQuery.sizeOf(context).width;
    return Positioned(
      top: _enableSaveProgressBtn.value == true || progess?.markedAsDone == true
          ? 50
          : 20,
      bottom: 0,
      right: 0,
      left: 0,
      child: kIsWeb
          ? webPlayerWidget(videoPlayerWidget, context)
          : CustomScrollView(
              primary: true,
              slivers: [
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Container(
                    margin: EdgeInsets.symmetric(
                        horizontal: _horizontalMargin,
                        vertical: _horizontalMargin),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        videoPlayerWidget,
                        const SizedBox(height: 16),
                        courseVideo.isCheckPointEnabled && kIsWeb
                            ? webPlayerCustomControls(context)
                            : Container(),
                        _videoTitle(),
                        const SizedBox(height: 1),
                        _videoDesc(context),
                        SizedBox(height: _comments.isEmpty ? 50 : 10),
                        _comments.isNotEmpty
                            ? const Divider(
                                thickness: 1,
                                height: 1,
                                color: AppTheme.dividerColorInReview,
                              )
                            : Container(),
                        SizedBox(height: _comments.isEmpty ? 0 : 15),
                        _commentsHead(),
                        SizedBox(height: _comments.isEmpty ? 0 : 15),
                        Flexible(child: _chatListView()),
                        const SizedBox(height: 60),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget webPlayerCustomControls(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.border,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10),
      width: MediaQuery.sizeOf(context).width,
      child: IFrame.YoutubeValueBuilder(
          controller: _webPlayerController,
          builder: (context1, value) {
            return Row(
              children: [
                // TODO : implement play or pause button
                // IconButton(
                //   onPressed: () {
                //     print("value.playerState => ${value.playerState}");
                //     print(
                //         "value.playerState => ${_webPlayerController?.value.playerState}");
                //     if (value.playerState == IFrame.PlayerState.unknown) {
                //       print("Webplayercontrller");
                //       print(
                //           "value.playerState => ${_webPlayerController?.value.playerState}");
                //       _webPlayerController?.play();
                //       print(
                //           "value.playerState => ${_webPlayerController?.value.playerState}");
                //     }
                //     value.playerState == IFrame.PlayerState.unknown
                //         ? _webPlayerController?.play()
                //         : _webPlayerController?.pause();
                //   },
                //   icon: Icon(
                //     value.playerState == IFrame.PlayerState.playing
                //         ? Icons.pause
                //         : Icons.play_arrow,
                //   ),
                // ),
                // const SizedBox(width: 10),
                Text(_formattedTime(timeInSecond: value.position.inSeconds)),
                const SizedBox(width: 10),
                Expanded(
                  child: LinearProgressIndicator(
                    value: _webPlayerController?.value.isReady ?? false
                        ? _getVideoPercentage()
                        : 0,
                    backgroundColor: AppTheme.whiteColor,
                    color: AppTheme.bgColor,
                  ),
                ),
                const SizedBox(width: 10),
                _rewindButton(),
                const SizedBox(width: 10),
                Text(_formattedTime(
                    timeInSecond: value.metaData.duration.inSeconds))
              ],
            );
          }),
    );
  }

  Widget _thumbnailPlaceHolder(BuildContext context) {
    double screenWidth = MediaQuery.sizeOf(context).width;
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
              color: AppTheme.pitchBlack.withOpacity(0.2),
              blurRadius: 23.0,
              offset: const Offset(0, 1),
              spreadRadius: 1),
        ],
      ),
      child: Image.asset(
        '$ASSETS_PATH/video_play.png',
      ),
    );
  }

  Widget _saveProgressBtn(BuildContext context,
      CourseListCubit _courseListCubit, CourseListState state, Size size) {
    return _enableSaveProgressBtn.value == true &&
                (isFromSectionDetails || isFromExamScreen) ||
            (progess?.markedAsDone ?? false)
        ? Positioned(
            top: 10,
            right: 16,
            child: SafeArea(
              child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadiusDirectional.vertical(
                      top: Radius.circular(16)),
                ),
                padding: const EdgeInsets.only(bottom: 16),
                // height: 60,
                child: Align(
                    alignment: Alignment.topRight,
                    child: ButtonWidget(
                        minimumSize: const Size(138, 35),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(buttonRadius),
                            side: const BorderSide(
                              color: AppTheme.examInfoTextColor,
                            )),
                        color: AppTheme.whiteTextColor,
                        child: progess?.markedAsDone == false &&
                                    isFromSectionDetails == true ||
                                isFromExamScreen
                            ? Text(
                                SLStrings.getTranslatedString(
                                    KEY_SAVE_PROGRESS_BTN),
                                style: LMSFonts.semiBoldFont(
                                    16, AppTheme.examInfoTextColor))
                            : isFromSectionDetails == true || isFromExamScreen
                                ? Text(SLStrings.getTranslatedString(KEY_DONE),
                                    style: LMSFonts.semiBoldFont(
                                        16, AppTheme.examInfoTextColor))
                                : Container(),
                        onPressed: _enableSaveProgressBtn.value == true &&
                                progess?.markedAsDone == false
                            ? () async {
                                _courseListCubit.setLoading(true);

                                CourseListState state = _courseListCubit.state;
                                _didSelectBtn = true;
                                if (kIsWeb) {
                                  _webPlayerController?.pause();
                                } else if (_isYoutubeVideo) {
                                  _youtubePlayerController?.pause();
                                } else {
                                  _videoPlayerController?.pause();
                                }
                                Map<String, dynamic> duration = kIsWeb
                                    ? _getWebPlayerDurationValue(state)
                                    : _isYoutubeVideo
                                        ? _getYoutubeDurationValue(state)
                                        : _getVideoDurationValue(state);
                                if (courseId != null) {
                                  await _courseListCubit.setCourseProgress(
                                      courseId!,
                                      courseVideo.instanceId,
                                      duration);
                                }
                                state = _courseListCubit.state;
                                _courseListCubit.setLoading(false);
                                if (state is SetResourceProgressState) {
                                  didSaveProgress = true;
                                  disableClickOnPlayer();

                                  _showProgressDialog(context, duration);
                                } else if (state
                                    is ResourceProgressFailureState) {
                                  didSaveProgress = false;
                                  disableClickOnPlayer();

                                  showPGExceptionPopup(context, state.error);

                                  // _showProgressSubmissionFailure(context, state.error);
                                } else {
                                  didSaveProgress = false;
                                }
                              }
                            : null)),
              ),
            ))
        : Container();
  }

  _videoTitle() {
    return Visibility(
      visible: !_fullScreenView,
      child: Container(
        alignment: Alignment.centerLeft,
        child: Text(
          courseVideo.videoName.trim(),
          textAlign: TextAlign.left,
          style:
              LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
        ),
      ),
    );
  }

  _videoDesc(BuildContext context) {
    return Visibility(
        visible: !_fullScreenView,
        child: kIsWeb
            ? LayoutBuilder(
                builder: (context, constraints) {
                  TextPainter textPainter = TextPainter(
                    textDirection: TextDirection.ltr,
                    text: TextSpan(
                      text: courseVideo.description,
                      style: LMSFonts.regularFontWithHeight(
                        14,
                        AppTheme.courseVideoPrimaryTextColor,
                        1.5,
                      ),
                    ),
                    maxLines: 4,
                  );
                  textPainter.layout(
                      maxWidth: constraints
                          .maxWidth); // Determine if scrolling is needed based on text length and container width

                  if (textPainter.didExceedMaxLines) {
                    return ScrollConfiguration(
                      behavior: ScrollConfiguration.of(context)
                          .copyWith(scrollbars: true, dragDevices: {
                        PointerDeviceKind.touch,
                        PointerDeviceKind.mouse,
                      }),
                      child: SizedBox(
                        height: MediaQuery.sizeOf(context).height * 0.10,
                        child: SingleChildScrollView(
                          child: Text(
                            courseVideo.description,
                            style: LMSFonts.regularFontWithHeight(
                              14,
                              AppTheme.courseVideoPrimaryTextColor,
                              1.5,
                            ),
                          ),
                        ),
                      ),
                    );
                  } else {
                    return Container(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        courseVideo.description,
                        style: LMSFonts.regularFontWithHeight(
                          14,
                          AppTheme.courseVideoPrimaryTextColor,
                          1.5,
                        ),
                        maxLines: 4,
                      ),
                    );
                  }
                },
              )
            :
            // child:
            Container(
                alignment: Alignment.centerLeft,
                child: ValueListenableBuilder(
                  valueListenable: readMore,
                  builder: (context, value, child) {
                    final text = courseVideo.description;
                    final textPainter = TextPainter(
                      textDirection: TextDirection.ltr,
                      text: TextSpan(
                        text: text,
                        style: LMSFonts.regularFontWithHeight(
                          14,
                          AppTheme.courseVideoPrimaryTextColor,
                          1.5,
                        ),
                      ),
                      maxLines: 4,
                    )..layout(maxWidth: MediaQuery.sizeOf(context).width);

                    bool isOverflowing = textPainter.didExceedMaxLines;

                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          text,
                          maxLines: value ? null : 4,
                          overflow: value ? null : TextOverflow.ellipsis,
                          style: LMSFonts.regularFontWithHeight(
                            14,
                            AppTheme.courseVideoPrimaryTextColor,
                            1.5,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Align(
                          alignment: Alignment.topRight,
                          child: Visibility(
                              visible: isOverflowing,
                              child: InkWell(
                                  onTap: () {
                                    readMore.value = !readMore.value;
                                  },
                                  child: Text(
                                    value ? "View less" : "View more",
                                    style: LMSFonts.regularFontWithHeight(
                                        10, AppTheme.nextBtnColor, 1),
                                  ))),
                        )
                      ],
                    );
                  },
                ),
              ));
  }

  void _showProgressDialog(
      BuildContext context, Map<String, dynamic> duration) {
    showDialog(
      context: context,
      builder: (context) {
        return MultiActionDialogue(
            alertType: AlertType.success,
            title: SLStrings.getTranslatedString(KEY_SUCCESS),
            content: SLStrings.getTranslatedString(
                KEY_PROGRESS_SUCESS_ALERT_CONTENT),
            cancelBtnText: "",
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
            onCancelTap: () {},
            onContinueTap: didSaveProgress == false
                ? () {
                    Navigator.of(context).pop();
                    appRouter.pop(duration);
                  }
                : () {
                    Navigator.of(context).pop();
                    kIsWeb
                        ? Beamer.of(context).beamBack()
                        : appRouter.pop(duration);
                  });
      },
    ).then((value) => enableClickOnPlayer());
  }

  // _showProgressSubmissionFailure(BuildContext context, String msg) {
  //   showDialog(
  //     context: context,
  //     barrierDismissible: false,
  //     builder: (BuildContext context) {
  //       return SingleActionDialogue(
  //         title: '',
  //         message:
  //             msg, // SLStrings.getTranslatedString(KEY_SAVE_PROGRESS_FAILURE),
  //         iconWidget: const Icon(Icons.error, size: 60),
  //         buttonText: SLStrings.getTranslatedString(KEY_OK),
  //         isDefaultAction: true,
  //         handleOkCallback: () {},
  //       );
  //     },
  //   );
  // }

  _onBackTapped(CourseListState state, BuildContext context) {
    isPlayerClosed.value = true;

    // if (_didSelectBtn == false) {
    //   double? studyprogress = progess != null ? progess!.progress : 0.0;
    //   String progressValue = studyprogress.toString();
    //   String time = '${0} $HOURS ${0} $MINUTES ${0} $SECONDS';
    //   Map<String, dynamic> args = {
    //     'progress': progressValue,
    //     'time_spent': time
    //   };
    //   isFromExamScreen
    //       ? kIsWeb
    //           ? Beamer.of(context).beamBack()
    //           : appRouter.popUntil((route) =>
    //               route.settings.name == SectionDetailsViewRoute.name)
    //       : kIsWeb
    //           ? Beamer.of(context).beamBack()
    //           : appRouter.pop(args);
    // } else {
    //   Map<String, dynamic> duration = _isYoutubeVideo
    //       ? _getYoutubeDurationValue(state)
    //       : _getVideoDurationValue(state);

    isFromExamScreen
        ? kIsWeb
            ? Beamer.of(context).beamBack()
            : appRouter.popUntil(
                (route) => route.settings.name == SectionDetailsViewRoute.name)
        : kIsWeb
            ? Beamer.of(context).beamBack()
            : appRouter.pop();
    // }
  }

  Map<String, dynamic> _getWebPlayerDurationValue(CourseListState state) {
    double progressValue = _webPlayerController != null
        ? (_currentDuration / _webPlayerController!.metadata.duration.inSeconds)
            .abs()
        : 0.0;

    return _getDurationForVideo(progressValue, state);
  }

  Map<String, dynamic> _getYoutubeDurationValue(CourseListState state) {
    double progressValue = _youtubePlayerController != null
        ? (_currentDuration /
                _youtubePlayerController!.metadata.duration.inSeconds)
            .abs()
        : 0.0;

    return _getDurationForVideo(progressValue, state);
  }

  Map<String, dynamic> _getVideoDurationValue(CourseListState state) {
    Map<String, dynamic> args = {};
    if (_videoPlayerController != null &&
        _videoPlayerController!.value.isInitialized) {
      Duration totalDuration =
          _videoPlayerController?.value.duration ?? const Duration();
      double progressValue = (_currentDuration / totalDuration.inSeconds).abs();

      args = _getDurationForVideo(progressValue, state);
    }
    return args;
  }

  Map<String, dynamic> _getDurationForVideo(
      double progressValue, CourseListState state) {
    // Ensure progressValue is within the range [0, 1]
    progressValue = progressValue.clamp(0.0, 1.0);

    // Update video progress in the state
    state.videoProgress = progressValue;

    // Calculate progress percentage
    String progress = (progressValue * 100).toStringAsFixed(2);
    progress = progress == 'NaN' ? '0' : progress;

    // Format duration
    Duration duration = Duration(seconds: _currentDuration.toInt());
    String time = formatDuration(duration);

    return {'progress': progress, 'time_spent': time};
  }

  String formatDuration(Duration duration) {
    int hours = duration.inHours;
    int minutes = duration.inMinutes.remainder(60);
    int seconds = duration.inSeconds.remainder(60);

    String hoursString = hours > 1 ? HOURS : HOUR;
    String minutesString = minutes > 1 ? MINUTES : MINUTE;
    String secondsString = seconds > 1 ? SECONDS : SECOND;

    return '$hours $hoursString $minutes $minutesString $seconds $secondsString';
  }

// TODO: remove later
/*
  void _showDialog(BuildContext context, CourseListCubit _courseCubit,
      CourseListState state, bool isYoutube) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertPopup(
          alertType: AlertType.warning,
          title: WARNING,
          content: PROGRESS_ALERT_CONTENT,
          cancelBtnText: NO,
          confirmBtnText: YES,
          onContinueTap: () async {
            if (isYoutube == true) {
              _youtubePlayerController?.pause();
              Map<String, dynamic> duration = _getYoutubeDurationValue(state);

              await _courseCubit.setCourseProgress(
                  courseId!, courseVideo.instanceId, duration);
              state = _courseCubit.state;

              if (state is setResourceProgressState) {
                Navigator.of(context).pop();
                _showProgressDialog(context, duration);
              }
            } else {
              _videoPlayerController?.pause();
              Map<String, dynamic> duration = _getVideoDurationValue(state);

              _courseCubit.setCourseProgress(
                  courseId!, courseVideo.instanceId, duration);

              if (state is setResourceProgressState) {
                _showProgressDialog(context, duration);
              }
            }
          },
          onCancelTap: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
          },
        );
      },
    );
  }
*/

  Widget _chewiePlayerWidget(BuildContext context, CourseListCubit _courseCubit,
      CourseListState state, Size size) {
    return _mainContainer(context, _courseCubit, state, size,
        videoPlayerWidget: _videoPlayer(size, context));
  }

  _videoPlayer(Size size, BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: _chewieController != null
            ? Chewie(
                controller: _chewieController!,
              )
            : _thumbnailPlaceHolder(context),
      ),
    );
    //  Expanded(
    //     child: ClipRRect(
    //       borderRadius: const BorderRadius.only(
    //           topLeft: Radius.circular(16), topRight: Radius.circular(16)),
    //       child: SizedBox(
    //         width: size.width,
    //         child: Column(
    //           mainAxisAlignment: MainAxisAlignment.center,
    //           children: [
    //             const CircularProgressIndicator(),
    //             const SizedBox(height: 20),
    //             Text(SLStrings.getTranslatedString(KEY_LOADING)),
    //           ],
    //         ),
    //       ),
    //     ),
    //   );
  }

  ///
  /// comments
  ///

  Widget _commentsHead() {
    return Visibility(
      visible: _comments.isNotEmpty && courseVideo.instanceId.isNotEmpty,
      child: Text(
        SLStrings.getTranslatedString(KEY_VIDEO_COMMENTS),
        style: LMSFonts.semiBoldFont(16, AppTheme.courseVideoPrimaryTextColor),
      ),
    );
  }

  Widget _chatListView() {
    return Visibility(
      visible: courseVideo.instanceId.isNotEmpty,
      child: Column(
        children: List.generate(
          _comments.length,
          (commentIndex) => Column(
            children: [
              _chatItem(_comments[commentIndex]),
              _comments[commentIndex].children != null
                  ? Padding(
                      padding: const EdgeInsets.only(left: 34),
                      child: Column(
                        children: List.generate(
                          _comments[commentIndex].children!.length,
                          (childIndex) => Column(
                            children: [
                              _chatItem(_comments[commentIndex]
                                  .children![childIndex]),
                            ],
                          ),
                        ),
                      ),
                    )
                  : Container()
            ],
          ),
        ),
      ),
    );
  }

  Widget _chatItem(ResourseComment commentObj) {
    return Card(
      // color: Colors.red,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 0,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _profileIcon(commentObj.profilePic),
          const SizedBox(width: 14),
          _userCommentInfo(commentObj),
          const SizedBox(width: 10),
          _commentTypeDateInfo(commentObj),
          const SizedBox(width: 5),
        ],
      ),
    );
  }

  Widget _profileIcon(String avatar) {
    return Container(
      height: 60,
      width: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          width: 2,
          color: AppTheme.whiteColor,
        ),
        boxShadow: [
          BoxShadow(
            color: AppTheme.blackColor.withOpacity(0.1),
            blurRadius: 1,
            spreadRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: CircleAvatar(
        backgroundColor: AppTheme.transparentColor,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(5.0),
          child: avatar != null && avatar.trim().isNotEmpty
              ? CachedNetworkImage(
                  imageUrl: avatar,
                  fit: BoxFit.cover,
                  placeholder: (context, url) =>
                      const CircularProgressIndicator(),
                  errorWidget: (context, url, error) => _placeHolderWidget(),
                )
              : _placeHolderWidget(),
        ),
      ),
    );
  }

  Widget _placeHolderWidget() {
    return const ProfilePlaceholderWidget(maxWidth: 44 * 2);
  }

  Widget _userCommentInfo(ResourseComment commentObj) {
    return Expanded(
      child: Align(
        alignment: Alignment.topLeft,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 7),
            Text(
              commentObj.user,
              style: LMSFonts.semiBoldFontWithHeight(
                  16, AppTheme.courseVideoPrimaryTextColor, 1.2),
            ),
            const SizedBox(height: 4),
            Text(
              commentObj.message.toString().trim(),
              style: LMSFonts.regularFontWithHeight(
                  14, AppTheme.courseVideoPrimaryTextColor, 1.2),
            ),
            SizedBox(height: commentObj.commentedTime != null ? 5 : 0),
          ],
        ),
      ),
    );
  }

  Widget _commentTypeDateInfo(ResourseComment commentObj) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        const SizedBox(height: 7),
        Text(
          commentObj.commentedTime != null
              ? AppDateFormatter().formatDatedmmmmy(commentObj.commentedTime!)
              : '',
          style: LMSFonts.regularFontWithHeight(
              12, AppTheme.courseCommentDate, 1.2),
        ),
        const SizedBox(height: 7),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            border: Border.all(),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 2.5),
            child: Text(
              commentObj.commentType,
              style:
                  LMSFonts.regularFontWithHeight(10, AppTheme.blackColor, 1.2),
            ),
          ),
        ),
      ],
    );
  }

  Widget _commentBtn(BuildContext context, CourseListCubit courseCubit) {
    return Visibility(
      visible: courseVideo.instanceId.isNotEmpty,
      child: Positioned(
        bottom: 8,
        right: 8,
        child: BlocBuilder<ConnectivityCubit, ConnectivityState>(
          builder: (context, state) {
            return ValueListenableBuilder(
                valueListenable: isPlayerReady,
                builder: (context, playerReady, _) {
                  return IconButton(
                      iconSize: 50,
                      icon: Image.asset('$ASSETS_PATH/comment_icon.png'),
                      onPressed: state is! InternetConnected || !playerReady
                          ? null
                          : () async {
                              if (kIsWeb) {
                                disableClickOnPlayer();
                                _webPlayerController?.pause();
                              } else {
                                _youtubePlayerController?.pause();
                              }
                              _commentController.clear();
                              // disableClickOnPlayer();
                              _showCommentBox(context, courseCubit);
                            });
                });
          },
        ),
      ),
    );
  }

  _showCommentBox(BuildContext mainContext, CourseListCubit courseCubit) {
    showDialog(
        context: mainContext,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return BlocBuilder<CourseListCubit, CourseListState>(
            builder: (context, state) {
              if (state is CommentTypeSelected) {
                commentType = state.commentType;
              }

              return ValueListenableBuilder(
                  valueListenable: enableCommentSubmitBtn,
                  builder: (context, isPlayer, _) {
                    return AddCommentDialouge(
                      commentController: _commentController,
                      courseCubit: courseCubit,
                      selectedType: commentType,
                      enableSubmitBtn: enableCommentSubmitBtn,
                      onSubmit: () async {
                        if (enableCommentSubmitBtn.value) {
                          enableCommentSubmitBtn.value = false;

                          await _addNewComment(context, courseCubit,
                              _commentController.text.trim());
                          _commentController.clear();
                        }
                      },
                      onTypeSelected: (val) {
                        commentType = val;
                        courseCubit.handleCommentTypeSelection(val);
                        courseCubit.emit(
                            CommentTypeSelected(commentType: commentType));
                      },
                      onChanged: (val) {
                        enableCommentSubmitBtn.value =
                            _commentController.text.trim().isNotEmpty;
                      },
                      onClose: () {
                        kIsWeb ? enableClickOnPlayer() : null;
                        Navigator.of(context).pop();
                      },
                    );
                  });
            },
          );
        });
  }

  _fetchComments(BuildContext context, CourseListCubit cubit) async {
    bool _hasAccess = await _checkAccessToGetComments();
    if (_hasAccess) {
      String instanceId = courseVideo.instanceId;
      await cubit.setLoading(true);
      await cubit.fetchCommentsForId(instanceId);
      CourseListState state = cubit.state;
      if (state is CourseCommentsFetched) {
        isDataLoading = false;
        _comments = state.comments;
      } else if (state is CommentsError) {
        _showCommentError(context, cubit, state.error);
      }
      await cubit.setLoading(false);
    } else {
      // no access to fetch comments
    }
  }

  _addNewComment(
      BuildContext context, CourseListCubit cubit, String comment) async {
    /// locally update first
    /// upload to server in background
    ///
    try {
      ResourseComment _studentComment = ResourseComment(
          commentId: '',
          message: comment,
          subject: TOPIC_NAME,
          commentType: commentType,
          user: SLUser.shared.first_name + ' ' + SLUser.shared.last_name,
          commentedTime: DateTime.now(),
          profilePic: SLUser.shared.avatar_url);

      Map<String, dynamic> jsonReqBody =
          _comments.isNotEmpty && _comments.first.commentId.isNotEmpty
              ? {
                  "comment_data": {
                    // "parent_id": _comments.first.commentId,
                    "subject": TOPIC_ID,
                    "message": comment,
                    "type": commentType
                  },
                  "instance_id": courseVideo.instanceId,
                  "user_id": USER_ID
                }
              : {
                  "comment_data": {
                    "subject": TOPIC_ID,
                    "message": comment,
                    "type": commentType
                  },
                  "instance_id": courseVideo.instanceId,
                  "user_id": USER_ID
                };
      await cubit.setLoading(true);
      await cubit.uploadComment(jsonReqBody);
      CourseListState state = cubit.state;
      Navigator.pop(context);

      if (state is CommentsError) {
        _showCommentError(context, cubit, state.error);
      } else {
        _commentController.clear();
        await cubit.addComment(_studentComment, _comments);
        cubit.setLoading(false);
        _showSubmissionDialgoue(context);
      }
    } on Exception catch (e) {
      debugPrint(e.toString());
    }
  }

  _showCommentError(BuildContext context, CourseListCubit cubit, String error) {
    isDataLoading = false;
    cubit.setLoading(false);
    showPGExceptionPopup(context, error);
  }

  // Function to show the dialogue
  void _showSubmissionDialgoue(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: SingleActionDialogue(
            title: '',
            message: SLStrings.getTranslatedString(KEY_COMMENT_SUBMITTED),
            iconWidget: const Icon(Icons.check_circle, size: 60),
            buttonText: SLStrings.getTranslatedString(KEY_OK),
            isDefaultAction: false,
            handleOkCallback: () {
              if (kIsWeb) {
                enableClickOnPlayer();
                _webPlayerController?.play();
              } else {
                _youtubePlayerController?.play();
              }
              Navigator.of(context).pop();
            },
          ),
        );
      },
    );
  }

  ///
  /// checkpoint
  ///
  ///

  void disableClickOnPlayer() {
    if (kIsWeb) {
      try {
        String jsCode = '''
              var iframes = document.getElementsByTagName("iframe"); 
              for(var i = 0; i < iframes.length; i++) { 
                 iframes[i].style.pointerEvents = "none";
              } 
          ''';
        js.context.callMethod("eval", [jsCode]);
        // ignore: avoid_catches_without_on_clauses
      } catch (e) {
        debugPrint('Error _fixYoutubeOnWeb: $e');
      }
    }
  }

  void enableClickOnPlayer() {
    if (kIsWeb) {
      try {
        String jsCode = '''
              var iframes = document.getElementsByTagName("iframe"); 
              for(var i = 0; i < iframes.length; i++) { 
                 iframes[i].style.pointerEvents = "auto";
              } 
          ''';
        js.context.callMethod("eval", [jsCode]);
        // ignore: avoid_catches_without_on_clauses
      } catch (e) {
        debugPrint('Error _fixYoutubeOnWeb: $e');
      }
    }
  }

  // checkpoint
  // custom popup opens depending on the type of milestone
  void _showCheckPointInfoPopup(BuildContext context, CheckPoint item) {
    disableClickOnPlayer();
    if (_fullScreenView) {
      _youtubePlayerController?.toggleFullScreenMode();
    }
    // FirebaseAnalytics.instance.logEvent(
    //   name: "milestone_popup_opened",
    //   parameters: {
    //     "content_type": item.checkpointType,
    //     "item_id": item.checkpointId,
    //     "pop_start_at": DateTime.now().toString(),
    //     "milestone_index": item.courseModuleId
    //   },
    // );
    //start timer
    // startCounter();

    // Implement your custom popup logic here
    if (item.checkpointType == CheckPointType.alert.value) {
      _showAlertCheckpointPopup(context, item);
    } else if (item.checkpointType == CheckPointType.prompt.value) {
      _showFormFieldCheckpointPopup(context, item);
    } else if (item.checkpointType == CheckPointType.exam.value) {
      _showExamCheckpointPopup(context, item);
    }
  }

  _showAlertCheckpointPopup(BuildContext context, CheckPoint item) {
    return showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Text(SLStrings.getTranslatedString(KEY_POPUP_TITLE)),
            content: Text(SLStrings.getTranslatedString(KEY_POPUP_MSG)),
            actions: <Widget>[
              ValueListenableBuilder(
                  valueListenable: counter,
                  builder: (BuildContext context, int counter, Widget? _) {
                    return Text(_formattedTime(timeInSecond: counter));
                  }),
              TextButton(
                child: Text(SLStrings.getTranslatedString(KEY_YES)),
                onPressed: () {
                  Navigator.of(context).pop();
                  _onClosePopup(item, SLStrings.getTranslatedString(KEY_YES));
                },
              ),
              TextButton(
                child: Text(SLStrings.getTranslatedString(KEY_NO)),
                onPressed: () {
                  Navigator.of(context).pop();
                  _onClosePopup(item, SLStrings.getTranslatedString(KEY_NO));
                },
              ),
            ],
          ),
        );
      },
    ).then((value) => enableClickOnPlayer());
  }

  _showFormFieldCheckpointPopup(BuildContext context, CheckPoint item) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return PopScope(
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              content: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Form(
                  key: _formKey,
                  child: _fullScreenView
                      ? Row(
                          children: [
                            SizedBox(
                                width: 100,
                                child: Text(SLStrings.getTranslatedString(
                                    KEY_PROMPT_TEXT))),
                            Expanded(
                              child: TextFormField(
                                controller: _promptController,
                                validator: (value) {
                                  if (value == null || value.length <= 3) {
                                    return SLStrings.getTranslatedString(
                                        KEY_PROMPT_VALIDATION_MSG);
                                  }
                                },
                              ),
                            ),
                          ],
                        )
                      : SizedBox(
                          height: 150,
                          child: Column(
                            children: [
                              Text(SLStrings.getTranslatedString(
                                  KEY_PROMPT_TEXT)),
                              TextFormField(
                                controller: _promptController,
                                validator: (value) {
                                  if (value == null || value.length <= 3) {
                                    return SLStrings.getTranslatedString(
                                        KEY_PROMPT_VALIDATION_MSG);
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                ),
              ),
              actions: [
                ValueListenableBuilder(
                    valueListenable: counter,
                    builder: (BuildContext context, int counter, Widget? _) {
                      return Text(_formattedTime(timeInSecond: counter));
                    }),
                TextButton(
                  child: Text(SLStrings.getTranslatedString(KEY_SUBMIT),
                      style: LMSFonts.buttonStyle(16)),
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      Navigator.of(context).pop();
                      _onClosePopup(item, _promptController.text);
                    }
                  },
                ),
              ],
            ),
            canPop: false,
          );
        }).then((value) => enableClickOnPlayer());
  }

  void navigateToExamIntro(BuildContext context, CheckPoint item) {
    _courseCubitObj?.setQuestionLoading(true);
    _fetchExamQuestions(context, item.instanceId ?? "", item);
  }

  _showExamCheckpointPopup(BuildContext context, CheckPoint item) {
    return showDialog(
        context: context,
        builder: (context1) => PopScope(
              canPop: false,
              child: SingleActionDialogue(
                title: '',
                message: SLStrings.getTranslatedString(
                    KEY_CHECKPOINT_EXAM_NAVIGATION_MSG),
                buttonText: SLStrings.getTranslatedString(KEY_OK),
                isDefaultAction: false,
                handleOkCallback: () {
                  _courseCubitObj?.setQuestionLoading(true);

                  Navigator.of(context1).pop();
                  _fetchExamQuestions(context, item.instanceId ?? "", item);
                },
              ),
            ));
  }

  _showVideoCompletePopup(BuildContext context) {
    return showDialog(
        context: context,
        builder: (context) => PopScope(
            canPop: false,
            child: SingleActionDialogue(
                title: '',
                message:
                    SLStrings.getTranslatedString(KEY_CHECKPOINT_COMPLETE_MSG),
                buttonText: SLStrings.getTranslatedString(KEY_OK),
                isDefaultAction: false,
                handleOkCallback: () {
                  Navigator.of(context).pop();
                }))).then((value) {
      enableClickOnPlayer();

      Beamer.of(context).beamToNamed('/section-details');
    });
  }

  _fetchExamQuestions(
      BuildContext context, String examId, CheckPoint item) async {
    var route = ModalRoute.of(_scaffoldKey.currentState?.context ?? context);
    final _examCubit = BlocProvider.of<ExamCubit>(context);

    // _examCubit.setQuestionsLoading(true);
    // await _examCubit.fetchQuestions(examId);
    await _examCubit.startCheckpointQuiz(item.checkpointId ?? '');
    ExamState state = _examCubit.state;
    if (state is ExamPGException) {
      _courseCubitObj?.setQuestionLoading(false);
      disableClickOnPlayer();
      _showPGExceptionPopup(
          _scaffoldKey.currentState?.context ?? context, state.error);
    } else if (state is ExamQuestionsFetched) {
      if (_examCubit.state.examData.isNotEmpty) {
        _questionData = _examCubit.state.examData.first;
      }
      _questions = _examCubit.state.examData.isEmpty
          ? []
          : _examCubit.state.examData.first.questions;
      // check if the exam is valid
      if (_questions.isNotEmpty &&
          route != null &&
          route.isActive &&
          _questionData != null &&
          _questionData?.quizAttemptId != null) {
        //Navigate to Exam Intro screen
        // appRouter.pop();
        // _examCubit.setQuestionsLoading(false);
        _courseCubitObj?.setQuestionLoading(false);
        kIsWeb ? enableClickOnPlayer() : null;
        _examCubit.updateNavigationStatus(true);
        kIsWeb
            ? Beamer.of(context).beamToNamed('/exam-view', data: {
                'examDuration': _questionData?.duration,
                'shouldShowTimer': true,
                'isFromViewResult': false,
                'quizAttemptId': _questionData?.quizAttemptId,
                'questionData': _questionData,
                'isFromVideoView': true
              })
            : await appRouter.push(ExamViewRoute(
                examDuration: _questionData?.duration ?? 0,
                shouldShowTimer: true,
                isFromViewResult: false,
                quizAttemptId: _questionData?.quizAttemptId ?? '',
                questionData: _questionData!,
                isFromVideoView: true));
      } else {
        _courseCubitObj?.setQuestionLoading(false);
        // no questions available for the exam
        if (route != null && route.isActive) {
          disableClickOnPlayer();

          _showQuestionsEmptyPopup(
              _scaffoldKey.currentState?.context ?? context);
        }
      }
    }
  }

  _showQuestionsEmptyPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        // return CustomAlertDialog(title: SORRY, message: QUESTION_NOT_AVAILABLE);
        return PopScope(
          canPop: false,
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: SLStrings.getTranslatedString(KEY_QUESTION_NOT_AVAILABLE),
            cancelBtnText: "",
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
            alertType: AlertType.error,
            onCancelTap: () {},
            onContinueTap: () {
              isPlayerClosed.value = true;
              if (kIsWeb) {
                Navigator.of(context).pop();
                Beamer.of(context).beamBack();
              } else {
                appRouter.popUntil((route) =>
                    route.settings.name == SectionDetailsViewRoute.name);
              }
            },
          ),
        );
      },
    ).then((value) => enableClickOnPlayer());
  }

  _showPGExceptionPopup(BuildContext context, String msg) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: msg,
            alertType: AlertType.error,
            onCancelTap: () => appRouter.pop(),
            onContinueTap: () {
              isPlayerClosed.value = true;
              if (kIsWeb) {
                Navigator.of(context).pop();
                Beamer.of(context).beamBack();
              } else {
                appRouter.popUntil((route) =>
                    route.settings.name == SectionDetailsViewRoute.name);
              }
            },
            cancelBtnText: '',
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
          ),
        );
      },
    ).then((value) => enableClickOnPlayer());
  }

  /// sort checkpoints received from api
  _sortCheckpoints(List<CheckPoint> checkpoints) {
    checkpoints.sort((a, b) {
      // Extract the start time strings from the maps
      String startTimeA = a.startTime ?? "";
      String startTimeB = b.startTime ?? "";

      // Convert the start time strings to DateTime objects for comparison
      DateTime dateTimeA = DateTime.parse("1970-01-01 $startTimeA");
      DateTime dateTimeB = DateTime.parse("1970-01-01 $startTimeB");

      // Compare the DateTime objects
      return dateTimeA.compareTo(dateTimeB);
    });
  }

  /// manage checkpoints fetched
  /// check whether
  _manageCheckPoints(CourseListCubit _courseCubit) {
    if (!kIsWeb) {
      _courseCubit.getVideoPlayerData(courseVideo.videoId).then((value) {
        if (value.isNotEmpty) {
          _getCompletedCheckpoints(value.first.milestoneDurations);
        }
      });
    } else {
      _courseCubit.hiveGetVideoPlayerData(courseVideo.videoId).then((value) {
        if (value != null) {
          _getCompletedCheckpoints(value.mileStoneDurations);
        }
      });
    }
    if (isExamPassed) {
      _completedCheckPoints.add(
          _checkPoints[_courseCubitObj?.videoPlayerData['checkPointIndex']]);
      lastWatchTime = _courseCubitObj?.videoPlayerData['lastWatchTime'] + 1;
      _checkPoints.removeRange(
          0, _courseCubitObj?.videoPlayerData['checkPointIndex'] + 1);
    } else {
      final lastCheckpointIndex =
          _courseCubitObj?.videoPlayerData['checkPointIndex'];
      if (lastCheckpointIndex == 0) {
        lastWatchTime = 0;
      } else {
        int startTimeInSec = convertCheckPointTimetoSec(
            _checkPoints[lastCheckpointIndex - 1].startTime);
        _checkPoints.removeRange(0, lastCheckpointIndex);
        lastWatchTime = startTimeInSec + 1;
      }
    }
  }

  Future<void> _getLatsWatchTime(CourseListCubit _courseCubit) async {
    //checking the last watchtime of the video from sqlite
    if (!kIsWeb) {
      _courseCubit.getVideoPlayerData(courseVideo.videoId).then((value) {
        debugPrint('value => ${value.toString()}');
        if (value.isNotEmpty) {
          lastWatchTime = value.first.lastWatchTime;
          _checkPoints.removeRange(0, value.first.milestonesCompleted);
          _getCompletedCheckpoints(value.first.milestoneDurations);
        }
      });
    } else {
      // TODO: disabling for testing in web
      // _courseCubit.hiveGetVideoPlayerData(courseVideo.videoId).then((value) {
      //   if (value != null) {
      //     lastWatchTime = value.lastWatchTime;
      //     _webPlayerController?.seekTo(Duration(seconds: value.lastWatchTime));
      //     _checkPoints.removeRange(0, value.mileStonesCompleted);
      //     _getCompletedCheckpoints(value.mileStoneDurations);
      //   }
      // });
    }
  }

  _getCompletedCheckpoints(String item) {
    // Extracting content between '(' and ')'
    RegExp regExp = RegExp(r"\((.*?)\)");
    Iterable<Match> matches = regExp.allMatches(item);

    List<String> extractedValues = [];

    // Extracting values from each match
    for (Match match in matches) {
      extractedValues.add(match.group(1)!);
    }
    for (String value in extractedValues) {
      List<String> valuesList = value.split(', ').map((e) => e.trim()).toList();
      CheckPoint checkpoint = CheckPoint(
        orgId: valuesList[0],
        sequence: int.parse(valuesList[1]),
        createdAt: DateTime.parse(valuesList[2]),
        createdBy: valuesList[3],
        startTime: valuesList[4],
        updatedAt: DateTime.parse(valuesList[5]),
        updatedBy: valuesList[6],
        instanceId: valuesList[7],
        moduleName: valuesList[8],
        isMandatory: valuesList[9] == "true" ? true : false,
        checkpointId: valuesList[10],
        moduleTypeId: valuesList[11],
        checkpointName: valuesList[12],
        checkpointType: valuesList[13],
        courseModuleId: valuesList[14],
      );
      _completedCheckPoints.add(checkpoint);
    }
  }

  /// convert checkpoint start time in String("hh:mm:ss") to Duration in seconds
  int convertCheckPointTimetoSec(String? time) {
    List<String> parts = time!.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1]);
    int seconds = int.parse(parts[2]);
    Duration duration =
        Duration(hours: hours, minutes: minutes, seconds: seconds);
    int timeInSeconds = duration.inSeconds;
    return timeInSeconds;
  }

  _formattedTime({required int timeInSecond}) {
    int sec = timeInSecond % 60;
    int min = (timeInSecond / 60).floor();
    String minute = min.toString().length <= 1 ? "0$min" : "$min";
    String second = sec.toString().length <= 1 ? "0$sec" : "$sec";
    return "$minute : $second";
  }

// Upon the user completing a milestone session, the response will be saved to SQLite and transmitted to Firebase Analytics.
  void _onClosePopup(CheckPoint item, String response) async {
    _timer?.cancel();
    _youtubePlayerController?.play();
    final data = Milestone(
      id: item.courseModuleId ?? "",
      milestoneId: item.checkpointId ?? '',
      type: item.checkpointType ?? "",
      milestoneDuration: counter.value,
    );
    _milestoneDurations.add(data);
    _courseCubitObj?.insertToMilestoneData(MilestoneData(
      videoId: item.courseModuleId ?? "",
      milestoneId: item.checkpointId ?? "",
      sessionStart: DateTime.now()
          .subtract(Duration(seconds: counter.value))
          .millisecondsSinceEpoch,
      sessionEnd: DateTime.now().millisecondsSinceEpoch,
      sessionResponse: response,
      sessionStatus: _milestoneDurations.length == checkPoints.length ? 1 : 0,
    ));
    // FirebaseAnalytics.instance.logEvent(
    //   name: "milestone_popup_closed",
    //   parameters: {
    //     "content_type": item.checkpointType,
    //     "item_id": item.checkpointId,
    //     "popup_close_at": DateTime.now().toString(),
    //     "milestone_duration": counter.value,
    //     "session_response": response,
    //   },
    // );
    counter.value = 0;
  }

  _showVideoPlayerErrorPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: MultiActionDialogue(
            title: SLStrings.getTranslatedString(KEY_SORRY),
            content: SLStrings.getTranslatedString(KEY_VIDEO_PLAYER_ERROR),
            alertType: AlertType.error,
            onCancelTap: () => appRouter.pop(),
            onContinueTap: () {
              isPlayerClosed.value = true;
              isFromExamScreen
                  ? kIsWeb
                      ? Beamer.of(context).beamBack()
                      : appRouter.popUntil((route) =>
                          route.settings.name == SectionDetailsViewRoute.name)
                  : kIsWeb
                      ? Beamer.of(context).beamBack()
                      : appRouter.popUntil((route) =>
                          route.settings.name == CourseDetailsViewRoute.name);
            },
            cancelBtnText: '',
            confirmBtnText: SLStrings.getTranslatedString(KEY_OK),
          ),
        );
      },
    ).then((value) => enableClickOnPlayer());
  }

  double _getVideoPercentage() {
    if (_webPlayerController?.value.isReady == true) {
      double totalDuration =
          _webPlayerController?.metadata.duration.inMilliseconds.toDouble() ??
              0;
      double currentPosition =
          _webPlayerController?.value.position.inMilliseconds.toDouble() ?? 0;
      double _percentagePlayed = (currentPosition / totalDuration);
      if (_percentagePlayed.isFinite) {
        return _percentagePlayed;
      } else {
        return 0.0;
      }
    } else {
      return 0.0;
    }
  }
}

class AddCommentDialouge extends StatelessWidget {
  final TextEditingController commentController;
  final CourseListCubit courseCubit;
  final String selectedType;
  final ValueNotifier<bool> enableSubmitBtn;
  final VoidCallback onSubmit;
  final StringCallback onTypeSelected;
  final StringCallback onChanged;
  final VoidCallback onClose;

  const AddCommentDialouge({
    super.key,
    required this.commentController,
    required this.courseCubit,
    required this.selectedType,
    required this.enableSubmitBtn,
    required this.onSubmit,
    required this.onTypeSelected,
    required this.onChanged,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      },
      child: Center(
        child: Material(
          color: Colors.transparent,
          child: LayoutBuilder(builder: (context, constraints) {
            double webWidth = 379.6;
            // constraints.maxWidth / 5;
            double mobileWidth = constraints.maxWidth;
            double allowedWidth = constraints.maxWidth > 1200
                ? webWidth
                : constraints.maxWidth >= 500
                    ? webWidth
                    : mobileWidth;
            return Container(
              width: allowedWidth,
              margin: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
              decoration: BoxDecoration(
                  color: AppTheme.whiteColor,
                  borderRadius: BorderRadius.circular(8.0)),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 25, horizontal: 20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        SLStrings.getTranslatedString(KEY_COMMENT_BOX_TITLE),
                        textAlign: TextAlign.center,
                        style: LMSFonts.semiBoldFont(
                            16, AppTheme.courseVideoPrimaryTextColor),
                      ),
                    ),
                    const SizedBox(height: 13),
                    _commentTypeOptions(context),
                    const SizedBox(height: 14),
                    Text(
                      selectedType == FEEDBACK
                          ? SLStrings.getTranslatedString(
                              KEY_COMMENT_BOX_SUB_TITLE_FEEDBACK)
                          : SLStrings.getTranslatedString(
                              KEY_COMMENT_BOX_SUB_TITLE_SUGGESTION),
                      style:
                          LMSFonts.regularFont(14, AppTheme.commentBoxContent),
                    ),
                    const SizedBox(height: 13),
                    _commentBoxTextField(),
                    const SizedBox(height: 20),
                    Row(
                      children: [
                        Expanded(child: _cancelBtnComment(context)),
                        // const SizedBox(width: 2),
                        Expanded(child: _addCommentBtn(context, selectedType))
                      ],
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _commentTypeOptions(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<String>(
        isExpanded: true,
        items: resourseCommentType
            .map((String item) => DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: LMSFonts.regularFont(14, AppTheme.commentBoxContent),
                    overflow: TextOverflow.ellipsis,
                  ),
                ))
            .toList(),
        value: selectedType,
        onChanged: (String? newValue) {
          if (newValue != null) onTypeSelected(newValue);
        },
        buttonStyleData: ButtonStyleData(
          height: 35,
          width: double.infinity,
          padding: const EdgeInsets.only(left: 13, right: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: AppTheme.courseVideoPrimaryTextColor),
            color: Colors.white,
          ),
          elevation: 0,
        ),
        dropdownStyleData: DropdownStyleData(
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.courseVideoPrimaryTextColor),
            borderRadius: BorderRadius.circular(10),
            color: Colors.white,
          ),
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 35,
        ),
      ),
    );

    // return Container(
    //   padding: EdgeInsets.zero,
    //   decoration: BoxDecoration(
    //     borderRadius: BorderRadius.circular(10),
    //     border: Border.all(color: AppTheme.courseVideoPrimaryTextColor),
    //   ),
    //   width: 300.0,
    //   child: DropdownButtonHideUnderline(
    //     child: ButtonTheme(
    //       alignedDropdown: true,
    //       child: DropdownButton<String>(
    //         padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 5),
    //         isDense: true,
    //         underline: Container(),
    //         elevation: 0,
    //         value: selectedType,
    //         onChanged: (String? newValue) {
    //           if (newValue != null) onTypeSelected(newValue);
    //         },
    //         items: resourseCommentType.map((String item) {
    //           return DropdownMenuItem<String>(
    //             value: item,
    //             child: Text(
    //               item,
    //               style: LMSFonts.regularFont(14, AppTheme.commentBoxContent),
    //             ),
    //           );
    //         }).toList(),
    //       ),
    //     ),
    //   ),
    // );
  }

  Widget _commentTypeChip(BuildContext context, bool isSelected, String title) {
    return ChoiceChip(
      elevation: 1.0,
      selected: isSelected,
      label: Text(title),
      labelStyle:
          LMSFonts.boldFont(16, isSelected ? Colors.white : AppTheme.iconColor),
      backgroundColor: LmsColors.white,
      disabledColor: LmsColors.white,
      selectedColor: AppTheme.iconColor,
      onSelected: (bool selectedStatus) {
        onTypeSelected(title);
      },
    );
  }

  Widget _commentBoxTextField() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.commentTextFieldBg.withOpacity(0.15),
        borderRadius: BorderRadius.circular(10),
      ),
      child: TextField(
        controller: commentController,
        keyboardType: TextInputType.multiline,
        maxLines: 5,
        onChanged: (val) => onChanged(val),
        style: LMSFonts.regularFont(14, AppTheme.commentBoxContent),
        decoration: InputDecoration(
          fillColor: AppTheme.commentTextFieldBg.withOpacity(0.15),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 13, vertical: 7),
          hintText: SLStrings.getTranslatedString(KEY_COMMENT_BOX_PLACEHOLDER),
          focusedBorder: InputBorder.none,
          enabledBorder: InputBorder.none,
        ),
      ),
    );
  }

  Widget _addCommentBtn(BuildContext context, String commentType) {
    final _connectivityCubit = BlocProvider.of<ConnectivityCubit>(context);
    Size popupSize = MediaQuery.of(context).size;
    return Align(
      alignment: Alignment.bottomCenter,
      child: ButtonWidget(
          child: Text(
            SLStrings.getTranslatedString(KEY_ADD_COMMENT) + commentType,
            style: LMSFonts.buttonStyle(14),
            textAlign: TextAlign.center,
          ),
          // textColor: LmsColors.white,
          // minimumSize: Size(popupSize.width / 2.2, 35),
          color: AppTheme.submitBtnColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(popupSize.width, 35),
          onPressed: enableSubmitBtn.value ? onSubmit : null),
    );
  }

  Widget _cancelBtnComment(BuildContext context) {
    Size popupSize = MediaQuery.of(context).size;
    return Align(
      alignment: Alignment.bottomCenter,
      child: ButtonWidget(
          child: Text(
            SLStrings.getTranslatedString(KEY_CANCEL),
            style: LMSFonts.buttonStyle(14),
            textAlign: TextAlign.center,
          ),
          textColor: AppTheme.submitBtnColor,
          // minimumSize: Size(popupSize.width / 2.2, 35),
          color: AppTheme.whiteColor,
          shape: RoundedRectangleBorder(
            side: const BorderSide(color: AppTheme.submitBtnColor),
            borderRadius: BorderRadius.circular(50),
          ),
          minimumSize: Size(popupSize.width, 35),
          onPressed: onClose),
    );
  }

  // Function to show the dialogue
  void _showSubmissionDialgoue(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return SingleActionDialogue(
          title: '',
          message: SLStrings.getTranslatedString(KEY_COMMENT_SUBMITTED),
          iconWidget: const Icon(Icons.check_circle, size: 60),
          buttonText: SLStrings.getTranslatedString(KEY_OK),
          isDefaultAction: true,
          handleOkCallback: () {},
        );
      },
    ).then((value) => onSubmit!());
  }
}

mixin CourseVideoViewScreenMixin {
  // TO DO: Remove later: Checkpoint

  /// corresponding answer/option index of the question
  // final List<int> _selectedAnswerIndexes = [];

  /// list of all the answers student attended
  // final List<Map<String, dynamic>> _answeredQuestionList = [];

  /// json to submit to api
  // Map<String, dynamic> _examJson = {};

  /*
  _startExam(BuildContext context, ExamCubit _examCubit,
      QuestionData questionData, CheckPoint item) async {
    try {
      int examDuration = questionData.duration;
      _examCubit.setLoading(true);
      await _examCubit.startExam(
          questionData.questions, questionData.questions[0].quizId ?? '');

      ExamState _state = _examCubit.state;
      if (_state is ExamPGException) {
        /// no quiz id
        /// request has been rejected by admin
        /// show popup
        _examCubit.setLoading(false);
        _showPGExceptionPopup(context, _state.error);
      } else if (_examCubit.state is ExamQuizIdFailure) {
        /// no quiz id
        /// request has been rejected by admin
        /// show popup
        _examCubit.setLoading(false);
        _showInvalidQuizIdPopup(context);
      } else {
        _examCubit.setLoading(true);
        SharedPreferences _pref = await SharedPreferences.getInstance();
        String quizAttemptId = _pref.getString('quiz_attempt_id') ?? '';
        if (isFromSectionDetails == true) {
          _pref.setBool('isFromSectionDetails', true);
        } else {
          _pref.setBool('isFromSectionDetails', false);
        }
        if (quizAttemptId.isNotEmpty) {
          await _examCubit.saveQstnsToDB(questionData);
          // appRouter.pop();
          showExamPopup(context, item);
          // appRouter.pop();
          // appRouter.push(
          //   ExamViewRoute(
          //       examDuration: examDuration,
          //       shouldShowTimer: true,
          //       isFromViewResult: false,
          //       questionData: questionData,
          //       quizAttemptId: quizAttemptId),
          // );
          _examCubit.setLoading(false);
        } else {
          appRouter.pop();
          _examCubit.setLoading(false);
          _showInvalidQuizIdPopup(context);
        }
      }
    } on Exception catch (e) {
      debugPrint('[Exam Intro View][StartExam]: $e');
    }
  }


  showExamPopup(BuildContext context, CheckPoint item) {
    showDialog(context: context, builder: (context) => examSlider(item));
  }


  Widget examSlider(CheckPoint item) {
    return PopScope(
      onWillPop: () async => false,
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Container(
          width: 500,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            // color: const Color.fromARGB(255, 238, 219, 219),
          ),
          child: BlocBuilder<ExamCubit, ExamState>(
            builder: (context, state) {
              final _examCubit = BlocProvider.of<ExamCubit>(context);
              if (state is ExamListChangeIndex) {
                _currentIndex = state.changedIndex;
              } else if (state is ExamMilestoneCheckBoxStatus) {
                _checkStatus = state.status;
                selectedAnswerIndexes = state.selectedAnswerIndexes;
              }
              return !_fullScreenView
                  ? Container(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                              alignment: Alignment.topLeft,
                              child: Countdown(
                                  controller: countdownController,
                                  seconds: _questionData!.duration * 60,
                                  build: (context, time) {
                                    Duration hours =
                                        Duration(seconds: time.toInt());
                                    String countDownValue = hours.toString();
                                    countDownValue =
                                        countDownValue.split('.').first;
                                    return Text(
                                      countDownValue,
                                      style: LMSFonts.mediumFont(
                                          15, LmsColors.appBarGreen, 1.0),
                                    );
                                  })),
                          CarouselSlider.builder(
                            carouselController: _carouselController,
                            itemCount: _questions.length,
                            itemBuilder: (context, index, realIndex) {
                              final tempList = List.generate(
                                  _questions.length, (int index) => -1,
                                  growable: false);
                              final tempStringList = List.generate(
                                  _questions.length, (index) => '');
                              if (selectedAnswerIndexes.isEmpty) {
                                selectedAnswerIndexes.addAll(tempList);
                                // selectedAnswerIds.addAll(tempStringList);
                              }
                              return SizedBox(
                                  height: 450,
                                  child: Column(
                                    children: [
                                      Text("Question : ${index + 1}"),
                                      const SizedBox(height: 5),
                                      Container(
                                        height: 150,
                                        alignment: Alignment.center,
                                        child: TeXView(
                                          child: TeXViewDocument(
                                              _questions[index].questionText ??
                                                  "No Data"),
                                          renderingEngine:
                                              const TeXViewRenderingEngine
                                                  .katex(),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 350,
                                        child: ListView.separated(
                                            shrinkWrap: true,
                                            itemBuilder: (context, listIndex) {
                                              return Container(
                                                  padding:
                                                      const EdgeInsets.all(8),
                                                  margin:
                                                      const EdgeInsets.all(5),
                                                  decoration: BoxDecoration(
                                                    border: Border.all(),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                    color: selectedAnswerIds
                                                            .value
                                                            .contains(_questions[
                                                                    index]
                                                                .options[
                                                                    listIndex]
                                                                .answerId)
                                                        ? AppTheme
                                                            .primaryAppColor
                                                        : Colors.white,
                                                    // color: selectedAnswerIndexes
                                                    //         .isEmpty
                                                    //     ? Colors.white
                                                    //     : selectedAnswerIndexes
                                                    //                 .length ==
                                                    //             index
                                                    //         ? Colors.white
                                                    //         : selectedAnswerIndexes[
                                                    //                     index] ==
                                                    //                 listIndex +
                                                    //                     1
                                                    //             ? Color
                                                    //                 .fromARGB(
                                                    //                     255,
                                                    //                     155,
                                                    //                     227,
                                                    //                     243)
                                                    //             : Colors.white,
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      Flexible(
                                                        child: TeXView(
                                                          child: TeXViewInkWell(
                                                              rippleEffect:
                                                                  false,
                                                              onTap: (_) async {
                                                                print(
                                                                    "_onAnswerSelected => $index, $listIndex, ${_questions[index].options[listIndex].id},");
                                                                print(
                                                                    "_onAnswerSelected2 => ${_questions[index].options[listIndex]},");
                                                                await _onAnswerSelected(
                                                                    _examCubit,
                                                                    _questions[
                                                                        index],
                                                                    _questions[index]
                                                                            .options[listIndex]
                                                                            .id -
                                                                        1,
                                                                    index,
                                                                    selectedQuestionIndex,
                                                                    selectedAnswerIds);
                                                              },
                                                              // onTap: (_) {
                                                              //   final answerId =
                                                              //       _questions[
                                                              //               index]
                                                              //           .options[
                                                              //               listIndex]
                                                              //           .id;
                                                              //   selectedAnswerIndexes
                                                              //       .removeAt(
                                                              //           index);
                                                              //   selectedAnswerIndexes
                                                              //       .insert(
                                                              //           index,
                                                              //           answerId);
                                                              //   selectedAnswerIds[
                                                              //       index] = _questions[
                                                              //               index]
                                                              //           .options[
                                                              //               listIndex]
                                                              //           .answerId ??
                                                              //       '';
                                                              //   _examCubit
                                                              //       .updateCheckBox(
                                                              //           false,
                                                              //           selectedAnswerIndexes);
                                                              // },
                                                              child: TeXViewDocument(_questions[
                                                                          index]
                                                                      .options[
                                                                          listIndex]
                                                                      .answerVal ??
                                                                  "No Data"),
                                                              id: listIndex
                                                                  .toString()),
                                                        ),
                                                      ),
                                                    ],
                                                  ));
                                            },
                                            separatorBuilder:
                                                (BuildContext context,
                                                        int index) =>
                                                    const Divider(),
                                            itemCount: _questions[index]
                                                    .options
                                                    .length ??
                                                0),
                                      ),
                                    ],
                                  ));
                            },
                            options: CarouselOptions(
                              height: 550,
                              viewportFraction: 1,
                              enableInfiniteScroll: false,
                              onPageChanged: (index, _) {
                                _examCubit.changeIndex(index);
                              },
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              _currentIndex != 0
                                  ? IconButton(
                                      onPressed: () {
                                        _carouselController.previousPage();
                                      },
                                      icon: BACK_ARROW)
                                  : const SizedBox(),
                              _currentIndex != (_questions.length - 1)
                                  ? IconButton(
                                      onPressed: () {
                                        _carouselController.nextPage();
                                      },
                                      icon: const Icon(Icons.arrow_forward_ios))
                                  : IconButton(
                                      onPressed: () async {
                                        await _submitExam(context, _examCubit);
                                        // if (!selectedAnswerIndexes
                                        //     .contains(-1)) {
                                        //   Navigator.of(context).pop();
                                        //   onClosePopup(item,
                                        //       selectedAnswerIds.toString());
                                        // } else {
                                        //   final index =
                                        //       selectedAnswerIndexes.indexWhere(
                                        //           (element) => element == -1);
                                        //   _carouselController
                                        //       .animateToPage(index);
                                        // }
                                      },
                                      icon: const Icon(Icons.check)),
                            ],
                          ),
                        ],
                      ),
                    )
                  : Container(
                      child: Column(
                        children: [
                          CarouselSlider.builder(
                              carouselController: _carouselController,
                              itemCount: _questions.length,
                              itemBuilder: (context, index, _) {
                                final tempList = List.generate(
                                    _questions.length, (int index) => -1,
                                    growable: false);
                                final tempStringList = List.generate(
                                    _questions.length ?? 0, (index) => '');
                                if (selectedAnswerIndexes.isEmpty) {
                                  selectedAnswerIndexes.addAll(tempList);
                                  // selectedAnswerIds.addAll(tempStringList);
                                }
                                return Container(
                                  height: 300,
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 350,
                                        child: Column(
                                          children: [
                                            Text("Question : ${index + 1}"),
                                            const SizedBox(height: 5),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: TeXView(
                                                child: TeXViewDocument(
                                                    _questions[index]
                                                            .questionText ??
                                                        "No Data"),
                                                renderingEngine:
                                                    const TeXViewRenderingEngine
                                                        .katex(),
                                              ),
                                            ),
                                            Container(
                                              margin: EdgeInsets.only(top: 90),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  _currentIndex != 0
                                                      ? IconButton(
                                                          onPressed: () {
                                                            _carouselController
                                                                .previousPage();
                                                          },
                                                          icon: const Icon(Icons
                                                              .arrow_back_ios))
                                                      : const SizedBox(),
                                                  _currentIndex !=
                                                          (_questions.length -
                                                              1)
                                                      ? IconButton(
                                                          onPressed: () {
                                                            _carouselController
                                                                .nextPage();
                                                          },
                                                          icon: const Icon(Icons
                                                              .arrow_forward_ios))
                                                      : IconButton(
                                                          onPressed: () {
                                                            if (!selectedAnswerIndexes
                                                                .contains(-1)) {
                                                              Navigator.of(
                                                                      context)
                                                                  .pop();
                                                              onClosePopup(
                                                                  item,
                                                                  selectedAnswerIds
                                                                      .toString());
                                                            } else {
                                                              final index = selectedAnswerIndexes
                                                                  .indexWhere(
                                                                      (element) =>
                                                                          element ==
                                                                          -1);
                                                              _carouselController
                                                                  .animateToPage(
                                                                      index);
                                                            }
                                                          },
                                                          icon: const Icon(
                                                              Icons.check)),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        child: Column(
                                          children: [
                                            ListView.separated(
                                                shrinkWrap: true,
                                                itemBuilder:
                                                    (context, listIndex) {
                                                  return Container(
                                                      padding:
                                                          EdgeInsets.fromLTRB(
                                                              20, 2, 0, 2),
                                                      decoration: BoxDecoration(
                                                        border: Border.all(),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(16),
                                                        color: selectedAnswerIndexes
                                                                .isEmpty
                                                            ? Colors.white
                                                            : selectedAnswerIndexes
                                                                        .length ==
                                                                    index
                                                                ? Colors.white
                                                                : selectedAnswerIndexes[
                                                                            index] ==
                                                                        listIndex +
                                                                            1
                                                                    ? Color
                                                                        .fromARGB(
                                                                            255,
                                                                            155,
                                                                            227,
                                                                            243)
                                                                    : Colors
                                                                        .white,
                                                      ),
                                                      child: TeXView(
                                                        child: TeXViewInkWell(
                                                            rippleEffect: false,
                                                            onTap: (_) {
                                                              selectedAnswerIndexes[
                                                                  index] = _questions[
                                                                          index]
                                                                      .options[
                                                                          listIndex]
                                                                      .id ??
                                                                  -1;
                                                              // selectedAnswerIds[
                                                              //     index] = _questions[
                                                              //             index]
                                                              //         .options[
                                                              //             listIndex]
                                                              //         .answerId ??
                                                              //     '';
                                                              _examCubit
                                                                  .updateCheckBox(
                                                                      false,
                                                                      selectedAnswerIndexes);
                                                            },
                                                            child: TeXViewDocument(
                                                                _questions[index]
                                                                        .options[
                                                                            listIndex]
                                                                        .answerVal ??
                                                                    "No Data"),
                                                            id: listIndex
                                                                .toString()),
                                                      ));
                                                },
                                                separatorBuilder:
                                                    (BuildContext context,
                                                            int index) =>
                                                        const Divider(),
                                                itemCount: _questions[index]
                                                        .options
                                                        .length ??
                                                    0),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                              options: CarouselOptions(
                                height: 250,
                                viewportFraction: 1,
                                enableInfiniteScroll: false,
                                onPageChanged: (index, _) {
                                  _examCubit.changeIndex(index);
                                },
                              )),
                        ],
                      ),
                    );
            },
          ),
        ),
      ),
    );
  }



  _onAnswerSelected(
    ExamCubit examCubit,
    Question quest,
    int answerItemIndex,
    int questionIndex,
    ValueNotifier<int> selectedQuestionIndex,
    ValueNotifier<List<String>> selectedAnswerIds,
  ) async {
    String tempSelectedAnswerId = quest.options[answerItemIndex].answerId;

    if (selectedAnswerIds.value.contains(tempSelectedAnswerId)) {
      // remove if answer is already selected()- toggle action
      selectedAnswerIds.value.remove(tempSelectedAnswerId);
      _selectedAnswerIndexes.remove(answerItemIndex);
      // ignore: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member
      selectedAnswerIds.notifyListeners();
      quest.selectedAnswerIndexes.remove(answerItemIndex);
      quest.selectedAnswerIds.remove(tempSelectedAnswerId);
    } else {
      // add into list if answer is not selected()toggle action
      selectedAnswerIds.value.add(quest.options[answerItemIndex].answerId);
      // ignore: invalid_use_of_visible_for_testing_member, invalid_use_of_protected_member
      selectedAnswerIds.notifyListeners();
      _selectedAnswerIndexes.add(answerItemIndex);
      quest.selectedAnswerIndexes.add(answerItemIndex);
      quest.selectedAnswerIds.add(tempSelectedAnswerId);
    }
    if (quest.selectedAnswerIds.isNotEmpty) {
      quest.questionsStatus = QuestionStatus.answered;
    } else {
      quest.questionsStatus = QuestionStatus.unAnswered;
    }
    examCubit.updateColor(
        quest.options[answerItemIndex].answerVal, _selectedAnswerIndexes);
    _saveAnswer(quest, selectedQuestionIndex.value, examCubit);
  }

  _saveAnswer(Question answeredQuestion, int selectedQuestionIndex,
      ExamCubit examCubit) async {
    examCubit.saveAnswer(_questionData!, answeredQuestion,
        selectedQuestionIndex, _answeredQuestionList, _examJson);
  }

  _finalizeSubmitJson() {
    String quizId = _questionData?.questions[0].quizId ?? '';
    Map<String, dynamic> _newQuestion = {};
    List<String> _answeredQuestionIds =
        _answeredQuestionList.map((e) => e["question_id"].toString()).toList();

    for (var unAnsweredQstn in _questionData!.questions) {
      _answeredQuestionIds.toSet().toList();
      if (_answeredQuestionIds.contains(unAnsweredQstn.questionId)) {
        continue;
      } else {
        if (unAnsweredQstn.questionsStatus != QuestionStatus.answered) {
          _newQuestion = {
            "order_id": unAnsweredQstn.id,
            "question_id": unAnsweredQstn.questionId,
            "question_with_options": unAnsweredQstn.questionText,
            "response_summary": '',
            "selected_answer_ids": unAnsweredQstn.selectedAnswerIds,
          };
          _answeredQuestionList.add(_newQuestion);
          _answeredQuestionIds.add(unAnsweredQstn.questionId ?? '');
        }
      }
    }
    _answeredQuestionList.sort(
      (a, b) {
        int orderID1 = a["order_id"];
        int orderID2 = b["order_id"];
        return orderID1.compareTo(orderID2);
      },
    );
    _examJson = {
      "org_id": ORG_ID,
      "quiz_id": quizId,
      "user_id": USER_ID,
      "submit_datas": _answeredQuestionList,
    };
  }

  /// Submit the selected details to server
  _submitExam(BuildContext parentContext, ExamCubit _examCubit) async {
    countdownController.pause();
    ExamState state = _examCubit.state;
    _finalizeSubmitJson();
    _examCubit.setLoading(true);

    await _examCubit.submitExam(_examJson, _questionData!);
    state = _examCubit.state;
    _examCubit.setLoading(true);
    if (state is ExamsQuestionsFailureState) {
      if (state.error.contains(FAILED_HOST_LOOKUP)) {
        _examCubit.setLoading(false);
        _showExamError(parentContext, _examCubit, EXAM_NETWORK);
      } else {
        _examCubit.setLoading(false);
        _showExamError(parentContext, _examCubit, state.error);
      }
    } else {
      _examCubit.enableSubmitBtn(false);
      _examCubit.setLoading(true);
      String quizId = _questionData?.questions.first.quizId ?? '';
      SharedPreferences _pref = await SharedPreferences.getInstance();
      String quizAttemptId = _pref.getString('quiz_attempt_id') ?? '';
      await _examCubit.calculateQuizGrades(quizId, quizAttemptId);
    }
  }

  _showExamError(BuildContext context, ExamCubit examCubit, String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return CustomAlertDialog(
          title: WARNING,
          message: error.isNotEmpty ? error : EXAM_ALREADY_ATTEND,
        );
      },
    ).then((value) async {
      appRouter.pop();
    });
  }



  List<Map<String, dynamic>> parseMilestones(String inputString) {
    // Remove unnecessary characters and split the string into individual Milestone strings
    List<String> milestoneStrings = inputString
        .replaceAll('[', '')
        .replaceAll(']', '')
        .split('Milestone')
        .where((element) => element.trim().isNotEmpty)
        .toList();

    // Parse each Milestone string and convert it to a Map
    List<Map<String, dynamic>> milestoneMaps =
        milestoneStrings.map((milestoneString) {
      // Extracting id, milestone, type, and milestoneDuration using regular expressions
      RegExp exp = RegExp(
          r'id: (\d+), milestone: (\d+), type: (\w+), milestoneDuration: (\d+)');
      var match = exp.firstMatch(milestoneString);

      // Creating a map from the extracted values
      return {
        "id": int.parse(match!.group(1)!),
        "milestone": int.parse(match.group(2)!),
        "type": match.group(3)!,
        "milestoneDuration": int.parse(match.group(4)!),
      };
    }).toList();

    return milestoneMaps;
  }

  List<Milestone> parseToDurationList(String jsonString) {
    List<dynamic> parsedJson = json.decode(jsonString);

    // Map each dynamic element in the list to a Milestone object
    List<Milestone> milestones =
        parsedJson.map((json) => Milestone.fromJson(json)).toList();

    return milestones;
  }

  List<Milestone> convertMilestoneDurations(String durations) {
    List<Milestone> durationList = [];
    List<Map<String, dynamic>> milestones = parseMilestones(durations);
    durationList =
        milestones.map((e) => Milestone.fromJson(jsonEncode(e))).toList();
    return durationList;
  }

    void startCounter() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      counter.value++;
    });
  }

  // TO DO: Checkpoint- Remove later
  List<Positioned> _setCheckPoints(
      BuildContext context, CourseListCubit _courseCubit) {
    if (courseVideo.isCheckPointEnabled && checkPoints.isNotEmpty) {
      final startTimes = checkPoints.map((e) {
        return convertCheckPointTimetoSec(e.startTime) * 1000;
      }).toList();
      final items = startTimes.map((e) {
        double currentPosition = 0.0;
        // if(isFromExamScreen){
        //   e /
        // }
        if (_youtubePlayerController?.metadata.duration.inMilliseconds != 0) {
          currentPosition =
              e / _youtubePlayerController!.metadata.duration.inMilliseconds;
          print("currentPosition => $currentPosition");
        }
        return Positioned(
            left: _fullScreenView
                ? (MediaQuery.sizeOf(context).width * 0.75) * currentPosition
                : (MediaQuery.sizeOf(context).width * 0.60) * currentPosition,
            child: Container(
              color: Colors.red,
              height: 2,
              width: _fullScreenView ? 5 : 2,
            ));
      }).toList();
      // stackList = [
      //   LinearProgressIndicator(
      //     value: _youtubePlayerController?.value.isReady ?? false
      //         ? _getVideoPercentage()
      //         : 0,
      //     backgroundColor: AppTheme.whiteColor,
      //     color: AppTheme.bgColor,
      //   ),
      // ];
      stackList.addAll(items);
      return stackList;
    } else {
      return stackList;
    }
  }
*/
}
